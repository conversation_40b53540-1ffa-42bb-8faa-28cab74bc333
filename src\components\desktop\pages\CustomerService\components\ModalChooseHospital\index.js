// components
import {
  MDBCol,
  MDBContainer,
  MDBModal,
  MDBModalBody,
  MDBModalHeader,
  MDBRow
} from "mdbreact";
import React, { Component } from "react";
import InputSearch from "~/components/common/atoms/InputSearch";
import Select from "~/components/common/atoms/Select";
import ListHospital from "~/components/common/molecules/ListHospital";
import { getReplaceUTF8 } from "~/utils/getReplaceUTF8";
import styles from "./style.module.scss";

// needed props: HospitalList, toggle, open itemclickfunction

class ChooseHospitalModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      listHospitalFilter: [],
      loading: false,
      search: "",
      cityId: 0,
      typeHospital: "",
      modalMessage: "",
      isShowModalMessage: false
    };
  }

  toggleLoading = status => {
    this.setState({
      loading: status
    });
  };

  handleFilterByCity = (id, list) => {
    if (Number(id) === 0) {
      return list;
    }
    return list.filter(item => item.city_id === id);
  };

  handleFilterByType = (type, list) => {
    if (type === "") {
      return list;
    }
    return list.filter(item => item.type === type);
  };

  handleFilterByKeyword = (value, list) => {
    const listTemp = list.filter(item => {
      const regex = new RegExp(getReplaceUTF8(value), "i");
      return getReplaceUTF8(item.name).match(regex);
    });
    return listTemp;
  };

  handleSearchHospital = e => {
    const value = e.target.value;
    this.setState(
      {
        search: value
      },
      () => {
        this.filterListHospital();
      }
    );
  };

  handleSelectHospitalType = typeHospital => {
    // this.setState(
    //   {
    //     typeHospital
    //   },
    //   () => {
    //     this.filterListHospital();
    //   }
    // );
  };

  handleSelectCity = cityId => {
    this.setState(
      {
        cityId
      },
      () => {
        this.filterListHospital();
      }
    );
  };

  filterListHospital = () => {
    const { search, cityId, typeHospital } = this.state;
    const { hospitalList } = this.props;
    let list = [];
    // filter theo city
    list = this.handleFilterByCity(cityId, hospitalList);
    // filter theo type
    list = this.handleFilterByType(typeHospital, list);
    // filter theo keyword
    list = this.handleFilterByKeyword(search, list);
    this.setState({
      listHospitalFilter: list
    });
  };

  //hospital List Handlers
  handleChooseHospital = item => {
    this.props.changeHospitalOnStore(item);
    this.props.toggle();
  };

  toggleModalChoosePatient = () => {
    this.setState({
      isShowModalMessage: !this.state.isShowModalMessage
    });
  };

  componentDidMount() {
    this.filterListHospital();
  }

  componentWillUnmount() {
    this.toggleLoading(false);
  }

  render() {
    return (
      <MDBModal
        isOpen={this.props.openModal}
        toggle={this.props.toggle}
        size="lg"
      >
        <MDBModalHeader toggle={this.props.toggle}>
          Chọn bệnh viện
        </MDBModalHeader>
        <MDBModalBody>
          <MDBContainer>
            <MDBRow>
              <MDBCol size={12} style={{ padding: 0 }}>
                <div className={styles.wapper_page_inner}>
                  <div className={styles.search_benhvien}>
                    <div className={styles.select_group}>
                      {/* phần này dùng tạm khi chưa có filter theo tag --> style inline luôn */}
                      <div
                        className={styles.input_group}
                        style={{
                          width: "50%",
                          margin: 0,
                          paddingRight: 5
                        }}
                      >
                        <InputSearch
                          title="Tìm nhanh bệnh viện"
                          icon={<i className="fal fa-search" />}
                          onChange={this.handleSearchHospital}
                          value={this.state.search}
                        />
                      </div>
                      {/* end */}
                      <div className={styles.select_city}>
                        <Select
                          data={this.props.allCities}
                          onChange={this.handleSelectCity}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </MDBCol>
              <MDBCol size={12} style={{ padding: 0 }}>
                <ListHospital
                  listHospitalFilter={this.state.listHospitalFilter}
                  toggleLoading={this.toggleLoading}
                  message={this.state.modalMessage}
                  isShowMessage={this.state.isShowModalMessage}
                  toggleModalChoosePatient={this.toggleModalChoosePatient}
                  handleChooseHospital={this.handleChooseHospital}
                />
              </MDBCol>
            </MDBRow>
          </MDBContainer>
        </MDBModalBody>
      </MDBModal>
    );
  }
}

export default ChooseHospitalModal;
