import React, { Component } from "react";
import { connect } from "react-redux";
import {
  MDBContainer,
  MDBRow,
  MDBCol,
  MDBCardBody,
  MDBCard,
  MDBCardHeader
} from "mdbreact";
import ListCalendar from "~/components/common/molecules/ListCalendar";
import { requestHoliday } from "~/store/resource/resourceAction";
import { range } from "lodash";
import styles from "./style.module.scss";
import TimeSlotList from "~/components/desktop/molecules/TimeSlotList";
import PatientInfomationBooking from "~/components/desktop/molecules/PatientInfomationBooking";
import TagName from "~/components/common/atoms/TagName";
import PKHBtn from "~/components/common/atoms/Button";
import {
  showMessageError,
  submitDoctorAndTime,
  selectedRoomAndDoctor,
  resetRedirectToConfirmInfo,
  requestTimeSlotInDoctoFlow,
  selectedTime,
  removeDoctorAndTime
} from "~/store/room/roomAction";
import * as dateAndSpecialistActions from "~/store/dateAndSpecialist/dateAndSpecialistActions";
import {
  resetSelectDate,
  resetSelectTimeSlot
} from "~/store/totalData/actions";
import cx from "classnames";
import partnerId from "~/utils/partner";
class ChooseCalendarDoctorFlow extends Component {
  constructor(props) {
    super(props);
    this.buttonRef = React.createRef();
  }

  afterSelectedDate = () => {
    this.props.resetSelectTimeSlot();
  };

  handleCloseToChooseAgain = () => {
    this.props.closeToChooseCalendarAgain(); // fn show full calendar
    this.props.resetSelectDate();
    this.props.resetSelectTimeSlot();
  };

  componentDidMount() {
    this.props.selectedShowOneRow();
  }

  componentWillUnmount() {
    this.props.OnResetRedirectToConfirmInfo();
  }

  render() {
    const { rebookingSelected, dateSelected } = this.props;

    let disabledWeekdays = [];
    if (rebookingSelected) {
      disabledWeekdays = range(0, 7);
      const { detail = [] } = rebookingSelected;
      detail.forEach(({ ptime }) => {
        disabledWeekdays = disabledWeekdays.filter(
          weekday => weekday !== +ptime.weekday
        );
      });
    }

    return (
      <div
        className={cx(
          styles.wapper_page_desktop,
          styles["wapper_page_desktop_" + partnerId]
        )}
      >
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="3">
              <PatientInfomationBooking />
            </MDBCol>
            <MDBCol md="12" lg="9">
              <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between",
                      "title_header_mobile"
                    ]}
                  >
                    <span>Vui lòng chọn ngày khám</span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody
                  className={cx(styles.card_body, styles.body_calendar)}
                >
                  <ListCalendar
                    afterSelectedDate={this.afterSelectedDate}
                    noBookingAfterTime="16:30:00"
                  />
                </MDBCardBody>
                <div
                  ref={this.buttonRef}
                  className={styles.choose_time}
                  style={
                    dateSelected ? { display: "block" } : { display: "none" }
                  }
                >
                  <div className={styles.box_list_time}>
                    <TimeSlotList onNextStep={this.props.handleNextStep} />
                    <PKHBtn onClick={this.handleCloseToChooseAgain}>
                      <i className="fal fa-times" />
                      Đóng
                    </PKHBtn>
                  </div>
                </div>
              </MDBCard>
              <div className={styles.next_prev}>
                <PKHBtn
                  backdesktop="backdesktop"
                  onClick={this.props.handleGoBack}
                >
                  Quay lại
                </PKHBtn>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    rebooking: { rebookingSelected },
    doctor: { selectedDoctor },
    doctorAndTime: { selectedTime },
    dateAndSpecialist,
    dateAndSpecialist: { selectedSpecialist }
  } = state;
  return {
    rebookingSelected,
    selectedDoctor,
    selectedTime,
    dateAndSpecialist,
    selectedSpecialist
  };
};

const mapDispatchToProps = dispatch => ({
  requestHoliday: () => {
    dispatch(requestHoliday());
  },
  requestTimeSlotInDoctoFlow: () => dispatch(requestTimeSlotInDoctoFlow()),
  showMessageError: message => {
    dispatch(showMessageError(message));
  },
  onSubmitDoctorAndTime: dateAndSpecialist => {
    dispatch(submitDoctorAndTime(dateAndSpecialist));
  },
  selectedRoomAndDoctor: room => dispatch(selectedRoomAndDoctor(room)),
  OnResetRedirectToConfirmInfo: () => {
    dispatch(resetRedirectToConfirmInfo());
  },
  onSelectedTime: time => {
    dispatch(selectedTime(time));
  },
  selectDate: date => dispatch(dateAndSpecialistActions.selectedDate(date)),
  closeToChooseCalendarAgain: () => {
    dispatch(dateAndSpecialistActions.closeToChooseCalendarAgain());
  },
  selectedShowOneRow: () =>
    dispatch(dateAndSpecialistActions.selectedShowOneRow()),
  onRemoveDoctorAndTime: index => {
    dispatch(removeDoctorAndTime(index));
  },
  resetSelectDate: () => {
    dispatch(resetSelectDate());
  },
  resetSelectTimeSlot: () => {
    dispatch(resetSelectTimeSlot());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ChooseCalendarDoctorFlow);
