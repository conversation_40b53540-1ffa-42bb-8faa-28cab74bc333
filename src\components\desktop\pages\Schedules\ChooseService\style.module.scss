@import "src/assets/scss/pro-desktop/chooseService.scss";
@import "src/assets/scss/pro-common/node-description.scss";
.row_bhyt {
  padding: 0 0 0 65px;
  font-size: 14px;
  font-style: italic;
  @media #{$medium-and-down} {
    padding: 0;
  }
  margin-top: 10px;
  &:first-child {
    margin-top: 0;
  }
}

.error {
  transition: all 0.5s ease-out;
  color: orangered;
  font-size: 13px;
  font-style: italic;
  text-align: center;
  margin-top: 10px;
}

.hiddenErr {
  transition: all 0.5s ease-out;
  display: none;
}

.hidden {
  td {
    transition: all 0.5s ease-out;
    padding: 0 !important;
    border: none;
  }
}
// .active {
//   td {
//     // margin-bottom: 1rem;
//   }
// }

.collapsev {
  width: 98%;
}

.wapper_page_desktop_minhanh {
  .panels {
    .panels_header {
      background-color: #db2233 !important;
    }
  }
}
