@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/extend/placeholder.scss";
@import "src/assets/scss/pro-custom/button.scss";
.list_group {
  min-height: 450px;
  li {
    border: none;
  }
}

.red {
  color: red;
}
.pagination {
  padding: 0;
  list-style: none;
  li {
    border: 0;
    display: inline-block;
    vertical-align: middle;
    a {
      position: relative;
      display: block;
      padding: 0.5rem 0.75rem;
      margin-left: -1px;
      line-height: 1.25;
      color: #212529;
      outline: none;
      &:hover {
        background-color: #eee;
        border-radius: 0.125rem;
        transition: all 0.3s linear;
      }
    }
    &.active {
      a {
        color: #212529;
        background-color: #eee;
      }
    }
  }
}

.header {
  .header_pretitle {
    text-transform: uppercase;
    font-size: 0.625rem;
    letter-spacing: 0.08em;
    color: #627792;
  }
  .header_title {
    margin-bottom: 0;
    font-size: 1.425rem;
    font-weight: 500;
  }
}
.notification_panel {
  display: flex;
  position: relative;
  align-items: center;
  border-bottom: 1px solid #ebedf2;
  padding: 1.5rem 0;
  margin-bottom: 1.5rem;
  padding-top: 0px;
  ul {
    flex-direction: row;
    flex-wrap: wrap;
    position: relative;
    list-style: none;
    li {
      display: list-item;
      padding: 0;
      border: 0;
      margin-right: 30px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.timeline {
  line-height: 1.4em;
  list-style: none;
  margin: 0 0px 60px;
  padding: 0;
  width: 100%;
}

.timeline_item {
  padding-left: 40px;
  padding-top: 0;
  position: relative;
  border: 0 !important;
  &:last-child {
    padding-bottom: 0;
  }
  .delete_noti_button {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }
  .view_detail_noti {
    cursor: pointer;
    text-align: right;
    &:hover {
      color: $primary-color !important;
    }
  }
}

/*----- TIMELINE INFO -----*/
.timeline_info {
  font-weight: 500;
  margin: 0 0 0.5em 0;
  text-transform: uppercase;
  white-space: nowrap;
  font-size: 0.625rem;
  letter-spacing: 0.08em;
  color: #627792;
}
/*----- TIMELINE MARKER -----*/
.timeline_marker {
  position: absolute;
  top: 4px;
  bottom: 0;
  left: 0;
  width: 15px;
  &:before {
    background: $primary-color;
    border: 3px solid transparent;
    border-radius: 100%;
    content: "";
    display: block;
    height: 15px;
    top: 4px;
    left: 0;
    width: 15px;
  }
  &:after {
    content: "";
    width: 3px;
    background: #ebedf2 !important;
    display: block;
    position: absolute;
    top: 18px;
    bottom: 0;
    left: 6px;
  }
}
.timeline_item:not(.period):hover .timeline_marker:before {
  background: transparent;
  border: 3px solid #007bff;
}
.is_not_read {
  color: #000 !important;
  font-weight: 500;
}

/*----- TIMELINE CONTENT -----*/

.timeline_content {
  padding-bottom: 20px;
  cursor: pointer;
  font-size: 0.875rem;
  h3 {
    font-size: 16px;
    font-weight: 500;
  }
  p:last-child {
    margin-bottom: 0;
    color: #627792 !important;
    font-size: 0.875rem;
  }
}

//  ------------Minh anh

.wapper_page_inner_minhanh {
  .header {
    .header_body {
      .notification_panel {
        .btn_medicalBill {
          border: 1px solid #db2233 !important;
          color: #db2233 !important;
        }
      }
    }
  }

  .timeline_item {
    .timeline_marker {
      &:before {
        background: #db2233;
      }
    }
  }
  .timeline_item:not(.period):hover .timeline_marker:before {
    background: transparent;
    border: 3px solid #db2233;
  }
}
