import React, { Component } from "react";
import { connect } from "react-redux";
import { with<PERSON>out<PERSON> } from "react-router-dom";
import {
  MDBContainer,
  MDBRow,
  MDBCol,
  MDBCardHeader,
  MDBCard,
  MDBCardBody,
  MDBBreadcrumb,
  MDBBreadcrumbItem
} from "mdbreact";
import PatientInfomationLess from "~/components/desktop/molecules/PatientInfomationLess";
import PatientInfomationBooking from "~/components/desktop/molecules/PatientInfomationBooking";
import PKHBtn from "~/components/common/atoms/Button";
import ListRoom from "~/components/desktop/molecules/ListRoom";
import cx from "classnames";
import TagName from "~/components/common/atoms/TagName";
import Modal from "~/components/common/molecules/Modal";
import styles from "./style.module.scss";
import {
  requestRoomList,
  resetSelectTimeAndBHYT,
  submitDoctorAndTime,
  showMessageError,
  resetSelectDoctor,
  removeDoctorAndTime
} from "~/store/room/roomAction";
import { resetSelectDateAndSpecialist } from "~/store/dateAndSpecialist/dateAndSpecialistActions";
import BreadcrumbStream from "~/components/desktop/molecules/BreadCrumbStream";
class ChooseRoomAndTime extends Component {
  constructor(props) {
    super(props);
    this.scrollToDiv = React.createRef();
    this.state = {
      isShowModalMissInformation: false,
      messageErrorMissInfomation: ""
    };
  }

  toggleModalMissInformation = () => {
    this.setState({
      isShowModalMissInformation: !this.state.isShowModalMissInformation
    });
  };

  componentDidMount() {
    const { sumaryInfo, selectedSpecialist } = this.props;

    this.props.requestRoomList();
    this.props.resetSelectTimeAndBHYT();
    this.props.resetSelectDoctor();

    // Xử lý nút back bị duplicate phiếu khám
    if (sumaryInfo.length > 0) {
      const index = sumaryInfo.findIndex(
        item => item.selectedSpecialist.id === selectedSpecialist.id
      );
      if (index > -1) {
        this.props.onRemoveDoctorAndTime(sumaryInfo.length - 1);
      }
    }
  }

  onValidateBeforeSubmit = callback => {
    const { selectedBHYT, selectedTime, selectedRoomAndDoctor } = this.props;

    let message = "";
    const chooseRoom = Object.keys(selectedRoomAndDoctor).length;
    const chooseTime = Object.keys(selectedTime).length;
    const chooseBHYT = Object.keys(selectedBHYT).length;

    if (chooseTime > 1 && chooseBHYT > 1 && Number(selectedBHYT.id) > -1) {
      callback();
    } else {
      if (chooseRoom <= 1) {
        message =
          "Vui lòng chọn đầy đủ thông tin: Bác sĩ, giờ khám, hình thức bảo hiểm y tế!";
      } else {
        message = "Vui lòng chọn giờ khám, hình thức bảo hiểm y tế!";
      }
      this.setState({
        isShowModalMissInformation: !this.state.isShowModalMissInformation,
        messageErrorMissInfomation: message
      });
    }
  };

  onSubmitDoctorAndTime = () => {
    const { dateAndSpecialist } = this.props;
    this.props.onSubmitDoctorAndTime(dateAndSpecialist);
  };

  handleGoBack = () => {
    const route = "/umc/chon-lich-kham";
    this.props.history.push(route);
  };

  render() {
    const { selectedBHYT, selectedTime, listRoom } = this.props;
    const { messageErrorMissInfomation } = this.state;
    const objCheckNextStep = Object.create(null);

    const flowSteps = {
      step1: { name: "Ngày khám - Chuyên khoa", class: "prev_step" },
      step2: { name: "Phòng khám - Giờ khám - BHYT", class: "active" },
      step3: { name: "Xác nhận thông tin", class: "" },
      step4: { name: "Thanh toán", class: "" }
    };

    if (
      Object.keys(selectedBHYT).length > 1 &&
      Object.keys(selectedTime).length > 1 &&
      Number(selectedBHYT.id) > -1
    ) {
      objCheckNextStep.nextStep = true;
      if (this.scrollToDiv.current) {
        const {
          top,
          bottom
        } = this.scrollToDiv.current.getBoundingClientRect();
        if (top < 0 || bottom > window.innerHeight) {
          this.scrollToDiv.current.scrollIntoView({
            behavior: "smooth",
            block: "end"
          });
        }
      }
    }

    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow>
            <MDBCol md="4" lg="3">
              <PatientInfomationLess />
              <PatientInfomationBooking />
            </MDBCol>
            <MDBCol md="8" lg="9">
              <BreadcrumbStream flowSteps={flowSteps} />
              <MDBCard className={styles.panels}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between"
                    ]}
                  >
                    <span>Vui lòng chọn phòng và giờ khám </span>
                  </TagName>
                  {/* <PKHBtn create="create">Tiếp tục</PKHBtn> */}
                </MDBCardHeader>
                <MDBCardBody className={styles.card_body}>
                  <div className={styles.box_inner_desktop}>
                    <ListRoom />

                    <div className={styles.next_prev}>
                      <PKHBtn
                        backdesktop="backdesktop"
                        onClick={() => {
                          this.props.resetSelectDateAndSpecialist();
                          // this.props.history.goBack();
                          this.handleGoBack();
                        }}
                      >
                        Quay lại
                      </PKHBtn>
                      {listRoom && listRoom.length > 0 ? (
                        <PKHBtn
                          buttonArrow="buttonArrow"
                          onClick={() => {
                            this.onValidateBeforeSubmit(
                              this.onSubmitDoctorAndTime
                            );
                          }}
                          {...objCheckNextStep}
                          create="create"
                        >
                          Tiếp tục
                        </PKHBtn>
                      ) : null}
                    </div>
                  </div>
                  <Modal
                    modal={this.state.isShowModalMissInformation}
                    iconTitle={<i className="fal fa-bell" />}
                    title="Thông báo"
                    children={messageErrorMissInfomation}
                    toggle={this.toggleModalMissInformation}
                    centered
                    className="centered"
                  />
                </MDBCardBody>
              </MDBCard>
              <div
                className={cx(
                  "alert-primary",
                  styles.wrap_mdbreadcrumb,
                  styles.footer
                )}
              >
                <MDBBreadcrumb>
                  <MDBBreadcrumbItem>Chọn</MDBBreadcrumbItem>
                  <MDBBreadcrumbItem>Phòng khám</MDBBreadcrumbItem>
                  <MDBBreadcrumbItem>Giờ khám</MDBBreadcrumbItem>
                  <MDBBreadcrumbItem>Hình thức bảo hiểm y tế</MDBBreadcrumbItem>
                </MDBBreadcrumb>
              </div>
              <div ref={this.scrollToDiv} />
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    doctorAndTime: {
      data,
      isModalPickTime,
      selectedBHYT,
      selectedTime,
      loading,
      sumaryInfo,
      selectedRoomAndDoctor
    },
    dateAndSpecialist,
    dateAndSpecialist: { selectedSpecialist }
  } = state;
  return {
    listRoom: data,
    isModalPickTime,
    dateAndSpecialist,
    selectedBHYT,
    selectedTime,
    loading,
    sumaryInfo,
    selectedSpecialist,
    selectedRoomAndDoctor
  };
};

const mapDispatchToProps = dispatch => ({
  requestRoomList: () => {
    dispatch(requestRoomList());
  },
  resetSelectTimeAndBHYT: () => {
    dispatch(resetSelectTimeAndBHYT());
  },
  // onChangeBHYT: info => {
  //   dispatch(selectedBHYT(info));
  // },
  onSubmitDoctorAndTime: dateAndSpecialist => {
    dispatch(submitDoctorAndTime(dateAndSpecialist));
  },
  showMessageError: message => {
    dispatch(showMessageError(message));
  },
  resetSelectDoctor: () => {
    dispatch(resetSelectDoctor());
  },
  resetSelectDateAndSpecialist: () => {
    dispatch(resetSelectDateAndSpecialist());
  },
  onRemoveDoctorAndTime: index => {
    dispatch(removeDoctorAndTime(index));
  }
});

export default withRouter(
  connect(
    mapStateToProps,
    mapDispatchToProps
  )(ChooseRoomAndTime)
);
