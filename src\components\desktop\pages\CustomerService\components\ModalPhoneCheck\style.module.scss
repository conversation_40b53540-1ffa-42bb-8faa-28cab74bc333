.modal_container{
    width: 100%;
    height: fit-content;
    padding: 1vw;
    font-size: 1vw;
    border: 2px solid blue;
    border-radius: 0.5vw;
    .error_header {
        margin: auto;
        width: 100%;
        text-align: center;
        font-size: 1.2vw;
        font-weight: bold;
        margin-bottom: 1vw;
    }
    .pair_select {
        width: fit-content;
        display: flex;
        margin: auto;
    }
    .search_phone {
        margin: auto;
        width: 100%;
        .input_group {
            margin: auto;
            width: fit-content;
            // padding: 0vw 12vw;
            margin-top: 2vw;
            input {
                border:none;
                border: 1px solid black;
                width: 15vw;
                height: 2.5vw !important;
                background-color: white;
                border-radius: 0.2vw 0vw 0vw 0.2vw;
            }
            input:focus {
                border: none !important;
            }
            button {
                height: 2.5vw !important;
                width: 8vw;
                // background-image: linear-gradient(90deg, #cc6b03 0%, #c58643 100%);
                border-radius: 0vw 0.2vw 0.2vw 0vw;
                box-shadow: 1px 1px 5px gray;
                font-weight: bold;
                color: rgb(84, 83, 83);
                border: none;
                text-align: left;
                padding-left: 1vw;
                color: white;
            }
            button:active {
                border: none;
                box-shadow: none;
            }
        }
         .search_result {
            width: fit-content;
            margin: auto;
            padding: 1vw;
            background-color: rgb(237, 237, 237);
            box-shadow: 1px 1px 5px gray;
            font-size: 1vw;
            .account_info{
                width: 20vw;
                height: fit-content;
                display: flex;
                padding: 4px;
                border: 3px solid blue;
                color: black;
                .col_30{
                    width: 2vw;
                    line-height: 1.5vw;
                    text-align: center;
                    color: blue;
                }
                .col_70 {
                    width: fit-content;
                    line-height: 1.5vw;
                    text-align: left;
                }
            }
        }
        .button_wrapper {
            width: 100%;
            text-align: right;
        }
        .error_message {
            margin: auto;
            width: fit-content;
            margin-bottom: 2vw;
            color: red;
            font-size: 0.8vw;
        }
    }
   
}
.modal_header{
    font-weight: bold;
    background-image: linear-gradient(90deg, #0352cc 0%, #0b7df1 100%);
    color: white;
}