import cx from "classnames";
import Parser from "html-react-parser";
import { find, get } from "lodash";
import {
  MDBCard,
  MDBCardBody,
  MDBCol,
  MDBCollapse,
  MDBContainer,
  MDBIcon,
  MDBListGroup,
  MDBListGroupItem,
  MDBRow,
  MDBView
} from "mdbreact";
import React, { Component } from "react";
import TagName from "~/components/common/atoms/TagName";
import { partnerInfo } from "~/configs/partnerDetails";
import styles from "./style.module.scss";
import partnerId from "~/utils/partner";
import bannerFaq from "~assets/img/desktop/logo/minhanh/bannerFaq.jpg";
const info = get(partnerInfo, "menu");
const faq = find(info, { key: "thac-mac" });
const allFAQ = get(faq, "content", []);

let timer;
class FaqDetail extends Component {
  state = {
    collapseID: 1,
    groupQuestionId: 1
  };

  toggleCollapse = collapseID => () => {
    this.setState(prevState => ({
      collapseID: prevState.collapseID !== collapseID ? collapseID : ""
    }));
  };

  handleSearchQuestion = e => {
    const searchTerm = e.target.value;
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      this.props.updateShowedFAQBySearch(searchTerm);
    }, 300);
  };

  handleChooseGroupQuestion = q => {
    this.setState({
      groupQuestionId: q.id
    });
  };

  renderFAQGroupName = () => {
    const { groupQuestionId } = this.state;
    if (allFAQ.length > 0) {
      return allFAQ.map((question, index) => {
        const cls = groupQuestionId === question.id ? styles.active : "";
        return (
          <MDBListGroupItem
            key={index}
            href="#"
            className={cls}
            onClick={() => this.handleChooseGroupQuestion(question)}
          >
            {question.name}
          </MDBListGroupItem>
        );
      });
    }
    return "";
  };

  renderListQuestionBySearch = () => {
    const { faq } = this.props;

    const listGroup = get(faq, "showedFAQ.data", []);
    if (listGroup.length > 0) {
      return listGroup.map((group, indexGroup) => {
        return this.renderListQuestion(group.faq);
      });
    }
    return "Không có kết quả phù hợp. Vui lòng thử lại!";
  };

  renderListQuestionByGroup = () => {
    const { groupQuestionId } = this.state;

    if (allFAQ.length > 0) {
      const index = allFAQ.findIndex(group => group.id === groupQuestionId);
      const list = allFAQ[index].faq;

      return this.renderListQuestion(list);
    }
    return "";
  };

  renderListQuestion = listQuestion => {
    const { collapseID } = this.state;
    return listQuestion.map((item, index) => {
      const clsActive = collapseID === item.id ? styles.active : "";
      return (
        <MDBCard className={styles.card_collapse} key={index}>
          <div
            className={cx(styles.card_collapse_item, clsActive)}
            onClick={this.toggleCollapse(item.id)}
          >
            {item.question}
            <MDBIcon
              icon={collapseID === item.id ? "angle-up" : "angle-down"}
            />
          </div>
          <MDBCollapse
            id={item.id}
            isOpen={collapseID === item.id}
            className={styles.collapse_active}
          >
            <MDBCardBody>{Parser(item.answer)}</MDBCardBody>
          </MDBCollapse>
        </MDBCard>
      );
    });
  };

  render() {
    if (allFAQ.length === 0) {
      return (
        <div className={styles.wapper_page_desktop} style={{ padding: 30 }}>
          Nội dung đang được cập nhật...!
        </div>
      );
    }

    const banner = () => {
      switch (partnerId) {
        case "minhanh":
          return bannerFaq;

        default:
          return "";
      }
    };

    return (
      <div
        className={cx(
          styles.wapper_page_desktop,
          styles["wapper_page_desktop_" + partnerId]
        )}
      >
        <div
          className={cx(styles.banner, "d-none d-lg-block")}
          style={{
            backgroundImage: banner() && `url(${banner()})`,
            padding: "305px !important"
          }}
        >
          <MDBView className={styles.img_parallax} fixed>
            <MDBContainer>
              <MDBRow>
                <MDBCol>
                  <div className={styles.wapper_page_head}>
                    <TagName
                      element="h1"
                      className={[
                        "title_component",
                        "title_line",
                        "title_contact"
                      ]}
                    >
                      <span>Thắc Mắc</span>
                    </TagName>
                    <div className={styles.desc}>
                      <p>
                        Giải đáp các câu hỏi nhanh giúp quý khách hiểu rõ hơn về
                        sản phẩm, dịch vụ của chúng tôi.
                      </p>
                    </div>
                  </div>
                </MDBCol>
              </MDBRow>
            </MDBContainer>
          </MDBView>
        </div>
        <MDBContainer>
          <MDBRow>
            <MDBCol>
              <div className={cx("mw-100", styles.wapper_page_inner)}>
                <MDBRow>
                  <MDBCol md="3">
                    <TagName
                      element="h2"
                      className={["title_component", "title_thacmac"]}
                    >
                      <span>Giải đáp nhanh câu hỏi</span>
                    </TagName>
                    <MDBListGroup className={styles.list_category}>
                      {this.renderFAQGroupName()}
                    </MDBListGroup>
                  </MDBCol>
                  <MDBCol>
                    <div className={styles.md_accordion}>
                      {this.renderListQuestionByGroup()}
                    </div>
                  </MDBCol>
                </MDBRow>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default FaqDetail;
