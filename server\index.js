const express = require("express");
const fs = require("fs");
const path = require("path");

const app = express();

const PORT = process.env.PORT || 3000;

const indexPath = path.resolve(__dirname, "..", "build", "page.html");

require("dotenv").config({ path: "server/.env" });

const INDEX_HTML = fs.readFileSync(indexPath, { encoding: "utf8" });

app.use(
  express.static(path.resolve(__dirname, "..", "build"), { maxAge: "30d" })
);

app.use(
  ["/*"],
  express.static(path.resolve(__dirname, "..", "build"), {
    maxAge: "30d"
  })
);

app.get(["/tin-tuc", "/tin-tuc/:slug"], async (req, res) => {
  const { slug } = req.params;
  try {
    if (
      slug === "dia-chi-tiem-meso-uy-tin-o-tphcm" ||
      slug === "dia-chi-tiem-meso-uy-tin-o-tphcm/"
    ) {
      res.redirect(301, `https://medpro.com.vn/tin-tuc/${slug}`);
    }
    return res.status(404).send("<p>Page Not Found 404</p>");
  } catch (e) {
    console.error("handle redirect news error: ", e.toJSON ? e.toJSON() : e);
    return res.status(404).send("<p>Page Not Found 404</p>");
  }
});

app.get(["/walletId=:walletId", "/*"], async (req, res) => {
  return res.send(INDEX_HTML);
});

app.listen(PORT, error => {
  if (error) {
    return console.log("Error during app startup", error);
  }
  console.log("Custom server listening on " + PORT);
});
