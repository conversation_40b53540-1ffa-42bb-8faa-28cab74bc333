import { combineReducers } from "redux";
import global from "~/reducers/global";
import user from "~/store/user/userReducer";
import patientForm from "~/store/patientForm/patientFormReducer";
import supporter from "~/store/supporter/supporterReducer";
import patient from "~/store/patient/patientReducer";
import clinic from "~/store/clinic/clinicReducer";
import prescribe from "~/store/prescribe/prescribeReducer";
import fee from "~/store/fee/feeReducer";
import dateAndSpecialist from "~/store/dateAndSpecialist/dateAndSpecialistReducer";
import doctorAndTime from "~/store/room/roomReducer";
// import allPaymentMethod from "~/reducers/allPaymentMethod";
import rebooking from "~/store/rebooking/rebookingReducer";
import notification from "~/store/notifications/notificationsReducer";
import newArticle from "~/reducers/newArticle";
import healthInsuranceCard from "~/reducers/healthInsuranceCard";
import doctorList from "~/reducers/doctorList";
import umcSubmitPayment from "~/reducers/umcSubmitPayment";
import contact from "~/store/contact/contactReducer";
import faq from "~/store/faq/faqReducer";
import resource from "~/store/resource/resourceReducer";
import hospital from "~/store/hospital/hospitalReducer";
import features from "~/store/features/featuresReducer";
import booking from "~/store/booking/bookingReducer";
import doctor from "~/store/doctor/doctorReducer";
import totalData from "~/store/totalData/reducer";
// import partner from "~/store/partner/reducer";
import followUpExam from "~/store/followUpExam/reducer";
import payment from "~/store/payment/paymentReducer";
import getContent from "~/store/getContent/getContentReducer";
import invoice from "~/store/invoice/reducer";
import partnerUser from "~/store/partnerUser/reducers";
import login from "~/store/login/reducer";
import filter from "~/store/filter/reducer";
import news from "~/store/news/newsReducer";

export default combineReducers({
  news,
  global,
  user,
  patientForm,
  supporter,
  patient,
  clinic,
  prescribe,
  fee,
  dateAndSpecialist,
  faq,
  resource,
  contact,
  doctorAndTime,
  // allPaymentMethod,
  notification,
  newArticle,
  healthInsuranceCard,
  doctorList,
  umcSubmitPayment,
  hospital,
  features,
  booking,
  rebooking,
  totalData,
  doctor,
  // partner,
  followUpExam,
  payment,
  getContent,
  invoice,
  partnerUser,
  login,
  filter
});
