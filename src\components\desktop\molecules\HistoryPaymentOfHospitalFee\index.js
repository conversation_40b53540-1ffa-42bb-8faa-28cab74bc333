import React, { Component, Fragment } from "react";
import { MDBCard } from "mdbreact";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import cx from "classnames";
import ListHistoryPayment from "~/components/common/molecules/ListHistoryPayment";
import Select from "~/components/common/atoms/Select";
import styles from "./style.module.scss";
import { getFeesHistory } from "~/store/fee/feeActions";
import * as actions from "~/store/home/<USER>";
import {
  requestHospitalList,
  selectHospital,
  resetSelectedHospital
} from "~/store/hospital/hospitalActions";
import NoContentAlert from "~/components/desktop/atoms/NoContentAlert";
class HistoryOfHospitalFeePayment extends Component {
  constructor(props) {
    super(props);
    const {
      requestHospitalList,
      resetSelectedHospital,
      selectedHospital
    } = this.props;
    requestHospitalList();
    if (selectedHospital.id) {
      this.getFeesHitory();
    } else {
      resetSelectedHospital();
    }
  }

  getFeesHitory = () => {
    const {
      user,
      handleGetFeesHistory,
      hospitalList,
      selectedHospital
    } = this.props;
    if (hospitalList) {
      const data = {
        user_id: user.user_id,
        access_token: user.access_token,
        base_url: selectedHospital.base_url
      };
      handleGetFeesHistory(data);
    }
  };

  onChangeHospital = value => {
    const {
      user,
      hospitalList,
      handleGetFeesHistory,
      selectHospital,
      resetSelectedHospital
    } = this.props;
    const hospital = hospitalList.find(item => +item.id === +value);
    if (+value) {
      selectHospital(+value);
      if (hospital) {
        const data = {
          user_id: user.user_id,
          access_token: user.access_token,
          base_url: hospital.base_url
        };
        handleGetFeesHistory(data);
      }
    } else {
      resetSelectedHospital();
    }
  };

  render() {
    const { feesHistory, hospitalList, selectedHospital } = this.props;
    return (
      <Fragment>
        <div className={styles.select_patient_records}>
          <form className={styles.form_medpro}>
            <div className={cx("m-0", styles.form_group)}>
              <Select
                data={hospitalList}
                onChange={this.onChangeHospital}
                value={selectedHospital.id || 0}
              />
            </div>
          </form>
        </div>
        {selectedHospital.id ? (
          <MDBCard className={styles.card}>
            {feesHistory && feesHistory.length > 0 ? (
              <ListHistoryPayment feesHistory={feesHistory} />
            ) : (
              <NoContentAlert
                message={`Bạn không có lịch sử thanh toán tại ${selectedHospital.name}.`}
              />
            )}
          </MDBCard>
        ) : null}
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { info },
    fee: { feesHistory },
    hospital: {
      hospitalList: { data },
      selectedHospital
    }
  } = state;
  return {
    user: info,
    feesHistory,
    hospitalList: [{ id: 0, name: "Vui lòng chọn bệnh viện" }, ...data],
    selectedHospital
  };
};

const mapDispatchToProps = dispatch => ({
  requestHospitalListByService: (id, callback) => {
    dispatch(actions.requestHospitalListByServiceCallback(id, callback));
  },
  handleGetFeesHistory: data => {
    dispatch(getFeesHistory(data));
  },
  requestHospitalList: () => dispatch(requestHospitalList()),
  selectHospital: id => dispatch(selectHospital(id)),
  resetSelectedHospital: () => dispatch(resetSelectedHospital())
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(HistoryOfHospitalFeePayment));
