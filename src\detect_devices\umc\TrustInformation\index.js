import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { Redirect } from "react-router-dom";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
const TrustInformationPageMobile = Loadable({
  loader: () =>
    import(/* mobile */ "~/components/mobile/pages/umc/TrustInformation"),
  loading: LoadableLoading
});
const TrustInformationPageDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/umc/TrustInformation"),
  loading: LoadableLoading
});

class DetectTrustInformation extends Component {
  render() {
    const { device, sumaryInfo } = this.props;
    if (sumaryInfo.length === 0) return <Redirect push to="/" />;
    return (
      <React.Fragment>
        {device === "mobile" ? (
          <TrustInformationPageMobile />
        ) : (
          <TrustInformationPageDesktop />
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    doctorAndTime: { sumaryInfo }
  } = state;
  return {
    device: type,
    sumaryInfo
  };
};

const TrustInformationHelmet = withTitle({
  component: DetectTrustInformation,
  title: "Medpro | Xác nhận thông tin đặt khám"
});

export default connect(mapStateToProps)(TrustInformationHelmet);
