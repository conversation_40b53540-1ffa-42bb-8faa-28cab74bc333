import cx from "classnames";
import {
  MDBBreadcrumb,
  MDBBreadcrumbItem,
  MDBCol,
  MDBContainer,
  MDBRow
} from "mdbreact";
import React from "react";
import { connect } from "react-redux";
import { <PERSON>, withRouter } from "react-router-dom";
import { breadCrumb } from "~/utils/breadcrumb";
import styles from "./style.module.scss";
import partnerId from "~/utils/partner";
class MyBreadcrumb extends React.PureComponent {
  render() {
    const {
      location: { pathname },
      className
    } = this.props;
    const filter = breadCrumb(pathname).filter(item => item);
    return (
      <div
        className={cx(styles.bg_breakcum, styles["bg_breakcum_" + partnerId])}
      >
        <MDBContainer>
          <MDBRow>
            <MDBCol size={12}>
              <div className={cx(styles.wrap_mdbreadcrumb, styles[className])}>
                <MDBBreadcrumb>
                  {filter.map((item, index) => {
                    const isActivedCls = item.isActive ? styles.active : "";
                    return (
                      <MDBBreadcrumbItem
                        key={`bc${index}`}
                        className={isActivedCls}
                      >
                        {item.url ? (
                          <Link to={item.url}>{item.title}</Link>
                        ) : (
                          item.title
                        )}
                      </MDBBreadcrumbItem>
                    );
                  })}
                </MDBBreadcrumb>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default connect(null, null)(withRouter(MyBreadcrumb));
