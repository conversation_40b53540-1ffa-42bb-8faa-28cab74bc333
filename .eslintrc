{
  "parser": "babel-eslint",
  "extends": ["standard", "standard-react", "prettier"],
  "plugins": [
    "babel",
    "import",
    "node",
    "react",
    "promise",
    "prettier",
    "standard",
    "react-hooks"
  ],
  "env": {
    "browser": true
  },
  "rules": {
    "prettier/prettier": [
      "error",
      {
        // "singleQuote": true,
        // "trailingComma": "all",
        "bracketSpacing": true,
        // "jsxBracketSameLine": true,
        "parser": "flow",
        // "semi": false,
        "endOfLine": "auto"
      }
    ],
    "no-useless-concat": 0,
    "key-spacing": "off",
    // "max-len": [2, 120, 2],
    "object-curly-spacing": [2, "always"],
    "no-undef": 1,
    "radix": "off",
    "react/jsx-fragments": 0,
    "react/jsx-closing-bracket-location": 0,
    "react/prop-types": 0,
    "standard/no-callback-literal": 0,
    "camelcase": "off",
    "react/jsx-pascal-case": 0
  }
}
