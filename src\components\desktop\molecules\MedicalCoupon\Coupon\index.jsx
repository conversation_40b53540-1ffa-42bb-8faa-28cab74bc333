import React from "react";
import cx from "classnames";
import Attention from "../segment/Attention";
import BookingInfo from "../segment/BookingInfo";
import DisplayTypeCode from "../segment/DisplayTypeCode";
import HospitalInfo from "../segment/HospitalInfo";
import Line from "../segment/Line";
import NumbericalOrder from "../segment/NumbericalOrder";
import PatientInfo from "../segment/PatientInfo";
import ShortNotes from "../segment/ShortNotes";
import Status from "../segment/Status";
import Tagline from "../segment/Tagline";
import styles from "./style.module.scss";

const InformLayout = ({ informHtml, banner, keys }) => {
  return (
    <div className={styles.anounce_booking} key={keys}>
      <p
        className={styles.informBooking}
        dangerouslySetInnerHTML={{ __html: informHtml }}
      />
      {banner?.status ? (
        <a
          href={banner.url}
          className={styles.bannerInform}
          target={banner.cta.targetWeb}
        >
          <img
            src={banner.imageUrl}
            alt="inform"
            width={600}
            height={300}
            layout="responsive"
            objectFit="contain"
            objectPosition="center top"
            className={styles.imgInform}
          />
        </a>
      ) : null}
    </div>
  );
};

const DefaultLayout = ({ item, keys }) => {
  const { bookingInfo } = item;
  const showAttention = ![-2, 0, 6].includes(bookingInfo.status);

  return (
    <div className={styles.coupon} key={keys}>
      <Line top />

      {/* Hospital Information */}
      <HospitalInfo bookingInfo={bookingInfo} />

      <div
        className={cx(
          styles.info,
          !bookingInfo?.displayCodeBooking?.value && styles.info1
        )}
      >
        {bookingInfo?.displayCodeBooking?.value && (
          <DisplayTypeCode bookingInfo={bookingInfo} />
        )}
        <NumbericalOrder bookingInfo={bookingInfo} />
      </div>

      <Status bookingInfo={bookingInfo} />
      <ShortNotes bookingInfo={bookingInfo} />
      <Line />
      <BookingInfo bookingInfo={bookingInfo} />
      <PatientInfo bookingInfo={bookingInfo} />

      {showAttention && (
        <>
          <Line />
          <Attention bookingInfo={bookingInfo} />
        </>
      )}

      <Line />
      <Tagline />
      <Line bottom />
    </div>
  );
};

const Coupon = ({ item, keys }) => {
  if (!item) return null;

  const { layout } = item;

  switch (layout) {
    case "INFORM":
      return <InformLayout {...item} keys={keys} />;
    default:
      return <DefaultLayout item={item} keys={keys} />;
  }
};

export default Coupon;
