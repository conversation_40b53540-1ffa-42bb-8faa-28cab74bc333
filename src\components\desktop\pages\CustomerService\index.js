import { <PERSON><PERSON>pinner } from "mdbreact";
import queryString from "query-string";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Route, Switch } from "react-router-dom";
import history from "~/history";
import {
  clearSelectedPatientCSKH,
  getBookingbyPatientCSKH,
  getPatientsByPhoneCSKH,
  resetAllCS,
  resetBookingInfoCS,
  selectPatientCSKhById
} from "~/store/customerService/customerServiceActions";
import { requestHospitalList } from "~/store/hospital/hospitalActions";
import { clearState } from "~/store/patient/patientAction";
// actions
import v from "validator";
import { openToast } from "~/components/common/molecules/ToastNotification";
import { changeBookingFlow, setTypeAction } from "~/store/totalData/actions";
import CustomerServiceHeader from "./components/CustomerServiceHeader";
import HaveEverExamPage from "./HaveEverExamPage";
import MainPage from "./MainPage";
import PatientCreateNewPage from "./PatientCreateNewPage";
import PatientUpdate from "./PatientEditPage";
import { apiCheckRequirePatient } from "~/store/patient/patientApi";
import { currentEnv } from "~/configs";

class CustomerService extends Component {
  constructor(props) {
    super(props);
    const query = queryString.parse(props.location.search);
    this.state = {
      inputPatientMobile: "",
      inputPhone: query.phone ? query.phone : this.props.searchedPhone,
      mpTransaction: query.mpTransaction ? query.mpTransaction : null,
      phone_regexp: /((09|03|07|08|05)+([0-9]{8})\b)/,
      phoneValidate: false,
      requiredFields: [] // Add this line
    };
  }

  componentDidUpdate(prevProps) {
    if (this.props.searchedPhone !== prevProps.searchedPhone) {
      this.props.resetAllCS();
      this.props.setTypeAction(null);
    }
  }

  async componentDidMount() {
    const { selectedPatientCS, selectedHospital } = this.props;
    const { inputPhone } = this.state;

    const phoned = window.localStorage.getItem("searchedPhone");

    this.props.resetBookingInfoCS();

    if (phoned !== inputPhone) {
      this.props.resetAllCS();
      this.props.setTypeAction(null);
    }

    // Fetch required fields first
    await this.checkRequireInsertPatient(selectedHospital?.partnerId);

    const query = queryString.parse(window.location.search);
    if (query?.phone) {
      if (inputPhone) {
        this.props.requestPatientsInfoByPhone(query.phone);
        this.props.clearSelectedPatientCSKH();
      }
    } else {
      if (inputPhone) {
        this.props.requestPatientsInfoByPhone(inputPhone);
        if (selectedPatientCS?.id) {
          setTimeout(() => {
            console.log(2);
            if (selectedPatientCS?.secretKey) {
              this.props.getBookingbyPatientCSKH(selectedPatientCS);
            }
            this.props.selectPatientCSKhById(selectedPatientCS?.id);

            const elmnt = document.getElementById("activePatientCS");
            elmnt && elmnt.scrollIntoView({ behavior: "smooth" });
          }, 2000);
        }
      } else {
        this.props.clearSelectedPatientCSKH();
      }
    }
  }

  checkRequireInsertPatient = async partnerId => {
    try {
      const response = await apiCheckRequirePatient(
        currentEnv.RESTFULL_API_URL,
        { partnerId }
      );
      // Store the required fields in state and pass to child components
      this.setState({
        requiredFields: response?.data || []
      });
    } catch (error) {
      console.error(error);
    }
  };

  patientPhoneInputChangeHandler = event => {
    this.setState({ inputPatientMobile: event.target.value });
  };

  inputPhoneChangeHandler = event => {
    const phone = event.target.value;

    if (phone.length > 10) {
      this.setState({ inputPhone: phone });
      openToast(" Vui lòng nhập Số điện thoại tối đa 10 chữ số !", "error");
    } else if (phone.length === 10) {
      if (!v.isMobilePhone(phone, "vi-VN")) {
        openToast("Vui lòng nhập đúng định dạng số điện thoại !", "error");
      }
      this.setState({ inputPhone: phone });
    } else {
      this.setState({ inputPhone: phone });
    }
  };

  requestInfoByPhone = () => {
    history.push({
      pathname: "/cham-soc-khach-hang",
      search: "?phone=" + this.state.inputPhone
    });
    this.props.clearStatePatient();
    // this.props.requestPatientsInfoByPhone(this.state.inputPhone);
  };

  render() {
    const { loading } = this.props;
    const { requiredFields } = this.state;

    if (loading) {
      return (
        <div className="loading">
          <MDBSpinner big />
        </div>
      );
    }
    return (
      <>
        <CustomerServiceHeader
          inputPhone={this.state.inputPhone}
          inputPhoneChangeHandler={this.inputPhoneChangeHandler}
          requestInfoByPhone={this.requestInfoByPhone}
          phoneValidate={this.state.phoneValidate}
        />

        <Switch>
          <Route
            path="/cham-soc-khach-hang/da-tung-kham"
            exact
            component={HaveEverExamPage}
          />
          <Route
            path="/cham-soc-khach-hang/tao-moi-ho-so-benh-nhan"
            exact
            render={props => (
              <PatientCreateNewPage
                {...props}
                requiredFields={requiredFields}
              />
            )}
          />
          <Route
            path="/cham-soc-khach-hang/cap-nhat-ho-so-benh-nhan"
            exact
            render={props => (
              <PatientUpdate {...props} requiredFields={requiredFields} />
            )}
          />
          <Route
            path="/cham-soc-khach-hang"
            exact
            render={props => (
              <MainPage
                {...props}
                mpTransaction={this.state.mpTransaction}
                requestInfoByPhone={this.requestInfoByPhone}
              />
            )}
          />
        </Switch>
      </>
    );
  }
}

const mapStateToProps = state => {
  const {
    totalData: { flow, loading, typeAction },
    customerService: {
      searchedPhone,
      phoneSearchResult,
      selectedPatient: selectedPatientCS
    },
    user,
    hospital: {
      hospitalList: { data: hospitalList },
      selectedHospital
    }
  } = state;
  return {
    typeAction,
    loading,
    selectedPatientCS,
    flow,
    searchedPhone,
    phoneSearchResult,
    user,
    hospitalList,
    selectedHospital
  };
};

const mapDispatchToProps = {
  resetAllCS: resetAllCS,
  setTypeAction: setTypeAction,
  clearStatePatient: clearState,
  changeBookingFlow: changeBookingFlow,
  requestPatientsInfoByPhone: getPatientsByPhoneCSKH,
  requestHospitalList: requestHospitalList,
  selectPatientCSKhById: selectPatientCSKhById,
  getBookingbyPatientCSKH: getBookingbyPatientCSKH,
  clearSelectedPatientCSKH: clearSelectedPatientCSKH,
  resetBookingInfoCS: resetBookingInfoCS
};

export default connect(mapStateToProps, mapDispatchToProps)(CustomerService);
