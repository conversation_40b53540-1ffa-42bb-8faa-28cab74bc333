@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/listgroup.scss";
.feature_medpro {
  padding: 30px 0px;
  position: relative;
  background: url(../../../../assets/img/common/s3-bg.png) #fff no-repeat center;
  background-position: center;
}
.feature_medpro_inner {
  text-align: center;
  margin-bottom: 60px;
  @media #{$medium-and-down} {
    margin-bottom: 30px;
  }
  h3 {
    font-size: 2rem;
    font-weight: 500;
  }
}
.list_group {
  flex-direction: unset;
  justify-content: center;
  li {
    margin-bottom: 0;
    a {
      margin: 0 15px;
      width: 152px;
      height: 45px;
    }
  }
  &.list_left,
  &.list_right {
    flex-direction: column !important;
    justify-content: center !important;
    width: 40%;
    li {
      flex-direction: row;
      align-items: center !important;
      margin-bottom: 10%;
      background: transparent;
      transition: all 0.2s ease-in-out;
      font-size: 16px;

      p {
        margin-bottom: 5px;
        text-align: right;
      }
      img {
        min-width: 40px;
        width: 40px;
      }
    }
  }
  &.list_left {
    align-items: flex-end;
    li {
      align-items: flex-end;
      img {
        margin-left: 10px;
      }
      span {
        text-align: right;
      }
    }
  }
  &.list_right {
    align-items: flex-start;
    li {
      align-items: flex-start;
      img {
        margin-right: 10px;
      }
      span {
        text-align: left;
      }
    }
  }
}
.feature_medpro_list {
  display: flex;
  justify-content: space-between;
  @media (max-width: 767px) {
    flex-direction: column;
    ul {
      width: 100% !important;
      &:first-child {
        // margin-bottom: 30px;
        align-items: flex-start;
        li {
          min-height: 40px;
          width: 100% !important;
          padding-left: 50px !important;
          img {
            position: absolute;
            left: 0;
            top: 0;
            margin: 0;
          }
        }
      }
      li {
        margin-bottom: 15px !important;
      }
    }
  }
}
.dt {
  text-align: center;
  min-width: 275px;
  margin: 0px;
  display: flex;
  max-width: 400px;
  align-items: center;

  img {
    width: 100%;
    margin: 0 auto;
  }
}
@media (max-width: 992px) {
  .dt {
    margin: 0 5%;
  }
}
@media (max-width: 768px) {
  .dt {
    display: none;
  }
}
@media (max-width: 640px) {
  .feature_medpro_h3 {
    span {
      margin-top: 5px;
      display: inline-block;
    }
  }
}
.img-fluid {
  height: auto;
  max-width: 100%;
}

// ---------------------------Minh anh

.feature_medpro_minhanh {
  .feature_medpro_inner {
    .nameHospital {
      color: #db2233 !important;
    }
  }
}
