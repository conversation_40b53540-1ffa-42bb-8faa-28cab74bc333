import React from "react";
import { connect } from "react-redux";
import styles from "./style.module.scss";
import { Link } from "react-router-dom";
import { MDBBreadcrumb, MDBBreadcrumbItem } from "mdbreact";
import cx from "classnames";
const BreadcrumbStream = props => {
  const { step, selectedFeature } = props;

  const numberStep = Number(step);

  const flowSteps = {
    date: {
      step1: "Ngày khám - Chuyên khoa",
      step2: "Phòng khám - Giờ khám - BHYT",
      step3: "Xác nhận thông tin",
      step4: "Thanh toán",
      linkStep1: "/umc/chon-lich-kham",
      linkStep2: "/umc/chon-phong-kham",
      linkStep3: "/umc/xac-nhan-thong-tin"
    },
    doctor: {
      step1: "Bác sĩ",
      step2: "<PERSON>ày khám - G<PERSON><PERSON> khám - BHYT",
      step3: "Xác nhận thông tin",
      step4: "Thanh toán",
      linkStep1: "/umc/chon-bac-si",
      linkStep2: "/umc/dat-lich-theo-bac-si",
      linkStep3: "/umc/xac-nhan-thong-tin"
    }
  };

  const stepInfomation =
    selectedFeature && selectedFeature.id === 2
      ? flowSteps.doctor
      : flowSteps.date;

  const clsActive = styles.active;
  const clsPrev = styles.prev_step;

  const addStyle = id => {
    let clsName = "";
    if (numberStep > id) {
      clsName = cx(clsPrev);
    } else if (numberStep === id) {
      clsName = cx(clsActive);
    }
    return clsName;
  };

  return (
    <div className={styles.wrap_mdbreadcrumb}>
      <MDBBreadcrumb>
        <MDBBreadcrumbItem className={addStyle(1)}>
          <Link to={stepInfomation.linkStep1}>{stepInfomation.step1}</Link>
        </MDBBreadcrumbItem>
        <MDBBreadcrumbItem className={addStyle(2)}>
          {numberStep >= 2 ? (
            <Link to={stepInfomation.linkStep2}>{stepInfomation.step2}</Link>
          ) : (
            stepInfomation.step2
          )}
        </MDBBreadcrumbItem>
        <MDBBreadcrumbItem className={addStyle(3)}>
          {numberStep >= 3 ? (
            <Link to={stepInfomation.linkStep3}>{stepInfomation.step3}</Link>
          ) : (
            stepInfomation.step3
          )}
        </MDBBreadcrumbItem>
        <MDBBreadcrumbItem className={addStyle(4)}>
          {" "}
          {stepInfomation.step4}
        </MDBBreadcrumbItem>
      </MDBBreadcrumb>
    </div>
  );
};

const mapStateToProps = state => {
  const { features } = state;
  return features;
};

export default connect(mapStateToProps, null)(BreadcrumbStream);
