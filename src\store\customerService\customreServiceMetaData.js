// This file is used for storing data related to the page functions, logic, etc,...

// data used for restructure the received list inorder to render them to MDBdatatable
export const bookingListColumns = [
  {
    label: "#", // do not add "key" props here
    field: "index",
    width: 40,
    renderType: "default"
  },
  {
    label: "Thao tác", // do not add "key" props here
    field: "function",
    width: 150,
    renderType: "default"
  },
  {
    label: "Partner",
    field: "hospital",
    dataindex: "partnerId", // key props defines the path to the data in object
    width: 100,
    renderType: "center"
  },
  {
    label: "Mã phiếu",
    field: "bookingCode",
    dataindex: "bookingCode",
    width: 150,
    renderType: "strong"
  },
  {
    label: "Mã thanh toán",
    field: "transactionId",
    dataindex: "transactionId",
    width: 250,
    renderType: "strong"
  },
  {
    label: "Trạng thái",
    field: "paymentStatus",
    dataindex: "description",
    width: 150,
    renderType: "string"
  },
  {
    label: "<PERSON><PERSON> bệnh nhân",
    field: "patientId",
    dataindex: "patient.patientCode",
    width: 150,
    renderType: "center"
  },
  {
    label: "<PERSON>ê<PERSON> bệnh nhân",
    field: "patientName",
    dataindex: "patient.fullname",
    width: 150,
    renderType: "center"
  },
  {
    label: "Ngày sinh",
    field: "patientBirth",
    dataindex: "patient.birthdate",
    altDataIndex: ["patient.birthyear"],
    transform: "reformat-date",
    width: 120,
    renderType: "center"
  },
  {
    label: "Ngày khám",
    field: "date",
    dataindex: "date",
    transform: "date",
    width: 150,
    renderType: "center"
  },
  {
    label: "Ngày đặt",
    field: "createdAt",
    dataindex: "createdAt",
    transformORCriteria: [
      // this is an OR criteria
      {
        dataindex: "status",
        value: 1
      },
      {
        dataindex: "status",
        value: -2
      }
    ],
    transformIfTrue: "date-time", // must provide if there's a criteria
    transformIfFalse: "date", // must provide if there's a criteria
    transform: "date",
    width: 150,
    renderType: "center"
  },
  {
    label: "Sđt",
    field: "mobile",
    dataindex: "patient.mobile",
    width: 150,
    renderType: "center"
  }
  // {
  //   label: "Phương thức thanh toán",
  //   field: "paymentMethod",
  //   dataindex: "unknown",
  //   width: 200
  // }
];

export const patientListColumns = [
  {
    label: "#", // do not add "key" props here
    field: "index",
    width: 40,
    renderType: "default"
  },
  {
    label: "Thao tác", // do not add "key" props here
    field: "function",
    width: 200,
    renderType: "default"
  },
  {
    label: "Bệnh viện",
    field: "Hospital",
    dataindex: "partnerId",
    width: 80,
    renderType: "center"
  },
  {
    label: "Tên bệnh nhân",
    field: "patientName",
    dataindex: "fullname",
    width: 150,
    renderType: "center"
  },
  {
    label: "Mã hồ sơ",
    field: "msbn",
    dataindex: "patientCode",
    // transform: "strong",
    width: 150,
    renderType: "strong"
  },
  {
    label: "ngày sinh",
    field: "birthdate",
    dataindex: "birthdate",
    transform: "reformat-date",
    altDataIndex: ["birthyear"],
    width: 100,
    renderType: "center"
  },
  {
    label: "Số điện thoại",
    field: "Phone",
    dataindex: "mobile",
    // transform: "strong",
    width: 100,
    renderType: "center"
  },
  {
    label: "Giới tính",
    field: "Gender",
    dataindex: "sex",
    transform: "gender",
    width: 80,
    renderType: "center"
  },
  {
    label: "Địa chỉ",
    field: "Address",
    dataindex: "address",
    transform: "full-address",
    altDataIndex: ["district.name", "city.name"],
    width: 200
  }
];

// const dumbrespone = {
//       user: {
//         username: "+84845469854",
//         fullname: null
//       },
//       patients: [
//         {
//           relation: {
//             relative_name: "",
//             relative_mobile: "",
//             relative_type_id: "",
//             relative_email: ""
//           },
//           partnerId: "umc",
//           patientIdV1: 0,
//           patientIdV1DaLieu: 0,
//           patientIdV1CTCH: 0,
//           patientIdV1ThuDuc: 0,
//           patientIdV1UMC: 1113082,
//           address: "123 xvnt ",
//           birthdate: "",
//           birthyear: 1991,
//           mobile: "**********",
//           name: "23/08",
//           surname: "TESTER TEST",
//           email: "",
//           cmnd: "",
//           sex: 1,
//           country_code: "VIE",
//           nation: {
//             id: "medpro_1",
//             code: 1,
//             name: "Kinh",
//             note: "Việt",
//             partnerId: "medpro",
//             hospitalId: "5ed06cc1186750001958457f",
//             createdAt: "2020-06-09T07:31:58.300Z",
//             updatedAt: "2020-06-09T07:31:58.300Z"
//           },
//           dantoc_id: "medpro_1",
//           city: {
//             id: "medpro_79",
//             createTime: 1591623137744,
//             code: "79",
//             name: "Thành phố Hồ Chí Minh",
//             partnerId: "medpro",
//             status: 1,
//             description: "",
//             applyDate: "2020-06-08T13:32:09.802Z",
//             sourceUpdateTime: 1591623137736,
//             parent: "VIE",
//             updateTime: 1591623137744
//           },
//           city_id: "medpro_79",
//           district: {
//             id: "medpro_760",
//             createTime: 1591623143838,
//             code: "760",
//             name: "Quận 1",
//             partnerId: "medpro",
//             status: 1,
//             description: "",
//             applyDate: "2020-06-08T13:32:09.802Z",
//             sourceUpdateTime: 1591623143820,
//             parent: "79",
//             updateTime: 1591623143838
//           },
//           district_id: "medpro_760",
//           ward: {
//             id: "medpro_26740",
//             createTime: 1591625755142,
//             code: "26740",
//             name: "Phường Bến Nghé",
//             partnerId: "medpro",
//             status: 1,
//             description: "",
//             applyDate: "2020-06-08T14:06:24.245Z",
//             sourceUpdateTime: 1591625755092,
//             parent: "760",
//             updateTime: 1591625755142
//           },
//           ward_id: "medpro_26740",
//           country: {
//             code: "VIE",
//             name: "Việt Nam",
//             partnerId: "medpro",
//             hospitalId: "5ec4a4308151a55ee0b2ad17",
//             createdAt: "2020-05-25T03:26:57.057Z",
//             updatedAt: "2020-05-25T03:26:57.057Z",
//             id: "medpro_VIE",
//             status: 1,
//             checkStatus: 2,
//             errorDescription:
//               "E11000 duplicate key error collection: medpro.countries index:
// _id_ dup key: { : ObjectId('5ecb3b014ae1165edc747c5b') } ",
//             mappingStatus: 1,
//             mapToCode: "VN"
//           },
//           country_id: "5ecb3b014ae1165edc747c5b",
//           sourceId: "umc",
//           code: "MP-190823936888",
//           id: "1d3dd566211743c7a7bc78b461df4e4d",
//           createdAt: "2020-12-23T08:31:43.577Z",
//           updatedAt: "2020-12-23T08:49:43.399Z"
//         }
//       ],
//       bookings: [
//         {
//           visible: true,
//           bookingNote: "",
//           noPayment: false,
//           serviceType: "",
//           idReExam: "",
//           syncBookingType: 1,
//           syncBookingIdV1: 2,
//           syncUserIdV1: 5,
//           syncPatientIdV1: 1113082,
//           _id: "5fe3006fe5aabf12c42ef3a9",
//           id: "c97787ffc93b409e92e2a59c0fcc9014",
//           bookingId: "7335d4e0269a4a9ab031074c4510a8e0",
//           bookingCode: "W1908230203",
//           transactionId: "TTDev-190823322721",
//           partnerId: "umc",
//           partner: {
//             _id: "5ed067f054bdb00019e4741b",
//             name: "Bệnh viện Đại Học Y Dược TP.HCM"
//           },
//           subjectId: "8829638e5e1a4428a9e7d5a9b00ccb39",
//           subject: {
//             _id: "5ed06e5fbf9d04acb2c7dc92",
//             name: "DA LIỄU"
//           },
//           roomId: "aee83399b54744478742b65bd642b0da",
//           room: {
//             _id: "5ed06e5fbf9d04acb2c7dcbd",
//             name: "Phòng 31"
//           },
//           doctorId: "162c6ea6a0354c06a9222ef56e489c88",
//           doctor: {
//             _id: "5ed06e5fbf9d04acb2c7dd30",
//             name: "Phạm Thị Tiếng"
//           },
//           sequenceNumber: 48,
//           status: 0,
//           platform: "web",
//           date: "2019-08-24T16:59:00.000Z",
//           bookingSlotId: "NOT_SYNC_V1_YET",
//           patientNameV1: "TESTER TEST 23/08",
//           patientPhoneV1: "**********",
//           patientMSBNV1: null,
//           patientId: "1d3dd566211743c7a7bc78b461df4e4d",
//           patient: {
//             relation: {
//               relative_name: "",
//               relative_mobile: "",
//               relative_type_id: "",
//               relative_email: ""
//             },
//             partnerId: "umc",
//             patientIdV1: 0,
//             patientIdV1DaLieu: 0,
//             patientIdV1CTCH: 0,
//             patientIdV1ThuDuc: 0,
//             patientIdV1UMC: 1113082,
//             _id: "5fe3006fe5aabf12c42ef3a8",
//             address: "123 xvnt ",
//             birthdate: "",
//             birthyear: 1991,
//             mobile: "**********",
//             name: "23/08",
//             surname: "TESTER TEST",
//             email: "",
//             cmnd: "",
//             sex: 1,
//             country_code: "VIE",
//             nation: "5edf3aee60bf731ffc65b533",
//             dantoc_id: "medpro_1",
//             city: "5ede3de1747dbc4942ba191f",
//             city_id: "medpro_79",
//             district: "5ede3de7747dbc4942ba19ee",
//             district_id: "medpro_760",
//             ward: "5ede481bf409bf0c6f3767a9",
//             ward_id: "medpro_26740",
//             country: "5ecb3b014ae1165edc747c5b",
//             country_id: "5ecb3b014ae1165edc747c5b",
//             sourceId: "umc",
//             code: "MP-190823936888",
//             id: "1d3dd566211743c7a7bc78b461df4e4d",
//             createdAt: "2020-12-23T08:31:43.577Z",
//             updatedAt: "2020-12-23T08:49:43.399Z",
//             __v: 0,
//             fullname: "TESTER TEST 23/08"
//           },
//           createdAt: "2020-12-23T08:31:43.890Z",
//           updatedAt: "2020-12-23T08:49:43.662Z",
//           __v: 0,
//           userId: "5fe30247e5aabf12c42ef3b4",
//           description: "Đã thanh toán",
//           insuranceChoiceText: "",
//           invoiceUrl: ""
//         },
//         {
//           visible: true,
//           bookingNote: "",
//           noPayment: false,
//           serviceType: "",
//           idReExam: "",
//           syncBookingType: 1,
//           syncBookingIdV1: 2,
//           syncUserIdV1: 5,
//           syncPatientIdV1: 1113082,
//           _id: "5fe3006fe5aabf12c42ef3a9",
//           id: "c97787ffc93b409e92e2a59c0fcc9013",
//           bookingId: "7335d4e0269a4a9ab031074c4510a8e0",
//           bookingCode: "W1908230203",
//           transactionId: "TTDev-190823322721",
//           partnerId: "umc",
//           partner: {
//             _id: "5ed067f054bdb00019e4741b",
//             name: "Bệnh viện Đại Học Y Dược TP.HCM"
//           },
//           subjectId: "8829638e5e1a4428a9e7d5a9b00ccb39",
//           subject: {
//             _id: "5ed06e5fbf9d04acb2c7dc92",
//             name: "DA LIỄU"
//           },
//           roomId: "aee83399b54744478742b65bd642b0da",
//           room: {
//             _id: "5ed06e5fbf9d04acb2c7dcbd",
//             name: "Phòng 31"
//           },
//           doctorId: "162c6ea6a0354c06a9222ef56e489c88",
//           doctor: {
//             _id: "5ed06e5fbf9d04acb2c7dd30",
//             name: "Phạm Thị Tiếng"
//           },
//           sequenceNumber: 48,
//           status: 1,
//           platform: "web",
//           date: "2019-08-24T16:59:00.000Z",
//           bookingSlotId: "NOT_SYNC_V1_YET",
//           patientNameV1: "TESTER TEST 23/08",
//           patientPhoneV1: "**********",
//           patientMSBNV1: null,
//           patientId: "1d3dd566211743c7a7bc78b461df4e4d",
//           patient: {
//             relation: {
//               relative_name: "",
//               relative_mobile: "",
//               relative_type_id: "",
//               relative_email: ""
//             },
//             partnerId: "umc",
//             patientIdV1: 0,
//             patientIdV1DaLieu: 0,
//             patientIdV1CTCH: 0,
//             patientIdV1ThuDuc: 0,
//             patientIdV1UMC: 1113082,
//             _id: "5fe3006fe5aabf12c42ef3a8",
//             address: "123 xvnt ",
//             birthdate: "",
//             birthyear: 1991,
//             mobile: "**********",
//             name: "23/08",
//             surname: "TESTER TEST",
//             email: "",
//             cmnd: "",
//             sex: 1,
//             country_code: "VIE",
//             nation: "5edf3aee60bf731ffc65b533",
//             dantoc_id: "medpro_1",
//             city: "5ede3de1747dbc4942ba191f",
//             city_id: "medpro_79",
//             district: "5ede3de7747dbc4942ba19ee",
//             district_id: "medpro_760",
//             ward: "5ede481bf409bf0c6f3767a9",
//             ward_id: "medpro_26740",
//             country: "5ecb3b014ae1165edc747c5b",
//             country_id: "5ecb3b014ae1165edc747c5b",
//             sourceId: "umc",
//             code: "MP-190823936888",
//             id: "1d3dd566211743c7a7bc78b461df4e4d",
//             createdAt: "2020-12-23T08:31:43.577Z",
//             updatedAt: "2020-12-23T08:49:43.399Z",
//             __v: 0,
//             fullname: "TESTER TEST 23/08"
//           },
//           createdAt: "2020-12-23T08:31:43.890Z",
//           updatedAt: "2020-12-23T08:49:43.662Z",
//           __v: 0,
//           userId: "5fe30247e5aabf12c42ef3b4",
//           description: "Đã thanh toán",
//           insuranceChoiceText: "",
//           invoiceUrl: ""
//         }
//       ]
//     };
