@import "src/assets/scss/pro-desktop/paymentMethod.scss";

// @media #{$medium-and-down} {
//   .next_prev {
//     .group_btn {
//       li:not(last-child) {
//         margin-right: 0px;
//         margin-bottom: 0.5rem;
//       }
//     }
//   }
// }

// .scancode {
// }

.next_prev {
  justify-content: flex-end;
}
.infoQRCode {
  font-size: 0.875rem;

  .titleQRCode {
    font-size: 1rem;
  }
  .sumbTotal {
    font-size: 1rem;
  }

  .QRCode {
    border: 2px solid #008cff;
    padding: 3px;
  }
  .paymentGuide {
    font-style: italic;
  }
  .btnQRCode {
    text-transform: capitalize;
    background-color: #ff5c00 !important;
    width: 280px;
  }
}

.pillsGuide {
  font-size: 0.75rem;
  .active {
    background-color: #008cff;
  }
  li {
    width: 50%;
    text-align: center;
    padding: 10px 15px;
    background-color: #c3c3c3;
    a {
      border-radius: 0 !important;
      color: white;
    }
  }
}

.byTabOne {
  .stepByTabOne {
    list-style-type: none;
    padding-left: 0;
    font-size: 0.875rem;
  }
  .noteByTabOne {
    font-size: 0.875rem;
  }
}

.byTabTwo {
  .stepByTabTwo {
    list-style-type: none;
    padding-left: 0;
    font-size: 0.875rem;
  }
  .noteByTabTwo {
    font-size: 0.875rem;
  }
}

//  ----------------------Minh anh
.wapper_page_desktop_minhanh {
  .panels {
    .panels_header {
      background-color: #db2233 !important;
    }
    .list_group_payment {
      .sub_title {
        color: #db2233 !important;
      }
    }
  }
  .total_payment {
    .list_group {
      li {
        strong {
          color: #db2233 !important;
        }
      }
    }
  }
}
