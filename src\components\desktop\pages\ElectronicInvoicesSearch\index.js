/* eslint-disable standard/no-callback-literal */
/* eslint-disable no-undef */
import React, { Component } from "react";
import { MDBContainer, MDBRow, MDBCol, MDBSpinner } from "mdbreact";
import TagName from "~/components/common/atoms/TagName";
import styles from "./style.module.scss";
import cx from "classnames";
import FormFindElectronicInvoices from "~/components/common/molecules/FormFindElectronicInvoices";

class ElectricInvoiceSearch extends Component {
  validatorBirthday = (rule, value, cb) => {
    if (!moment(value, "DD/MM/YYYY").isValid()) {
      cb("Vui lòng nhập đúng định dạng ngày tháng");
    } else {
      cb();
    }
  };

  render() {
    const {
      onChangeInvoiceCode,
      onSearchInvoiceCode,
      onChangeDate,
      loading,
      errorInvoiceCode,
      errorDate
    } = this.props;

    if (loading) {
      return (
        <div className={styles.loading_spinner}>
          <div className={styles.loading}>
            <MDBSpinner big crazy tag="div" />
          </div>
        </div>
      );
    }
    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow>
            <MDBCol md="12">
              <div className={styles.wapper_page_inner}>
                <TagName
                  element="h1"
                  className={[
                    "title_component",
                    "title_line",
                    "title_header_mobile"
                  ]}
                >
                  <span>Tra cứu hóa đơn điện tử</span>
                </TagName>
                <div
                  className={cx(
                    styles.search_invoices_wrapper,
                    styles.bg_inner
                  )}
                >
                  <MDBContainer>
                    <MDBRow>
                      <MDBCol size={12}>
                        <div className={styles.tabcontent}>
                          <FormFindElectronicInvoices
                            changeInvoiceCode={onChangeInvoiceCode}
                            changeDate={onChangeDate}
                            searchInvoiceCode={onSearchInvoiceCode}
                            errorInvoiceCode={errorInvoiceCode}
                            errorDate={errorDate}
                          />
                        </div>
                      </MDBCol>
                    </MDBRow>
                  </MDBContainer>
                </div>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default ElectricInvoiceSearch;
