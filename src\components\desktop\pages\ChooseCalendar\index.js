import React, { Component } from "react";
import { connect } from "react-redux";
import {
  MDBContainer,
  MDBRow,
  MDBCol,
  MDBCardBody,
  MDBCard,
  MDBCardHeader
} from "mdbreact";
import ListCalendar from "~/components/common/molecules/ListCalendar";
import { requestHoliday } from "~/store/resource/resourceAction";
import { range } from "lodash";
import cx from "classnames";
import styles from "./style.module.scss";
import TimeSlotList from "~/components/desktop/molecules/TimeSlotList";
import PatientInfomationBooking from "~/components/desktop/molecules/PatientInfomationBooking";
import TagName from "~/components/common/atoms/TagName";
import PKHBtn from "~/components/common/atoms/Button";
import {
  showMessageError,
  submitDoctorAndTime,
  selectedRoomAndDoctor,
  resetRedirectToConfirmInfo,
  requestTimeSlotInDoctoFlow,
  selectedTime,
  removeDoctorAndTime
} from "~/store/room/roomAction";
import * as dateAndSpecialistActions from "~/store/dateAndSpecialist/dateAndSpecialistActions";
import BreadcrumbStream from "~/components/desktop/molecules/BreadCrumbStream";
import Modal from "~/components/common/molecules/Modal";
import {
  resetSelectDate,
  resetSelectTimeSlot,
  addSchedule
} from "~/store/totalData/actions";
import partnerId from "~/utils/partner";

class ChooseCalendarDoctorFlow extends Component {
  constructor(props) {
    super(props);
    this.buttonRef = React.createRef();

    this.props.onSelectedTime({ id: 0 });
    this.props.selectDate(undefined);

    this.state = {
      isShowModalMissInformation: false
    };
  }

  toggleModalMissInformation = () => {
    this.setState({
      isShowModalMissInformation: !this.state.isShowModalMissInformation
    });
  };

  afterSelectedDate = () => {
    this.props.onSelectedTime({ id: 0 });
    this.props.requestTimeSlotInDoctoFlow();
  };

  listDisabledDay = (listAvailableDay = ["0"]) => {
    return [0, 1, 2, 3, 4, 5, 6].filter(
      day => !listAvailableDay.includes(day.toString())
    );
  };

  onValidateBeforeSubmit = callback => {
    const { timeSlotId, history } = this.props;
    if (timeSlotId > 1) {
      this.props.addSchedule();
      history.push("/chon-ho-so");
    } else {
      this.setState({
        isShowModalMissInformation: !this.state.isShowModalMissInformation
      });
    }
  };

  onSubmitDoctorAndTime = () => {
    const {
      dateAndSpecialist,
      selectedDoctor: { doctor },
      selectedTime
    } = this.props;
    this.props.selectedRoomAndDoctor({ doctor, ...selectedTime });
    this.props.onSubmitDoctorAndTime(dateAndSpecialist);
  };

  handleCloseToChooseAgain = () => {
    this.props.closeToChooseCalendarAgain();
    this.props.resetSelectDate();
    this.props.resetSelectTimeSlot();
  };

  handleGoBack = () => {
    const route = "/chon-ho-so";
    this.props.history.push(route);
  };

  componentDidMount() {
    this.props.requestHoliday();
    this.props.selectedShowOneRow();
  }

  componentWillUnmount() {
    this.props.OnResetRedirectToConfirmInfo();
  }

  render() {
    const { rebookingSelected, selectedTime, dateSelected } = this.props;

    let disabledWeekdays = [];
    if (rebookingSelected) {
      disabledWeekdays = range(0, 7);
      const { detail = [] } = rebookingSelected;
      detail.forEach(({ ptime }) => {
        disabledWeekdays = disabledWeekdays.filter(
          weekday => weekday !== +ptime.weekday
        );
      });
    }

    const objCheckNextStep = Object.create(null);
    if (Object.keys(selectedTime).length > 1) {
      objCheckNextStep.nextStep = true;
      window.scrollTo({
        left: 0,
        top: this.buttonRef.current ? this.buttonRef.current.offsetTop : 0,
        behavior: "smooth"
      });
    }

    const flowSteps = {
      step1: { name: "Ngày khám - Giờ khám", class: "active" },
      step2: { name: "Xác nhận thông tin", class: "" },
      step3: { name: "Thanh toán", class: "" }
    };

    return (
      <div
        className={cx(
          styles.wapper_page_desktop,
          styles["wapper_page_desktop_" + partnerId]
        )}
      >
        <MDBContainer>
          <MDBRow>
            <MDBCol md="4" lg="3">
              <PatientInfomationBooking />
            </MDBCol>
            <MDBCol md="8" lg="9">
              <BreadcrumbStream flowSteps={flowSteps} />
              <MDBCard className={styles.card_box}>
                <MDBCardHeader className={styles.card_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between"
                    ]}
                  >
                    <span>Vui lòng chọn ngày khám</span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody className={styles.body_calendar}>
                  <ListCalendar
                    afterSelectedDate={this.afterSelectedDate}
                    disabledWeekdays={disabledWeekdays}
                    listDisabledDay={[0]}
                    disableCurrent
                    noBookingAfterTime="16:30:00"
                  />
                  <Modal
                    modal={this.state.isShowModalMissInformation}
                    iconTitle={<i className="fal fa-bell" />}
                    title="Thông báo"
                    children="Vui lòng chọn Giờ khám!"
                    toggle={this.toggleModalMissInformation}
                    centered
                    className="centered"
                  />
                </MDBCardBody>
                <div
                  className={styles.choose_time}
                  style={
                    dateSelected ? { display: "block" } : { display: "none" }
                  }
                >
                  <div className={styles.box_list_time}>
                    <TimeSlotList />
                    <PKHBtn onClick={this.handleCloseToChooseAgain}>
                      <i className="fal fa-times" />
                      Đóng
                    </PKHBtn>
                  </div>
                  <div className={styles.next_prev} ref={this.buttonRef}>
                    <PKHBtn
                      backdesktop="backdesktop"
                      onClick={this.handleGoBack}
                    >
                      Quay lại
                    </PKHBtn>
                    <PKHBtn
                      onClick={() => this.onValidateBeforeSubmit()}
                      buttonArrow="buttonArrow"
                      {...objCheckNextStep}
                      create="create"
                    >
                      Tiếp tục{" "}
                    </PKHBtn>
                  </div>
                </div>
              </MDBCard>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    rebooking: { rebookingSelected },
    doctor: { selectedDoctor },
    doctorAndTime: { selectedTime },
    dateAndSpecialist,
    dateAndSpecialist: { selectedSpecialist },
    totalData: { dateSelected, timeSlotId }
  } = state;
  return {
    rebookingSelected,
    selectedDoctor,
    selectedTime,
    dateAndSpecialist,
    selectedSpecialist,
    dateSelected,
    timeSlotId
  };
};

const mapDispatchToProps = dispatch => ({
  requestHoliday: () => {
    dispatch(requestHoliday());
  },
  requestTimeSlotInDoctoFlow: () => dispatch(requestTimeSlotInDoctoFlow()),
  showMessageError: message => {
    dispatch(showMessageError(message));
  },
  onSubmitDoctorAndTime: dateAndSpecialist => {
    dispatch(submitDoctorAndTime(dateAndSpecialist));
  },
  selectedRoomAndDoctor: room => dispatch(selectedRoomAndDoctor(room)),
  OnResetRedirectToConfirmInfo: () => {
    dispatch(resetRedirectToConfirmInfo());
  },
  onSelectedTime: time => {
    dispatch(selectedTime(time));
  },
  selectDate: date => dispatch(dateAndSpecialistActions.selectedDate(date)),
  closeToChooseCalendarAgain: () => {
    dispatch(dateAndSpecialistActions.closeToChooseCalendarAgain());
  },
  selectedShowOneRow: () =>
    dispatch(dateAndSpecialistActions.selectedShowOneRow()),
  onRemoveDoctorAndTime: index => {
    dispatch(removeDoctorAndTime(index));
  },
  resetSelectDate: () => {
    dispatch(resetSelectDate());
  },
  resetSelectTimeSlot: () => {
    dispatch(resetSelectTimeSlot());
  },
  addSchedule: () => {
    dispatch(addSchedule());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ChooseCalendarDoctorFlow);
