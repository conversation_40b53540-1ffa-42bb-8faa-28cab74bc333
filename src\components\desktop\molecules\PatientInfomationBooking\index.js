import {
  MD<PERSON>ard,
  MDBCardBody,
  MDBCardH<PERSON>er,
  MDBListGroup,
  MDBListGroupItem
} from "mdbreact";
import moment from "moment";
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import styles from "./style.module.scss";
import partnerId from "~/utils/partner";
import cx from "classnames";

class PatientInfomationBooking extends Component {
  render() {
    const {
      dateSelected,
      subjectName,
      serviceName,
      roomName,
      doctorName,
      timeSlot,
      selectedHospital
    } = this.props;

    return (
      <Fragment>
        <MDBCard className={cx(styles.panels, styles["panels_" + partnerId])}>
          <MDBCardHeader className={styles.panels_header}>
            Thông tin khám
          </MDBCardHeader>
          <MDBCardBody className={styles.card_body}>
            <MDBListGroup className={styles.list_group}>
              <MDBListGroupItem>
                <i className="fal fa-hospital" />
                {selectedHospital ? selectedHospital.name : ""}
              </MDBListGroupItem>
              {subjectName && (
                <MDBListGroupItem>
                  <i className="fal fa-stethoscope" />
                  Chuyên khoa: {subjectName}
                </MDBListGroupItem>
              )}
              {doctorName && (
                <MDBListGroupItem>
                  <i className="fal fa-user-md" />
                  Bác sĩ: {doctorName}
                </MDBListGroupItem>
              )}
              {serviceName && (
                <MDBListGroupItem>
                  <i className="fal fa-file-medical" />
                  Dịch vụ: {serviceName}
                </MDBListGroupItem>
              )}
              {roomName && (
                <MDBListGroupItem>
                  <i className="fal fa-address-card" />
                  Phòng khám: {roomName}
                </MDBListGroupItem>
              )}
              {dateSelected && (
                <MDBListGroupItem>
                  <i className="fal fa-calendar-alt" />
                  Ngày khám: {moment(dateSelected).format("DD/MM/YYYY")}
                </MDBListGroupItem>
              )}
              {Object.keys(timeSlot).length > 0 && (
                <MDBListGroupItem>
                  <i className="far fa-clock" />
                  Giờ khám: {timeSlot.startTime
                    ? timeSlot.startTime
                    : ""} - {timeSlot.endTime ? timeSlot.endTime : ""}
                </MDBListGroupItem>
              )}
            </MDBListGroup>
          </MDBCardBody>
        </MDBCard>
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    hospital: { selectedHospital },
    dateAndSpecialist,
    doctorAndTime,
    doctor: { selectedDoctor },
    totalData: {
      timeSlot,
      days: { date: dateSelected },
      subject: { name: subjectName },
      service: { name: serviceName },
      room: { name: roomName },
      doctor: { name: doctorName }
    }
  } = state;
  return {
    selectedHospital,
    dateAndSpecialist,
    doctorAndTime,
    selectedDoctor,
    dateSelected,
    timeSlot,
    subjectName,
    serviceName,
    roomName,
    doctorName
  };
};

export default connect(mapStateToProps)(PatientInfomationBooking);
