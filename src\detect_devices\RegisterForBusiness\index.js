/* eslint-disable max-len */
import { find, get } from "lodash";
import { MD<PERSON>ontainer, MDBSpinner } from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import { currentEnv } from "~/configs";
import { partnerInfo } from "~/configs/partnerDetails";
import styles from "./style.module.scss";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

class RegisterForBusiness extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: true
    };
  }

  hideSpinner = () => {
    this.setState({
      loading: false
    });
  };

  render() {
    console.log(
      "currentEnv.LINK_API_COVID19 :>> ",
      currentEnv.LINK_API_COVID19
    );
    return (
      <MDBContainer>
        <span className={styles.formTitle}>Nhập thông tin doanh nghiệp</span>
        {this.state.loading ? (
          <div className="loading">
            <MDBSpinner big />
          </div>
        ) : null}
        <iframe
          id="myiFrame"
          height="100%"
          onLoad={() => this.hideSpinner()}
          width="100%"
          // scrolling="no"
          className={styles.formInfo}
          src={`${currentEnv.LINK_API_COVID19}/vaccine-form/dang-ky.xhtml?userType=company&background=fff&showSuccess=true`}
          title="ĐĂNG KÝ KHAI BÁO DOANH NGHIỆP"
        />
      </MDBContainer>
    );
  }
}

const RegisterForBusinessHelmet = withTitle({
  component: RegisterForBusiness,
  title: `${hospitalName.value} | ĐĂNG KÝ KHAI BÁO DOANH NGHIỆP`
});

export default connect(null, null)(RegisterForBusinessHelmet);
