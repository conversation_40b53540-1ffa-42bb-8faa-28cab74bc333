import React from "react";
import styles from "./style.module.scss";
const SocialSupport = props => {
  return (
    <nav className={styles.social_sidebar}>
      <ul>
        <li>
          <a className={styles.call} href="tell:19002115" title="19002115">
            <i className="fal fa-phone" />
            <span>1900.2115</span>
          </a>
        </li>
        <li>
          <a
            className={styles.zalo}
            href="https://zalo.me/4018184502979486994"
            target="_blank"
            rel="noopener noreferrer"
          >
            Zalo
            <span>0865.420.418</span>
          </a>
        </li>
        <li>
          <a
            className={styles.fanpage}
            href="https://m.me/medpro.com.vn"
            target="_blank"
            rel="noopener noreferrer"
          >
            <i className="fab fa-facebook-f" />
            <span>Fanpge facebook</span>
          </a>
        </li>
        <li>
          <a
            className={styles.chatface}
            href="https://m.me/medpro.com.vn"
            target="_blank"
            rel="noopener noreferrer"
          >
            <i className="fab fa-facebook-messenger" />
            <span>Chat facebook</span>
          </a>
        </li>
        <li>
          <div
            className={styles.linkScroll}
            onClick={() => props.onHandleTop()}
          >
            <i className="fas fa-arrow-alt-circle-up" />
            <span>Lên đầu trang</span>
          </div>
        </li>
      </ul>
    </nav>
  );
};

export default SocialSupport;
