import { MDBCard, MDBCardBody, MDBIcon, MDBBtn } from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import styles from "./style.module.scss";

class StickyNotification extends Component {
  state = {
    show: false
  };

  componentWillUpdate(nextProps) {
    if (
      nextProps.selectedPatient !== this.props.selectedPatient ||
      nextProps.supportingAccount !== this.props.supportingAccount
    ) {
      this.toggleAutoClose();
    }
  }

  toggleAutoClose = () => {
    if (this.state.show) {
      return;
    }
    this.setState({ show: true });
    setTimeout(() => {
      this.setState({ show: false });
    }, 1000);
  };

  toggleShow = () => {
    this.setState({ show: !this.state.show });
  };

  render() {
    let showContentStyle = {};
    if (this.state.show) {
      showContentStyle = { marginLeft: "0%" };
    } else {
      showContentStyle = { marginLeft: "-100%" };
    }
    const { supportingAccount, selectedPatient } = this.props;
    return (
      <>
        <MDBCard className={styles.supporting_card} style={showContentStyle}>
          <MDBCardBody className={styles.content}>
            <span className={styles.title_card}>Đang hỗ trợ cho</span>
            <div className={styles.account_info}>
              {supportingAccount ? (
                <ul className={styles.list_support_info}>
                  <li>
                    <span className={styles.title}>
                      <MDBIcon icon="user" />
                    </span>
                    <span className={styles.value}>
                      {supportingAccount.fullname}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>
                      <MDBIcon icon="phone" />
                    </span>
                    <span className={styles.value}>
                      {supportingAccount.username}
                    </span>
                  </li>
                </ul>
              ) : null}
            </div>
            {selectedPatient ? (
              <>
                <div className={styles.pointer}>
                  <MDBIcon icon="angle-down" />
                </div>

                <ul className={styles.account_info}>
                  <li>
                    <span className={styles.title}>
                      <MDBIcon icon="user" />
                    </span>
                    <span className={styles.value}>
                      {selectedPatient.surname + " " + selectedPatient.name}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>
                      <MDBIcon far icon="file-alt" />
                    </span>
                    <span className={styles.value}>{selectedPatient.code}</span>
                  </li>
                </ul>
              </>
            ) : null}
          </MDBCardBody>
        </MDBCard>

        <MDBBtn
          color="red"
          className={styles.toggle_button}
          onClick={() => this.toggleShow()}
          size="sm"
        >
          {this.state.show ? (
            <MDBIcon icon="angle-left" />
          ) : (
            <MDBIcon icon="angle-right" />
          )}
        </MDBBtn>
      </>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { selectedPatient, supportingAccount }
  } = state;
  return { selectedPatient, supportingAccount };
};

const mapDispatchToProps = dispatch => ({});

export default connect(mapStateToProps, mapDispatchToProps)(StickyNotification);
