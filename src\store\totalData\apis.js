import * as api from "~/api";
import { SCHEDULE, BOOKING } from "~/utils/urlApi";
import { createQueryString } from "~/utils/func";

export const requestBookingTree = () => {
  const url = createQueryString(SCHEDULE.GET_BOOKING_TREE);
  return api.getHttpRequest(url);
};

export const reserveBooking = data => {
  const url = createQueryString(BOOKING.RESERVE, data);
  return api.getHttpRequest(url);
};
export const getPaymentInfo = data => {
  const url = createQueryString(BOOKING.GET_PAYMENT_INFO, data);
  return api.getHttpRequest(url);
};

export const reserveRePayment = (baseUrl, postData) => {
  return api.postHttpRequestAsync(`${baseUrl}`, postData);
};

export const getBookingsAsyncError = (baseUrl, postData) => {
  return api.postHttpRequestAsync(`${baseUrl}`, postData);
};
