import React from "react";
import styles from "./style.module.scss";
import cx from "classnames";

class SelectSearch extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      search: ""
    };
    this.wrapperRef = React.createRef();
    this.inputRef = React.createRef();
  }

  componentDidMount() {
    document.addEventListener("mousedown", this.handleClickOutside);
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.state.isOpen && !prevState.isOpen && this.inputRef.current) {
      this.inputRef.current.focus();
    }
  }

  componentWillUnmount() {
    document.removeEventListener("mousedown", this.handleClickOutside);
  }

  handleClickOutside = event => {
    if (this.wrapperRef && !this.wrapperRef.current.contains(event.target)) {
      this.setState({ isOpen: false });
    }
  };

  toggleDropdown = () => {
    if (!this.props.disabled) {
      this.setState(prev => ({ isOpen: !prev.isOpen }));
    }
  };

  handleSearchChange = e => {
    this.setState({ search: e.target.value });
  };

  handleSelect = item => {
    const { onChange, isReturnObject } = this.props;
    this.setState({ isOpen: false, search: "" });

    if (isReturnObject) {
      onChange({ id: item.id, name: item.name });
    } else {
      onChange(item.id);
    }
  };

  removeAccents = str => {
    return str
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .toLowerCase();
  };

  render() {
    const { data = [], disabled, isSearch, value } = this.props;
    const { isOpen, search } = this.state;

    const filtered = data.filter(item =>
      this.removeAccents(item.name).includes(this.removeAccents(search))
    );

    const selectedItem = data.find(item => item.id === value);

    const isDefault = value === 0;

    return (
      <div
        className={cx(styles.customSelectWrapper, {
          [styles.disabled]: disabled
        })}
        ref={this.wrapperRef}
      >
        <div
          className={cx(styles.customSelectBox, {
            [styles.isDefault]: isDefault
          })}
          onClick={this.toggleDropdown}
        >
          <span>{selectedItem ? selectedItem.name : "Chọn..."}</span>
          <span className={cx(styles.arrowIcon)} />
        </div>

        {isOpen && (
          <div className={cx(styles.customSelectDropdown)}>
            {isSearch && (
              <div className={cx(styles.inputWrapper)}>
                <input
                  type="text"
                  className={cx(styles.customSelectSearch)}
                  placeholder="Tìm kiếm..."
                  value={search}
                  onChange={this.handleSearchChange}
                  disabled={disabled}
                  ref={this.inputRef}
                />
                <div className={cx(styles.arrowIcon)} />
              </div>
            )}
            <ul
              className={cx(styles.customSelectOptions, {
                [styles.notSeach]: !isSearch
              })}
            >
              {filtered.length > 0 ? (
                filtered.map(item => (
                  <li
                    key={item.id}
                    className={cx(styles.customSelectOption, {
                      [styles.active]: item.id === value
                    })}
                    onClick={() => this.handleSelect(item)}
                  >
                    {item.name}
                  </li>
                ))
              ) : (
                <li className={cx(styles.customSelectNoOption)}>
                  Không có kết quả
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    );
  }
}

export default SelectSearch;
