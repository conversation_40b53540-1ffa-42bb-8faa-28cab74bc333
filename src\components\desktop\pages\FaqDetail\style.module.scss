@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/card.scss";
// @import "./../Faq/style.module.scss";

.banner {
  padding: 60px 0;
  background-image: linear-gradient(45deg, #6a78d1, #00a4bd);
  background-repeat: no-repeat;
  background-size: cover;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);

  .img_parallax {
    h1 {
      color: #fff !important;
      margin-bottom: 20px !important;
    }
    p {
      color: #fff !important;
      text-align: center;
    }
    overflow: unset;
  }
}

.wapper_page_inner {
  margin-top: 30px;
  margin-bottom: 30px;
}
.list_category {
  @media #{$medium-and-down} {
    border-bottom: 3px solid #ddd;
    margin-bottom: 15px;
  }
  li {
    border: 0;
    color: #12263f;
  }
  a {
    border: 0;
    border-radius: 0;
    color: #12263f;
    padding: 10px 15px;
    transition: all 0.3s ease-in-out !important;
    padding-left: 30px;
    &.active {
      color: #307be7;
      font-weight: 500;
    }
    &:after {
      content: "\f0da";
      font-family: "Font Awesome\ 5 Pro";
      font-weight: 700;
      position: absolute;
      top: 10px;
      left: 15px;
      transition: all 0.3s ease-out;
    }
    &:hover,
    &:focus {
      color: #307be7;
      &:after {
        color: #307be7;
      }
    }
  }
}
.md_accordion {
  .card_collapse {
    border: none;
    box-shadow: unset;
    margin: 0 0 15px 0;
  }
}
.card_collapse_item {
  padding: 11px 15px 11px 30px;
  cursor: pointer;
  background: #f8f8f9;
  box-shadow: unset;
  border-radius: 4px;
  i {
    float: right;
  }
  &.active {
    background: #307be7;
    color: #ffffff;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  &:after {
    content: "\f0da";
    font-family: "Font Awesome\ 5 Pro";
    font-weight: 700;
    position: absolute;
    top: 10px;
    left: 15px;
    transition: all 0.3s ease-out;
  }
}
.collapse_active {
  border: 1px solid #307be7;
}

//  ----------------minh anh
.wapper_page_desktop_minhanh {
  .wapper_page_inner {
    h2 {
      background: #db2233 !important;
    }
    a {
      color: #db2233 !important;
      :hover {
        color: #db2233 !important;
      }
    }
    .md_accordion {
      .card_collapse {
        .card_collapse_item {
          &.active {
            background: #db2233;
          }
        }
        .collapse_active {
          border: 1px solid #db2233;
        }
      }
    }
  }
}
