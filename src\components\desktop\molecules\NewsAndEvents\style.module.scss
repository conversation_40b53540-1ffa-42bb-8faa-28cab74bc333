@import "src/assets/scss/custom-variables.scss";
a {
  color: #12263f;
}

.colNews {
  font-family: "Averta", serif;
  .cardNews {
    margin-bottom: 30px;
    height: 100%;
    display: flex;
    justify-content: stretch;
    background: transparent;
    box-shadow: none;

    .cardBody {
      padding: 15px 0;

      .title {
        font-size: 0.875rem;
        color: #12263f;
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: 5px;
      }
      .tag {
        font-size: 0.75rem;
        color: #777;
      }
    }
  }

  .cardNewsLeft {
    margin-bottom: 30px;
    background: transparent;
    box-shadow: none;

    .cardBody {
      padding: 15px 0;
      .title {
        font-size: 0.875rem;
        color: #24292e;
        text-align: justify;
        font-weight: 700;
        text-transform: uppercase;
        margin-bottom: 5px;
      }
      .tag {
        margin-bottom: 5px;
        font-size: 0.75rem;
        color: #777;
      }
      .author {
        margin-bottom: 5px;
        font-size: 0.8125rem;
        color: #777;
        font-weight: 600;
      }
      .description {
        font-size: 0.875rem;
        text-align: justify;
      }
    }
  }

  @media #{$small-and-down} {
    .cardNews {
      box-shadow: none;
      display: flex;
      flex-direction: revert;
      height: 120px;
      margin-bottom: 1rem;

      .view {
        max-width: 170px;
      }
      .cardBody {
        padding: 0 0 0 15px;
        .title {
          font-weight: normal !important;
          color: black;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 4;
          -webkit-box-orient: vertical;
        }
        .description,
        .author {
          display: none;
        }
      }
    }
    .cardNewsLeft {
      font-family: "Montserrat", sans-serif;
      box-shadow: none;
      display: flex;
      flex-direction: revert;
      height: 120px;
      margin-bottom: 1rem;

      .view {
        max-width: 170px;
      }
      .cardBody {
        padding: 0 0 0 15px;
        .title {
          font-weight: normal;
          text-align: initial;
          color: black;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 4;
          -webkit-box-orient: vertical;
        }
        .description,
        .author {
          display: none;
        }
      }
    }
  }
  @media #{$medium-and-down} {
    .cardNews {
      box-shadow: none;
      display: flex;
      flex-direction: revert;
      height: 120px;
      margin-bottom: 1rem;
      .view {
        max-width: 170px;
      }
      .cardBody {
        padding: 0 0 0 15px;
        .title {
          font-weight: bolder;
          font-size: 0.875rem;
          text-align: justify;
          color: black;
          text-transform: capitalize;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 4;
          -webkit-box-orient: vertical;
        }
      }
    }
    .cardNewsLeft {
      box-shadow: none;
      display: flex;
      flex-direction: revert;
      margin-bottom: 1rem;

      .view {
        img {
          height: auto;
        }
      }
      .cardBody {
        max-width: 60%;
        padding: 0 0 0 15px;
        .title {
          font-size: 0.875rem;
          text-align: justify;
          text-transform: initial;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 4;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}
