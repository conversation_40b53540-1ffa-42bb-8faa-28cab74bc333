@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/listgroup.scss";

.hospital {
  flex-direction: unset;
  flex-wrap: wrap;
  // max-width: 800px;
  margin: 0 auto 3rem;
  border-bottom: 3px solid #dfe3eb;
  padding: 1rem 10px;
  background-color: #f2f2f2;
  @media (min-width: 750px) {
    padding: 20px 0;
    background-color: #fff;
  }
  li {
    margin-bottom: 15px !important;
    padding-bottom: 0;
    width: 100%;
    padding: 0 5px;
    @media (min-width: 768px) {
      flex: 0 1 33.33%; /* Changed from 50% to 33.33% for 3 columns */
      margin-bottom: 10px !important;
    }
    @media (max-width: 768px) {
      flex: 0 1 50%; /* Changed from 50% to 33.33% for 3 columns */
      margin-bottom: 10px !important;
    }
    @media (max-width: 576px) {
      flex: 0 1 100%; /* Changed from 50% to 33.33% for 3 columns */
      margin-bottom: 10px !important;
    }
  }
  .item_hospital {
    background-color: #fff;
    display: flex;
    width: 100%;
    color: $main_black;
    padding: 15px;
    border: 2px solid #e0e0e0;
    box-sizing: border-box;
    border-radius: 9px;
    align-items: center;
    height: 100%;
    &:hover,
    &:focus {
      border-color: #0352cc !important;
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.09);
      cursor: pointer;
      color: #0352cc;
    }
    img {
      width: 50px;
      height: 50px;
      min-width: 50px;
      margin-right: 20px;
    }
    .text_hospital {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      line-height: 16px;
      .name_hospital {
        font-weight: 700;
        // height: 30px;
        margin-bottom: 5px;
        @media (min-width: 600px) {
          // height: 40px;
        }
        .deliStatus {
          font-size: 11px;
          color: red;
          margin-bottom: 5px;
          font-weight: normal;
        }
      }

      .address_hospital {
        color: #828282;
        font-weight: 400;
      }
    }
  }
}
.wapper_loading {
  min-height: 250px;
  position: relative;
  text-align: center;
}
/* CSS cơ bản cho phân trang */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 8px;
  width: 100%;
}

/* Style cho nút phân trang */
.pageButton {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  color: #007bff;
  font-size: 14px;
  min-width: 40px;
  text-align: center;
  transition: background-color 0.2s, color 0.2s;
  flex-shrink: 0; /* Ngăn nút bị co lại */
}

/* Hover cho desktop */
.pageButton:hover:not(:disabled) {
  background-color: #007bff !important;
  color: #FFFFFF !important;
}

/* Trạng thái disabled */
.pageButton:disabled {
  cursor: not-allowed;
  color: #ccc;
  border-color: #ccc;
  background-color: #f5f5f5;
}

/* Trang hiện tại */
.pageButton.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

/* Dấu ba chấm */
.ellipsis {
  padding: 8px 12px;
  font-size: 14px;
  color: #666;
  flex-shrink: 0;
}

/* Desktop (>768px): Hiển thị trên một hàng, không xuống dòng */
@media (min-width: 769px) {
  .pagination {
    flex-wrap: nowrap; /* Không cho phép xuống dòng */
    white-space: nowrap; /* Đảm bảo nội dung không xuống dòng */
    padding: 0 10px;
  }
}

/* Mobile (≤768px): Hiển thị 3 nút, cho phép xuống dòng nếu cần */
@media (max-width: 768px) {
  .pagination {
    flex-wrap: wrap; /* Cho phép xuống dòng nếu không đủ chỗ */
    gap: 6px;
    padding: 0 10px;
  }

  .pageButton {
    padding: 6px 10px;
    font-size: 13px;
    min-width: 36px;
  }

  .pageButton:first-child,
  .pageButton:last-child {
    font-size: 14px;
    padding: 6px 12px;
  }

  .ellipsis {
    padding: 6px 10px;
    font-size: 13px;
  }
}

/* Màn hình rất nhỏ (≤480px) */
@media (max-width: 480px) {
  .pagination {
    gap: 4px;
  }

  .pageButton {
    min-width: 32px;
    padding: 5px 8px;
    font-size: 12px;
  }

  .pageButton:first-child,
  .pageButton:last-child {
    padding: 5px 10px;
    font-size: 13px;
  }

  .ellipsis {
    padding: 5px 8px;
    font-size: 12px;
  }
}
