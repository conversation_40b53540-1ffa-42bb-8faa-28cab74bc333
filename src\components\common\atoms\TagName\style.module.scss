@import "src/assets/scss/custom-variables.scss";
.title_component {
  button {
    padding: 8px 25px;
  }
  i {
    margin-right: 7px;
  }
  &.title_info_account {
    font-size: 0.875rem;
    margin: 0;
    font-weight: bold;
  }
  &.text_left {
    text-align: left !important;
  }
  &.title_thacmac {
    padding: 11px 15px;
    font-size: 1rem;
    line-height: 1.5rem;
    background: #1da1f2;
    color: #fff;
    border-radius: 4px;
  }
  &.title_line {
    margin-bottom: 35px;
    color: $main_black;
    text-align: center;
    font-weight: 500;
    position: relative;
    text-transform: uppercase;
    z-index: 2;
    font-size: 1.25rem;

    span {
      position: relative;
      &::after {
        content: "";
        position: absolute;
        border-bottom: 5px solid #9bf4cf;
        bottom: 4px;
        left: 0;
        width: 100%;
        z-index: -1;
      }
    }
    &.title_login {
      margin-bottom: 0.5rem;
      text-align: left;
      z-index: 0;
    }
  }
  &.title_user {
    font-size: 0.875rem;
  }
  &.title_headline {
    font-weight: 700 !important;
    color: #12263f;
    font-size: 1rem;
    text-align: center;
    margin: 10px 0 22px;
    text-transform: uppercase;
    width: 100%;
  }
  &.title_contact {
    color: #ffffff;
    z-index: 2;
    font-size: 2rem;
    span {
      &::after {
        content: "";
        position: absolute;
        border-bottom: 5px solid #54dd76;
        bottom: 4px;
        left: 0;
        width: 100%;
        z-index: -1;
      }
    }
  }
  &.title_header_mobile {
    @media #{$medium-and-down} {
      font-size: 1rem;
      text-transform: uppercase;
      margin: 15px 0;
      display: block;
      text-align: center;
      span {
        &:after {
          border: 0;
        }
      }
    }
  }
}
.title_section {
  border-radius: 3px;
  border: 1px solid #e3e6f0;
  padding: 6px 24px;
  color: #627792;
  text-transform: uppercase;
  font-size: 10px;
  font-weight: 400;
  margin-bottom: 2.5rem;
  display: inline-block;
  vertical-align: middle;
  letter-spacing: 4px;
  &.about_medpro {
    color: #ffffff;
    margin-bottom: 1rem;
    border-color: #ffffff;
  }
}
.sub_title_section {
  text-transform: uppercase;
  color: #12263f;
  font-size: 1.625rem;
  margin-bottom: 2.5rem;
  text-align: center;
  &.title_featureMedpro {
    span {
      background: #0352cc;
      padding: 4px 24px;
      border-radius: 3px;
      color: #ffffff;
    }
  }
  @media (max-width: 480px) {
    &.title_featureMedpro {
      font-size: 1.3rem;
      span {
        display: inline-block;
        margin-top: 10px;
      }
    }
  }
}
.title_choose_date {
  font-size: 1rem;
  text-align: left;
  font-weight: 500;
  margin-bottom: 0;
}
.space_between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex_end {
  justify-content: flex-end;
  display: flex;
}

.justify_center {
  display: flex;
  justify-content: center;
}

.title_select_banking_method {
  padding: 10px 10px;
  display: flex;
  justify-content: center;
  & > span {
    font-weight: 600;
    font-size: 17px;
    color: #1da1f2;
  }
}
