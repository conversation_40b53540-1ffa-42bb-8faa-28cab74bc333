import cx from "classnames";
import { get } from "lodash";
import { MDBCol, MDBListGroup, MDBListGroupItem, MDBRow } from "mdbreact";
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import { <PERSON>, withRouter } from "react-router-dom";
import NoContentAlert from "~/components/desktop/atoms/NoContentAlert";
import {
  checkReadNoti,
  getNotificationOnPage
} from "~/store/notifications/notificationsActions";
import { selectMedicalBill } from "~/store/totalData/actions";
import { getDurationFromNowTo, getNextStepViewNoti } from "~/utils/func";
import partnerId from "~/utils/partner";
import styles from "./style.module.scss";

class Notification extends Component {
  constructor(props) {
    super(props);
    this.scrollTop = 0;
    this.state = {
      toggleModal: false,
      category: 1
    };
    this.props.getNotificationOnPage({
      category: 1
    });
  }

  changeNotiType = id => {
    this.setState({ category: id });
  };

  handleViewDetailNoti = noti => {
    this.props.checkReadNoti(noti.id);

    const partnerId = get(noti, "eventData.partnerId", "");
    this.props.selectMedicalBill(partnerId, null);

    const typePaymentNoti = get(noti, "topicId", null);
    const url = getNextStepViewNoti(noti);
    typePaymentNoti === "invoice.confirm"
      ? window.open(url, "_blank") || window.location.replace(url)
      : this.props.history.push(url);
  };

  renderNotiList = data => {
    if (data.length === 0) {
      return <NoContentAlert message="Bạn chưa có thông báo." />;
    }
    return data.map((noti, i) => {
      const isRead = get(noti, "isRead", false);
      return (
        <MDBListGroupItem
          className={styles.timeline_item}
          key={i}
          onClick={() => this.handleViewDetailNoti(noti)}
        >
          <div className={styles.timeline_info}>
            <span>{getDurationFromNowTo(noti.createdAt)}</span>
          </div>
          <div className={styles.timeline_marker} />
          <div className={styles.timeline_content}>
            <p className={!isRead ? styles.is_not_read : ""}>{noti.title}</p>

            <p className={styles.view_detail_noti}>Xem chi tiết...</p>
          </div>
        </MDBListGroupItem>
      );
    });
  };

  render() {
    const { notification } = this.props;
    const { category } = this.state;
    return (
      <Fragment>
        <div
          className={cx(
            styles.wapper_page_inner,
            styles["wapper_page_inner_" + partnerId]
          )}
        >
          <MDBRow>
            <MDBCol>
              <div className={cx(styles.header)}>
                <div className={styles.header_body}>
                  <MDBRow>
                    <MDBCol>
                      <div className={styles.notification_panel}>
                        <MDBListGroup>
                          <MDBListGroupItem>
                            <Link
                              to="#"
                              className={cx(
                                styles.button,
                                styles.btn_medicalBill,
                                category === 1 ? styles.active : ""
                              )}
                              onClick={e => {
                                // e.preventDefault();
                                this.changeNotiType(1);
                              }}
                            >
                              <i className="far fa-file-invoice" /> Phiếu khám
                              bệnh
                            </Link>
                          </MDBListGroupItem>
                        </MDBListGroup>
                      </div>
                    </MDBCol>
                  </MDBRow>
                </div>
              </div>
            </MDBCol>
          </MDBRow>
          {this.renderNotiList(notification.data)}
        </div>
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const { user, notification } = state;
  return {
    IsAuthenticated: user.IsAuthenticated,
    notification
  };
};

export default connect(mapStateToProps, {
  getNotificationOnPage,
  checkReadNoti,
  selectMedicalBill
})(withRouter(Notification));
