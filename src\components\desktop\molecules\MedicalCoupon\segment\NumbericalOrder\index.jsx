import cx from "classnames";
import { MDBSpinner } from "mdbreact";
import moment from "moment";
import React from "react";
import styles from "./styles.module.scss";
import { get } from "lodash";
import partnerId from "~/utils/partner";

const NumbericalOrder = ({ bookingInfo, dataBookingTime }) => {
  const status = get(bookingInfo, "status", 1);
  const awaitMessage = get(bookingInfo, "awaitMessage", "");
  const sequenceNumber = get(bookingInfo, "sequenceNumber", "");
  const date = get(bookingInfo, "date", "");
  const waitingConfirmDate = get(bookingInfo, "waitingConfirmDate", "");
  const classNumber = styles.number;
  const classGray = styles.gray;
  const classWaiting = styles.classWaiting;

  const classCX = cx({
    [classNumber]: true,
    [classGray]: [-2].includes(status),
    [classWaiting]: !date
  });

  const bookingTimeBig = date
    ? moment(date).format("HH:mm")
    : waitingConfirmDate;

  if ([-2, 0, 6].includes(status)) return null;
  const titleBookingTime =
    partnerId === "trungvuong" ? "Giờ tiếp nhận dự kiến" : "Giờ khám dự kiến";
  return (
    <div className={styles.numbericalOrder}>
      {awaitMessage ? (
        <div className={styles.awaitMessage}>
          <p>{awaitMessage}</p>
          <MDBSpinner small yellow className={styles.spin} />
        </div>
      ) : (
        <div className={styles.numbericalOrderNext}>
          {bookingTimeBig && (
            <div className={classCX}>
              <p className={styles.title}>{titleBookingTime}</p>
              <p
                className={
                  (cx(styles.txtNum), bookingTimeBig && styles.txtNum1)
                }
              >
                {bookingTimeBig}
              </p>
            </div>
          )}
          {sequenceNumber && (
            <div className={classCX}>
              <p className={styles.title}>Số thứ tự hiện tại</p>
              <p
                className={cx(styles.txtNum, sequenceNumber && styles.txtNum1)}
              >
                {sequenceNumber}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NumbericalOrder;
