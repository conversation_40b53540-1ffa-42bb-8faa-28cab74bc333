import cx from "classnames";
import {
  MDBBtn,
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBContainer,
  MDBIcon,
  MDBModal,
  MDBModalBody,
  MDBModalHeader,
  MDBRow
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { resetTransactionInfoCSKH } from "~/store/customerService/customerServiceActions";
import ModalCheckTransaction from "../../components/ModalCheckTransaction";
import BoxBody from "./BillBoxBody";
import styles from "./style.module.scss";
import {
  changeUrlRedirectAfterCreatePatient,
  getPatientDetail,
  resetPatientForm
} from "~/store/patientForm/patientFormAction";
import { getExtraConfig, setTypeAction } from "~/store/totalData/actions";
import { selectedPatientDetail } from "~/store/patient/patientAction";
import * as hospitalActions from "~/store/hospital/hospitalActions";
import { sortBy } from "lodash";
import { getBookingsAsyncError } from "~/store/totalData/apis";
import { currentEnv } from "~/configs";
import { TypeAction, urlPage } from "~/utils/constants";
import moment from "moment";
import history from "~/history";

class BillBox extends Component {
  state = {
    showSearchBox: false,
    filteredList: [],
    modal: false,
    bookingsAsyncError: [],
    loadingBookingError: false,
    showBookingErrorModal: false,
    errorMessage: null // Reset any previous error messages
  };

  toggle = () => {
    const { resetTransactionInfoCSKH } = this.props;
    this.setState({
      modal: !this.state.modal
    });
    resetTransactionInfoCSKH();
  };

  fetchBookingErrorList = async () => {
    this.setState({
      loadingBookingError: true,
      errorMessage: null // Reset any previous error messages
    });

    try {
      const { data } = await getBookingsAsyncError(
        currentEnv.RESTFULL_API_URL + "/booking-gateway/booking-error",
        {}
      );

      this.setState({
        bookingsAsyncError: sortBy(data, [
          booking => {
            return booking.partnerId;
          }
        ])
      });
    } catch (error) {
      // Handle certificate expiration case
      if (
        error.response &&
        error.response.data &&
        error.response.data.statusCode === 400
      ) {
        this.setState({
          errorMessage: error.response?.data?.message || "Lỗi server..."
        });

        console.error("Certificate has expired:", error);
      } else {
        // Handle other errors
        const errorMsg =
          error.response?.data?.message ||
          error.message ||
          "Đã xảy ra lỗi khi tải dữ liệu";
        this.setState({
          errorMessage: errorMsg
        });

        console.error("Error fetching booking error list:", error);
      }
    } finally {
      this.setState({ loadingBookingError: false });
    }
  };

  handleEdit = (patient, secretKey, partnerId_Exam) => {
    this.props.resetPatientForm();
    this.props.handleSelectedPatient(patient);
    this.props.setTypeAction(TypeAction.SuaHoSo);
    this.props.changeUrlRedirectAfterCreatePatient(urlPage.CSKH);
    this.props.getExtraConfig(partnerId_Exam);
    this.props.selectHospital(partnerId_Exam);
    this.props.switchSelectFlow(true);
    this.props.handleGetPatientDetail(patient.id, secretKey, () => {
      history.push(`/cham-soc-khach-hang/cap-nhat-ho-so-benh-nhan`);
    });
  };

  render() {
    const { billList } = this.props;
    const { bookingsAsyncError, loadingBookingError } = this.state;
    return (
      <MDBCard className={cx(styles.CardBillBox, "my-3")}>
        <MDBCardHeader
          color="light-blue darken-2"
          className={cx(styles.CardHeader, "py-1 px-3")}
        >
          <MDBRow className=" d-flex flex-column">
            <MDBCol md="12">Danh Sách Phiếu Khám Bệnh</MDBCol>
            <MDBCol md="12">
              <MDBBtn
                color="info-color"
                size="sm"
                onClick={() => this.toggle()}
                className={styles.btnCheckTransaction}
              >
                <MDBIcon icon="layer-group" />
              </MDBBtn>
              <MDBBtn
                color="danger"
                className="m-0 px-3 py-2 z-depth-0"
                size="sm"
                onClick={() => {
                  this.fetchBookingErrorList();
                  this.setState({ showBookingErrorModal: true });
                }}
              >
                DS lỗi phiếu cần hỗ trợ
              </MDBBtn>
            </MDBCol>
          </MDBRow>
        </MDBCardHeader>
        <MDBCardBody className={cx(styles.CardBody)}>
          <BoxBody {...this.props} billList={billList} />
        </MDBCardBody>
        <ModalCheckTransaction isOpen={this.state.modal} toggle={this.toggle} />
        <MDBModal
          isOpen={this.state.showBookingErrorModal}
          toggle={() => {
            this.setState({
              showBookingErrorModal: !this.state.showBookingErrorModal
            });
          }}
          centered
        >
          <MDBModalHeader
            toggle={() => this.setState({ showBookingErrorModal: false })}
          >
            Danh sách phiếu khám cần hỗ trợ
          </MDBModalHeader>
          <MDBModalBody className={styles.modalBodyCustom}>
            {loadingBookingError ? (
              <div className="text-center my-3">
                <MDBIcon icon="spinner" spin size="3x" />
                <p className="mt-2">Đang tải dữ liệu...</p>
              </div>
            ) : this.state.errorMessage ? (
              <div className="text-center my-3">
                <MDBIcon
                  far
                  icon="times-circle"
                  className="text-danger"
                  size="3x"
                />
                <p className="mt-3 text-danger font-weight-bold">
                  {this.state.errorMessage}
                </p>
              </div>
            ) : (
              <div>
                <p className={styles.quanlityError}>
                  Số Lượng: <strong>{bookingsAsyncError?.length}</strong>
                </p>
                <div className={styles.listBookingError}>
                  {bookingsAsyncError.length > 0 ? (
                    bookingsAsyncError.map(item => (
                      <MDBCard
                        key={item._id}
                        cascade
                        className="my-2 hover-translate-y-n10 hover-shadow-lg"
                      >
                        <MDBCardBody
                          className={cx(styles.cardBodyWrapper, "py-1 px-2")}
                        >
                          <MDBContainer fluid>
                            <MDBRow>
                              <MDBCol
                                xl="24"
                                lg="24"
                                md="24"
                                className="pr-1 pl-0"
                              >
                                <ul
                                  className={cx(
                                    styles.listInfo,
                                    styles.cardInfoWrapper,
                                    "list-group list-unstyled d-flex flex-row flex-wrap"
                                  )}
                                >
                                  <li className={cx(styles.groupLi)}>
                                    <div className={cx(styles.groupLabel)}>
                                      <label
                                        className={cx(
                                          styles.value,
                                          styles.partner
                                        )}
                                      >
                                        {item?.partner?.name}
                                      </label>
                                    </div>
                                  </li>

                                  <li
                                    className={cx(
                                      styles.rowSpecial,
                                      styles.groupLi
                                    )}
                                  >
                                    <div className={cx(styles.groupLabel)}>
                                      <label className={cx(styles.title)}>
                                        <MDBIcon
                                          icon="hospital-alt"
                                          className="px-1"
                                        />
                                        <span>Họ tên: </span>
                                      </label>
                                      <label className={cx(styles.value)}>
                                        {item?.patient?.surname +
                                          " " +
                                          item?.patient?.name}
                                      </label>
                                    </div>
                                    <div className={cx(styles.actionExtend)}>
                                      <MDBBtn
                                        color="yellow"
                                        size="sm"
                                        className={styles.btnAct}
                                        onClick={() => {
                                          this.handleEdit(
                                            item.patient,
                                            item.secretKey,
                                            item.partnerId
                                          );
                                        }}
                                      >
                                        <MDBIcon
                                          icon="wrench"
                                          className="px-1"
                                        />{" "}
                                        Sửa
                                      </MDBBtn>
                                    </div>
                                  </li>
                                  <li className={cx(styles.groupLi)}>
                                    <div className={cx(styles.groupLabel)}>
                                      <label className={cx(styles.title)}>
                                        <MDBIcon
                                          icon="mobile-alt"
                                          className="px-1"
                                        />
                                        <span>SĐT Hồ sơ: </span>
                                      </label>
                                      <label className={cx(styles.value)}>
                                        {item?.patient?.mobile}
                                      </label>
                                    </div>
                                  </li>
                                  <li className={cx(styles.groupLi)}>
                                    <div className={cx(styles.groupLabel)}>
                                      <label className={cx(styles.title)}>
                                        <MDBIcon
                                          icon="mobile-alt"
                                          className="px-1"
                                        />
                                        <span>SĐT Tài khoản: </span>
                                      </label>
                                      <label className={cx(styles.value)}>
                                        {item?.user?.username}
                                      </label>
                                    </div>
                                  </li>
                                  <li className={cx(styles.groupLi)}>
                                    <div className={cx(styles.groupLabel)}>
                                      <label className={cx(styles.title)}>
                                        <MDBIcon
                                          icon="file-alt"
                                          className="px-1"
                                        />
                                        <span>Mã phiếu: </span>
                                      </label>
                                      <label className={cx(styles.value)}>
                                        {item.bookingCode}
                                      </label>
                                    </div>
                                  </li>
                                  <li className={cx(styles.groupLi)}>
                                    <div className={cx(styles.groupLabel)}>
                                      <label className={cx(styles.title)}>
                                        <MDBIcon
                                          icon="calendar-alt"
                                          className="px-1"
                                        />
                                        <span>Thời gian khám: </span>
                                      </label>
                                      <label className={cx(styles.value)}>
                                        {moment(item.date).format(
                                          "HH:mm, DD/MM/YYYY"
                                        )}
                                      </label>
                                    </div>
                                  </li>
                                  <li
                                    className={cx(
                                      styles.groupLi,
                                      styles.errorDetail
                                    )}
                                  >
                                    <div className={cx(styles.groupLabel)}>
                                      <label className={cx(styles.title)}>
                                        <MDBIcon
                                          icon="calendar-alt"
                                          className="px-1"
                                        />
                                        <span>Nội dung: </span>
                                      </label>
                                      <p className={cx(styles.value)}>
                                        {item.syncErrorDetail}
                                      </p>
                                    </div>
                                  </li>
                                </ul>
                              </MDBCol>
                            </MDBRow>
                          </MDBContainer>
                        </MDBCardBody>
                      </MDBCard>
                    ))
                  ) : (
                    <div className="text-center my-4">
                      <MDBIcon
                        far
                        icon="clipboard-check"
                        size="3x"
                        className="text-success mb-3"
                      />
                      <p>Không có phiếu khám nào cần hỗ trợ</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </MDBModalBody>
        </MDBModal>
      </MDBCard>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { transactionBill },
    patient: { selectedPatientId }
  } = state;
  return {
    transactionBill,
    selectedPatientId
  };
};

const mapDispatchToProps = {
  resetTransactionInfoCSKH: resetTransactionInfoCSKH,
  resetPatientForm: resetPatientForm,
  setTypeAction: setTypeAction,
  handleSelectedPatient: selectedPatientDetail,
  handleGetPatientDetail: getPatientDetail,
  changeUrlRedirectAfterCreatePatient: changeUrlRedirectAfterCreatePatient,
  getExtraConfig: getExtraConfig,
  selectHospital: hospitalActions.selectHospital,
  switchSelectFlow: hospitalActions.switchSelectFlow
};

export default connect(mapStateToProps, mapDispatchToProps)(BillBox);
