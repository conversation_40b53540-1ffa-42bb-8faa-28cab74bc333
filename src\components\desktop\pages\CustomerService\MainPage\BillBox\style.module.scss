@import "src/assets/scss/custom-variables.scss";
.CardBillBox {
  height: calc(100vh - 125px);

  .CardHeader {
    max-height: 76px;

    .btnCheckTransaction {
      color: white;
    }
  }
}

.CardBody {
  overflow: scroll;
  overflow-x: hidden;
  padding: 15px;
}

/* width */
::-webkit-scrollbar {
  display: none;
}

/* Handle */
::-webkit-scrollbar-thumb {
  display: none;
}

.modalBodyCustom {
  padding: 16px 11px !important;
  .listBookingError {
    padding: 0 5px;
    overflow: scroll;
    max-height: 600px;
  }
}

.quanlityError {
  font-size: 20px;
  margin-bottom: 10px;
}

.cardBodyWrapper {
  position: relative;
  padding: 5px 10px !important;

  .cardInfoWrapper {
    li {
      margin: 0 0 4px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        margin-bottom: 0;
        margin-left: 0;
        display: flex;
        align-items: center;
        gap: 5px;
        flex-shrink: 0;
        max-width: 100%;

        &.title {
          i {
            font-size: 15px;
            max-width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        &.value {
          .tag {
            padding: 3px 6px;
            border-radius: 4px;
          }
        }
      }

      .partner {
        font-size: 17px;
        font-weight: 700;
        margin-bottom: -4px;
        color: #0066ff !important;
      }

      .fullname {
        font-size: 18px;
        margin-bottom: -4px;
        font-weight: 700;
      }

      .bookingCode {
        border-bottom: 1px dashed #0066ff;
        cursor: pointer;
        color: #0066ff !important;
      }

      .dateBooking {
        color: #0066ff !important;
      }

      &.groupLi {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 8px;
        height: 25px;

        @media #{$small-and-down} {
          flex-direction: column;
          align-items: flex-start;
          height: auto;
        }
      }

      .groupLabel {
        display: flex;
        align-items: center;
        gap: 8px;

        @media #{$small-and-down} {
          flex-flow: wrap;
        }
      }
    }
    .errorDetail {
      height: max-content !important;
      width: 100%;
      .groupLabel {
        display: flex;
        align-items: flex-start !important;
        height: 100%;
      }
      .value {
        flex: 1 1 auto;
        color: #ff0000;
        font-weight: 400;
        font-size: 16px;
        max-width: 480px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
