import cx from "classnames";
import { <PERSON><PERSON>ler<PERSON>, MDBAnimation } from "mdbreact";
import React, { Component, Fragment } from "react";
import { Facebook } from "react-content-loader";
import { connect } from "react-redux";
import { selectTimeSlot } from "~/store/totalData/actions";
import { maxTimeSlot } from "~/utils/genSlotTime";
import styles from "./style.module.scss";
import partnerId from "~/utils/partner";

class TimeSlotList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      messageError:
        "Khung giờ trong ngày hiện tại đã hết ,vui lòng chọn khung giờ khác"
    };
  }

  checkIsFull = time => {
    if (typeof time !== typeof undefined)
      return maxTimeSlot(time.id, time.maxSlot, time.usedSlot) === 1;
    return false;
  };

  handleChooseTimeSlot = slot => {
    const { selectTimeSlot, onNextStep } = this.props;
    selectTimeSlot(slot);
    onNextStep();
  };

  renderSlotTime = timeSlots => {
    const { timeId } = this.props;
    return timeSlots.map(slot => {
      return (
        <div
          key={slot.timeId}
          className={
            slot.availableSlot > 0
              ? slot.timeId === timeId
                ? cx(styles.active, styles.time)
                : styles.time
              : cx(styles.full, styles.time)
          }
          onClick={
            slot.availableSlot > 0
              ? () => {
                  this.handleChooseTimeSlot(slot);
                  if (this.props.afterSelectingTimeSlot)
                    this.props.afterSelectingTimeSlot(true);
                }
              : () => {}
          }
        >
          {slot.startTime ? slot.startTime : ""} -{" "}
          {slot.endTime ? slot.endTime : ""}
        </div>
      );
    });
  };

  renderPickTime = () => {
    const { listShift } = this.props;

    return (
      <Fragment>
        {listShift.map(shift => {
          return (
            <div key={shift.id}>
              <div className={styles.list_time_session}>
                {shift.id === "1"
                  ? "Buổi sáng"
                  : shift.id === "2"
                  ? "Buổi chiều"
                  : "Buổi tối"}
              </div>
              <div className={cx(styles.list_time_detail)}>
                {this.renderSlotTime(shift.timeSlotInDay)}
              </div>
            </div>
          );
        })}
      </Fragment>
    );
  };

  render() {
    const { loading, dateSchedules, listShift } = this.props;
    const { messageError } = this.state;

    // chưa check full chỗ này được
    const isFull = dateSchedules && dateSchedules.list.every(this.checkIsFull);
    const isEmpty = !listShift;

    if (isEmpty || !!isFull) {
      return (
        <Fragment>
          <MDBAlert color="warning">{messageError}</MDBAlert>
        </Fragment>
      );
    }

    return (
      <MDBAnimation type="fadeIn">
        <div className={cx(styles.list_time, styles["list_time_" + partnerId])}>
          {loading ? <Facebook /> : this.renderPickTime()}
        </div>
      </MDBAnimation>
    );
  }
}

const mapStateToProps = state => {
  const {
    totalData: {
      dateSchedules,
      days: { date },
      shifts: { list: listShift },
      timeSlot: { timeId }
    }
  } = state;
  return {
    dateSchedules,
    timeId,
    date,
    listShift
  };
};

const mapDispatchToProps = dispatch => ({
  selectTimeSlot: time => dispatch(selectTimeSlot(time))
});

export default connect(mapStateToProps, mapDispatchToProps)(TimeSlotList);
