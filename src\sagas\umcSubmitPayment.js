import { put, call, fork, all, takeLatest, select } from "redux-saga/effects";
import * as submitPaymentTypes from "~/actions/umc/submitPayment/types";
import moment from "moment";
import { apiSubmitPayment } from "~/api/umc/submitPayment";
import { getBaseUrl } from "~/utils/getBaseUrl";
import { PLATFORM_NAME, WEB_NAME } from "~/utils/constants";
function* umcSubmitPayment() {
  try {
    const baseUrl = yield select(state => getBaseUrl(state.hospital));
    let { selectedPatient } = yield select(state => state.patient);
    if (!selectedPatient || Object.keys(selectedPatient).length === 0) {
      const { rebooking } = yield select(state => state.rebooking);
      selectedPatient = rebooking.patient;
    }
    const { info } = yield select(state => state.user);
    const { sumaryInfo } = yield select(state => state.doctorAndTime);
    const { selectedDate } = yield select(state => state.dateAndSpecialist);
    const { selectedMethod, methodId } = yield select(state => state.payment);
    const bookingData = sumaryInfo.map(item => {
      const {
        selectedRoomAndDoctor,
        selectedSpecialist,
        selectedTime,
        selectedBHYT
      } = item;
      return {
        doctor_id: selectedRoomAndDoctor.doctor.id,
        room_id: selectedRoomAndDoctor.id,
        email: selectedPatient.email,
        hospital_id: selectedRoomAndDoctor.hospital_id,
        subject_id: selectedSpecialist.id,
        bhyt: selectedBHYT.id,
        booking_time_id: selectedTime.id,
        booking_phone: selectedPatient.mobile
      };
    });
    const postData = {
      bookings: bookingData,
      method_id: methodId,
      bank_id: selectedMethod.id,
      date: moment(selectedDate).format("YYYY-MM-DD"),
      patient_id: selectedPatient.id,
      user_id: info.user_id,
      platform: PLATFORM_NAME,
      access_token: info.access_token,
      app: WEB_NAME
    };
    const response = yield call(apiSubmitPayment, baseUrl, postData);
    const { data } = response;
    if (typeof data.error_code === typeof undefined) {
      yield put({
        type: submitPaymentTypes.UMC_SUMBIT_PAYMENT_SUCCESS,
        data
      });
      yield call(() => {
        if (methodId !== 6 && data.payment_url) {
          window.location.href = data.payment_url;
        }
      });
    } else {
      yield put({
        type: submitPaymentTypes.UMC_SUMBIT_PAYMENT_FAIL,
        // error: data.error_message
        error: data
      });
    }
  } catch (error) {
    yield put({
      type: submitPaymentTypes.UMC_SUMBIT_PAYMENT_FAIL,
      error
    });
  }
}

function* watchUMCSubmitPayment() {
  yield takeLatest(submitPaymentTypes.UMC_SUMBIT_PAYMENT, umcSubmitPayment);
}

export default function* root() {
  yield all([fork(watchUMCSubmitPayment)]);
}
