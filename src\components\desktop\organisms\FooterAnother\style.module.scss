@import "src/assets/scss/custom-variables";
.footer {
  overflow: hidden;
  .footer_inner {
    background-color: #f8fafd;
    padding: 2rem 0 2rem 0;
    border-top: 1px solid #dfe3eb;
  }
  .footer_copy_right {
    background-image: linear-gradient(45deg, #6a78d1, #00a4bd);
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    padding: 1rem 0;
  }
  .item_info {
    overflow: hidden;
    li {
      border: 0 !important;
      border-radius: 0 !important;
      background-color: transparent;
      font-size: 14px;
      padding-left: 0 !important;
      padding-right: 0 !important;
      padding: 5px 0;
      a {
        color: inherit;
        &:hover,
        &:focus {
          color: #0252cd;
        }
      }
    }
  }
  .icons {
    flex-direction: unset;
    flex-wrap: wrap;
    li {
      flex: 0 1 50%;
      margin-bottom: 5px;
    }
    a {
      display: block;
      padding: 0 10px;
      img {
        width: 100%;
      }
    }
  }
  .textPKH {
    color: #0352cc !important;
  }
  .logo_footer {
    max-width: 160px !important;
  }
  @media #{$medium-and-down} {
    .footer_inner {
      text-align: center;
    }
    .icons {
      max-width: 400px;
      margin: 0 auto;
      justify-content: center;
    }
  }
  @media (max-width: $small-screen) {
    .footer_copy_right,
    .icons {
      li {
        flex: 0 1 40%;
      }
    }
  }
}

//  -------------------- Minh Anh

.footer_minhanh {
  .footer_inner {
    .footer_item {
      .item_info {
        .textPKH {
          color: #db2233 !important;
        }

        li {
          a {
            &:hover,
            &:focus {
              color: #db2233 !important;
            }
          }
        }
      }
    }
  }
  .footer_copy_right {
    background: #db2233 !important;
  }
}

//  -------------------- nhidong hcm
.footer_nhidonghcm{
  .footer_inner {
    background-color: #012A2A;
    .footer_item {
      .logo_footer{
        max-width: 100% !important;
      }
      .item_info {
        li {
          span{
            color: white;
            font-size: 12px;
          }
          a {
            color: white !important;
          }
        }
      }
    }
  }
  .rowFooter{
    display: flex;
    align-items: flex-end;
    .colMedpro{
      .item_info{
        .footer_item{
          padding-bottom: 15px;
        }
      }
    }
  }

  .footer_copy_right {
    background: linear-gradient(45deg, #012A2A, #048a8a) !important;
  }
}
