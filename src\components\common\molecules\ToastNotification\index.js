import React, { Component } from "react";
import { toast, ToastContainer, cssTransition } from "mdbreact";
import "react-toastify/scss/main.scss";
import styles from "./style.module.scss";
import cx from "classnames";
export class ToastNotification extends Component {
  render() {
    return (
      <ToastContainer
        hideProgressBar
        newestOnTop
        autoClose={4000}
        toastClassName={styles.toast}
        closeButton={false}
      />
    );
  }
}

export const openToast = (message, type = "") => {
  if (!type) {
    toast.info(message);
  } else if (type === "warning") {
    toast.warn(message);
  } else if (type === "error") {
    toast.error(message);
  } else if (type === "modal") {
    toast(message, {
      position: "bottom-left",
      className: cx(styles.modal),
      closeOnClick: true,
      autoClose: 5000,
      transition: cssTransition({
        enter: "zoomIn",
        exit: "zoomOut",
        duration: 0
      })
    });
  }
};
