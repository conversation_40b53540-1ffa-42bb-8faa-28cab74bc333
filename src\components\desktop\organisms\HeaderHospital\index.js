import cx from "classnames";
import { find, get } from "lodash";
import {
  MDBAnimation,
  MDBCol,
  MDBCollapse,
  MDBContainer,
  MDBDropdown,
  MDBDropdownMenu,
  MDBDropdownToggle,
  MDBListGroup,
  MDBListGroupItem,
  MDBNavbar,
  MDBNavbarBrand,
  MDBNavbarNav,
  MDBNavbarToggler,
  MDBNavItem,
  MDBNavLink,
  MDBRow
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Link, withRouter } from "react-router-dom";
import { partnerInfo } from "~/configs/partnerDetails";
import { checkReadNoti } from "~/store/notifications/notificationsActions";
import { logOut } from "~/store/user/userAction";
import { getDurationFromNowTo, getNextStepViewNoti } from "~/utils/func";
import { urlLogoHeader } from "~/utils/manageResource";
import styles from "./style.module.scss";

class HeaderHospital extends Component {
  state = {
    collapseID: ""
  };

  notiBell = React.createRef();
  userButton = React.createRef();

  toggleCollapse = collapseID => () => {
    this.setState(prevState => ({
      collapseID: prevState.collapseID !== collapseID ? collapseID : ""
    }));
  };

  renderSomeNotifications = () => {
    const {
      notification: { data }
    } = this.props;

    // Wait update API noti
    if (!data) {
      return <div />;
    }

    return data.slice(0, 3).map((noti, i) => {
      const titleNoti = noti.hospital ? noti.hospital.name : "";
      return (
        <MDBListGroupItem
          key={i}
          className={!noti.isRead ? styles.new : null}
          onClick={() => this.handleViewDetailNoti(noti)}
        >
          <div className={styles.list_news_head}>{titleNoti}</div>
          <p>{noti.title}</p>
          <p>
            <i className="fal fa-clock" />
            {getDurationFromNowTo(noti.createdAt)}
          </p>
        </MDBListGroupItem>
      );
    });
  };

  handleViewDetailNoti = noti => {
    const { history } = this.props;
    this.props.checkReadNoti(noti.id);
    const typePaymentNoti = get(noti, "topicId", null);

    const url = getNextStepViewNoti(noti);
    typePaymentNoti === "invoice.confirm"
      ? window.open(url, "_blank") || window.location.replace(url)
      : history.push(url);
  };

  getActiveClass = pathname => {
    return this.props.location.pathname === pathname ? styles.active : "";
  };

  render() {
    const { IsAuthenticated, notification, logOut, infoUser } = this.props;

    const fullName = get(infoUser, "fullName");
    const userName = get(infoUser, "userName");
    const displayName = fullName !== "" ? fullName : userName;

    const info = get(partnerInfo, "info");
    const hotlinePartner = find(info, { key: "hotline-partner" });
    const hotlineMedpro = find(info, { key: "hotline-medpro" });

    // const logoHeader = find(info, { key: "logo-header" });
    const nameInfo = find(info, { key: "name" });

    const menu = get(partnerInfo, "menu");
    const renderMenu = menu.map((item, index) => {
      return (
        <MDBNavItem
          className={this.getActiveClass(item.url)}
          key={index}
          // onClick={this.toggleCollapse("navbarCollapse3")}
        >
          <MDBNavLink
            className=""
            to={item.link}
            replace={this.props.location.pathname === item.link}
          >
            {item.name}
          </MDBNavLink>
        </MDBNavItem>
      );
    });

    const buttonOnHeader = () => {
      const { IsAuthenticated } = this.props;
      if (IsAuthenticated) {
        return "";
      }
      return (
        <MDBNavItem>
          <MDBNavLink className={styles.dang_nhap} to="/login">
            Đăng nhập
          </MDBNavLink>
        </MDBNavItem>
      );
    };

    return (
      <MDBAnimation
        type="fadeIn"
        className={cx(
          styles.header,
          styles.header_classic,
          styles.header_shadow
        )}
      >
        <div className={styles.nav_info}>
          <MDBContainer>
            <MDBRow>
              <MDBCol className={styles.nav_info_support}>
                <div className={styles.nav_info_support__item}>
                  {" "}
                  <span>
                    {hotlinePartner.displayPrefix}:{" "}
                    <b>{hotlinePartner.value}</b>{" "}
                  </span>
                  <span>
                    {" "}
                    {hotlineMedpro.displayPrefix}:<b>{hotlineMedpro.value}</b>
                  </span>{" "}
                </div>
                <MDBListGroup className={styles.list_group_dangnhap}>
                  {/* Thông báo  */}
                  {IsAuthenticated ? (
                    <MDBListGroupItem>
                      <MDBDropdown className={styles.mdbropdown}>
                        <MDBDropdownToggle
                          ref={this.notiBell}
                          className={cx(styles.button, styles.bell)}
                        >
                          <i className="fal fa-bell" />
                          {notification.totalNew ? (
                            <span className={styles.count}>
                              {notification.totalNew}
                            </span>
                          ) : null}
                        </MDBDropdownToggle>
                        <MDBDropdownMenu
                          onClick={() => this.notiBell.current.onClick()}
                          right
                          className={cx(
                            styles.mdbdropdownMenu,
                            styles.mdbdropdownBell
                          )}
                        >
                          <div className={styles.head_alert}>
                            <span>Danh sách thông báo</span>
                          </div>
                          <div className={styles.mdbdropdownItem}>
                            <MDBListGroup className={styles.list_news}>
                              {this.renderSomeNotifications()}
                              <MDBListGroupItem>
                                <Link
                                  to={{
                                    pathname: "/user",
                                    state: { activeItem: 3 }
                                  }}
                                  replace={
                                    this.props.location.pathname === "/user"
                                  }
                                  className={styles.view_all}
                                >
                                  <span>Xem tất cả </span>
                                </Link>
                              </MDBListGroupItem>
                            </MDBListGroup>
                          </div>
                        </MDBDropdownMenu>
                      </MDBDropdown>
                    </MDBListGroupItem>
                  ) : null}
                  {IsAuthenticated && (
                    <MDBListGroupItem>
                      <MDBDropdown className={styles.mdbropdown}>
                        <MDBDropdownToggle
                          className={cx(styles.button, styles.after_login)}
                          ref={this.userButton}
                        >
                          {displayName}
                        </MDBDropdownToggle>
                        <MDBDropdownMenu
                          onClick={() => this.userButton.current.onClick()}
                          right
                          basic
                          className={cx(
                            styles.mdbdropdownMenu,
                            styles.mdbdropdownProfile
                          )}
                        >
                          <MDBListGroup>
                            <MDBListGroupItem className={styles.item_user}>
                              <div className={styles.item_user_icon}>
                                <i className="fas fa-user-circle" />{" "}
                              </div>
                              <div className={styles.info}>
                                <span>Xin chào!</span>
                                <div className={styles.info_name}>
                                  <b>{displayName}</b>
                                </div>
                              </div>
                            </MDBListGroupItem>
                            <MDBListGroupItem
                              className={styles.item_information}
                            >
                              <Link
                                to={{
                                  pathname: "/user",
                                  state: { activeItem: 1 }
                                }}
                                replace={
                                  this.props.location.pathname === "/user"
                                }
                              >
                                <i className="fal fa-address-book" /> Hồ sơ bệnh
                                nhân{" "}
                              </Link>
                            </MDBListGroupItem>
                            <MDBListGroupItem
                              className={styles.item_information}
                            >
                              <Link
                                to={{
                                  pathname: "/user",
                                  state: { activeItem: 2 }
                                }}
                                replace={
                                  this.props.location.pathname === "/user"
                                }
                              >
                                <i className="fal fa-file-medical" /> Phiếu khám
                                bệnh
                              </Link>
                            </MDBListGroupItem>
                            <MDBListGroupItem
                              className={styles.item_information}
                            >
                              <Link
                                to={{
                                  pathname: "/user",
                                  state: { activeItem: 3 }
                                }}
                                replace={
                                  this.props.location.pathname === "/user"
                                }
                              >
                                <i className="fal fa-bell" />
                                Thông báo
                              </Link>
                            </MDBListGroupItem>
                            <MDBListGroupItem className={styles.item_sign_out}>
                              <Link
                                onClick={() => {
                                  logOut();
                                  this.props.history.push("/");
                                }}
                                to="#"
                              >
                                <i className="fal fa-power-off" /> Thoát
                              </Link>
                            </MDBListGroupItem>
                          </MDBListGroup>
                        </MDBDropdownMenu>
                      </MDBDropdown>
                    </MDBListGroupItem>
                  )}
                </MDBListGroup>
              </MDBCol>
            </MDBRow>
          </MDBContainer>
        </div>

        <div className={styles.wrap}>
          <nav className={styles.navigator}>
            <MDBNavbar expand="lg" className={styles.mdbNavbar}>
              <Link to="/">
                <MDBNavbarBrand className={styles.brand_logo}>
                  <img src={urlLogoHeader} alt={nameInfo} />
                </MDBNavbarBrand>
              </Link>
              <MDBNavbarToggler
                className={styles.mdbNavbarToggler}
                onClick={this.toggleCollapse("navbarCollapse3")}
              >
                <i className="fas fa-bars" />
              </MDBNavbarToggler>
              <MDBCollapse
                className={styles.mdbCollapse}
                id="navbarCollapse3"
                isOpen={this.state.collapseID}
                navbar
              >
                <MDBNavbarNav right className={styles.mdbNavbarNav}>
                  {renderMenu}
                  {buttonOnHeader()}
                </MDBNavbarNav>
              </MDBCollapse>
            </MDBNavbar>
          </nav>
        </div>
      </MDBAnimation>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { IsAuthenticated, info: infoUser },
    notification,
    partner
  } = state;
  return {
    IsAuthenticated,
    notification,
    partner,
    infoUser
  };
};

export default connect(mapStateToProps, { logOut, checkReadNoti })(
  withRouter(HeaderHospital)
);
