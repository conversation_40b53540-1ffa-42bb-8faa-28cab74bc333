@import "src/assets/scss/pro-desktop/trustInformation.scss";

.groupBtnRight {
  display: flex;
  gap: 1rem;
}
.medproCareItem {
  width: 100%;
  margin-bottom: 12px;
  label {
    width: 100% !important;
  }
  &:last-of-type {
    margin-bottom: 0;
  }
  p {
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    color: #24313d;
    margin-bottom: 2px;
  }
  .support {
    position: relative;
    .description {
      display: none;
      width: 700px;
      position: absolute;
      top: 24px;
      left: -200px;
      background-color: #e8e8e8;
      padding: 16px;
      border-radius: 12px;
      z-index: 9999;
    }
    &:hover {
      .description {
        display: block;
      }
    }
  }
  .badge {
    display: flex;
    gap: 4px;
    background-color: #fffce8;
    border: calc(1px / 2) solid #fa791c;
    border-radius: 4px;
    padding: 4px 6px;
    span {
      font-weight: 400;
      font-size: 10px;
      line-height: 100%;
      color: #fa791c;
    }
  }
  .ItemCare247 {
    width: 100%;
    position: relative;
  }
  .careItemName {
    display: flex;
    gap: 4px;
    font-size: 16px;
    font-weight: 500;
    line-height: 18.75px;
    color: #003553;
  }
  .careItemPrice {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    .price .originalPrice {
      font-size: 16px;
      font-weight: 500;
      line-height: 18.75px;
    }
    .price {
      color: #f5222d;
    }
    .originalPrice {
      color: #b2b0b0;
      text-decoration: line-through;
    }
  }
  .detailBtn {
    position: absolute;
    top: 0;
    right: 0;
  }
}
