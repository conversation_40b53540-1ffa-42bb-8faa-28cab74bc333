import React, { Component } from "react";
import { MDBContainer, MDBRow, MDBCol } from "mdbreact";
import styles from "./style.module.scss";
import { data } from "./utils/data";
import { uniqueId } from "lodash-es";

class FeatureMedproClinic extends Component {
  render() {
    return (
      <MDBContainer className={styles.Container}>
        <MDBRow className={styles.rowFeature}>
          <MDBCol xl={12} className={styles.colFeature}>
            <div className={styles.Label}>
              <p>
                <span className={styles.title}>
                  TẠI SAO BẠN NÊN CHỌN CHÚNG TÔI?
                </span>
                <span className={styles.value}>
                  Chúng tôi đã tích hợp Hệ thống Quản lý Phòng khám/Phòng mạch
                  hoàn chỉnh của mình với nhiều tính năng ưu việt.
                  <br /> Điều này giúp chúng tôi tạo ra một hệ sinh thái tiện
                  dụng và an toàn cho Bệnh viện và Phòng khám/Phòng mạch.
                </span>
              </p>
            </div>
            <ul className={styles.listFeature}>
              {data.map((item, index) => {
                return (
                  <li key={uniqueId()}>
                    <div
                      className={
                        index % 2 !== 0
                          ? styles.itemFeature_even
                          : styles.itemFeature_odd
                      }
                    >
                      <figure className={styles.img}>
                        <img src={item.img} alt="" />
                      </figure>
                      <p className={styles.description}>
                        <span className={styles.title}>{item.title}</span>
                        <span className={styles.value}>{item.value}</span>
                      </p>
                    </div>
                  </li>
                );
              })}
            </ul>
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    );
  }
}
export default FeatureMedproClinic;
