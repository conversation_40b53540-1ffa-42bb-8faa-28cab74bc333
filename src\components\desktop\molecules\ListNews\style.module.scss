@import "src/assets/scss/custom-variables.scss";

a {
  color: black;
}

.card_news {
  box-shadow: none;
  display: flex;
  flex-direction: revert;
  margin-bottom: 2.5rem;
  .view {
    min-width: 280px;
    max-height: 170px;
  }
  .card_body {
    font-family: "Averta", serif;
    padding: 0 0 0 17px !important;
    max-height: 170px;
    .title {
      font-size: 1.125rem;
      text-transform: uppercase;
      margin-bottom: 10px;
    }
    .tag {
      font-size: 0.75rem;
      color: #777;
      overflow: hidden;
      margin-bottom: 5px;
    }
    .description {
      font: 400 1rem arial;
      line-height: 1.45;
      text-align: justify;
      text-justify: inter-word;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3; /* number of lines to show */
      -webkit-box-orient: vertical;
    }
  }
  @media #{$medium-and-down} {
    .view {
      width: 150px;
      min-width: 150px;
      max-height: 120px;
    }
    .card_body {
      max-height: 120px;
      .title {
        font-size: 0.875rem;
        text-transform: initial;
        text-overflow: ellipsis;
        text-justify: inter-word;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 4; /* number of lines to show */
        -webkit-box-orient: vertical;
      }
      .description {
        display: none;
      }
    }
  }
}
