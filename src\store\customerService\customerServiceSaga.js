import { get, isArray } from "lodash";
import {
  all,
  delay,
  fork,
  put,
  select,
  takeEvery,
  takeLatest
} from "redux-saga/effects";
import { openToast } from "~/components/common/molecules/ToastNotification";
import history from "~/history";
import * as notiAction from "~/store/notifications/notificationsActions";
import { huyi } from "~/utils/clog";
import * as constants from "~/utils/constants";
import { client } from "~/utils/medproSDK";
import { SELECT_HOSPITAL_BY_ID } from "../hospital/hospitalTypes";
import { selectPatientFromData, setPatientId } from "../patient/patientAction";
import {
  getBookingTree,
  offLoading,
  onLoading,
  resetSchedule
} from "../totalData/actions";
import {
  BOOKING_INFO_CSKH_SUCCESS,
  CHANGE_BOOKING_FLOW,
  SET_TYPE_ACTION
} from "../totalData/types";
import {
  getPatientsByPhoneCSKH,
  selectPatientInfoCSKH,
  setDataHistoryBooking
} from "./customerServiceActions";
import * as types from "./customerServiceTypes";

function* requestTransactionBookingCSKH({ transactionId }) {
  try {
    const { cskhToken } = yield select(state => state.customerService);
    const response = yield client.getTransactionInfoCSKH(
      { transactionId },
      { cskhtoken: cskhToken }
    );

    yield put({
      type: types.GET_TRANSACTIONID_INFO_CSKH_SUCCESS,
      transactionBill: response.data
    });
  } catch (error) {
    console.log(error);

    const { statusCode } = error.response.data;

    if (statusCode === 403) {
      openToast(
        "Bạn không có quyền để thực hiện chức năng, vui lòng liên hệ quản trị viên để tìm hiểu chi tiết",
        "error"
      );
    }

    if (statusCode === 500 || statusCode === 400) {
      yield put({
        type: types.GET_TRANSACTIONID_INFO_CSKH_FAIL,
        messageGetTransactionError: "Không tìm thấy phiếu !"
      });
    }
  }
}

function* requesTransactionBookingCSKHWatcher() {
  yield takeLatest(
    types.GET_TRANSACTIONID_INFO_CSKH,
    requestTransactionBookingCSKH
  );
}

function* requestBookingInfoCSKH({ payload }) {
  try {
    const {
      id,
      smsCode,
      transactionId,
      bookingCode,
      partnerId,
      typeAction,
      phone
    } = payload;
    const postData = {};

    if (transactionId) {
      postData.transactionId = transactionId;
    }
    if (id) {
      postData.id = id;
    }
    if (smsCode) {
      postData.smsCode = smsCode;
      postData.phone = phone;
    }
    if (bookingCode) {
      postData.bookingCode = bookingCode;
    }
    const response = yield client.getBookingInfoCSKH(postData, {
      partnerid: partnerId
    });

    if (!response.data) {
      openToast("Không tìm thấy thông tin phiếu khám!", "error");
      history.push("/cham-soc-khach-hang");
    }

    const editData = isArray(response.data) ? response.data : [response.data];

    console.log("editData :>> ", editData);

    yield put({
      type: types.GET_BOOKING_INFO_CSKH_SUCCESS,
      payload: response.data
    });

    yield put({
      type: BOOKING_INFO_CSKH_SUCCESS,
      payload: editData
    });

    yield put(selectPatientFromData(response?.data?.patient));

    yield put(setPatientId(response?.data?.patientId));

    if (typeAction === constants.TypeAction.SuaPhieuKham) {
      const { treeId, partnerId, id } = response.data;

      console.log(" response.data SuaPhieuKham:>> ", response.data);

      yield put({ type: SET_TYPE_ACTION, value: typeAction });
      yield put({ type: SELECT_HOSPITAL_BY_ID, id: partnerId });
      yield put({ type: CHANGE_BOOKING_FLOW, flow: treeId });
      yield put(resetSchedule());
      yield put(getBookingTree({ partnerId, idBooking: id }));
    }
  } catch (error) {
    console.log("error requestBookingInfoCSKH :>> ", error);
    const message = get(error, "response.data.message");
    openToast(message, "error");
  } finally {
    yield put(notiAction.getNotificationOnPage({ category: 1 }));
  }
}

function* requestPaymentInfoCSKHWatcher() {
  yield takeLatest(types.GET_BOOKING_INFO_CSKH, requestBookingInfoCSKH);
  yield takeLatest(types.SEND_BOOKING_VIA_SMS, requestSendBookingToMobile);
}
function* requestSendBookingToMobile({ payload }) {
  try {
    yield client.sendSMS({
      mobile: payload.mobile,
      bookingCode: payload.bookingCode
    });
    openToast("Đã gửi tin nhắn thành công!", "success");
  } catch (e) {
    openToast("Gửi tin nhắn thất bại!", "error");
    console.log("payload", e ?? JSON.stringify(e.response, null, 2));
  }
}

function* requestPatientsInfoByPhone(payload) {
  try {
    yield put(onLoading());
    const { cskhToken } = yield select(state => state.customerService);
    const total = yield select(state => state.totalData);

    const token = yield select(state => state.user.info.token);

    window.localStorage.setItem("searchedPhone", payload.phone);

    const response = yield client.getPatientsByPhoneCskh(
      {
        phone: payload.phone
      },
      { token, cskhtoken: cskhToken }
    );

    if (!total.typeAction) {
      yield put({ type: types.RESET_ALL_CS });
    }

    // create an action
    yield put({
      type: types.GET_PATIENTS_BY_PHONE_CSKH_SUCCESS,
      payload: response.data
    });

    yield put(offLoading());
  } catch (error) {
    // openToast("Lỗi kết nối tới máy chủ.", "error");
    yield put(offLoading());

    console.log("error getPatientsByPhoneCskh:>> ", error);
    if (
      error.response.data.statusCode === 403 ||
      error.response.data.statusCode === 401
    ) {
      openToast(
        "Bạn không có quyền để thực hiện chức năng, vui lòng liên hệ quản trị viên để biết thêm chi tiết",
        "error"
      );
    }

    delay(2000);

    yield put({
      type: types.GET_PATIENTS_BY_PHONE_CSKH_FAIL
    });

    // yield put(loginActions.redirectToMedproId());
  }
}
function* requestPatientsInfoByPhoneWatcher() {
  yield takeEvery(types.GET_PATIENTS_BY_PHONE_CSKH, requestPatientsInfoByPhone);
}

function* requestBookingsByPatientCskh({ patientInfo }) {
  try {
    if (patientInfo?.secretKey) {
      const token = yield select(state => state.user.info.token);
      const response = yield client.getBookingsByPatientCskh(
        {
          cskhtoken: patientInfo?.secretKey
        },
        { token }
      );

      yield put({
        type: types.REQUEST_BOOKINGS_BY_PATIENT_CSKH_SUCCESS,
        payload: response.data
      });
    } else {
      console.log("Không có secretKey:>> ");
    }
    yield put(offLoading());
  } catch (error) {
    yield put(offLoading());

    huyi({ name: "getBookingsByPatientCskh", child: error, type: "error" });
    const err = get(error, "response.data", "");
    yield put({
      type: types.REQUEST_BOOKINGS_BY_PATIENT_CSKH_SUCCESS,
      payload: [{ error: true, ...err }]
    });
  }
}
function* requestBookingsByPatientCskhWatcher() {
  yield takeEvery(
    types.REQUEST_BOOKINGS_BY_PATIENT_CSKH,
    requestBookingsByPatientCskh
  );
}

function* setCskhSecretKey({ payload }) {
  try {
    yield client.setCskhToken(payload);
    window.localStorage.setItem("cskhToken", payload);
  } catch (error) {
    console.log(error);
  }
}

function* setCskhSecretKeyWatcher() {
  yield takeLatest(types.SET_CSKH_SECRET_KEY, setCskhSecretKey);
}

function* deleteSelectedPatientByCSKH() {
  try {
    // set cskh token before request
    const { cskhToken } = yield select(state => state.customerService);
    if (!cskhToken) {
      throw new Error("Invalid Token!");
    }
    yield client.setCskhToken(cskhToken);

    const { selectedPatient } = yield select(state => state.customerService);
    yield client.unlinkPatient({ id: selectedPatient.id });

    openToast("Bạn đã xóa hồ sơ bệnh nhân thành công!");

    const { searchedPhone } = yield select(state => state.customerService);
    if (!searchedPhone || searchedPhone === "") {
      throw new Error("Unexpected Error!");
    }

    yield put({
      type: types.GET_PATIENTS_BY_PHONE_CSKH,
      phone: searchedPhone
    });
  } catch (e) {
    console.log(e);
    const message = get(e, "response.data.message", "");
    openToast(message, "error");
  }
}

function* deleteSelectedPatientByCSKHWatcher() {
  yield takeLatest(types.DELETE_PATIENT_BY_CSKH, deleteSelectedPatientByCSKH);
}

function* sendBookingViaEmail({ payload }) {
  try {
    const response = yield client.sendBookingToMail({
      id: payload.id,
      email: payload.email
    });

    if (response.status === 201) {
      openToast("Gửi phiếu thành công!!");
      return;
    }
  } catch (error) {
    console.log(error);
    openToast("Lỗi: Không gửi được phiếu.");
  }
}

function* sendBookingViaEmailWatcher() {
  yield takeLatest(types.SEND_BOOKING_VIA_EMAIL, sendBookingViaEmail);
}

function* insertPatientIntoUserCskh({ patientInfo }) {
  try {
    // yield put(onLoading());
    const { searchedPhone } = yield select(state => state.customerService);

    const response = yield client.insertPatientIntoUserCskh({
      cskhtoken: patientInfo?.secretKey
    });

    yield put(selectPatientInfoCSKH(response.data));
    yield put(getPatientsByPhoneCSKH(searchedPhone));
    // yield put(offLoading());

    openToast("Thêm hô sơ thành công!!");
  } catch (error) {
    yield put(offLoading());
    console.log("error insertPatientIntoUserCskh :>> ", error);
    const message = get(error, "response.data.message");
    openToast(message, "error");
  }
}

function* insertPatientIntoUserCskhWatcher() {
  yield takeLatest(
    types.INSERT_PATIEN_INTO_USER_CSKH,
    insertPatientIntoUserCskh
  );
}

// Care247 Pay
function* insertCare247IndependentCskh({ bookingId, actionType = "cancel" }) {
  try {
    let response;

    switch (actionType) {
      case "refund":
        // Handle refund request
        response = yield client.createCare247PaymentIndependent({
          bookingId: bookingId,
          action: "request_refund" // Add this parameter to your API endpoint
        });
        break;

      case "cancel":
      default:
        // Handle cancel bill (default behavior)
        response = yield client.createCare247PaymentIndependent({
          bookingId: bookingId,
          action: "cancel_bill" // Add this parameter to your API endpoint
        });
        break;
    }

    yield put({
      type: types.GET_REQUEST_CARE247_INDEPENDENT_CSKH_SUCCESS,
      payload: response.data
    });
  } catch (error) {
    yield put({
      type: types.GET_REQUEST_CARE247_INDEPENDENT_CSKH_FAIL,
      payload: { error: error.message }
    });
    console.error(error);
    openToast("Đã có lỗi xảy ra", "error");
  }
}
function* insertCare247IndependentCskhWatcher() {
  yield takeLatest(
    types.REQUEST_CARE247_INDEPENDENT_CSKH,
    insertCare247IndependentCskh
  );
}

function* getBookingUpdateHistory({ payload }) {
  try {
    // const { selectedPatient, cskhToken } = yield select(
    //   state => state.customerService
    // );

    const { info } = yield select(state => state.user);

    const response = yield client.getBookingUpdateHistory(
      {
        bookingId: payload.bookingId,
        page: 0,
        limit: 10
      },
      {
        token: info?.token
      }
    );

    yield put(setDataHistoryBooking(response.data));
  } catch (error) {
    console.log("error getBookingUpdateHistory :>> ", error);
    const message = get(error, "response.data.message");
    openToast(message, "error");
  }
}

function* getBookingUpdateHistoryWatcher() {
  yield takeLatest(types.GET_HISTORY_BOOKING, getBookingUpdateHistory);
}

export default function* root() {
  yield all([
    fork(getBookingUpdateHistoryWatcher),
    fork(insertPatientIntoUserCskhWatcher),
    fork(requestPaymentInfoCSKHWatcher),
    fork(requestBookingsByPatientCskhWatcher),
    fork(requestPatientsInfoByPhoneWatcher),
    fork(setCskhSecretKeyWatcher),
    fork(deleteSelectedPatientByCSKHWatcher),
    fork(sendBookingViaEmailWatcher),
    fork(requesTransactionBookingCSKHWatcher),
    fork(insertCare247IndependentCskhWatcher)
  ]);
}
