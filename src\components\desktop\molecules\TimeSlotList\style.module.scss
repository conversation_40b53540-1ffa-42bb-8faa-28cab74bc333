@import "src/assets/scss/custom-variables.scss";

// ---------------------------Minh anh

.list_time_minhanh {
  .time {
    color: #db2233 !important;
    border: 1px solid #db2233 !important;
    &:hover,
    &.active {
      color: white !important;
      background-color: #db2233;
    }
  }
}
//  ----------------------------
.list_time {
  border-top: 3px solid #dfe3eb;
  margin-top: 10px;
  svg {
    padding: 15px 0;
    width: 35%;
  }
}
.list_time_session {
  margin: 10px 0 0 0;
  font-weight: bold;
}
.list_time_detail {
  margin: 10px 0 0 0;
  display: flex;
  flex-wrap: wrap;
}
.time {
  min-width: 160px;
  border-radius: 3px;
  cursor: pointer;
  text-align: center;
  padding: 8px 45px;
  white-space: nowrap;
  display: flex;
  flex: 0 1 22%;
  vertical-align: middle;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #0352cc;
  margin-bottom: 10px;
  margin: 5px;
  color: #0352cc;
  transition: all 0.3s ease-in-out;
  &.full {
    cursor: not-allowed;
    color: #fff !important;
    background: #d0d3d8;
    border: 2px solid transparent;
    transform: none !important;
    box-shadow: unset !important;
    &:hover {
      background: #d0d3d8 !important;
    }
  }
  &:hover,
  &.active {
    background-color: #0352cc;
    color: #fff;
    transform: translateY(-0.5%);
    // box-shadow: 0 2rem 4rem rgba(0, 0, 0, 0.2);
  }
  @media #{$medium-and-down} {
    min-width: 120px;
    padding: 8px 15px;
  }
}
