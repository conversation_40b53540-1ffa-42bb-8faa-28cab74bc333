@import "src/assets/scss/pro-mobile/medicalBillPrint.scss";

.reasonCancel {
  width: 100%;
}
.headerModal {
  :global {
    .modal-title {
      font-size: 18px !important;
    }
  }
}
.bodyModal {
  min-height: 500px;
  position: relative;
  .containerLoading {
    height: 450px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.6);
  z-index: 10;
  transition: opacity 0.3s;
}

.loadingBlur {
  filter: blur(2px) grayscale(0.2);
  opacity: 0.7;
  pointer-events: none;
  transition: filter 0.3s, opacity 0.3s;
}

:global(.modal-dialog) {
  max-width: 650px;
}
.content {
  font-size: 16px;
  font-weight: 500;
  span {
    color: #0099ff;
    font-weight: 500;
  }
}
.doctor-side,
.patient-side {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
}

h5 {
  color: #4285f4;
}
.titleForm {
  margin-bottom: 12px;
  font-weight: 700;
  font-size: 16px;
  sup {
    // font-size: 14px;
    color: #cc0000;
  }
}
.errorMessage {
  font-size: 12px;
  font-style: italic;
  color: #cc0000;
}

.timelineSection {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 20px;

  h5 {
    font-weight: 600;
    margin-bottom: 15px;
  }
}

.verticalTimelineWrapper {
  max-height: 320px;
  overflow-y: auto;
  position: relative;
  margin-left: 18px;
  border-left: 2px solid #ececec;
  display: flex;
  flex-direction: column;
  gap: 22px;
}

.timelineItem {
  position: relative;
  display: flex;
  align-items: flex-start;
  min-height: 36px;
  padding-left: 25px;
  font-size: 15px;
}

.timelineDot {
  position: absolute;
  left: 3px;
  top: 7px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #bdbdbd;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #ececec;
  z-index: 2;
}

.doctor .timelineDot {
  background: #bdbdbd;
}
.patient .timelineDot {
  background: #bdbdbd;
}

.timelineTitle {
  font-weight: 600;
  // color: #222;
}

.doctor .timelineTitle {
  // color: #222;
}
.patient .timelineTitle {
  // color: #222;
}

.timelineContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.timelineHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  // color: #222;
}

.timelineTitle {
  font-weight: 600;
  // color: #222;
}

.timelineStatus {
  font-size: 13px;
  font-weight: 500;
  padding: 2px 10px;
  border-radius: 12px;
  margin-left: 4px;
  background: #e8f5e9;
  color: #388e3c;
}

.joined .timelineStatus {
  background: #e8f5e9;
  color: #388e3c;
}
.left .timelineStatus {
  background: #ffebee;
  color: #d32f2f;
}

.timelineTime {
  display: flex;
  gap: 10px;
  font-size: 13px;
  color: #757575;
  font-weight: 400;
  margin-top: 2px;
  border-bottom: 1px solid #e0e0e0;
}


.timelineUserName {
  font-weight: 600;
  color: #4285f4;
}

.timelineTime,
.timelineDevice,
.timelineLocation {
  font-size: 14px;
}


.timelineDevice {
  color: #757575;
  margin-left: 15px;
}

.timelineLocation {
  // color: #00c851;
}

.timelineEvent {
  display: inline-block;
  padding: 3px 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #4285f4;
  font-size: 13px;
  font-weight: 500;
  margin-top: 8px;
}

.horizontalTimelineWrapper {
  overflow-x: auto;
  width: 100%;
  padding: 30px 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    height: 2px;
    background-color: #e0e0e0;
    width: 100%;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    z-index: 1;
  }
}

:global {
  .timeline {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: max-content;
    min-width: 100%;
    position: relative;
    justify-content: space-around;
    align-items: center;
    padding: 20px 0;

    .timeline-step {
      margin: 0 25px;
      min-width: 180px;
      position: relative;
      z-index: 2;

      &:nth-child(odd) {
        margin-bottom: 60px;
        padding-bottom: 15px;

        &::before {
          content: '';
          position: absolute;
          height: 30px;
          width: 2px;
          background-color: #e0e0e0;
          bottom: -15px;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      &:nth-child(even) {
        margin-top: 60px;
        padding-top: 15px;

        &::before {
          content: '';
          position: absolute;
          height: 30px;
          width: 2px;
          background-color: #e0e0e0;
          top: -15px;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      .timeline-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #4285f4;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        z-index: 3;

        &.warning {
          background-color: #ffbb33;
        }

        &.success {
          background-color: #00c851;
        }

        &.info {
          background-color: #33b5e5;
        }
      }

      &:nth-child(odd) .timeline-circle {
        bottom: -30px;
      }

      &:nth-child(even) .timeline-circle {
        top: -30px;
      }

      &:last-child {
        margin-right: 0;
      }
    }

    // Remove or neutralize the anchor tag styling
    li a {
      display: none;
    }
  }
  .stepper.timeline.stepper-vertical li:not(:last-child):after {
    display: none;
  }
  .style_horizontalTimelineWrapper__32V2L::before {
    display: none;
  }
  .timeline-step-text {
    font-size: 13px;
  }

  .stepper.timeline li {
    position: relative;

    // Adjust for anchor removal
    &:not(:last-child):after {
      display: none;
    }

    .step-content {
      padding: 15px;
      border-radius: 8px;
      background-color: #f9f9f9;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      width: 100% !important;
      float: none !important;
      left: 0 !important;

      &:before, &:after {
        display: none !important;
      }
    }
  }
}
