import { get } from "lodash";

const size = "font-size: 13px";

export const huyi = ({ name, child, type }) => {
  const e = get(child, "response.data", "");
  // const env = process.env.REACT_APP_STAGE;

  // if (env === "testing") {
  switch (type) {
    case 0:
    case "error":
      if (e) {
        console.info(
          `%cLỗi - ${name} :>> `,
          `color: #dc3545; ${size}`,
          `
          - StatusCode :>>  ${e.statusCode || 404},
          - Message :>> ${e.message},
        `
        );
        console.log(
          `- %cChi tiết lỗi ${name} :>> `,
          `color: #dc3545; ${size}`,
          e
        );
      } else {
        console.info(`%cLỗi - ${name} :>> `, `color: #dc3545; ${size}`, child);
      }
      break;
    case 1:
    case "warning":
      return console.info(
        `%cCảnh báo - ${name} :>> `,
        `color: #ffc107; ${size}`,
        child
      );

    case 2:
    case "info":
      return console.info(
        `%cThông tin - ${name} :>> `,
        `color: #007bff; ${size}`,
        child
      );

    case 3:
    case "success":
      return console.log(
        `%cThành công - ${name} :>> `,
        `color:#28a745 ; ${size}`,
        child
      );

    default:
      return console.log(
        `%cKết quả - ${name} :>> `,
        `color:#6f42c1 ; ${size}`,
        child
      );
  }
  // } else {
  //   return null;
  // }
};
