import { get } from "lodash";
import React from "react";
import styles from "./styles.module.scss";
import { getInfoPatient } from "~/utils/flowRouting";
import { BOOKING_STATUS } from "~/utils/constants";
import Line from "../Line";

const PatientInfo = ({ bookingInfo }) => {
  const userInfo = get(bookingInfo, "userInfo");
  const patientInfo = get(bookingInfo, "patient");
  const { fullName } = getInfoPatient(patientInfo);
  const mobilePatient = get(patientInfo, "mobile", "");
  const Instructor = get(bookingInfo, "medproCare.instructor", "");
  const status = get(bookingInfo, "status", 1);
  if ([BOOKING_STATUS.CHUA_THANH_TOAN].includes(status)) {
    return "";
  }
  return (
    <>
      <div className={styles.patientInfo}>
        <ul className={styles.listInfo}>
          {userInfo?.fullname && (
            <li>
              <span className={styles.column_left}>Người đặt:</span>
              <b>
                {userInfo?.fullname} - {userInfo?.username}
              </b>
            </li>
          )}

          <li>
            <span className={styles.column_left}>Bệnh nhân:</span>
            <b>{fullName + " - " + mobilePatient}</b>
          </li>
          {Instructor && (
            <li>
              <span className={styles.column_left}>Người hướng dẫn:</span>
              <b>{Instructor?.info}</b>
            </li>
          )}
        </ul>
      </div>
    </>
  );
};

export default PatientInfo;
