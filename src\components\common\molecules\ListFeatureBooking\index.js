import cx from "classnames";
import { MDBAnimation, MDBListGroup, MDBListGroupItem } from "mdbreact";
import React, { Fragment } from "react";
import LazyLoad from "react-lazyload";
import { Link } from "react-router-dom";
import styles from "./style.module.scss";

const ListFeatureBooking = ({ handleChooseFeature, listFeatures }) => {
  const filterListFeature = listFeatures.filter(item =>
    item.type.includes("booking")
  );

  const renderFeatureList = list => {
    const ChooseFeatureBookingListNode = list.map(item => {
      return (
        <MDBListGroupItem
          key={`hospital${item.id}`}
          onClick={() => handleChooseFeature(item)}
        >
          <Link
            to="#"
            onClick={event => {
              event.preventDefault();
            }}
          >
            <LazyLoad height={60} once={item.once} key={item.id} debounce={500}>
              <img src={item.image} alt="" />
            </LazyLoad>
            <span>{item.name}</span>
          </Link>
        </MDBListGroupItem>
      );
    });
    return <Fragment>{ChooseFeatureBookingListNode}</Fragment>;
  };

  return (
    <Fragment>
      <MDBAnimation type="fadeIn">
        <MDBListGroup className={cx(styles.list_group, styles.hospital)}>
          {renderFeatureList(filterListFeature)}
        </MDBListGroup>
      </MDBAnimation>
    </Fragment>
  );
};

export default ListFeatureBooking;
