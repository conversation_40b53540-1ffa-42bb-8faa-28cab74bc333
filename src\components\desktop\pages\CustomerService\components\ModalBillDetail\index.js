/* eslint-disable react/jsx-handler-names */
import {
  MDBBtn,
  MDBModal,
  MDBModalBody,
  MDBModalFooter,
  MDBModalHeader
} from "mdbreact";
import React, { Component } from "react";
import MedicalCoupon from "~/components/desktop/molecules/MedicalCoupon";
import history from "~/history";
import styles from "./style.module.scss";
import _ from "lodash";
// components

class DetailModal extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  state = {
    bookingStatus: 1
  };

  resetTabs = () => {
    this.setState({ activeItemBooking: "NormalMedpro" });
  };

  renderMedicalBill = paymentInformation => {
    console.log("paymentInformation :>> ", paymentInformation);
    const statusBooking = _.get(paymentInformation, "status", 1);
    const funcBtn = () => {
      switch (statusBooking) {
        case 0:
        case 6:
          return (
            <React.Fragment>
              <MDBBtn
                color="success"
                size="sm"
                onClick={() => this.props.sendMedicalBill(paymentInformation)}
              >
                G<PERSON><PERSON> phiếu khám
              </MDBBtn>
            </React.Fragment>
          );
        case 1:
          return (
            <React.Fragment>
              <MDBBtn
                color="success"
                size="sm"
                onClick={() => {
                  history.push({
                    pathname: "/cskh/booking",
                    search: `?mpTransaction=${paymentInformation.bookingInfo.transactionId}`,
                    state: { printState: true }
                  });
                }}
              >
                In phiếu khám
              </MDBBtn>
              <MDBBtn
                color="success"
                size="sm"
                onClick={() => this.props.sendMedicalBill(paymentInformation)}
              >
                Gửi phiếu khám
              </MDBBtn>
            </React.Fragment>
          );
        case 2:
          return (
            <React.Fragment>
              <MDBBtn
                color="success"
                size="sm"
                onClick={() => this.props.sendMedicalBill(paymentInformation)}
              >
                Gửi phiếu khám
              </MDBBtn>
            </React.Fragment>
          );
        default:
          return null;
      }
    };

    return (
      <React.Fragment>
        <MDBModalBody className={styles.cardBody}>
          <MedicalCoupon type="CSKH" />
        </MDBModalBody>
        <MDBModalFooter>
          {funcBtn()}
          <MDBBtn
            color="secondary"
            size="sm"
            onClick={() => {
              this.props.toggleShowModal();
              this.resetTabs();
            }}
          >
            Đóng
          </MDBBtn>
        </MDBModalFooter>
      </React.Fragment>
    );
  };

  render() {
    const modalBody = this.props.paymentInformation ? (
      <React.Fragment>
        {this.renderMedicalBill(this.props.paymentInformation)}
      </React.Fragment>
    ) : (
      <React.Fragment>
        <MDBModalBody>Không tìm thấy thông tin phiếu khám</MDBModalBody>
        <MDBModalFooter>
          <MDBBtn
            color="secondary"
            size="sm"
            onClick={() => {
              this.props.toggleShowModal();
              this.resetTabs();
            }}
          >
            Đóng
          </MDBBtn>
        </MDBModalFooter>
      </React.Fragment>
    );
    return (
      <MDBModal
        isOpen={this.props.modalShow}
        toggle={() => {
          this.props.toggleShowModal();
          this.resetTabs();
        }}
      >
        <MDBModalHeader
          toggle={() => {
            this.props.toggleShowModal();
            this.resetTabs();
          }}
        >
          Thông tin phiếu khám
        </MDBModalHeader>
        {modalBody}
      </MDBModal>
    );
  }
}

export default DetailModal;
