/* eslint-disable camelcase */
import axios from "axios";
import { get } from "lodash";
import moment from "moment";
import { all, fork, put, select, takeLatest } from "redux-saga/effects";
import { openToast } from "~/components/common/molecules/ToastNotification";
import * as patientFormActions from "~/store/patientForm/patientFormAction";
import history from "~/history";
import {
  getPatientsByPhoneCSKH,
  selectPatientInfoCSKH
} from "~/store/customerService/customerServiceActions";
import { getPatientList } from "~/store/patient/patientAction";
import * as patientType from "~/store/patient/patientType";
import * as types from "~/store/patientForm/patientFormType";
import { handleAge } from "~/utils/constants";
import { client } from "~/utils/medproSDK";
import { handleUnAuthorize } from "~/utils/unAuthorize";
import { offLoading, onLoading } from "../totalData/actions";
import { logOutWithoutNoti } from "../user/userAction";

function* requestCreatePatientInForm({ force }) {
  try {
    const { searchedPhone, phoneSearchResult } = yield select(
      state => state.customerService
    );
    const { info: patientInfo } = yield select(state => state.patientForm);
    const { urlRedirectAfterCreatePatient } = yield select(
      state => state.patientForm
    );
    const partnerid = yield select(state => state.totalData.partnerId);
    axios.defaults.headers.partnerid = partnerid;

    console.log("patientInfo :>> ", patientInfo);

    const keys = Object.keys(patientInfo);
    const postData = {};

    keys.map(key => {
      postData[key] = patientInfo[key]?.value;
      return "";
    });

    const getFullname = postData.fullname.split(" ");
    postData.surname = getFullname.slice(0, -1).join(" ");
    postData.name = getFullname.slice(-1).join(" ");

    if (postData.birthdate && postData.birthdate.includes("/")) {
      const [birthday, birthmonth, birthyear] =
        postData.birthdate.split("/") || [];
      postData.birthyear = birthyear;
      postData.birthmonth = birthmonth;
      postData.birthday = birthday;
      postData.birthdate = moment(
        `${postData.birthyear}-${postData.birthmonth}-${postData.birthday}`,
        "YYYY-MM-DD"
      )
        .utc(true)
        .toISOString();
    } else {
      postData.birthdate = moment(
        `${postData.birthyear}-${postData.birthmonth}-${postData.birthday}`,
        "YYYY-MM-DD"
      )
        .utc(true)
        .toISOString();
    }

    postData.force = force;

    const yearOld = moment().diff(postData.birthdate, "years", true);

    if (postData.relative_mobile) {
      if (yearOld < handleAge(partnerid)) {
        postData.mobile = postData.relative_mobile;
      }

      if (!postData.mobile) {
        postData.mobile = postData.relative_mobile;
      }
    }

    delete postData.birthmonth;
    delete postData.birthday;

    const token = yield select(state => state.user.info.token);
    const { cskhToken } = yield select(state => state.customerService);

    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";

    console.log("postData :>> ", postData);

    const response = yield client.insertPatient(
      { ...postData },
      {
        cskhtoken: cskhToken,
        partnerid,
        appId: "medpro",
        token,
        platform
      }
    );

    const { data } = response;
    if (data.isRecommended) {
      yield put({
        type: types.CHECK_PATIENT_BEFORE_CREATE,
        newPatient: data.patientForm,
        listOldPatient: data.recommendedPatientList
      });
      openToast(data.message, "error");
      history.push("/check-patient-before-create");
    } else {
      yield put({
        type: patientType.SELECTED_PATIENT_AFTER_CREATE,
        selectedPatient: data
      });

      yield put({
        type: types.CREATE_PATIENT_FORM_SUCCESS,
        info: data
      });
      yield put(getPatientList());
      yield put(getPatientsByPhoneCSKH(searchedPhone));
      const patientInsert = phoneSearchResult?.patients?.find(patient => {
        return data?.id === get(patient, "_id");
      });
      if (patientInsert) {
        data.secretKey = patientInsert.secretKey;
      }

      yield put(selectPatientInfoCSKH(data));

      openToast("Tạo mới hồ sơ thành công.");

      history.push(urlRedirectAfterCreatePatient || "/cham-soc-khach-hang");
    }
  } catch (error) {
    console.log(error);
    const errorCMND = get(
      error,
      "response.data.message[0].constraints.isNumberString",
      false
    );
    const errMobile = get(
      error,
      "response.data.message[0].constraints.isMobilePhone",
      false
    );
    const errorCMND2 = get(
      error,
      "response.data.message[0].constraints.CheckCMND",
      false
    );
    const trungHoSo = get(error, "response.data.message", false);
    if (error.response.data.statusCode === 400) {
      if (errMobile) openToast(errMobile, "error");
      if (errorCMND) openToast(errorCMND, "error");
      if (errorCMND2) openToast(errorCMND2, "error");
    }
    if (error.response.data.statusCode === 401) {
      yield put(logOutWithoutNoti());
      handleUnAuthorize();
    }
    if (error.response.data.statusCode === 409) {
      if (trungHoSo) openToast(trungHoSo, "error");
    }
    yield put({
      type: types.CREATE_PATIENT_FORM_FAILURE,
      error
    });
  }
}

function* watchRequestCreatePatientForm() {
  yield takeLatest(
    types.CREATE_PATIENT_FORM_REQUEST,
    requestCreatePatientInForm
  );
}

function* getPatientDetailWorker({ id, patientToken, callbackFn }) {
  try {
    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";
    const token = yield select(state => state.user.info.token);
    const { cskhToken } = yield select(state => state.customerService);

    const response = yield client.getPatientInfoToUpdate(
      { id },
      { token, platform, cskhtoken: patientToken || cskhToken }
    );

    yield put({
      type: types.DETAIL_PATIENT_FORM_SUCCESS,
      info: {
        ...response.data,
        dantoc_id: response.data.dantoc_id || "medpro_1"
      }
    });

    yield put({
      type: patientType.SELECTED_PATIENT_DETAIL,
      value: response.data
    });
    if (callbackFn) {
      callbackFn();
    }
  } catch (error) {
    openToast(error.response.data.message, "error");
    yield put({
      type: types.DETAIL_PATIENT_FORM_FAILURE,
      error
    });
  }
}

function* updatePatientlWorker() {
  try {
    const { cskhToken, searchedPhone } = yield select(
      state => state.customerService
    );
    const { info: patientInfo } = yield select(state => state.patientForm);
    const { phoneNumberVerify } = yield select(state => state.patient);
    const isUpdateFull = get(patientInfo, "isUpdateFull.value");
    const keys = Object.keys(patientInfo);

    const partnerid = yield select(state => state.totalData.partnerId);

    const postData = {};
    keys.map(key => {
      postData[key] = patientInfo[key].value;
      return "";
    });

    const getFullname = postData.fullname.split(" ");
    postData.surname = getFullname.slice(0, -1).join(" ");
    postData.name = getFullname.slice(-1).join(" ");
    postData.relative_name = postData.relative_name.toUpperCase();

    if (postData?.mobile?.includes("xx")) {
      postData.mobile = phoneNumberVerify;
    }

    const currentYear = Number(moment().format("YYYY"));
    const yearOld = currentYear - postData.birthyear;

    if (!postData.mobile && yearOld < 18) {
      postData.mobile = postData.relative_mobile;
    }

    if (postData.birthdate && postData.birthdate.includes("/")) {
      const [birthday, birthmonth, birthyear] =
        postData.birthdate.split("/") || [];
      postData.birthyear = birthyear;
      postData.birthmonth = birthmonth;
      postData.birthday = birthday;
      postData.birthdate = moment(
        `${postData.birthyear}-${postData.birthmonth}-${postData.birthday}`,
        "YYYY-MM-DD"
      )
        .utc(true)
        .toISOString();
    } else {
      postData.birthdate = moment(
        `${postData.birthyear}-${postData.birthmonth}-${postData.birthday}`,
        "YYYY-MM-DD"
      )
        .utc(true)
        .toISOString();
    }

    const token = yield select(state => state.user.info.token);
    const { urlRedirectAfterCreatePatient } = yield select(
      state => state.patientForm
    );
    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";

    let response;
    if (isUpdateFull) {
      response = yield client.updatePatientWithoutPatientCode(postData, {
        cskhtoken: cskhToken,
        partnerid,
        token,
        platform
      });
    } else {
      response = yield client.updatePatientWithPatientCode(postData, {
        cskhtoken: cskhToken,
        partnerid,
        token
      });
    }

    yield put({
      type: types.UPDATE_PATIENT_FORM_SUCCESS,
      info: response.data
    });

    yield put(getPatientList());

    yield put(getPatientsByPhoneCSKH(searchedPhone));

    openToast("Cập nhật thành công!");

    history.push(urlRedirectAfterCreatePatient);
  } catch (error) {
    const statusCode = get(error, "response.data.statusCode", 0);
    const errorCMND = get(
      error,
      "response.data.message[0].constraints.isNumberString",
      false
    );
    const errorCMND2 = get(
      error,
      "response.data.message[0].constraints.CheckCMND",
      false
    );

    if (statusCode === 400) {
      if (errorCMND) openToast(errorCMND, "error");
      else if (errorCMND2) openToast(errorCMND2, "error");
      else {
        const message = get(error, "response.data.message", "");
        openToast(message, "error");
      }
    } else if (statusCode === 401) {
      window.localStorage.removeItem("jwt");
      yield put(logOutWithoutNoti());
    } else {
      const message = get(error, "response.data.message", "");
      openToast(message, "error");
    }

    yield put({
      type: types.UPDATE_PATIENT_FORM_FAILURE,
      error
    });
  }
}

function* watchPatient() {
  yield all([
    takeLatest(types.DETAIL_PATIENT_FORM_REQUEST, getPatientDetailWorker),
    takeLatest(types.UPDATE_PATIENT_FORM_REQUEST, updatePatientlWorker)
  ]);
}

function* getInfoPatientCMNDConfirm(data) {
  try {
    const partnerid = yield select(state => state.totalData.partnerId);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";
    const response = yield client.findByCMND(
      {
        cccd: data?.payload?.cmnd,
        birthday: data?.payload?.birthdate,
        fullName: data?.payload?.fullname
      },
      {
        partnerid,
        platform
      }
    );
    yield put(
      patientFormActions.getInfoPatientCMNDConfirmSuccess(response.data)
    );
  } catch (error) {
    console.log(error);
    yield put(
      patientFormActions.getInfoPatientCMNDFailure(error.response.data)
    );
  }
}

function* watchPatientInfoPatientCMNDConfirm() {
  yield all([
    takeLatest(types.GET_INFO_PATIENT_CMND, getInfoPatientCMNDConfirm)
  ]);
}

export default function* root() {
  yield all([
    fork(watchRequestCreatePatientForm),
    fork(watchPatient),
    fork(watchPatientInfoPatientCMNDConfirm)
  ]);
}
