import { first, get, size } from "lodash";
import { MDBSpinner } from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import history from "~/history";
import { resetPayment } from "~/store/payment/paymentAction";
import {
  addScheduleRepayment,
  checkRepayment,
  reserveBookingAddonCare247,
  reserveBookingCare247
} from "~/store/totalData/actions";
import { getRouteFollowPartnerId } from "~/utils/func";
import Coupon from "./Coupon";
import CouponNoPayment from "./CouponNoPayment";
// import CardDownload from "./segment/CardDownload";
import { getMedproCare } from "~/store/booking/bookingAction";
import { BOOKING_STATUS } from "~/utils/constants";
import { client } from "~/utils/medproSDK";
import styles from "./style.module.scss";

class MedicalCouponCare extends Component {
  constructor(props) {
    super(props);
    this.state = {
      listAddon: [],
      listCare247: []
    };
  }

  componentDidMount() {
    const { paymentInformation, care247Available } = this.props;
    const bookingInfo = get(paymentInformation?.[0], "bookingInfo", "");
    const status = get(care247Available, "status");
    if (
      [BOOKING_STATUS.CHUA_THANH_TOAN, BOOKING_STATUS.DA_HUY].includes(status)
    ) {
      this.fetchServicesCare247(
        bookingInfo?.partnerId,
        bookingInfo?.transactionId
      );
    }
    if (status === 1) {
      this.fetchMoreTime(bookingInfo?.transactionId);
    }
  }

  fetchMoreTime = async transactionId => {
    try {
      const { data } = await client.getMedproCareAddonCskh({
        transactionId: transactionId
      });
      this.setState({
        listAddon: data
      });
    } catch (error) {
      console.log("error", error);
    }
  };

  fetchServicesCare247 = async (partnerId, transactionId) => {
    try {
      const { data } = await client.getMedproCareAfter({
        partnerId: partnerId,
        transactionId
      });
      this.setState({
        listCare247: data
      });
    } catch (error) {
      console.log("error", error);
    }
  };

  handleRePayment = () => {
    const { paymentInformation, IsAuthenticated } = this.props;

    this.props.checkRepayment(true);
    this.props.addScheduleRepayment(paymentInformation?.[0]?.bookingInfo);

    if (IsAuthenticated) {
      this.props.resetPayment();
      const route = getRouteFollowPartnerId(
        "/xac-nhan-thong-tin",
        paymentInformation?.[0]?.bookingInfo.partnerId
      );
      history.push(route);
    } else {
      history.push(
        `/${paymentInformation?.[0]?.bookingInfo.partnerId}/hinh-thuc-thanh-toan`
      );
    }
  };

  renderCoupon = () => {
    const { care247Available, togglePayment2Bill } = this.props;
    const { listCare247, listAddon } = this.state;
    const status = get(care247Available, "status");
    return (
      <div className={styles.item}>
        {[BOOKING_STATUS.CHUA_THANH_TOAN, BOOKING_STATUS.DA_HUY].includes(
          status
        ) ? (
          <>
            {care247Available.popupPayPending.status && (
              <div className={styles.notice}>
                <p>
                  <i className="fal fa-info-circle" />
                  Bạn chưa thanh toán dịch vụ đặt thêm. Vui lòng thanh toán để
                  được sử dụng dịch vụ
                </p>
                <div className={styles.btnMore} onClick={togglePayment2Bill}>
                  Xem thêm
                </div>
              </div>
            )}
            <CouponNoPayment
              listCare247={listCare247}
              care247Available={care247Available}
              onPaymentCare247={this.onPaymentCare247}
            />
          </>
        ) : (
          <Coupon
            listAddon={listAddon}
            care247Available={care247Available}
            onPaymentAddon={this.onPaymentAddon}
          />
        )}
      </div>
    );
  };

  onPaymentCare247 = checkedMedproCare => {
    const { paymentInformation } = this.props;
    const bookingInfo = first(paymentInformation).bookingInfo;
    const urlBookingDetail =
      size(paymentInformation) > 1
        ? `transactions/${bookingInfo.transactionId}`
        : `booking/${bookingInfo.bookingCode}`;
    const payload = {
      transactionId: bookingInfo.transactionId,
      redirectUrl: `${window.location.origin}/${urlBookingDetail}`,
      medproCareServiceIds: [checkedMedproCare]
    };
    this.props.reserveBookingCare247(payload);
  };

  onPaymentAddon = timeId => {
    const { paymentInformation } = this.props;
    const bookingInfo = first(paymentInformation).bookingInfo;
    const urlBookingDetail =
      size(paymentInformation) > 1
        ? `transactions/${bookingInfo.transactionId}`
        : `booking/${bookingInfo.bookingCode}`;
    const payload = {
      transactionId: bookingInfo.transactionId,
      redirectUrl: `${window.location.origin}/${urlBookingDetail}`,
      addonId: timeId
    };
    this.props.reserveBookingAddonCare247(payload);
  };

  render() {
    const { loading = true } = this.props;

    if (loading) {
      return (
        <div className={styles.loading}>
          <MDBSpinner big crazy tag="div" />
        </div>
      );
    }

    const renderCoupon = this.renderCoupon();

    return (
      <React.Fragment>
        <div className={styles.listCoupon}>{renderCoupon}</div>
      </React.Fragment>
    );
  }
}
const mapStateToProps = state => {
  const {
    global,
    user: { IsAuthenticated },
    totalData: { paymentInformation, loading, reviewBooking, partnerId },
    booking: { medproCare }
  } = state;
  return {
    global,
    IsAuthenticated,
    paymentInformation,
    loading,
    reviewBooking,
    partnerId,
    medproCare
  };
};
const mergeProps = (stateProps, dispatchProps, ownProps) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps
});
export default connect(
  mapStateToProps,
  {
    checkRepayment,
    addScheduleRepayment,
    resetPayment,
    getMedproCare,
    reserveBookingCare247,
    reserveBookingAddonCare247
  },
  mergeProps
)(MedicalCouponCare);
