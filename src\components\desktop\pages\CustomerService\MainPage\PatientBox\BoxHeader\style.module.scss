.box_header {
    width: 100%;
    height: fit-content;
    background-image: linear-gradient(90deg, #0352cc 0%, #0b7df1 100%);
    display: flex;
    border: 1px solid black;
    box-shadow: 1px 1px 5px gray;
    border-radius: 5px;
    button {
        margin: 1%;
        width: 15%;
        // background-image: linear-gradient(90deg, #11ece1 0%, #0cd5a9 100%);
        border: none;
        box-shadow: 1px 1px 5px gray;
        border-radius: 10px;
    }
    button:hover {
        border: 1px solid black;
    }
}

.box_header_title {
    // padding: 10px;
    width: 80%;
    text-align: center;
    align-content: center;
    align-items: center;
}
.title {
    color: white;
    font-size: 1.3vw;
    margin: 10px;
    font-weight: bold;
}

.button_text {
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 15px;
}