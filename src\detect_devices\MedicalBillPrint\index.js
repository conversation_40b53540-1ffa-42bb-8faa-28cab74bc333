import React, { Component } from "react";
import { connect } from "react-redux";
import Loadable from "react-loadable";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import { Redirect } from "react-router-dom";
import qs from "qs";
import { partnerInfo } from "~/configs/partnerDetails";
import { get, find } from "lodash";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const MedicalBillPrintPageMobile = Loadable({
  loader: () =>
    import(/* mobile */ "~/components/mobile/pages/MedicalBillPrint"),
  loading: LoadableLoading
});
const MedicalBillPrintPageDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/mobile/pages/MedicalBillPrint"),
  loading: LoadableLoading
});

class DetectMedicalBillPrint extends Component {
  componentDidMount = () => {
    if (
      qs.parse(this.props.location.search, { ignoreQueryPrefix: true }).print
    ) {
      window.print();
    }
  };

  render() {
    const { device, IsAuthenticated } = this.props;
    // console.log("bill");
    if (!IsAuthenticated) {
      return <Redirect to="/" />;
    }
    return (
      <React.Fragment>
        {device === "mobile" ? (
          <MedicalBillPrintPageMobile {...this.props} />
        ) : (
          <MedicalBillPrintPageDesktop {...this.props} />
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { IsAuthenticated }
  } = state;
  return {
    device: type,
    IsAuthenticated
  };
};

const MedicalBillPrintHelmet = withTitle({
  component: DetectMedicalBillPrint,
  title: `${hospitalName.value} | In phiếu khám bệnh`
});

export default connect(mapStateToProps)(MedicalBillPrintHelmet);
