import React, { Component } from "react";
import { MD<PERSON><PERSON>r, MDBRow, MDBCol } from "mdbreact";
import styles from "./style.module.scss";
import { data } from "./utils/data";
import { uniqueId } from "lodash-es";

class StatisMedproClinic extends Component {
  render() {
    return (
      <MDBContainer className={styles.Container}>
        <div className={styles.Label}>
          <p>Thông số thống kê</p>
        </div>
        <MDBRow className={styles.rowStatis}>
          <MDBCol xl={12} className={styles.colStatis}>
            <ul className={styles.listCard}>
              {data.map(item => {
                return (
                  <li key={uniqueId()}>
                    <div className={styles.card}>
                      <figure className={styles.img}>
                        <img src={item.img} alt="" />
                      </figure>
                      <p>
                        <span className={styles.statis}>{item.statis}</span>
                        <span
                          className={styles.subtitle}
                          dangerouslySetInnerHTML={{ __html: item.subtitle }}
                        />
                      </p>
                    </div>
                  </li>
                );
              })}
            </ul>
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    );
  }
}
export default StatisMedproClinic;
