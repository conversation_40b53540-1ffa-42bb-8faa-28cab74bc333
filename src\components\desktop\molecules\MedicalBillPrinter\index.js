import cx from "classnames";
import { first, get, isEmpty, isNull, size } from "lodash";
import { MDBSpinner } from "mdbreact";
import moment from "moment";
import React, { Component } from "react";
import Barcode from "react-barcode";
import { connect } from "react-redux";
import PKHBtn from "~/components/common/atoms/Button";
import { getInfoPatient } from "~/utils/flowRouting";
import styles from "./style.module.scss";

const QRCode = require("qrcode.react");

class MedicalBillPrinter extends Component {
  constructor(props) {
    super(props);
    this.state = {
      shareToPay: true
    };
  }

  onTitle_STT = ({ partnerId }) => {
    switch (partnerId) {
      case "choray":
        return "Số thứ tự phòng khám"; // đang làm

      default:
        return "Số thứ tự tiếp nhận";
    }
  };

  renderMedicalBill = () => {
    const { type } = this.props;
    const bill = first(this.props.paymentInformation);
    let bookingTimeBig;
    const partner = get(bill, "bookingInfo.partner");
    const totalPaymentMessage = get(
      bill,
      "bookingInfo.totalPaymentMessage",
      ""
    );
    const bookingCode = get(bill, "bookingInfo.bookingCode");
    const partnerId = get(bill, "bookingInfo.partnerId");
    const date = get(bill, "bookingInfo.date", "");

    const dateStr = get(bill, "bookingInfo.dateStr", "");
    const timeStr = get(bill, "bookingInfo.timeStr", "");
    const waitingConfirmDate = get(bill, "bookingInfo.waitingConfirmDate");
    const dates = moment(date).format("DD/MM/YYYY");
    const time = moment(date).format("HH:mm");
    if (date) {
      bookingTimeBig = moment(date).format("HH:mm");
    } else {
      bookingTimeBig = "";
    }

    const bookingDate = dateStr || date ? dates : waitingConfirmDate;
    const bookingTime = timeStr || date ? time : waitingConfirmDate;

    const serviceName = get(bill, "bookingInfo.service.name", "");
    const addonServices = get(bill, "bookingInfo.addonServices", "");
    const subjectName = get(bill, "bookingInfo.subject.name", "");
    const roomName = get(bill, "bookingInfo.room.name", "");
    const doctorName = get(bill, "bookingInfo.doctor.name", "");
    const patient = get(bill, "patientInfo", {});
    const { fullName, birthdate } = getInfoPatient(patient);
    const code = get(bill, "bookingInfo.displayCodeBooking", "");
    const insuranceCode = get(bill, "bookingInfo.insuranceCode");
    const price = get(bill, "bookingInfo.service.priceText", "");
    const description = get(bill, "checkInRoom.description", "");
    const sectionName = get(bill, "bookingInfo.section.name", "");
    const sectionRoomName = get(bill, "bookingInfo.room.sectionName", "");
    const patientCode = get(bill, "bookingInfo.patient.patientCode", "");

    const status = get(bill, "bookingInfo.status", 1);
    const bookingNote = get(bill, "bookingInfo.bookingNote", null);
    const bookingNumber = get(bill, "bookingInfo.sequenceNumber", "");
    const titleBookingTime =
      partnerId === "trungvuong" ? "Giờ tiếp nhận dự kiến" : "Giờ khám dự kiến";
    const canRepayment = get(bill, "bookingInfo.canRepayment", true);
    const awaitMessage = get(bill, "bookingInfo.awaitMessage", "");
    const classNumber = styles.number;
    const classGray = styles.gray;
    const classCX = cx({
      [classNumber]: true,
      [classGray]: status === -2
    });
    const isBinhThanh = partnerId === "binhthanhhcm";
    const checkCode = e => {
      if (!e || e.value === "" || isNull(e.value) || isEmpty(e.value)) {
        return "d-none";
      } else {
        return "";
      }
    };
    const typeCode = e => {
      switch (e?.type) {
        case "barcode":
          return (
            <React.Fragment>
              <p>{e.title}</p>
              <Barcode
                value={e.value}
                format="CODE128"
                height={50}
                width={1}
                fontSize={14}
              />
            </React.Fragment>
          );
        case "qrcode":
          return (
            <React.Fragment>
              <p>{e.title}</p>
              <QRCode fgColor="#000000" size={90} value={e.value} />
            </React.Fragment>
          );
        default:
          return null;
      }
    };
    const CustomLine = ({ top, bottom, normal = true }) => {
      return (
        <div
          className={cx(
            styles.line,
            top && styles.top,
            bottom && styles.bottom,
            normal && styles.normal
          )}
        >
          <div className={styles.circleLeft} />
          <div className={styles.dashed} />
          <div className={styles.circleRight} />
        </div>
      );
    };

    const onShowBox = bookingInfo => {
      return [
        bookingInfo?.room?.name,
        bookingInfo?.subject?.name,
        bookingInfo?.room?.sectionName,
        bookingInfo?.section?.name
      ].some(item => !!item);
    };
    return (
      <div className={styles.print}>
        <CustomLine top />
        {/* Thông tin bệnh viện: logo, tên, địa chỉ */}
        <div className={styles.title_hospital}>
          {/* <img src={getLogoInBill(partnerId)} alt="" /> */}
          <p className={styles.sub_title}>PHIẾU KHÁM BỆNH</p>
          <p className={styles.nameParner}>{partner?.name}</p>
          <p className={styles.addressParner}>{partner?.address}</p>
          {isBinhThanh && (
            <div className={cx(styles.bar_code, checkCode(code))}>
              {typeCode(code)}
            </div>
          )}
        </div>

        <div className={cx(styles.info, !code && styles.info1)}>
          {/* render lại QRcode hay BarCode theo phiếu BE trả về */}
          {!isBinhThanh && (
            <div className={cx(styles.bar_code, checkCode(code))}>
              {typeCode(code)}
            </div>
          )}
          {/* Sô thứ tự, thời gian dự kiến hoặc đợi */}
          {status !== 0 ? (
            <React.Fragment>
              {awaitMessage !== "" ? (
                <div className={styles.awaitMessage}>
                  <p>{awaitMessage}</p>
                  <MDBSpinner small yellow className={styles.spin} />
                </div>
              ) : (
                <div
                  className={cx(
                    styles.numbericalOrderNext,
                    isBinhThanh && styles.binhthanhStyles
                  )}
                >
                  {bookingTimeBig && (
                    <div className={classCX}>
                      <p className={cx(classCX)}>{titleBookingTime}</p>
                      <p
                        className={
                          (cx(styles.txtNum), bookingTimeBig && styles.txtNum1)
                        }
                      >
                        {bookingTimeBig}
                      </p>
                    </div>
                  )}
                  {bookingNumber && (
                    <div className={classCX}>
                      <p className={cx(classCX)}>Số thứ tự hiện tại</p>
                      <p
                        className={cx(
                          styles.txtNum,
                          bookingNumber && styles.txtNum1
                        )}
                      >
                        {bookingNumber}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </React.Fragment>
          ) : null}
        </div>

        {/* nếu phiếu chưa được thanh toán hoặc thanh toán hộ thì xuất hiện thanh toán lại */}
        {(status === 0 || status === 6) &&
          canRepayment &&
          type !== "UserForCSKH" &&
          type !== "CSKH" && (
            <div className={styles.thanhToanLai}>
              <p className={styles.noti_unpayment}>
                Vui lòng THANH TOÁN để hoàn tất <br /> đăng ký phiếu khám bệnh
              </p>
              <PKHBtn
                outline
                className={cx(styles.button)}
                onClick={this.handleRePayment}
              >
                Thanh toán lại
              </PKHBtn>
            </div>
          )}

        {/* text: số tiền bệnh viện thanh toán giúp */}
        {(status === 2 || status === 1 || status === -2) &&
          totalPaymentMessage && (
            <div className={styles.totalPaymentMessage}>
              <span>{totalPaymentMessage}</span>
            </div>
          )}

        <CustomLine />
        {/* phần thông tin phiếu phòng khám và chuyên khoa */}
        <div style={{ margin: "0 -8px" }}>
          {onShowBox(bill?.bookingInfo) && (
            <div className={styles.info_room_subjectName}>
              <ul>
                {roomName && (
                  <li>
                    <span className={styles.column_left}>Phòng khám:</span>
                    <b className={styles.value}> {roomName}</b>
                  </li>
                )}
                {(sectionName || sectionRoomName) && (
                  <li>
                    <span className={styles.column_left}>Khu vực:</span>
                    <b className={styles.value}>
                      {sectionName || sectionRoomName}
                    </b>
                  </li>
                )}
                {subjectName && (
                  <li>
                    <span className={styles.column_left}>Chuyên khoa:</span>
                    <b className={styles.value}> {subjectName}</b>
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
        {/* phần thông tin phiếu */}
        <div className={styles.list_detail}>
          <ul>
            {(code.type !== "barcode" && !code.value) || !bookingCode ? null : (
              <li>
                <span className={styles.column_left}>Mã phiếu: </span>
                {bookingCode}
              </li>
            )}
            {doctorName && (
              <li>
                <span className={styles.column_left}>Bác sĩ:</span>
                <b>{doctorName}</b>
              </li>
            )}
            {serviceName && (
              <li>
                <span className={styles.column_left}>Dịch vụ:</span>
                <b>{serviceName}</b>
              </li>
            )}

            <li>
              <span className={styles.column_left}>Hình thức khám:</span>
              <b> {insuranceCode ? " Có BHYT" : " Không có BHYT"}</b>
            </li>
            {(sectionName || sectionRoomName) && (
              <li>
                <span className={styles.column_left}>Khu vực:</span>
                <b className={styles.value}>
                  {" "}
                  {sectionName || sectionRoomName}
                </b>
              </li>
            )}

            {/* <li>
              <b className={cx(styles.title)}>Ngày khám:</b>
              <b className={cx(styles.value, status === 1 && styles.green)}>
                 
              </b>
            </li> */}
            {awaitMessage === "" && (
              <li>
                <span className={cx(styles.column_left)}>Thời gian khám:</span>
                <b
                  className={cx(
                    styles.value,
                    status === 1 ? styles.green : "",
                    isBinhThanh ? styles.textSpecial : ""
                  )}
                >
                  {!isBinhThanh && <span>{bookingTime} -</span>} {bookingDate}
                </b>
              </li>
            )}

            <li>
              <span className={styles.column_left}>Phí khám:</span>
              <b>{price}</b>
            </li>

            {partnerId === "bvsingapore" && size(addonServices) > 0 && (
              <>
                <CustomLine />
                <>
                  <p>Các dịch vụ khác: </p>
                  {addonServices.map((v, i) => {
                    return (
                      <li key={i}>
                        <span className={styles.column_left}>{v.name}:</span>
                        <span className={styles.column_right}>
                          {v.priceText}
                        </span>
                      </li>
                    );
                  })}
                </>
              </>
            )}
            <CustomLine />

            {/* phần thông tin bệnh nhân */}
            <li>
              <span className={styles.column_left}>Bệnh nhân:</span>
              <b>{fullName}</b>
            </li>

            <li>
              <span className={styles.column_left}>Năm sinh:</span>
              <b>{birthdate}</b>
            </li>

            {patientCode && (
              <li>
                <span className={styles.column_left}>Mã bệnh nhân:</span>
                <b>{patientCode}</b>
              </li>
            )}
          </ul>
        </div>

        <CustomLine />
        {/* Phần lưu ý cuối */}
        {![-2, 0, 6].includes(status) ? (
          <>
            {(bookingNote || description) && (
              <b className={styles.attention}>Lưu ý:</b>
            )}
            {description && (
              <div className={styles.note}>
                <b
                  dangerouslySetInnerHTML={{
                    __html: description
                  }}
                />
              </div>
            )}
            {bookingNote && (
              <b
                dangerouslySetInnerHTML={{
                  __html: bookingNote
                }}
              />
            )}
          </>
        ) : null}
        <div className={styles.organization}>
          <p>
            Được phát triển bởi
            <span> Medpro </span>
          </p>
        </div>
        <CustomLine bottom />
      </div>
    );
  };

  renderBanner = () => {
    return (
      <div className={styles.banner}>
        <p className={styles.title}>Quét QR để tải app !</p>

        <div className={styles.group}>
          <div className={styles.QR}>
            <QRCode
              fgColor="#000000"
              size={90}
              value="https://medpro.vn/getapp"
            />
          </div>
          <div className={styles.linkDown}>
            <p>
              Quý khách vui lòng cài đặt ứng dụng để xem chi tiết hướng dẫn và
              quản lý hồ sơ khám bệnh.
            </p>
          </div>
        </div>
      </div>
    );
  };

  render() {
    return (
      <div className={styles.printercskh}>
        {this.renderBanner()}
        {this.renderMedicalBill()}
      </div>
    );
  }
}
const mapStateToProps = state => {
  const {
    global,
    user: { IsAuthenticated },
    totalData: { paymentInformation, loading, reviewBooking }
  } = state;
  return {
    global,
    IsAuthenticated,
    paymentInformation,
    loading,
    reviewBooking
  };
};
const mergeProps = (stateProps, dispatchProps, ownProps) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps
});
export default connect(mapStateToProps, {}, mergeProps)(MedicalBillPrinter);
