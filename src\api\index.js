import axios from "axios";

const accessToken = window.localStorage.getItem("jwt") || "";
axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
// if (accessToken !== "") {
//   axios.defaults.headers.common.Authorization = `Bearer ${JSON.parse(
//     accessToken
//   )}`;
// }

export const getHttpRequest = async url => axios.get(url);
export const postHttpRequest = async url => axios.post(url);
export const deleteHttpRequest = async url => axios.delete(url);

export const getHttpRequestAsync = async (url, config = {}) =>
  axios.get(url, config);
export const getHttpRequestParamsAsync = async (url, params, config = {}) =>
  axios.get(url, { params: { ...params } }, config);
// export const getHttpRequest = (url, config = {}) => axios.get(url, config)

export const deleteHttpRequestAsync = async (url, config = {}) =>
  axios.delete(url, config);
export const deleteHttpRequestParamsAsync = async (url, params, config = {}) =>
  axios.delete(url, { params: { ...params } }, config);
// export const deleteHttpRequest = (url, config = {}) =>
//   axios.delete(url, config);

export const postHttpRequestAsync = async (url, data = {}, config = {}) =>
  axios.post(url, data, config);
// export const postHttpRequest = (url, data = {}, config = {}) =>
//   axios.post(url, data, config);

export const putHttpRequestAsync = async (url, data = {}, config = {}) =>
  axios.put(url, data, config);
export const putHttpRequest = (url, data = {}, config = {}) =>
  axios.put(url, data, config);

export const patchHttpRequestAsync = async (url, data = {}, config = {}) =>
  axios.patch(url, data, config);
export const patchHttpRequest = (url, data = {}, config = {}) =>
  axios.patch(url, data, config);
