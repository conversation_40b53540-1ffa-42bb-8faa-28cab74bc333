/* eslint-disable no-unused-expressions */
import { find, get } from "lodash";
import moment from "moment";
import { createForm, createForm<PERSON>ield } from "rc-form";
import React, { Component } from "react";
import InputMask from "react-input-mask";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { Redirect, withRouter } from "react-router-dom";
import Alert from "~/components/common/atoms/Alert";
import PKHBtn from "~/components/common/atoms/Button";
import Modal from "~/components/common/molecules/Modal";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import AddInfoXNC from "~/components/desktop/molecules/AddInfoXNC";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import history from "~/history";
import * as p from "~/store/patient/patientAction";
import * as pf from "~/store/patientForm/patientFormAction";
import * as t from "~/store/totalData/actions";
import * as cts from "~/utils/constants";
import * as fnc from "~/utils/func";
import { client } from "~/utils/medproSDK";
import styles from "./style.module.scss";
/* eslint-disable react/jsx-indent */
const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const ChoosePatientDesktop = Loadable({
  loader: () => import("~/components/desktop/pages/ChoosePatient"),
  loading: LoadableLoading
});

class DetectChoosePatient extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowModalCheckInsurance: false,
      isShowModalOverAge: false,
      isShowModalAge: false,
      isShowModalSubject: false,
      isShowModalXNC: false,
      quocGiaXuatCanh: []
    };
  }

  async componentDidMount() {
    const {
      handleGetPatientList,
      resetModalChoosePatient,
      resetModalConfirmPatient,
      patientExam,
      selectPatientFromData,
      isDirectionBack,
      patient,
      resetErrorCheckInsurance,
      serviceType,
      CSKH
    } = this.props;

    resetModalChoosePatient();
    resetModalConfirmPatient();
    resetErrorCheckInsurance();
    // setInsuranceChoice("");
    // (!patient.data || patient.data.length < 1) && handleGetPatientList();
    handleGetPatientList();
    const id_selectFromCS = get(CSKH, "selectedPatient.id", 0);
    console.log("id_selectFromCS :>> ", id_selectFromCS);

    const { scanInsuranceSuccess } = patient;
    if (scanInsuranceSuccess) {
      this.methods.handleContinueAction();
    } else {
      if (
        patientExam &&
        (serviceType === cts.SERVICE_TYPE_INSURANCE || serviceType === "BOTH")
      ) {
        // console.log("luồng tái khám");
        this.props.selectPatientReExam();
      } else {
        // console.log("luồng khác");
        this.toggleModalCheckInsurance(false);
      }
    }

    if (patientExam !== null) {
      selectPatientFromData(patientExam);
      if (isDirectionBack) {
        this.methods.handleGoBack();
      }
    }

    const { selectedFeature } = this.props;
    if (selectedFeature.type === cts.FEATURE_TYPES.bookingXNC) {
      try {
        const response = await client.getCountrySupportBookingXnc();

        this.setState({ quocGiaXuatCanh: response.data });
      } catch (error) {
        console.log(error);
      }
    }

    this.props.selectPatientFromList(id_selectFromCS);

    if (patient.data.length > 3) {
      const height = window.innerHeight;
      if (height > 350) {
        setTimeout(() => {
          var elmnt = document.getElementById("activeDetailsPatient");
          elmnt && elmnt?.scrollIntoView({ behavior: "smooth" });
        }, 300);
      }
    }
    this.props.changeUrlRedirectAfterCreatePatient("/chon-ho-so");
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { patient, patientExam } = this.props;

    if (
      patient.formCheckInsurance !== nextProps.patient.formCheckInsurance &&
      patientExam
    ) {
      this.toggleModalCheckInsurance(true);
    }
  }

  componentDidUpdate = prevProps => {
    const { isShowModalCheckInsurance } = this.state;
    const { patient } = this.props;
    const errorCheckInsurance = get(patient, "errorCheckInsurance");
    if (errorCheckInsurance && !isShowModalCheckInsurance) {
      this.toggleModalCheckInsurance(true);
      this.props.resetErrorCheckInsurance();
    }
    if (patient.data !== prevProps.patient.data) {
      this.props.selectPatientFromList(patient.selectedPatientId);
    }
  };

  componentWillUnmount() {
    this.props.resetModalChoosePatient();
    this.props.resetStatusScanQrCodeInsurance();
    // this.toggleModalCheckInsurance(false);
  }

  toggleModalCheckXNC = () => {
    console.log("this.state.isShowModalXNC", this.state.isShowModalXNC);
    this.setState({
      isShowModalXNC: !this.state.isShowModalXNC
    });
  };

  methods = {
    handleContinueAction: () => {
      const {
        serviceType,
        partnerId,
        patient: { selectedPatient },
        requiredCheckInsurance,
        selectedFeature
      } = this.props;

      const routePush = url => {
        const route = fnc.getRouteFollowPartnerId(url, partnerId);
        history.push(route);
      };

      switch (selectedFeature.type) {
        case cts.FEATURE_TYPES.examresult: {
          // if (cts.listAppId.includes(partnerId)) {
          //   history.push("/chon-benh-vien");
          // } else {
          const url_examresult = "/tra-ket-qua-kham-benh";
          routePush(url_examresult);
          // }
          break;
        }
        case cts.FEATURE_TYPES.bookingVaccine: {
          const url_bookingVaccine = "/chon-dich-vu-tiem-chung";
          routePush(url_bookingVaccine);
          break;
        }
        // xuất nhập cảnh
        case cts.FEATURE_TYPES.bookingXNC: {
          this.toggleModalCheckXNC();
          break;
        }

        default: {
          if (selectedPatient.warningMessage) {
            this.setState({
              isShowModalOverAge: true
            });
          } else {
            // kiểm tra có chọn BHYT
            if (serviceType === cts.SERVICE_TYPE_INSURANCE) {
              // kiểm tra có bắt buộc BHYT hay không ?
              if (partnerId !== "choray" && requiredCheckInsurance) {
                // hiển thị modals
                this.checkInsurance();
              } else {
                // đi bước kế tiếp
                this.onNextStep();
              }
            } else {
              this.onNextStep();
            }
          }
          break;
        }
      }
    },
    handleCreateNewPatient: () => {
      const { infoUser } = this.props;

      this.props.history.push(
        infoUser?.isCS === "true"
          ? "/cham-soc-khach-hang/tao-moi-ho-so-benh-nhan"
          : "/tao-moi-ho-so"
      );
    },
    handleGoBack: () => {
      const {
        partnerId,
        isScheduleWithoutDate,
        goBackStep,
        resetSchedule
      } = this.props;
      if (isScheduleWithoutDate) {
        resetSchedule();
        goBackStep("calendar");
      } else {
        const route = fnc.getRouteFollowPartnerId("/chon-lich-kham", partnerId);
        this.props.history.push(route);
      }
    }
  };

  hideAlertInfo = () => {
    this.props.onHideAlertInfo();
  };

  checkInsurance = () => {
    const { patient } = this.props;
    const insuranceCode = get(patient, "selectedPatient.insuranceCode");
    const birthdate = get(patient, "selectedPatient.birthdate");
    if (!!insuranceCode && moment(birthdate, "DD/MM/YYYY").isValid()) {
      this.props.checkInsuranceOfPatient(
        this.onNextStep,
        cts.CHECK_WITH_INSURANCE_CODE_EXIST
      );
    } else {
      this.props.handleToggleModalChoosePatient();
      this.toggleModalCheckInsurance(true);
    }
  };

  onNextStep = () => {
    const { partnerId, serviceType } = this.props;
    let route = "";
    if (serviceType === "NO_INSURANCE_ONLY") {
      route = fnc.getRouteFollowPartnerId("/xac-nhan-thong-tin", partnerId);
    } else {
      if (partnerId === "leloi") {
        route = fnc.getRouteFollowPartnerId(
          "/huong-dan-bao-hiem-y-te-dung-tuyen",
          partnerId
        );
      } else {
        route = fnc.getRouteFollowPartnerId("/xac-nhan-thong-tin", partnerId);
      }
    }
    this.props.history.push(route);
  };

  renderOverMaxModal = () => {
    const { toggleModalNotification, isOverMaxModal } = this.props;
    return (
      <Modal
        title="Thông báo!"
        modal={isOverMaxModal}
        iconTitle={<i className="fal fa-bell" />}
        children="Bạn chỉ được phép tạo tối đa 10 hồ sơ."
        centered
        className="centered"
        toggle={toggleModalNotification}
      />
    );
  };

  renderDeletePatientModal = () => {
    const {
      patient: { showModalConfirm },
      handleDeletePatient,
      handleToggleModalConfirm
    } = this.props;
    return (
      <Modal
        backdropClassName="backdrop_centered"
        modal={showModalConfirm}
        title="Thông báo"
        iconTitle={<i className="fal fa-bell" />}
        children={
          <span style={{ color: "red" }}>
            Bạn có chắc muốn xóa bệnh nhân này!
          </span>
        }
        centered
        className="centered"
        footer
        footerConfirm="true"
        cancelText="Đóng"
        okText="Xác nhận"
        onCancel={handleToggleModalConfirm}
        onOk={handleDeletePatient}
        toggle={handleToggleModalConfirm}
      />
    );
  };

  renderCheckInsuranceModal = () => {
    const { patientExam, partnerId } = this.props;
    const stepOne = fnc.getStepFollowTreeMap("service", partnerId);
    return (
      <Modal
        modal={this.state.isShowModalCheckInsurance}
        title="Cập nhật thông tin"
        iconTitle={<i className="fal fa-bell" />}
        children={this.renderBodyModalCheckInsurance()}
        centered
        noBackDrop={patientExam && false}
        className="centered"
        toggle={() => {
          patientExam
            ? this.props.history.push(stepOne)
            : this.toggleModalCheckInsurance(false);
        }}
      />
    );
  };

  renderBodyModalCheckInsurance = () => {
    const {
      form: { getFieldProps, getFieldError },
      serviceTypeDefault,
      partnerId,
      extraConfig
    } = this.props;

    const isMobile = fnc.checkDevice();

    const fieldFullName = getFieldError("fullName");
    const fullNameMessage = fieldFullName ? fieldFullName.join(", ") : null;

    const fieldInsuranceCode = getFieldError("code");
    const insuranceCodeMessage = fieldInsuranceCode
      ? fieldInsuranceCode.join(", ")
      : null;
    const fieldBirhtday = getFieldError("birthday");
    const birthdayMessage = fieldBirhtday ? fieldBirhtday.join(", ") : null;

    // const stepOne = fnc.getStepFollowTreeMap("service", partnerId);

    const quet_bhyt_btn = get(extraConfig, "quet_bhyt_btn", false);

    return (
      <div>
        <span>Vui lòng nhập thông tin y tế: </span>
        <div className={styles.form_insurance}>
          <div className={styles.form_group}>
            <label htmlFor="label_fullname">
              Họ và tên<sup>*</sup>
            </label>
            <input
              className="form-control"
              {...getFieldProps("fullName", {
                trigger: ["onChange"],
                rules: [
                  {
                    required: true,
                    message: "Vui lòng nhập họ tên"
                  }
                ]
              })}
              type="text"
              disabled
            />
          </div>
          {fullNameMessage && (
            <span className={styles.red}>{fullNameMessage}</span>
          )}
          <div className={styles.form_group}>
            <label htmlFor="label_code">
              Mã BHYT<sup>*</sup>
            </label>
            <input
              className="form-control"
              {...getFieldProps("code", {
                trigger: ["onChange"],
                rules: [
                  {
                    required: true,
                    message: "Vui lòng nhập mã số BHYT"
                  }
                ]
              })}
              placeholder="CHxxxxxxxxxxxxxxxx"
              type="text"
            />
          </div>
          {insuranceCodeMessage && (
            <span className={styles.red}>{insuranceCodeMessage}</span>
          )}
          <React.Fragment>
            <div className={styles.form_group}>
              <label htmlFor="label_birthday">
                Ngày tháng năm sinh<sup>*</sup>
              </label>
              <InputMask
                className="form-control"
                {...getFieldProps("birthday", {
                  rules: [{ validator: this.validatorBirthday }]
                })}
                mask="99/99/9999"
                type="text"
              />
            </div>
            {birthdayMessage && (
              <span className={styles.red}>{birthdayMessage}</span>
            )}
          </React.Fragment>
        </div>

        {isMobile && quet_bhyt_btn && (
          <p
            className={styles.url_scan_qr_code}
            onClick={() => this.props.history.push("/check-bhyt")}
          >
            Quét mã BHYT
          </p>
        )}

        <p style={{ fontSize: "13px", fontStyle: "italic" }}>
          ***Nếu bạn không có BHYT vui lòng chọn{" "}
          <strong>"Tôi không có thẻ BHYT"</strong>.
        </p>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <PKHBtn
            onClick={() => {
              if (serviceTypeDefault === cts.SERVICE_TYPE_INSURANCE) {
                history.push(
                  fnc.routerByPartnerId(cts.urlPage.ChonDichVu, partnerId)
                );
                this.props.setActionBHYT("SelectedNoBHYT");
              } else {
                this.toggleModalCheckInsurance(false, "noBHYT");
                this.props.changeServiceType(cts.SERVICE_TYPE_NO_INSURANCE);
                this.onNextStep();
              }
              this.props.setGoBack();
            }}
          >
            Tôi không có thẻ BHYT
          </PKHBtn>
          <PKHBtn
            create="create"
            pd35="pd35"
            onClick={this.handleCheckInsurance}
          >
            Tiếp tục
          </PKHBtn>
        </div>
        <div id="reader" className={styles.screen_scan} />
      </div>
    );
  };

  toggleModalCheckInsurance = (status, type) => {
    this.setState({
      isShowModalCheckInsurance: status
    });
    if (!status && type !== "noBHYT") {
      this.props.selectPatientFromList(0);
      this.props.resetErrorCheckInsurance();
    }
    this.props.resetStatusScanQrCodeInsurance();
  };

  validatorBirthday = (rule, value, cb) => {
    if (!moment(value, "DD/MM/YYYY").isValid()) {
      cb("Vui lòng nhập đúng định dạng ngày tháng");
    } else {
      cb();
    }
  };

  handleCheckInsurance = () => {
    this.props.form.validateFields((error, value) => {
      if (!error) {
        this.props.checkInsuranceOfPatient(this.onNextStep);
      }
    });
  };

  toggleModalOverAge = () => {
    this.setState({
      isShowModalOverAge: !this.state.isShowModalOverAge
    });
  };

  renderOverAgeModal = () => {
    const {
      patient: { selectedPatient }
    } = this.props;

    return (
      <Modal
        modal={this.state.isShowModalOverAge}
        title="Thông báo"
        iconTitle={<i className="fal fa-bell" />}
        children={selectedPatient.warningMessage}
        centered
        className="centered"
        toggle={this.toggleModalOverAge}
      />
    );
  };

  renderXNCModal = () => {
    return (
      <AddInfoXNC
        open={this.state.isShowModalXNC}
        toggle={this.toggleModalCheckXNC}
        quocGiaXuatCanh={this.state.quocGiaXuatCanh}
        postInfoXNC={this.props.postInfoXNC}
        selectedPatientId={this.props.patient.selectedPatientId}
      />
    );
  };

  render() {
    const {
      patient: { isAlertInfo, alertMsg },
      bookingTree,
      patientExam,
      serviceType
    } = this.props;

    if (
      patientExam !== null &&
      !(serviceType === cts.SERVICE_TYPE_INSURANCE || serviceType === "BOTH")
    ) {
      return <Redirect to="/xac-nhan-thong-tin" />;
    }

    if (bookingTree === null) {
      return <Redirect to="/" />;
    }

    return (
      <React.Fragment>
        <ChoosePatientDesktop {...this.props} {...this.methods} />

        <Alert
          isModal={isAlertInfo}
          message={alertMsg}
          toggleAlert={this.hideAlertInfo}
        />
        {this.renderOverMaxModal()}
        {this.renderDeletePatientModal()}
        {this.renderCheckInsuranceModal()}
        {this.renderOverAgeModal()}
        {this.renderXNCModal()}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: CSKH,
    global: {
      device: { type }
    },
    patientForm: { redirectToPatientUpdate, isModal: isOverMaxModal },
    patient,
    features: { selectedFeature, featureListBooking, selectedFeatureBooking },
    user: { IsAuthenticated, info: infoUser },
    totalData: {
      bookingTree,
      partnerId,
      isDirectionBack,
      subject: { name: subjectName },
      service: { serviceType, serviceTypeDefault, requiredCheckInsurance },
      days: { bookingDate },
      isScheduleWithoutDate
    },
    followUpExam: { patientExam }
  } = state;

  return {
    infoUser,
    CSKH,
    requiredCheckInsurance,
    subjectName,
    device: type,
    redirectToPatientUpdate,
    isOverMaxModal,
    patient,
    selectedFeature,
    featureListBooking,
    selectedFeatureBooking,
    IsAuthenticated,
    bookingTree,
    partnerId,
    patientExam,
    isDirectionBack,
    serviceType,
    serviceTypeDefault,
    bookingDate,
    isScheduleWithoutDate
  };
};

const mapDispatchToProps = {
  postInfoXNC: p.postInfoXNC,
  selectPatientReExam: p.selectPatientReExam,
  onHideAlertInfo: p.hideAlertInfoChoosePatient,
  handleGetPatientList: p.getPatientList,
  handleGetPatientDetail: pf.getPatientDetail,
  handleToggleModalConfirm: p.toggleModalConfirm,
  handleDeletePatient: p.deletePatient,
  handleToggleModalChoosePatient: p.toggleModalChoosePatient,
  toggleModalNotification: pf.createPatientOverMaxCloseModal,
  resetModalChoosePatient: p.resetModalChoosePatient,
  resetModalConfirmPatient: p.resetModalConfirmPatient,
  resetSelectedPatient: p.updateSelectedPatient,
  changeUrlRedirectAfterCreatePatient: pf.changeUrlRedirectAfterCreatePatient,
  selectPatientFromData: p.selectPatientFromData,
  saveFieldsCheckInsurance: p.saveFieldsCheckInsurance,
  checkInsuranceOfPatient: p.checkInsuranceOfPatient,
  resetFormCheckInsurance: p.resetFormCheckInsurance,
  changeServiceType: t.changeServiceType,
  selectHospital: t.selectHospital,
  resetErrorCheckInsurance: p.resetErrorCheckInsurance,
  resetStatusScanQrCodeInsurance: p.resetStatusScanQrCodeInsurance,
  selectPatientFromList: p.selectPatientFromList,
  setInsuranceChoice: t.setInsuranceChoice,
  goBackStep: t.goBackStep,
  resetSchedule: t.resetSchedule
};

const DetectChoosePatientHelmet = withTitle({
  component: DetectChoosePatient,
  title: `${hospitalName.value} | Chọn hồ sơ bệnh nhân`
});

export default withRouter(
  connect(
    mapStateToProps,
    mapDispatchToProps
  )(
    createForm({
      mapPropsToFields: props => {
        const {
          patient: { formCheckInsurance }
        } = props;
        return {
          fullName: createFormField(formCheckInsurance.fullName),
          code: createFormField(formCheckInsurance.code),
          birthday: createFormField(formCheckInsurance.birthday)
        };
      },
      onFieldsChange(props, fields) {
        let code = fields.code;
        if (code !== undefined) {
          code = { ...code, value: code.value.toUpperCase()?.trim() };
          props.saveFieldsCheckInsurance({ code });
        } else {
          props.saveFieldsCheckInsurance(fields);
        }
      }
    })(DetectChoosePatientHelmet)
  )
);
