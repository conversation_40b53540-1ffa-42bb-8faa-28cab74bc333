.CardPatientBox {
  height: calc(100vh - 125px);

  .<PERSON><PERSON>eader {
    max-height: 76px;
  }
}

.CardBody {
  overflow: scroll;
  overflow-x: hidden;
  padding: 15px;
}

/* width */
::-webkit-scrollbar {
  display: none;
}

/* Handle */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  --webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-image: -webkit-linear-gradient(330deg, #e0c3fc 0%, #8ec5fc 100%);
  background-image: linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%);
}

.divAction {
  display: flex;
  align-items: center;
  flex-direction: row;
  align-content: center;
  text-align: right;
  justify-content: flex-end;

  .searchPatient {
    margin: 6px;
    min-height: 31.4px;
    box-shadow: 0 2px 5px 0 #00000029, 0 2px 10px 0 #0000001f;

    .hidden {
      opacity: 0;
      visibility: hidden;
      width: 0;
      margin: 0 !important;
      transition: 0.3s;
    }

    input {
      width: 100%;
      opacity: 1;
      height: fit-content !important;
      padding: 0;
      margin: 7px;
      color: white;
      font-size: 11px !important;
      transition: width 0.5s, opacity 0.3s;

      &::placeholder {
        color: white !important;
        font-size: 11px !important;
      }
    }

    background-color: #1da1f2;
    padding: 0 10px;
    border-radius: 3px;
  }

  .highlight {
    background-color: yellow;
  }
}
.modal {
  border-radius: 8px;
}
.Header {
  background-color: #3699ff;

  color: white !important;
  padding: 10px 15px 10px;
  .TitleHeader {
    font-size: 16px;
    font-weight: 700;
    padding-left: 10px;
  }
  button {
    span {
      color: white;
    }
  }
}
.Body {
  // height: 400px;
  // max-height: 700px;
  // overflow: scroll;
}
