import React from "react";
import {
  MDBModal,
  MDBModalBody,
  MDBModalHeader,
  MDBModalFooter
} from "mdbreact";
import PKHBtn from "~/components/common/atoms/Button";
import cx from "classnames";
import styles from "./style.module.scss";
import partnerId from "~/utils/partner";
class Modal extends React.Component {
  static defaultProps = {
    title: "Thông báo",
    iconTitle: null,
    footer: false,
    footerConfirm: false,
    children: "<PERSON><PERSON>y ra lỗi, Vui lòng thử lại!",
    onOk: () => {},
    okText: "OK",
    openText: "",
    cancelText: "Cancel",
    onCancel: () => {},
    onOpen: () => {},
    centered: false
  };

  toggle = () => {
    this.props.toggle();
  };

  handleOk = e => {
    const { onOk } = this.props;
    if (onOk) {
      onOk(e);
    }
  };

  handleCancel = e => {
    const { onCancel } = this.props;
    if (onCancel) {
      onCancel(e);
    }
  };

  handleOpenText = e => {
    const { onOpen } = this.props;
    if (onOpen) {
      onOpen(e);
    }
  };

  renderFooterContent = () => {
    const {
      footerConfirm,
      cancelText,
      okText,
      openText,
      footer,
      okDisabled
    } = this.props;
    if (footerConfirm) {
      return (
        <MDBModalFooter className={styles.modal_footer}>
          {cancelText ? (
            <PKHBtn key="btn-cancel" onClick={this.handleCancel}>
              {cancelText}
            </PKHBtn>
          ) : null}
          {openText ? (
            <PKHBtn
              key="btn-ok"
              confirm="confirm"
              onClick={this.handleOpenText}
            >
              {openText}
            </PKHBtn>
          ) : null}
          <PKHBtn
            confirm="confirm"
            key="btn-ok"
            onClick={this.handleOk}
            disabled={okDisabled}
          >
            {okText}
          </PKHBtn>
        </MDBModalFooter>
      );
    } else {
      return (
        <MDBModalFooter className={styles.modal_footer}>
          {footer}
        </MDBModalFooter>
      );
    }
  };

  render() {
    const {
      title,
      footer,
      children,
      position,
      iconTitle,
      centered,
      fullHeight,
      size,
      modal,
      className,
      backdropClassName,
      image,
      noBackDrop
    } = this.props;
    const obj = Object.create(null);
    if (typeof position !== typeof undefined) {
      obj.position = position;
    }
    if (typeof centered !== typeof undefined) {
      obj.centered = centered;
    }
    if (typeof fullHeight !== typeof undefined) {
      obj.fullHeight = fullHeight;
    }
    if (typeof size !== typeof undefined) {
      obj.size = size;
    }
    // const { modal } = this.state
    return (
      <MDBModal
        isOpen={modal}
        toggle={this.toggle}
        {...obj}
        backdrop={noBackDrop}
        className={cx(
          styles.popup_modal,
          styles[className],
          styles["popup_modal_" + partnerId]
        )}
        backdropClassName={styles[backdropClassName]}
      >
        <MDBModalHeader toggle={this.toggle} className={cx(styles.title_popup)}>
          {iconTitle || []}
          {title}
        </MDBModalHeader>
        <MDBModalBody className={styles.body}>
          {image ? (
            <img src={image} alt="ma so ho so" style={{ width: "100%" }} />
          ) : (
            children
          )}
        </MDBModalBody>
        {footer ? this.renderFooterContent() : null}
      </MDBModal>
    );
  }
}

export default Modal;
