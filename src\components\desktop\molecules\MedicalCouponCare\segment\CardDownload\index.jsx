import React from "react";
import styles from "./styles.module.scss";
import { get } from "lodash";
import { Android_MEDPRO, IOS_MEPRO } from "~/utils/constants";
import PKHBtn from "~/components/common/atoms/Button";
const QRCode = require("qrcode.react");

const CardDownload = ({ global }) => {
  const os = get(global, "device.os", "android");
  const urlDown = () => {
    switch (os) {
      case "android":
        return Android_MEDPRO;
      case "ios":
        return IOS_MEPRO;
      default:
        return "/";
    }
  };

  return (
    <div className={styles.cardDownload}>
      <p className={styles.title}>Chúc mừng đặt khám thành công !</p>
      <p className={styles.subTitle}>
        Quý khách vui lòng cài đặt ứng dụng để xem chi tiết <br /> hướng dẫn và
        quản lý hồ sơ khám bệnh.
      </p>
      <div className={styles.group}>
        <div className={styles.QR}>
          <QRCode
            fgColor="#000000"
            size={90}
            value="https://medpro.vn/getapp"
          />
        </div>
        <div className={styles.linkDown}>
          <p>
            CHẠM ĐỂ KHÁM <br /> SCAN ĐỂ TẢI APP
          </p>
          <a
            href={urlDown()}
            target={urlDown() === "/" ? "_parent" : "_blank"}
            rel="noopener noreferrer"
          >
            <PKHBtn downloadApp> Tải ứng dụng</PKHBtn>
          </a>
        </div>
      </div>
    </div>
  );
};

export default CardDownload;
