.container {
  .Header {
    background-color: #3699ff;

    color: white !important;
    padding: 10px 15px 10px;
    .TitleHeader {
      font-size: 18px;
      font-weight: 800;
      padding-left: 10px;
    }
    button {
      span {
        color: white;
      }
    }
  }
  .Body {
    .description {
      margin-bottom: 1rem;
      color: #00b5f1;
    }
    .AddInfo {
      display: flex;
      flex-direction: column;
      margin-bottom: 1rem;
      .alertSpan {
        font-size: 12px;
        color: red;
        font-style: italic;
      }
    }
    .Input {
      border-radius: 5px;
      border: 1px solid #e0e2e6;
      padding: 10px;
      color: #303233;
      &:focus {
        outline: none !important;
        border: 1px solid #808080;
      }
    }

    .nextObj {
      margin-top: 20px;
      float: right;
      background-color: #00b5f1;
      border: none;
      padding: 10px 30px;
      border-radius: 5px;
      color: white;
    }
    .btnDisable {
      margin-top: 20px;
      float: right;
      background-color: gainsboro;
      border: none;
      padding: 10px 30px;
      border-radius: 5px;
      color: white;
    }
  }
}
