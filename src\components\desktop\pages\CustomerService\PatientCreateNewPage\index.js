/* eslint-disable camelcase */
import { MD<PERSON><PERSON><PERSON>, MDBBtn } from "mdbreact";
import queryString from "query-string";
import React, { Component } from "react";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import FormStepper from "~/components/desktop/molecules/FormStepper";
import {
  resetRedirectPatientForm,
  resetPatientForm
} from "~/store/patientForm/patientFormAction";
import {
  changePatientNumber,
  getPatientByMSBN,
  hideLoading,
  resetData,
  resetFormGetPatienByMSBN
} from "~/store/patient/patientAction";
import {
  onChangeCity,
  onChangeCountry,
  onChangeDistrict,
  onChangeNation,
  requestAllCareers,
  requestAllCountries,
  requestAllNation,
  requestAllRelationShip,
  resetCity,
  resetDistrict,
  resetSelectedCountry,
  resetWard
} from "~/store/resource/resourceAction";
import styles from "./style.module.scss";

class PatientPage extends Component {
  constructor(props) {
    super(props);
    const query = queryString.parse(props.location.search);

    this.state = {
      inputPhone: query.phone ? query.phone : null,
      showModalPhoneCheck: false,
      shouldAutoCloseModalPhoneCheck: true
    };
  }

  // methods
  toggle = tab => {
    this.setState({
      activeItem: tab
    });
  };

  // disable autoClose (or can be see as stop ComponentWillUpdate from closing the modal)
  disableAutoCloseModalPhoneCheck = () => {
    this.setState({ shouldAutoCloseModalPhoneCheck: false });
  };

  toggleModalPhoneCheck = () => {
    this.setState({ showModalPhoneCheck: !this.state.showModalPhoneCheck });
  };

  UNSAFE_componentWillMount() {
    if (!this.props.supportingAccount) {
      this.setState({ showModalPhoneCheck: true });
    }
  }

  componentWillUpdate(nextProps) {
    // trigger autoclose if supporting account exist
    if (
      this.props !== nextProps &&
      (this.props.supportingAccount || nextProps.supportingAccount)
    ) {
      if (this.state.shouldAutoCloseModalPhoneCheck)
        this.setState({ showModalPhoneCheck: false });
    }
  }

  renderNoAccountNotification = () => {
    return (
      <div className={styles.notification_message}>
        <MDBAlert className={styles.message}>
          <p>
            Chưa xác định được tài khoản mà bạn đang hỗ trợ, Để tiếp tục tạo tài
          </p>
          khoản bạn cần thực hiện tìm kiếm theo số điện thoại tại{" "}
          <MDBBtn
            className={styles.hover_link}
            onClick={() => this.toggleModalPhoneCheck()}
            size="sm"
          >
            đây
          </MDBBtn>
        </MDBAlert>
      </div>
    );
  };

  componentDidMount() {
    const {
      formData,
      isCreateWithSuggestInfo,
      resetPatientForm,
      resetCity,
      resetDistrict,
      resetWard,
      requestAllCountries,
      requestAllCareers,
      requestAllRelationShip,
      requestAllNation,
      resetFormGetPatienByMSBN,
      onChangeCountry,
      onChangeCity,
      onChangeDistrict
    } = this.props;
    resetPatientForm();
    resetCity();
    resetDistrict();
    resetWard();
    requestAllCountries();
    requestAllCareers();
    requestAllRelationShip();
    requestAllNation();
    if (!isCreateWithSuggestInfo) {
      resetFormGetPatienByMSBN();
    }
    onChangeCountry({
      id: formData.country_code.value
    });

    if (formData.city_id.value) {
      onChangeCity({
        id: formData.city_id.value
      });
    } else {
      onChangeCity({
        id: 0
      });
      onChangeDistrict({
        id: 0
      });
    }
    if (formData.district_id.value) {
      onChangeDistrict({
        id: formData.district_id.value
      });
    }
  }

  async componentDidUpdate(prevProps) {
    if (
      prevProps.formData.city_id.value !== this.props.formData.city_id.value
    ) {
      await this.props.onChangeCity({
        id: this.props.formData.city_id.value
      });
    }
    if (
      prevProps.formData.district_id.value !==
      this.props.formData.district_id.value
    ) {
      await this.props.onChangeDistrict({
        id: this.props.formData.district_id.value
      });
    }
  }

  render() {
    const { requiredFields } = this.props;

    return (
      <div className={styles.wapper_page_desktop}>
        <FormStepper type="create" requiredFields={requiredFields} />
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { supportingAccount },
    global: {
      device: { type }
    },
    patientForm: {
      redirectToChooseCalendar,
      redirectToChooseProfile,
      info: formData,
      isCreateWithSuggestInfo
    },
    patient: {
      selectedPatient,
      redirectToCheckPatientPhone,
      errorMessage,
      patientNumber
    },
    totalData: { extraConfig },
    user: { info }
  } = state;
  return {
    supportingAccount,
    extraConfig,
    device: type,
    redirectToChooseCalendar,
    redirectToChooseProfile,
    user: info,
    selectedPatient,
    redirectToCheckPatientPhone,
    errorMessage,
    patientNumber,
    formData,
    isCreateWithSuggestInfo
  };
};

const mapDispatchToProps = dispatch => ({
  resetRedirectPatientForm: () => {
    dispatch(resetRedirectPatientForm());
  },
  resetPatientForm: () => {
    dispatch(resetPatientForm());
  },
  requestAllCountries: () => {
    dispatch(requestAllCountries());
  },
  requestAllCareers: () => {
    dispatch(requestAllCareers());
  },
  requestAllRelationShip: () => {
    dispatch(requestAllRelationShip());
  },
  requestAllNation: () => {
    dispatch(requestAllNation());
  },
  handleChangePatientNumber: value => {
    dispatch(changePatientNumber(value));
  },
  handleGetPatient: () => {
    dispatch(getPatientByMSBN());
  },
  handleResetData: () => dispatch(resetData()),
  resetSelectedCountry: () => {
    dispatch(resetSelectedCountry());
  },
  resetCity: () => {
    dispatch(resetCity());
  },
  resetDistrict: () => {
    dispatch(resetDistrict());
  },
  resetWard: () => {
    dispatch(resetWard());
  },
  onChangeNation: id => {
    dispatch(onChangeNation(id));
  },
  onChangeCountry: selectedCountry => {
    dispatch(onChangeCountry(selectedCountry));
  },
  onChangeCity: selectedCity => {
    dispatch(onChangeCity(selectedCity));
  },
  onChangeDistrict: selectedDistrict => {
    dispatch(onChangeDistrict(selectedDistrict));
  },
  resetFormGetPatienByMSBN: () => {
    dispatch(resetFormGetPatienByMSBN());
  },
  hideLoading: () => {
    dispatch(hideLoading());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(PatientPage));
