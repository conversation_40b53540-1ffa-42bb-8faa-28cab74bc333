import React, { Fragment, Component } from "react";
import { connect } from "react-redux";
import cx from "classnames";
import {
  MDBListGroup,
  MDBListGroupItem,
  MDBCard,
  MDBCardBody,
  MDBCardHeader
} from "mdbreact";
import styles from "./style.module.scss";
import { getInfoPatient } from "~/utils/flowRouting";
import partnerId from "~/utils/partner";

class PatientInfomationLess extends Component {
  render() {
    const { selectedPatient } = this.props;

    const { fullName, mobile, fullAddress } = getInfoPatient(selectedPatient);

    return (
      <Fragment>
        <MDBCard className={cx(styles.panels, styles["panels_" + partnerId])}>
          <MDBCardHeader className={styles.panels_header}>
            Thông tin bệnh nhân
          </MDBCardHeader>
          <MDBCardBody className={cx("mb-4", styles.card_body)}>
            <MDBListGroup className={styles.list_group}>
              <MDBListGroupItem>
                <i className="far fa-user-circle" />
                {fullName}
              </MDBListGroupItem>
              <MDBListGroupItem>
                <i className="far fa-mobile-alt" />
                {mobile}
              </MDBListGroupItem>
              <MDBListGroupItem>
                <i className="far fa-map-marker" />
                {fullAddress}
              </MDBListGroupItem>
            </MDBListGroup>
          </MDBCardBody>
        </MDBCard>
      </Fragment>
    );
  }
}

const mapStateToProps = state => ({
  selectedPatient: state.patient.selectedPatient,
  selectedHospital: state.hospital.selectedHospital
});

const mergeProps = (stateProps, dispatchProps, ownProps) => {
  return { ...stateProps, ...dispatchProps, ...ownProps };
};

export default connect(
  mapStateToProps,
  null,
  mergeProps
)(PatientInfomationLess);
