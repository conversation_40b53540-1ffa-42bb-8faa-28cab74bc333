@import "./../Faq/style.module.scss";
@import "src/assets/scss/custom-variables.scss";

.sub_card_header {
  margin: 10px 0;
}
.back_btn {
  margin: 15px 0;
}

.card_news {
  font-family: "Averta", serif;
  margin-bottom: 1.25rem;
  border: none;
  box-shadow: none;
  // background-color: #fbfcfe;
  display: flex;
  justify-content: stretch;
  .card_body {
    padding: 1.25rem 0 !important;
    .title {
      font-size: 1.3rem;
      font-weight: bolder;
      text-transform: uppercase;
      margin-bottom: 5px;
    }
    .author {
      font-weight: bolder;
      font-size: 0.875rem;
      color: #777;
      margin-bottom: 5px;
    }
    .description {
      font-size: 0.975rem;
      line-height: 1.45;
      text-align: justify;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
    }
  }
  @media #{$medium-and-down} {
    .titleNews {
      background-color: brown;
      font-size: 1rem;
    }
    .card_body {
      .categories {
        font-size: 0.875rem;
      }
      .title {
        font-weight: 500;
        text-transform: initial;
        font-size: 0.975rem;
      }
      .author {
        font-size: 0.775rem;
      }
      .description {
        font-size: 0.875rem;
        margin-bottom: 0 !important;
      }
    }
  }
}

@media #{$medium-and-down} {
  .titleNews {
    font-size: 1.2rem;
  }
}
