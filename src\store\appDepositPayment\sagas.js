import { get } from "lodash";
import { all, fork, select, takeLatest } from "redux-saga/effects";
import history from "~/history";
import * as types from "~/store/appDepositPayment/types";
import { client } from "~/utils/medproSDK";
import AppId from "~/utils/partner";

function* requestCreateDepositApp({ data: { token, email, subTotal } }) {
  try {
    const paymentMethodReducer = yield select(state => state.payment);
    const selectedHospital = yield select(
      state => state.hospital.selectedHospital
    );

    const hospitalName = get(selectedHospital, "name", "");
    const methodId = get(paymentMethodReducer, "methodId", "NO_PAYMENT");
    const price = get(paymentMethodReducer, "price", 0);
    const domain = window.location.origin;
    const postData = {
      email,
      subTotal,
      methodId,
      paymentTypeDetail: get(
        paymentMethodReducer,
        "selectedMethod.code",
        "NO_PAYMENT"
      ),
      platform: "web",
      redirectUrl: `${domain}/payment-app-success`
    };
    const response = yield client.createDeposit(postData, {
      token,
      partnerid: AppId,
      appid: AppId
    });
    const {
      data: { qrCodeUrl, transactionId }
    } = response;
    // window.location.href = qrCodeUrl;

    // đá sang page quét qrCode theo môi trường testing
    if (methodId === "testing") {
      const url = qrCodeUrl;
      history.push({
        pathname: "/quet-ma-qrcode-thanh-toan-phieu-kham",
        state: {
          url,
          transactionId,
          feeInfo: {
            billInfo: null,
            hospitalName,
            price
          },
          redirectRouter: `/payment-app-success`
        }
      });
    } else {
      window.location.href = qrCodeUrl;
    }
  } catch (error) {
    console.log(error);
  }
}

function* watcherCreateDepositApp() {
  yield takeLatest(types.CREATE_DEPOSIT_APP, requestCreateDepositApp);
}

export default function*() {
  yield all([fork(watcherCreateDepositApp)]);
}
