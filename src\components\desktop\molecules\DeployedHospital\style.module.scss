@import "src/assets/scss/custom-variables.scss";

.card_news {
  background: transparent;
  display: flex;
  justify-content: stretch;
  box-shadow: none;
  .view {
    min-height: 212px;
  }
  .card_body {
    font-size: 1.125rem;
    font-weight: 400;
    text-align: center;
    text-justify: inter-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.DeployedHospital {
  background-color: #f8fafd;
}
@media #{$medium-and-down} {
  .DeployedHospital {
    margin-top: 40px !important;
  }
}
