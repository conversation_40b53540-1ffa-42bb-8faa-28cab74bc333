import partner from "~/utils/partner";
const checkApiByParnerId = () => {
  const hostname = window.location.hostname;
  if (hostname.includes("beta")) {
    return "https://api-111.medpro.com.vn";
  }
  if (hostname.includes("hotfix")) {
    return "https://api-hotfix.medpro.com.vn";
  }

  switch (partner) {
    default:
      return "https://api-hotfix.medpro.com.vn";
  }
};

// Api backend
export const RESTFULL_API_URL = checkApiByParnerId();

export const MEDPRO_LOGIN = `https://id-v121.medpro.com.vn/check-phone`;

export const COOKIE_EXPIRES = 43200; // 12*60*60; // 12 tiếng

export const SOCKET_CHAT = "wss://alpha-api.medpro.com.vn/cable";

export const PAYMENT_HUB_URL = "https://payment-gateway-qrcode.medpro.com.vn";

export const CSKH_API_URL = "https://portal.medpro.com.vn";

export const STATIC_CONTENT_API_URL = `https://resource.medpro.com.vn`;

export const BO_WEB = "https://backoffice-beta.medpro.com.vn";

export const MEDPRO_WEB = "https://beta.medpro.com.vn";
