.ModalSendBooking {
  display: flex;
  align-items: center;
  min-height: calc(100% - 3.5rem);
  .header {
    background: #15b7e6;
    color: #fff;
    font-weight: 600;
    justify-content: center;
  }
  .section {
    margin-bottom: 24px;
  }
  .label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
  }
  .copyRow,
  .smsRow {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .input {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 15px;
    background: #f8f9fa;
  }
  .copyBtn,
  .sendBtn {
    min-width: 140px;
    font-weight: 600;
  }
}
.method_content {
  :global {
    .input-group {
      border-radius: 8px;
    }
  }
}
