// import { MD<PERSON><PERSON>, MDBContainer, MDBRow } from "mdbreact";
// import moment from "moment";
// import React, { useEffect, useState } from "react";
// import { useSelector } from "react-redux";
// import * as api from "~/api";
// import scancode from "~/assets/img/svg/scan.svg";
// import TagName from "~/components/common/atoms/TagName";
// import CountDownTimer from "~/components/common/molecules/countDownTimer";
// import history from "~/history";
// import {
//   GATEWAY_CHECK_STT_TRANSACTION,
//   GATEWAY_RECHECK_STT_TRANSACTION
// } from "~/utils/constants";
// import { checkOS, formatAmount } from "~/utils/func";
// import { getLogoPng } from "~/utils/manageResource";
// import AppId from "~/utils/partner";
// import styles from "./style.module.scss";

// var jsonObj = require("../../assets/content/index.json");
// let timeOut;
// const PaymentWithQrCode = ({ location }) => {
//   const { type } = useSelector(state => state.global.device);
//   let [count, setCount] = useState(0);
//   const [qrCodeUrl, setQrCodeUrl] = useState("");
//   const [extendData, setExtendData] = useState("");
//   const [redirectRouter, setRedirectRouter] = useState("");
//   const [feeInfo, setFeeInfo] = useState(null);
//   const domain = window.location.origin;
//   const { transactionId } = location.state;

//   // call api to check and recheck status of payment
//   const getTransactionStt = async (apiUrl, transactionId) => {
//     const res = await api.postHttpRequestAsync(
//       apiUrl + `?t=${moment().unix()}`,
//       {
//         isCallback: 0,
//         transactionId
//       }
//     );
//     return res;
//   };

//   // hàm đệ quy dùng để gọi lại hành động call api khi dựa trên status payment
//   const checkStt = async () => {
//     const res =
//       count === 0
//         ? await getTransactionStt(
//             GATEWAY_CHECK_STT_TRANSACTION,
//             location?.state?.transactionId
//           )
//         : await getTransactionStt(
//             GATEWAY_RECHECK_STT_TRANSACTION,
//             location?.state?.transactionId
//           );
//     const {
//       data: {
//         data: {
//           TransactionInfo: { status }
//         }
//       }
//     } = res;
//     switch (status) {
//       case 1:
//         setCount((count += 1));
//         if (count < 100)
//           timeOut = setTimeout(() => {
//             checkStt();
//           }, 3000);
//         break;
//       case 2:
//         return history.push(redirectRouter);

//       case 3:
//         return history.push(`/`);
//       default:
//         setCount((count += 1));
//         if (count < 100)
//           timeOut = setTimeout(() => {
//             checkStt();
//           }, 3000);
//         break;
//     }
//   };

//   // Render list banking
//   const renderListBanking = list => {
//     return list.map((item, index) => {
//       return (
//         <div className={styles.banking} key={index}>
//           <img
//             alt="/"
//             onClick={() => {
//               jsonObj[item].scheme !== "#"
//                 ? handleAfterSelectBank(
//                     generateDeepLink(jsonObj[item].scheme, jsonObj[item].packid)
//                   )
//                 : handleAfterSelectBank("#");
//             }}
//             src={`https://medpro-inside-testing.medpro.com.vn/static/
//             images/common/app/banking-icon/${item.toLowerCase()}.png`}
//           />
//           <span>{item}</span>
//         </div>
//       );
//     });
//   };

//   // Check device's OS and generate deepLink
//   const generateDeepLink = (scheme, packId) => {
//     const isAndroid = checkOS();
//     const enCodedCallbackUrl = encodeURIComponent(`${domain}${redirectRouter}`);
//     const enCodedExtendData = encodeURIComponent(`${extendData}`);
//     const viewData = `${enCodedExtendData}&callbackurl=${enCodedCallbackUrl}`;
//     let deepLink = `intent://view?data=${viewData}/#Intent;scheme=${scheme};package=${packId};end`;
//     if (isAndroid) {
//       return deepLink;
//     } else {
//       deepLink = `${scheme}://${viewData}`;
//       return deepLink;
//     }
//   };

//   // Handle after select banking
//   const handleAfterSelectBank = deepLink => {
//     if (deepLink !== "#") {
//       console.log(deepLink);
//       window.location.href = deepLink;
//     } else {
//       alert("Ngân hàng chưa hỗ trợ");
//     }
//   };

//   if (location?.state?.url) {
//     setQrCodeUrl(location.state.url);
//   }
//   if (location?.state?.feeInfo) {
//     setFeeInfo(location.state.feeInfo);
//   }
//   if (location?.state?.redirectRouter) {
//     // console.log(location.state.redirectRouter);
//     setRedirectRouter(location.state.redirectRouter);
//   }
//   if (location?.state?.extendData) {
//     // console.log(location.state.extendData);
//     setExtendData(location.state.extendData);
//   }

//   useEffect(() => {
//     if (qrCodeUrl !== "") {
//       checkStt();
//     }
//   }, [qrCodeUrl]);

//   useEffect(() => {
//     return () => {
//       clearTimeout(timeOut);
//     };
//   }, []);

//   // if (!feeInfo && qrCodeUrl === "") {
//   //   return <Redirect to="/" />;
//   // }

//   if (type === "mobile")
//     return (
//       <MDBContainer className="">
//         <MDBRow className={styles.wrapper_content}>
//           <MDBCol md="12">
//             <div className={styles.logo_parner}>
//               <div className={styles.logobv}>
//                 <img src={getLogoPng(AppId)} alt="" className="img-fluid" />
//               </div>
//             </div>
//             <div className={styles.content}>
//               <h3 className={styles.title}>Quét mã để thanh toán</h3>
//               <img
//                 src={qrCodeUrl}
//                 alt="thumbnail"
//                 width={250}
//                 height={250}
//                 className={styles.code}
//               />
//               <div className={styles.info_payment_mobile}>
//                 <div className={styles.paymentContent}>
//                   <p>
//                     Thanh toán phiếu khám <strong>{transactionId}</strong>
//                   </p>
//                 </div>
//                 <div className={styles.paymentTotal}>
//                   <label>Tổng cộng</label>
//                   <p>
//                     {feeInfo ? (
//                       <strong>
//                         {formatAmount(feeInfo?.price?.grandTotal)}đ
//                       </strong>
//                     ) : (
//                       ""
//                     )}
//                   </p>
//                 </div>
//               </div>
//               <p>
//                 <span className={styles.scancode}>
//                   <img src={scancode} alt="" className="img-fluid" />
//                 </span>{" "}
//                 Sử dụng các ứng dụng ngân hàng để quét mã và tiến hành thanh
//                 toán.
//               </p>
//               <div className={styles.close} onClick={() => history.push("/")}>
//                 Đóng
//               </div>
//             </div>
//             <div className={styles.wrapper_banking}>
//               <TagName element="h6" className={["title_select_banking_method"]}>
//                 <span>Ngân hàng hỗ trợ thanh toán</span>
//               </TagName>
//               <MDBCol sm="10" className={styles.listBanking}>
//                 {renderListBanking(Object.keys(jsonObj))}
//               </MDBCol>
//             </div>
//           </MDBCol>
//         </MDBRow>
//       </MDBContainer>
//     );

//   return (
//     <MDBContainer className="">
//       <MDBRow className={styles.wrapper_content}>
//         <MDBCol md="3">
//           <div className={styles.info_payment}>
//             <ul>
//               <li>
//                 <div className={styles.icon}>
//                   <i className="fal fa-clock" />
//                 </div>
//                 <div className={styles.item}>
//                   <p>Đơn hàng sẽ hết hạn sau</p>
//                   <p>
//                     <b>
//                       <CountDownTimer
//                         hours={0}
//                         minutes={5}
//                         seconds={0}
//                         callback={() => history.push("/")}
//                       />
//                     </b>
//                   </p>
//                 </div>
//               </li>
//               <li>
//                 <div className={styles.icon}>
//                   <i className="fal fa-hospitals" />
//                 </div>
//                 <div className={styles.item}>
//                   <p>Nhà cung cấp</p>
//                   <p>
//                     <b>{feeInfo ? feeInfo?.hospitalName : ""}</b>
//                   </p>
//                 </div>
//               </li>
//               <li>
//                 <div className={styles.icon}>
//                   <i className="fal fa-file-alt" />
//                 </div>
//                 <div className={styles.item}>
//                   <p>Nội dung thanh toán</p>
//                   <p>
//                     <b>{feeInfo ? feeInfo?.billInfo?.content : ""}</b>
//                   </p>
//                 </div>
//               </li>
//               <li>
//                 <div className={styles.icon}>
//                   <i className="fal fa-file-invoice-dollar" />
//                 </div>
//                 <div className={styles.item}>
//                   <p>Đơn hàng</p>
//                   <p>
//                     <b>{feeInfo ? feeInfo?.billInfo?.fee_code : ""}</b>
//                   </p>
//                 </div>
//               </li>
//               <li>
//                 <div className={styles.icon}>
//                   <i className="fal fa-receipt" />
//                 </div>
//                 <div className={styles.item}>
//                   <p>Số tiền thanh toán</p>
//                   <p>
//                     <b>
//                       {feeInfo ? formatAmount(feeInfo?.price?.grandTotal) : ""}{" "}
//                       đ
//                     </b>
//                   </p>
//                 </div>
//               </li>
//             </ul>
//           </div>
//         </MDBCol>
//         <MDBCol md="9">
//           <div className={styles.logo_parner}>
//             <div className={styles.logobv}>
//               <img src={getLogoPng(AppId)} alt="" className="img-fluid" />
//             </div>
//           </div>
//           <div className={styles.content}>
//             <h3 className={styles.title}>Quét mã để thanh toán</h3>
//             <img
//               src={qrCodeUrl}
//               alt="thumbnail"
//               width={250}
//               height={250}
//               className={styles.code}
//             />
//             <p>
//               <span className={styles.scancode}>
//                 <img src={scancode} alt="" className="img-fluid" />
//               </span>{" "}
//               Sử dụng các ứng dụng ngân hàng để quét mã và tiến hành thanh toán.
//             </p>
//             {/* process.env.NODE_ENV */}
//             <div className={styles.close} onClick={() => history.push("/")}>
//               Đóng
//             </div>
//           </div>
//         </MDBCol>
//       </MDBRow>
//     </MDBContainer>
//   );
// };

// export default PaymentWithQrCode;
