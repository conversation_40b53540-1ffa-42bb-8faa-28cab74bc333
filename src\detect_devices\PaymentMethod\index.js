import { find, get, reduce } from "lodash";
import { <PERSON><PERSON><PERSON><PERSON>, MDBSpinner } from "mdbreact";
import React, { Component, Fragment } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { Redirect } from "react-router-dom";
import {
  resetAlertSubmitFail,
  submitPayment
} from "~/actions/umc/submitPayment";
import Alert from "~/components/common/atoms/Alert";
import Modal from "~/components/common/molecules/Modal";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import {
  getAllPaymentMethod,
  hideModalPaymentMethod,
  resetPaymentMethod,
  selectedPaymentMethod,
  togglePaymentMethod
} from "~/store/payment/paymentAction";
import {
  bookingShareToPay,
  getBookingTree,
  getExtraConfig,
  repaymentBooking,
  reserveBooking
} from "~/store/totalData/actions";
import { getPartnerFromUrl, getRouteFollowPartnerId } from "~/utils/func";
import styles from "./style.module.scss";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const PaymentMethodPageDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/PaymentMethod"),
  loading: LoadableLoading
});

class DetectPaymentMethod extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowAlert: false,
      isShowPaymentConfirm: false,
      isShowPaymentHelpsConfirm: false,
      loadingSubmitBooking: false
    };
  }

  methods = {
    calculate: () => {
      const { schedulesSelected, selectedMethod } = this.props;
      const total = reduce(
        schedulesSelected,
        function(sum, item) {
          const price = Number(item.service.price);
          return sum + price;
        },
        0
      );
      const medpro = selectedMethod.medproFee
        ? Number(selectedMethod.medproFee)
        : 0;
      const fee = selectedMethod.rate ? Number(selectedMethod.rate) : 0;
      const feeMore = selectedMethod.constRate
        ? Number(selectedMethod.constRate)
        : 0;
      const phi = parseInt((total + medpro) * fee + feeMore);
      const phiThanhToan = phi;
      const phiThanhToanGiaoDich = phiThanhToan || 0;
      const totalPhi = medpro + phiThanhToanGiaoDich;
      return {
        total,
        totalPhi
      };
    },
    toggleAlertFail: () => {
      this.props.OnResetErrorMessgeSubmitPayment();
      this.setState(state => ({
        loadingSubmitBooking: false
      }));
    },
    handleDoPaymentHelps: () => {
      const { schedulesSelected } = this.props;

      this.props.selectedPaymentMethod(
        {
          code: "SHARE_PAYMENT",
          gatewayId: "SHARE_PAYMENT",
          sequence: 1,
          subTotal: get(schedulesSelected[0].service, "price", 0),
          totalFee: 0,
          grandTotal: get(schedulesSelected[0].service, "price", 0),
          type: "SHARE_PAYMENT"
        },
        "SHARE_PAYMENT"
      );
      this.props.reserveBooking();
      this.setState(state => ({
        isShowPaymentHelpsConfirm: false
      }));
    },
    handleDoPayment: () => {
      const { isRepayment } = this.props;
      if (isRepayment) {
        this.props.repaymentBooking();
      } else {
        this.props.reserveBooking();
      }
      this.setState(state => ({
        isShowPaymentConfirm: false
      }));
    },

    toggleShowPaymentConfirm: () => {
      this.setState(state => ({
        isShowPaymentConfirm: !state.isShowPaymentConfirm
      }));
    },
    toggleShowPaymentHelpsConfirm: () => {
      this.setState(state => ({
        isShowPaymentHelpsConfirm: !state.isShowPaymentHelpsConfirm
      }));
    },

    handleSelectedMethod: (method, methodId) => {
      this.props.selectedPaymentMethod(method, methodId);
    },
    toggleAlert: () => {
      this.setState({
        isShowAlert: !this.state.isShowAlert
      });
    },
    handleGoBack: () => {
      const { partnerId } = this.props;
      const route = getRouteFollowPartnerId("/xac-nhan-thong-tin", partnerId);
      this.props.history.push(route);
    }
  };

  styles = {
    loading: {
      position: "fixed",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)"
    },

    loading_submit_booking: {
      position: "fixed",
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      zIndex: 1000,
      backgroundColor: "black",
      opacity: 0.5
    },

    body_modal: {
      fontSize: "1rem"
    },
    body_modal_image: {
      margin: "auto",
      display: "block",
      objectFit: "cover",
      marginBottom: "10px",
      maxHeight: "100px"
    }
  };

  renderBodyModalConfirmPayment = () => {
    const { selectedMethod, price } = this.props;
    const selectedMethodName =
      Object.keys(selectedMethod).length > 0
        ? selectedMethod.name
        : "Chọn phương thức thanh toán";

    const grandTotal = get(price, "grandTotal", 0);
    const selectedMethodImage = get(selectedMethod, "paymentIcon.path", "");

    if (selectedMethod.bankAccount) {
      const accountHolder = get(
        selectedMethod,
        "bankAccount.accountHolder",
        ""
      );
      const accountNumber = get(
        selectedMethod,
        "bankAccount.accountNumber",
        ""
      );
      const bankBranch = get(selectedMethod, "bankAccount.bankBranch", "");
      return (
        <Fragment>
          <div style={this.styles.body_modal}>
            {selectedMethodImage && (
              <img
                style={this.styles.body_modal_image}
                src={selectedMethodImage}
                alt={selectedMethodName}
              />
            )}
            <MDBAlert color="primary">
              <p>Thông tin chuyển khoản:</p>
              Tài khoản: <strong>{accountHolder}</strong> <br />
              Số tài khoản: <strong>{accountNumber}</strong> <br />
              Chi nhánh: <strong>{bankBranch}</strong> <br />
              Số tiền:{" "}
              <strong>{Number(grandTotal).toLocaleString("vi-VN")}đ</strong>
            </MDBAlert>
          </div>
        </Fragment>
      );
    }

    return (
      <Fragment>
        <div style={this.styles.body_modal}>
          {selectedMethodImage && (
            <img
              style={this.styles.body_modal_image}
              src={selectedMethodImage}
              alt={selectedMethodName}
            />
          )}
          <p>
            Thanh toán số tiền{" "}
            <strong>{Number(grandTotal).toLocaleString("vi-VN")}đ</strong> bằng{" "}
            {selectedMethodName}
          </p>
          <MDBAlert color="primary">
            Bạn sẽ nhận được phiếu khám bệnh ngay khi{" "}
            <strong>thanh toán thành công</strong>. Trường hợp không nhận được
            phiếu khám bệnh, vui lòng liên hệ <strong>********</strong>.
          </MDBAlert>
        </div>
      </Fragment>
    );
  };

  componentDidMount() {
    const {
      resetPaymentMethod,
      OnRequestAllPaymentMethod,
      hideModalPaymentMethod,
      schedulesSelected,
      getBookingTree,
      getExtraConfig
    } = this.props;

    getExtraConfig();

    if (schedulesSelected.length === 0) {
      const currentPagePartnerId = getPartnerFromUrl(
        this.props.history.location.pathname
      );
      getBookingTree("", currentPagePartnerId);
    }

    resetPaymentMethod();
    OnRequestAllPaymentMethod();
    hideModalPaymentMethod();
  }

  componentWillUnmount() {
    this.props.OnResetErrorMessgeSubmitPayment();
  }

  render() {
    const {
      errorMsg,
      errorBooking,
      isRedirectToPaymentSupportPage,
      loading,
      schedulesSelected
    } = this.props;

    if (schedulesSelected.length === 0) {
      return (
        <div className="loading">
          <MDBSpinner big />
        </div>
      );
    }

    const {
      isShowAlert,
      isShowPaymentConfirm,
      isShowPaymentHelpsConfirm,
      loadingSubmitBooking
    } = this.state;

    const errorMessage =
      errorBooking.error_code === 1011
        ? `Chuyên khoa ${errorBooking.booking_info.subject.name} 
        đã hết số khung giờ này. Vui lòng chọn lại khung giờ khác!`
        : errorBooking.error_code;

    if (isRedirectToPaymentSupportPage)
      return <Redirect to="/ho-tro-thanh-toan" />;

    if (loading) {
      return (
        <div className={styles.loading_spinner}>
          <div className={styles.loading}>
            <MDBSpinner big crazy tag="div" />
          </div>
        </div>
      );
    }

    return (
      <Fragment>
        <PaymentMethodPageDesktop {...this.props} {...this.methods} />

        {loadingSubmitBooking && !errorMsg && (
          <div style={this.styles.loading_submit_booking}>
            <div style={this.styles.loading}>
              <MDBSpinner big crazy tag="div" />
            </div>
          </div>
        )}

        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={isShowPaymentConfirm}
          title="Xác nhận thanh toán"
          children={this.renderBodyModalConfirmPayment()}
          centered
          footer
          footerConfirm
          className="centered"
          toggle={this.methods.toggleShowPaymentConfirm}
          cancelText="Quay lại"
          okText="Đồng ý"
          onCancel={() => this.methods.toggleShowPaymentConfirm()}
          onOk={this.methods.handleDoPayment}
        />

        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={isShowPaymentHelpsConfirm}
          title="Xác nhận thanh toán"
          children={this.renderBodyModalConfirmPayment()}
          centered
          footer
          footerConfirm
          className="centered"
          toggle={this.methods.toggleShowPaymentHelpsConfirm}
          cancelText="Quay lại"
          okText="Đồng ý"
          onCancel={() => this.methods.toggleShowPaymentHelpsConfirm()}
          onOk={this.methods.handleDoPaymentHelps}
        />

        <Alert
          isModal={!!errorBooking}
          message={errorMessage || ""}
          toggleAlert={this.methods.toggleAlertFail}
        />
        <Alert
          isModal={isShowAlert}
          message="Vui lòng chọn phương thức thanh toán"
          toggleAlert={this.methods.toggleAlert}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { IsAuthenticated },
    payment: { selectedMethod, data, showModal, price },
    doctorAndTime: { sumaryInfo },
    umcSubmitPayment: {
      isRedirectToPaymentSupportPage,
      errorMsg,
      errorBooking
    },
    features: { selectedFeature, selectedFeatureBooking },
    totalData: {
      schedulesSelected,
      bookingTree,
      loadingReserveBooking,
      loading,
      isRepayment,
      partnerId,
      subject: { isHasSubject },
      service: { isHasService },
      room: { isHasRoom },
      doctor: { isHasDoctor },
      extraConfig
    },
    hospital: { selectedHospital }
  } = state;
  return {
    extraConfig,
    IsAuthenticated,
    device: type,
    selectedMethod,
    sumaryInfo,
    isRedirectToPaymentSupportPage,
    errorMsg,
    errorBooking,
    showModal,
    data,
    selectedFeature,
    selectedFeatureBooking,
    schedulesSelected,
    bookingTree,
    loadingReserveBooking,
    price,
    loading,
    isRepayment,
    selectedHospital,
    partnerId,
    isHasSubject,
    isHasService,
    isHasRoom,
    isHasDoctor
  };
};

const mapDispatchToProps = dispatch => ({
  bookingShareToPay: code => {
    dispatch(bookingShareToPay(code));
  },
  getExtraConfig: () => {
    dispatch(getExtraConfig());
  },
  OnRequestAllPaymentMethod: () => {
    dispatch(getAllPaymentMethod());
  },
  OnTogglePaymentMethod: () => {
    dispatch(togglePaymentMethod());
  },
  OnSubmitPayment: () => {
    dispatch(submitPayment());
  },
  OnResetErrorMessgeSubmitPayment: () => {
    dispatch(resetAlertSubmitFail());
  },
  selectedPaymentMethod: (method, methodId) => {
    dispatch(selectedPaymentMethod(method, methodId));
  },
  hideModalPaymentMethod: () => {
    dispatch(hideModalPaymentMethod());
  },
  resetPaymentMethod: () => dispatch(resetPaymentMethod()),
  reserveBooking: () => dispatch(reserveBooking()),
  repaymentBooking: () => dispatch(repaymentBooking()),
  getBookingTree: (text, id) => dispatch(getBookingTree(text, id))
});

const PaymentMethodHelmet = withTitle({
  component: DetectPaymentMethod,
  title: `${hospitalName.value} | Chọn hình thức thanh toán`
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PaymentMethodHelmet);
