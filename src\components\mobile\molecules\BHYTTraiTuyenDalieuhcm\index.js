import cx from "classnames";
import { find, findIndex, get, isArray, uniqueId } from "lodash";
import {
  MD<PERSON>ard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBIcon,
  MDBSpinner,
  MDBTable,
  MDBTableBody,
  MDBTableHead
} from "mdbreact";
import moment from "moment";
import { createForm } from "rc-form";
import React, { Fragment } from "react";
import Pagination from "react-bootstrap-4-pagination";
import InputMask from "react-input-mask";
import { connect } from "react-redux";
import PKHBtn from "~/components/common/atoms/Button";
import InputSearch from "~/components/common/atoms/InputSearch";
import Select from "~/components/common/atoms/Select";
import TagName from "~/components/common/atoms/TagName";
import Modal from "~/components/common/molecules/Modal";
import { openToast } from "~/components/common/molecules/ToastNotification";
import history from "~/history";
import { checkFilter, getNextStep } from "~/store/filter/actions";
import {
  resetFilterCheckData,
  setInsuranceChoice,
  uploadFile
} from "~/store/totalData/actions";
import { getRouteFollowPartnerId, name } from "~/utils/func";
import { client } from "~/utils/medproSDK";
import styles from "./style.module.scss";

class BHYTTraiTuyenDalieuhcm extends React.Component {
  constructor(props) {
    super(props);
    this.listInCorrectBHYT = [
      { id: "TAI_KHAM", name: `Tái khám theo lịch hẹn` },
      {
        id: "CHUYEN_TUYEN",
        name: `Chuyển tuyến`
      }
    ];

    const idParams = get(this.props, "match.params.id", 0);
    const bookingDate = get(this.props, "match.params.date");

    this.pageSize = 10;
    this.state = {
      disableBtnNext: false,
      id: idParams,
      bookingDate,
      NoiGioiThieu: "",
      dsNoiGioiThieu: [],
      totalRows: "",
      textSearch: "",
      textOtherInput: "",
      insuranceChoice: "TAI_KHAM",
      choice: [],
      isNoiGioiThieu: false,
      InfoLocation: "",
      otherInput: false,
      loading: false,
      currentPage: 1
    };
  }

  componentDidMount() {
    this.props.resetFilterCheckData();
  }

  handleContinueAction = () => {
    const { partnerId, type, getNextStep } = this.props;
    const { insuranceChoice, choice, id } = this.state;
    const choiceAfter = find(choice, { type: insuranceChoice });

    const errorImage =
      "Vui lòng tải lên giấy " +
      (insuranceChoice === "CHUYEN_TUYEN"
        ? "chuyển tuyến"
        : "hẹn hoặc giấy chuyển viện");

    this.props.form.validateFields((error, value) => {
      console.log(error, value);
      if (!error) {
        if (!choiceAfter) {
          openToast(errorImage, "error");
        } else {
          const objNext = {
            id,
            plattform: type,
            insurance: insuranceChoice,
            urlImg: choiceAfter.images.url,
            ...value
          };

          this.props.setInsuranceChoice(objNext);
          getNextStep(objNext);

          if (type === "desktop") {
            const route = getRouteFollowPartnerId(
              "/xac-nhan-thong-tin",
              partnerId
            );
            history.push(route);
          }
        }
      }
    });
  };

  handleFilterByKeyword = (value, list) => {
    const listTemp = list.filter(item => {
      const regex = new RegExp(name(value), "i");
      return name(item.name).match(regex);
    });
    return listTemp;
  };

  handleInputSearch = async e => {
    const value = e.target.value;
    this.setState({
      textSearch: name(value),
      loading: true
    });

    await this.getPartnerReferral(name(value));

    if (!value) {
      this.setState({
        dsNoiGioiThieu: []
      });
    }
  };

  handleBtnOke = () => {
    this.setState({
      InfoLocation: this.state.textOtherInput,
      isNoiGioiThieu: false
    });
  };

  handleOtherInput = e => {
    const value = e.target.value;
    this.setState({
      textOtherInput: name(value)
    });
  };

  async getPartnerReferral(keySearch, page = 1) {
    try {
      const response = await client.getHospitalReferral({
        pageSize: this.pageSize,
        pageIndex: page - 1,
        search: keySearch
      });
      this.setState({
        currentPage: page,
        dsNoiGioiThieu: response.data.rows,
        totalRows: response.data.totalRows,
        loading: false
      });
    } catch (error) {
      console.error(error);
    }
  }

  ContentModal = () => {
    const {
      dsNoiGioiThieu,
      textSearch,
      otherInput,
      textOtherInput,
      loading,
      totalRows,
      currentPage
    } = this.state;
    var stt = 1;
    const paginationConfig = {
      threeDots: true,
      activeBgColor: "#00BFFF",
      activeColor: "white",
      disabledColor: "green",
      totalPages: Math.ceil(totalRows / this.pageSize) || 1,
      currentPage: currentPage,
      showMax: 10,
      size: "sm",
      prevNext: true,
      center: true,
      onClick: async (page = 1) => {
        await this.getPartnerReferral(textSearch, page);
      }
    };
    return (
      <Fragment>
        <MDBCol xl={12} className={styles.colSearch}>
          <InputSearch
            title="Tìm nơi giới thiệu"
            icon={<i className="fal fa-search" />}
            onChange={this.handleInputSearch}
            value={textSearch}
            className="form_control"
          />
          {/*
          <PKHBtn create="create" className={styles.search}>
            Tìm kiếm
          </PKHBtn>
          */}

          <div
            className={cx(
              styles.form_group,
              "w-100 mt-3",
              otherInput ? "" : "d-none"
            )}
          >
            <label>
              Nơi giới thiệu khác <sup>*</sup>
            </label>
            <div className="d-flex">
              <input
                type="text"
                className="form-control form-control-sm"
                placeholder="Nhập nơi giới thiệu khác"
                onChange={this.handleOtherInput}
                value={textOtherInput}
              />
              <PKHBtn
                create="create"
                groupBtnInput
                className={styles.btnOke}
                onClick={this.handleBtnOke}
              >
                Đồng ý
              </PKHBtn>
            </div>
          </div>
        </MDBCol>
        <MDBCol xl={12} className={cx(styles.colListIntro, "table")}>
          <MDBTable bordered className={styles.tbl_Introduces}>
            <MDBTableHead>
              <tr className={styles.titleIntroduce}>
                <th className={styles.STT}>
                  <span>STT</span>
                </th>
                <th className={styles.location}>Nơi giới thiệu</th>
              </tr>
            </MDBTableHead>
            <MDBTableBody className={styles.tbody}>
              {loading && (
                <div className="loading absolute">
                  <MDBSpinner big />
                </div>
              )}
              {dsNoiGioiThieu.map((item, i) => {
                return (
                  <tr
                    key={uniqueId()}
                    className={styles.listIntroduce}
                    onClick={() => this.selectNoiGioiThieu(item)}
                  >
                    <td className={styles.STT}>
                      <span>{item.index}</span>
                    </td>
                    <td className={styles.location}>
                      <b>{item.name}</b>
                      <p>
                        <em>{item.address}</em>
                      </p>
                    </td>
                  </tr>
                );
              })}
              <tr
                key={uniqueId()}
                className={cx(styles.listIntroduce, "d-none")}
                onClick={() => this.selectNoiGioiThieu("other")}
              >
                <td className={styles.STT}>
                  <span>{stt++}</span>
                </td>
                <td className={styles.location}>
                  <b>Khác</b>
                </td>
              </tr>
            </MDBTableBody>
          </MDBTable>
          {totalRows !== "" ? <Pagination {...paginationConfig} /> : null}
        </MDBCol>
      </Fragment>
    );
  };

  selectNoiGioiThieu = e => {
    if (e === "other") {
      this.setState({ otherInput: true });
    } else {
      this.setState({
        InfoLocation: e.name,
        isNoiGioiThieu: !this.state.isNoiGioiThieu
      });
      this.props.form.setFieldsValue({
        noiGioiThieu: e.code
      });
    }
  };

  toggle = () => {
    this.setState({
      isNoiGioiThieu: !this.state.isNoiGioiThieu
      // otherInput: false
    });
  };

  handleChangeType = id => {
    this.setState({
      ...this.state,
      insuranceChoice: id
    });
  };

  handleFileInput = e => {
    const file = e.target.files[0];

    const { insuranceChoice, choice } = this.state;

    const haveErr = findIndex(choice, {
      images: { status: "error" },
      type: insuranceChoice
    });

    const isUp = findIndex(choice, { type: insuranceChoice });

    if (haveErr > -1) {
      choice.splice(haveErr, 1);

      this.setState({
        choice: choice
      });
    }

    if (file) {
      let obj;
      let error;
      if (file.size > 5000000) {
        error = "Vui lòng chọn file có kích thước nhỏ hơn 5Mbs !";
      }
      if (!file.name.toLowerCase().match(/\.(jpg|jpeg|png|pdf)$/)) {
        error = "Vui lòng chọn file hình ảnh (jpg, jpeg, png, pdf) !";
      }

      console.log("error :>> ", error);

      if (error) {
        obj = {
          type: insuranceChoice,
          disableBtnNext: true,
          images: {
            status: "error",
            key: uniqueId(),
            file: "",
            preview: "",
            error: error,
            url: ""
          }
        };
        if (isUp > -1) {
          choice.splice(haveErr, 1);
        }

        this.setState({
          choice: [...choice, obj]
        });
      } else {
        const pdf = file.name.match(/\.(pdf)$/);
        const reader = new FileReader();
        reader.onloadend = () => {
          const _pre = pdf ? "/assets/img/pdf.svg" : reader.result;
          const randomString = Math.random().toString(36);

          this.uploadFile(file).then(url => {
            const obj = {
              type: insuranceChoice,
              disableBtnNext: false,
              images: {
                key: randomString,
                file: file,
                preview: _pre,
                error: "",
                url: url
              }
            };

            if (url) {
              if (isUp > -1) {
                choice[isUp] = obj;
                this.setState({
                  choice: choice
                });
              } else {
                this.setState(prevState => ({
                  choice: [...prevState.choice, obj]
                }));
              }
            } else {
              this.setState({
                choice: [...this.state.choice]
              });
              openToast("Lỗi server tải lên hình ảnh", "error");
            }
          });
        };

        // upload
        reader.readAsDataURL(file);
      }
    }
  };

  handleRemoveImage = () => {
    const { insuranceChoice, choice } = this.state;
    const index = findIndex(choice, { type: insuranceChoice });
    choice.splice(index, 1);

    this.setState({
      choice: choice
    });
  };

  async uploadFile(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);
      const rs = await client.uploadFile(formData);
      return rs.data.url;
    } catch (error) {
      console.log("error uploadFile:>> ", error);
      return null;
    }
  }

  validReExamDate = async value => {
    try {
      const { days, service, type } = this.props;
      const { bookingDate } = this.state;

      const _date =
        type === "app" ? bookingDate : moment(days.date).toISOString();
      this.setState({ disableBtnNext: true });

      const response = await client.validReExamDate({
        serviceId: service.id,
        bookingDate: _date,
        reExamDate: value
      });
      console.log("validReExamDate :>> ", response.data);
      this.setState({ disableBtnNext: false });
      return response.data;
    } catch (error) {
      this.setState({ disableBtnNext: false });
      return error;
    }
  };

  validatorDay = async (rule, value, cb) => {
    console.log("value :>> ", value);

    const date = moment(value, "DD/MM/YYYY");

    console.log("date :>> ", date.toISOString());

    if (!date.isValid()) {
      cb("Vui lòng nhập đúng định dạng ngày tháng! Ví dụ: 19/07/2021");
    } else {
      if (value === "__/__/20__") {
        cb();
      } else {
        const valid = await this.validReExamDate(date.toISOString());
        const message = get(valid, "response.data.message");
        if (valid.isOk) {
          cb();
        }
        if (!isArray(message)) {
          cb(message);
        }

        cb();
      }
    }
  };

  validatorDayBHYT = (rule, value, cb) => {
    const date = moment(value, "DD/MM/YYYY");
    const cur = moment();
    const years = date.year();

    if (years < cur.year() - 5) {
      cb("Ngày tháng không hợp lệ. Vui lòng kiểm tra lại.");
    }

    if (value) {
      if (!date.isValid()) {
        cb("Vui lòng nhập đúng định dạng ngày tháng! Ví dụ: 19/07/2021");
      } else {
        cb();
      }
    } else {
      cb();
    }
  };

  validatorNoiGioiThieu = (rule, value, callback) => {
    console.log("value :>> ", value);

    if (Number(value) === 0 || !value) {
      callback("Vui lòng nơi giới thiệu!");
    } else {
      callback();
    }
  };

  render() {
    const imgDefault = "/assets/img/hoso.svg";
    const { insuranceChoice, choice, disableBtnNext } = this.state;
    const choiceAfter = find(choice, { type: insuranceChoice });
    const chuyentuyen = insuranceChoice === "CHUYEN_TUYEN";
    const taikham = insuranceChoice === "TAI_KHAM";

    const {
      form: { getFieldProps, getFieldError }
    } = this.props;

    const errorMessage = ele => {
      const field = getFieldError(ele);
      const mess = field ? field.join(", ") : null;
      return <span className={styles.alertSpan}>{mess}</span>;
    };

    return (
      <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
        <MDBCardHeader className={styles.panels_header}>
          <TagName
            element="h2"
            className={[
              "title_component",
              "title_choose_date",
              "space_between",
              "title_header_mobile"
            ]}
          >
            <span>
              Quy định dành cho bệnh nhân khám Bảo hiểm (Chuyển Tuyến)
            </span>
          </TagName>
        </MDBCardHeader>
        <MDBCardBody className={styles.card_body}>
          <form>
            <div className={styles.groupForm}>
              <div className={styles.left}>
                <div className={styles.form_group}>
                  <label>
                    <u>Vui lòng chọn hình thức bảo hiểm:</u>
                  </label>
                  <Select
                    data={this.listInCorrectBHYT}
                    onChange={this.handleChangeType}
                  />
                </div>

                {taikham ? (
                  <div className={styles.form_group}>
                    <label htmlFor="ngayHenTaiKham">
                      Ngày hẹn tái khám <sup>*</sup>
                    </label>

                    <InputMask
                      className="form-control"
                      {...getFieldProps("ngayHenTaiKham", {
                        rules: [{ validator: this.validatorDay }]
                      })}
                      placeholder="Ngày/Tháng/Năm"
                      mask="99/99/2099"
                      type="text"
                    />

                    {errorMessage("ngayHenTaiKham")}
                  </div>
                ) : null}

                {chuyentuyen ? (
                  <div className={cx(styles.form_group, styles.noiGioiThieu)}>
                    <label>
                      Nơi giới thiệu <sup>*</sup>
                    </label>
                    <input
                      readOnly
                      type="text"
                      className="form-control form-control-sm"
                      placeholder="Chọn nơi giới thiệu"
                      value={this.state.InfoLocation}
                      onClick={() => {
                        this.setState({
                          isNoiGioiThieu: !this.state.isNoiGioiThieu
                        });
                      }}
                    />
                    <input
                      readOnly
                      type="hidden"
                      className="form-control form-control-sm"
                      placeholder="Chọn nơi giới thiệu"
                      value={this.state.InfoLocation}
                      {...getFieldProps("noiGioiThieu", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorNoiGioiThieu }]
                      })}
                    />
                    {errorMessage("noiGioiThieu")}
                  </div>
                ) : null}
                <Modal
                  modal={this.state.isNoiGioiThieu}
                  title="Chọn nơi giới thiệu"
                  children={this.ContentModal()}
                  toggle={this.toggle}
                  centered
                  className="centered"
                />

                <div className={styles.list_type}>
                  <div className={styles.instruction}>
                    <p>
                      <b>Khi đến khám vui lòng mang theo:</b>
                      <br /> &emsp; - Thẻ BHYT.
                      <br /> &emsp; - Giấy chuyển tuyến hoặc giấy hẹn.
                      <br /> &emsp; - Giấy xác nhận tham gia bhyt liên tục 5 năm
                      (nếu có).
                      <br />
                      <b> Nếu đã cài đặt VssID, bệnh nhân chỉ cần mang:</b>
                      <br /> &emsp; - Giấy chuyển tuyến hoặc giấy hẹn.
                      <br /> &emsp; - Giấy xác nhận tham gia bhyt liên tục 5 năm
                      (nếu có).
                      <br />
                      <b>
                        Bệnh viện có quyền từ chối tiếp nhận trong các trường
                        hợp sau:
                      </b>
                      <br /> &emsp; - Thẻ báo giảm ngay ngày đăng ký khám.
                      <br /> &emsp; - Trường hợp tái khám trễ quá 10 ngày.
                      <br /> &emsp; - Giấy chuyển tuyến, giấy hẹn tái khám không
                      hợp lệ.
                      <br /> &emsp; - Thông tin bệnh nhân khai báo sai so với
                      thẻ BHYT.
                      <br /> &emsp; - Thẻ BHYT do bộ công an và quân đội cung
                      cấp và quản lý.
                    </p>
                  </div>
                </div>
              </div>
              <div className={cx(styles.right)}>
                <div className={styles.form_group}>
                  <label htmlFor="maCanNgheo">Mã cận nghèo (nếu có)</label>
                  <input
                    {...getFieldProps("maCanNgheo")}
                    type="text"
                    className="form-control"
                    placeholder="Vui lòng nhập mã cận nghèo"
                  />
                  {errorMessage("maCanNgheo")}
                </div>

                <div className={styles.form_group}>
                  <label htmlFor="thoiDiemBHYT">
                    Thời điểm thẻ BHYT đủ 5 năm liên tục (nếu có)
                  </label>
                  <InputMask
                    className="form-control"
                    {...getFieldProps("thoiDiemBHYT", {
                      rules: [{ validator: this.validatorDayBHYT }]
                    })}
                    placeholder="Ngày/Tháng/Năm"
                    mask="99/99/2099"
                    type="text"
                  />
                  {errorMessage("thoiDiemBHYT")}
                </div>

                <div className={styles.form_group}>
                  <label className="bolder">
                    Tải lên giấy {chuyentuyen ? "chuyển tuyến" : "hẹn "}
                    <sup>*</sup>
                  </label>

                  <figure className={styles.imgPreview}>
                    <img
                      src={
                        choiceAfter
                          ? choiceAfter.images.preview || imgDefault
                          : imgDefault
                      }
                      alt="hình ảnh"
                      className={cx(styles.imgFile, "img-thumbnail")}
                    />
                  </figure>

                  <div className={styles.txtError}>
                    <p>{choiceAfter ? choiceAfter.images.error : null}</p>
                  </div>
                  <div className={styles.groupInput}>
                    <label className={styles.fileInput}>
                      <span className={styles.txtlabel}>Chọn File</span>
                      <input
                        key={choiceAfter ? choiceAfter.images.key : 0}
                        className="d-none"
                        type="file"
                        onChange={this.handleFileInput}
                        accept="image/png, image/gif, image/jpeg, application/pdf"
                      />
                    </label>
                    <p className={styles.nameFile}>
                      {choiceAfter
                        ? choiceAfter.images.file.name
                        : "Tải lên file của bạn !"}
                    </p>

                    {choiceAfter ? (
                      <MDBIcon
                        far
                        icon="times-circle"
                        onClick={this.handleRemoveImage}
                      />
                    ) : null}
                  </div>
                  {errorMessage("urlImg")}
                </div>
              </div>
            </div>
            <div className={styles.next_prev}>
              <PKHBtn
                disabled={disableBtnNext}
                onClick={this.handleContinueAction}
                buttonArrow="buttonArrow"
                nextStep
                create="create"
              >
                Tiếp tục
              </PKHBtn>
            </div>
          </form>
        </MDBCardBody>
      </MDBCard>
    );
  }
}

const mapStateToProps = state => {
  const {
    totalData: { partnerId, days, service }
  } = state;
  return {
    partnerId,
    days,
    service
  };
};

export default connect(mapStateToProps, {
  getNextStep,
  uploadFile,
  setInsuranceChoice,
  resetFilterCheckData: resetFilterCheckData,
  checkFilter
})(createForm()(BHYTTraiTuyenDalieuhcm));
