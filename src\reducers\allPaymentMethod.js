// /* eslint-disable no-case-declarations */
// import { get } from "lodash";
// import * as types from "~/store/payment/paymentType";

// const initialState = {
//   loading: false,
//   data: [],
//   selectedMethod: {},
//   methodId: 0,
//   showModal: false,
//   errors: {},
//   price: {
//     subTotal: 0,
//     totalFee: 0,
//     grandTotal: 0
//   }
// };

// export default function allPaymnetMethod(state = initialState, action = {}) {
//   switch (action.type) {
//     case types.UMC_GET_ALL_PAYMENT_METHODS:
//       return {
//         ...state,
//         loading: true
//       };
//     case types.UMC_GET_ALL_PAYMENT_METHODS_SUCCESS:
//       return {
//         ...state,
//         loading: false,
//         data: [...action.data]
//       };
//     case types.UMC_GET_ALL_PAYMENT_METHODS_FAIL:
//       return {
//         ...state,
//         loading: false,
//         errors: action.errors
//       };
//     case types.GET_ALL_PAYMENT_METHOD_FEE:
//       return {
//         ...state,
//         loading: true
//       };
//     case types.TOGGLE_MODAL_PAYMENT_METHOD:
//       return {
//         ...state,
//         showModal: !state.showModal
//       };
//     case types.HIDE_MODAL_PAYMENT_METHOD:
//       return {
//         ...state,
//         showModal: false
//       };
//     case types.SELECTED_PAYMENT_METHOD:
//       const subTotal = get(action, "method.subTotal", 0);
//       const totalFee = get(action, "method.totalFee", 0);
//       const grandTotal = get(action, "method.grandTotal", 0);
//       return {
//         ...state,
//         selectedMethod: { ...action.method },
//         methodId: action.methodId,
//         showModal: false,
//         price: {
//           ...state.price,
//           subTotal,
//           totalFee,
//           grandTotal
//         }
//       };
//     case types.RESET_ALL_PROPS_PAYMENT_METHOD:
//       return {
//         ...state,
//         selectedMethod: {},
//         methodId: 0,
//         price: { ...initialState.price }
//       };
//     default:
//       return state;
//   }
// }
