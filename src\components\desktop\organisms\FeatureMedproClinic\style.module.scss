
@mixin description{
  .title{
    font-size: 22px;
    font-weight: bold;
    color: #1E56C5;
    margin: 0;
  }
  .value{
    margin-top: 12px;
    font-size: 16px;
    font-weight: 400;
  }
}
.Container{
  padding: 64px;
  min-width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;

  .rowFeature{
    max-width: 1440px;
    .colFeature{
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .Label{
        padding-bottom: 80px;
        p{
          display: flex;
          flex-direction: column;
          .title{
            font-size: 28px;
            font-weight: 600;
            color: #1E56C5;
            text-transform: uppercase;
            padding-bottom: 16px;
          }
          .value{
            font-size: 22px;
            font-weight: 400;
          }
        }
      }
      .listFeature{
        list-style: none;
        margin: 0;
        li{
          &:first-child{
            .itemFeature_odd{
              padding: 0;
            }
          }
          .itemFeature_even{
            display: flex;
            flex-direction: row-reverse;
            align-items: flex-end;
            margin-top: -23%;
            .description{
              width: 35%;
              display: flex;
              flex-direction: column;
              text-align: end;
              padding-right: 20px;
              margin: 0;
              @include description;
            }
          }
          .itemFeature_odd{
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            padding-top: 78px;
            .description{
              width: 35%;
              display: flex;
              flex-direction: column;
              padding-left: 20px;
              text-align: start;
              @include description;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px){
  .Container{
    padding: 64px 12px;
    .Label{
      text-align: justify;
      .title{
        font-size: 22px !important;
        text-align: center !important;
      }
      .value{
        font-size: 14px !important;
      }
    }
    .listFeature{
      padding: 0;
      li{
        .itemFeature_even, .itemFeature_odd{
          display: flex !important;
          flex-direction: column !important;
          margin: 0 !important;
          padding: 0 !important;
          .img{
            width: 100% !important;
            display: flex ;
            justify-content: center !important;
          }
          .description{
            width: 100% !important;
            text-align: justify !important;
            padding: 16px 0 20px !important;
            .title{
              font-size: 18px !important;
            }
          }
        }
      }
    }
  }
}

@media (min-width: 768px) AND (max-width: 1024px){
  .Label{
    text-align: left !important;
  }
  .itemFeature_even, .itemFeature_odd{
    margin: 0 !important;
    display: flex;
    align-items: center !important;
    .description{
      width: 100% !important;
    }
  }
}


