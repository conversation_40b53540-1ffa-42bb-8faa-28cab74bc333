import cx from "classnames";
import {
  MDBBtn,
  MDBCol,
  MDBIcon,
  MDBInputGroup,
  MDBModal,
  MDBModalBody,
  MDBModalHeader,
  MDBRow
} from "mdbreact";
import moment from "moment";
import React, { Component } from "react";
import { connect } from "react-redux";
import { getTransactionInfoCSKH } from "~/store/customerService/customerServiceActions";
import { getFormatMoney } from "~/utils/getFormatMoney";
import styles from "./style.module.scss";
class ModalCheckTransaction extends Component {
  state = {
    inputTransactionId: ""
  };

  handlerOnchangeInputTransaction = event => {
    const inputTransactionId = event.target.value;
    this.setState({ inputTransactionId });
  };

  toggleClose = () => {
    this.setState({
      inputTransactionId: ""
    });
    this.props.toggle();
  };

  checkHiddenInfo = () => {
    const { transactionBill, messageGetTransactionError } = this.props;
    if (transactionBill === null) {
      return "d-none";
    }
    if (messageGetTransactionError) {
      return "d-none";
    }
    return "";
  };

  render() {
    const { transactionBill, messageGetTransactionError } = this.props;

    const { inputTransactionId } = this.state;

    return (
      <React.Fragment>
        <MDBModal
          isOpen={this.props.isOpen}
          toggle={() => this.props.toggle()}
          backdrop={false}
        >
          <MDBModalHeader toggle={() => this.toggleClose()}>
            Kiểm tra mã giao dịch
          </MDBModalHeader>
          <MDBModalBody>
            <MDBRow>
              <MDBCol>
                <MDBInputGroup
                  size="sm"
                  value={inputTransactionId}
                  onChange={event => {
                    this.handlerOnchangeInputTransaction(event);
                  }}
                  onKeyDown={event => {
                    if (event.keyCode === 13) {
                      event.preventDefault();
                      this.props.getTransactionInfoCSKH(inputTransactionId);
                    }
                  }}
                  hint="Mã giao dịch"
                  containerClassName=""
                  append={
                    <MDBBtn
                      size="sm"
                      onClick={() => {
                        this.props.getTransactionInfoCSKH(inputTransactionId);
                      }}
                      color="ins"
                      className="m-0 px-3 py-2 z-depth-0"
                    >
                      <MDBIcon icon="search" />
                    </MDBBtn>
                  }
                />
              </MDBCol>
            </MDBRow>
            <MDBRow className="mt-3">
              <MDBCol>
                <span className="text-danger">
                  {messageGetTransactionError}
                </span>
                <ul
                  className={cx(
                    styles.listInfoTransaction,
                    " list-unstyled border rounded-3 p-2  ",
                    this.checkHiddenInfo()
                  )}
                >
                  <li>
                    <span className={styles.title}>Bệnh viên:</span>
                    <span
                      className={
                        (styles.value,
                        "font-weight-bold text-danger text-center")
                      }
                    >
                      {transactionBill?.bookingInfo.partner.name || ""}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>Mã phiếu:</span>
                    <span
                      className={(styles.value, "font-weight-bold text-danger")}
                    >
                      {transactionBill?.bookingInfo.bookingCode || ""}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>
                      Phương thức thanh toán:
                    </span>
                    <span className={styles.value}>
                      {transactionBill?.payment.paymentMethod || ""}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>Cổng thanh toán:</span>
                    <span className={styles.value}>
                      {transactionBill?.payment.gatewayId || ""}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>Trạng thái thanh toán:</span>
                    <span className={styles.value}>
                      {transactionBill?.bookingInfo?.description || ""}
                    </span>
                  </li>

                  <li>
                    <span className={styles.title}>Trạng thái đồng bộ:</span>
                    <span
                      className={
                        (styles.value,
                        transactionBill?.syncStatus === "success"
                          ? "text-success"
                          : "text-danger")
                      }
                    >
                      {transactionBill?.syncStatus === "success"
                        ? "Đã đồng bộ"
                        : "Chưa đồng bộ"}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>Họ tên:</span>
                    <span className={styles.value}>
                      {transactionBill?.patientInfo.surname +
                        " " +
                        transactionBill?.patientInfo.name}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>Ngày đặt:</span>
                    <span className={styles.value}>
                      {moment(transactionBill?.payment.paymentTime).format(
                        "DD/MM/YYYY HH:mm"
                      )}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>Ngày Khám:</span>
                    <span className={(styles.value, "text-warning")}>
                      {moment(transactionBill?.date).format("DD/MM/YYYY HH:mm")}
                    </span>
                  </li>
                  <li className={transactionBill?.canceledDate ? "" : "d-none"}>
                    <span className={styles.title}>Ngày hủy:</span>
                    <span className={(styles.value, "text-warning")}>
                      {transactionBill?.canceledDate
                        ? moment(transactionBill?.canceledDate).format(
                            "DD/MM/YYYY HH:mm"
                          )
                        : null}
                    </span>
                  </li>

                  <li>
                    <span className={styles.title}>Năm sinh:</span>
                    <span className={styles.value}>
                      {" "}
                      {transactionBill?.patientInfo.birthyear}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>Giới tính:</span>
                    <span className={styles.value}>
                      {" "}
                      {transactionBill?.patientInfo.sex === 1 ? "Nam" : "Nữ"}
                    </span>
                  </li>
                  <li>
                    <span className={styles.title}>Tiền khám:</span>
                    <span className={styles.value}>
                      {" "}
                      {getFormatMoney(transactionBill?.payment.subTotal)} VND
                    </span>
                  </li>
                </ul>
              </MDBCol>
            </MDBRow>
          </MDBModalBody>
        </MDBModal>
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { transactionBill, messageGetTransactionError }
  } = state;
  return {
    transactionBill,
    messageGetTransactionError
  };
};

const mapDispatchToProps = dispatch => ({
  getTransactionInfoCSKH: transactionId => {
    dispatch(getTransactionInfoCSKH(transactionId));
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ModalCheckTransaction);
