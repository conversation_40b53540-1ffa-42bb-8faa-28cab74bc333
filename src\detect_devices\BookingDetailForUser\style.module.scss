.wrapper_bill {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  background-color: whitesmoke;

  .groupBtn {
    display: flex;
    width: fit-content;
    margin: auto;
    margin-bottom: 2rem;

    .btnRepayment {
      border-radius: 2rem;
      width: 100%;
      padding: 10px 15px;
      white-space: nowrap;
      max-width: fit-content;
      font-size: 12px;
    }
  }

  .wrapper_text {
    width: 100%;
    margin-top: 30px;
    text-align: center;
    margin-bottom: 3rem;
  }

  .wrapper_print {
    padding: 15px;
    max-width: 360px;
    color: #12263f;
    font-size: 14px;
    margin: 0;

    @media screen and (max-width: 576px) {
      margin: 0px;
      max-width: unset;
      width: 100%;
    }
  }
}
.confirmPhone {
  position: relative;
  height: 190px;
  @media (max-width: 576px) {
    height: 235px;
  }
  .alertSpan {
    display: block;
    color: red;
    font-size: 13px;
    margin: 0.5rem 0;
    height: 30px;
  }
  .btnConfirm {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: #007bff;
    color: white;
    border: none;
    padding: 6px 16px;
    border-radius: 12px;
  }
  .note {
    font-style: italic;
    span {
      color: red;
    }
  }
}
.tabsBooking {
  padding: 10px;
  margin: 0;
  border: 0;
  width: 100%;
  display: flex;
  justify-content: space-around;

  span {
    padding: 12px 22px 10px;
    font-size: 16px;
    color: #003553;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    &:hover {
      cursor: pointer;
    }
    @media (max-width: 576px) {
      font-size: 16px;
    }
  }
}
.isDots {
  position: relative;
  &::after {
    content: " ";
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    top: 0;
    right: 5px;
    background-color: red;
  }
}
.NormalMedpro,
.MedproCare {
  span {
    color: #00b5f1 !important;
    border-bottom-color: #00b5f1 !important;
    border-radius: 14px 14px 0 0;
  }
}
.wapper_loading {
  min-height: 250px;
  position: relative;
  text-align: center;
}

.body {
  .description {
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 8px;
  }
}
.infoPayment {
  p {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0;
    span {
      font-family: Inter;
      font-weight: 400;
      font-size: 16px;
      line-height: 23px;
    }
  }
  margin-bottom: 8px;
  .totalFee {
    span {
      font-weight: 600;
      color: #24313d;
    }
  }
}
.note {
  font-weight: 400;
  font-style: italic;
  font-size: 14px;
  line-height: 18px;

  color: #f5222d;
  i {
    margin-right: 4px;
    color: #f5222d;
  }
}
