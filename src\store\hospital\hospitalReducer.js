import * as types from "./hospitalTypes";

const initialState = {
  hospitalList: {
    loading: false,
    data: [],
    error: ""
  },
  selectedHospital: "",
  selectedHospitalBefore: "",
  hospitalListByFeatureId: {},
  searchPattern: "",
  featureList: {
    loading: false,
    data: [],
    error: ""
  },
  flowForward: true
};

export default function hospitalReducer(state = initialState, action = {}) {
  switch (action.type) {
    case types.RESET_LIST_HOSPITAL:
      return {
        ...state,
        hospitalList: { ...initialState.hospitalList }
      };

    case types.HOSPITAL_LIST_IS_LOADING:
      return {
        ...state,
        hospitalList: { ...state.hospitalList, loading: true }
      };

    case types.REQUEST_HOSPITAL_LIST:
      return {
        ...state,
        loading: true
      };
    case types.REQUEST_HOSPITAL_LIST_SUCCESS:
      return {
        ...state,
        hospitalList: {
          loading: false,
          data: action.data,
          error: ""
        }
      };
    case types.REQUEST_HOSPITAL_LIST_FAIL:
      return {
        ...state,
        hospitalList: {
          ...state.hospitalList,
          loading: false,
          error: action.error
        }
      };
    case types.SELECT_HOSPITAL_BY_ID: {
      const selectedHospital = state.hospitalList.data.find(
        hospital => hospital.partnerId === action.id
      );
      return { ...state, selectedHospital };
    }

    case types.SELECT_HOSPITAL_BEFORE_BY_ID: {
      const selectedHospitalBefore = state.hospitalList.data.find(
        hospital => hospital.partnerId === action.id
      );
      return { ...state, selectedHospitalBefore };
    }

    case types.RESET_SELECTED_HOSPITAL:
      return { ...state, selectedHospital: "" };

    case types.HOSPITAL_LIST_BY_FEATURE_ID_IS_LOADING:
      return {
        ...state,
        hospitalListByFeatureId: {
          ...state.hospitalListByFeatureId,
          [action.id]: { loading: true }
        }
      };

    case types.REQUEST_HOSPITAL_LIST_BY_FEATURE_ID_SUCCESS:
      return {
        ...state,
        hospitalListByFeatureId: {
          ...state.hospitalListByFeatureId,
          [action.id]: { loading: false, data: action.data, error: "" }
        }
      };

    case types.REQUEST_HOSPITAL_LIST_BY_FEATURE_ID_FAIL:
      return {
        ...state,
        hospitalListByFeatureId: {
          ...state.hospitalListByFeatureId,
          [action.id]: { loading: false, error: action.error }
        }
      };
    case types.SET_SEARCH_PATTERN_HOSPITAL:
      return {
        ...state,
        searchPattern: action.pattern
      };

    // Dat09 04-02-2020
    case types.REQUEST_FEATURE_LIST_BY_HOSPITAL_ID:
      return {
        ...state,
        featureList: { ...state.featureList, loading: true }
      };

    case types.REQUEST_FEATURE_LIST_BY_HOSPITAL_ID_SUCCESS:
      return {
        ...state,
        featureList: { ...state.featureList, data: action.data, loading: false }
      };

    case types.RESET_FEATURE_LIST_BY_HOSPITAL:
      return {
        ...state,
        featureList: []
      };

    case types.SWITCH_SELECT_FLOW:
      return {
        ...state,
        flowForward: action.payload
      };

    default:
      return state;
  }
}
