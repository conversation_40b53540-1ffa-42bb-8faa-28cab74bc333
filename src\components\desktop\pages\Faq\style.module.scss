@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/card.scss";

.img_parallax {
  padding-top: 60px !important;
  padding-bottom: 60px !important;
  background-image: linear-gradient(45deg, #6a78d1, #00a4bd);
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  h1 {
    color: #fff !important;
    margin-bottom: 20px !important;
  }
  p {
    color: #fff !important;
    text-align: center;
  }
  overflow: unset;
}

.wapper_page_head {
  width: 100%;
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  .suggested_faq_list {
    position: relative;
    width: 100%;
    .card {
      position: absolute;
      top: -20px;
      padding: 25px;
      left: 0;
      z-index: 99;
      width: 100%;
      min-height: 75px;
      max-height: 300px;
      overflow: scroll;
      overflow-x: hidden;
      border: none;
      h4 {
        text-align: left;
        font-size: 20px;
        font-weight: 500;
      }
      ul {
        border: none;
        li {
          border: none;
          padding: 0;
          margin: 10px 0;
          text-align: left;
          &:hover {
            cursor: pointer;
            color: #0352cc;
          }
        }
      }
    }
    .suggested {
      overflow: hidden;
    }
  }
}
.wapper_page_inner {
  max-width: 700px;
  margin: 60px auto;
}
.card_item {
  padding: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: unset;
  &:hover {
    // border: 1px solid rgba(136,149,162,0.2) !important;
    background-color: #fcfcfc;
  }
  .icons {
    width: 120px;
    text-align: center;
    height: 100%;
    margin-right: 20px;
    svg {
      width: 48px;
      margin: 0 auto;
    }
  }
  .content {
    width: 100%;
    .card_title {
      font-size: 18px;
      color: #0352cc;
      font-weight: 500;
      margin-bottom: 8px;
      a {
        color: inherit;
      }
    }
    .author {
      margin-top: 20px;
      display: flex;
      align-items: center;
      font-size: 12px;
      strong {
        color: #0352cc;
      }
      .avata {
        width: 32px;
        height: 32px;
        margin-right: 15px;
      }
    }
  }
  .count_question {
    font-size: 12px;
  }
}
