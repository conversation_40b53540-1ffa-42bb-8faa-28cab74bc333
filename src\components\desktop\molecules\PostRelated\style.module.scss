@import "src/assets/scss/custom-variables.scss";
.tag_news {
  text-align: left;
  text-transform: uppercase;
  font-weight: bold;
  @media screen and (max-width: 768px) {
    text-align: center;
  }
}

.card_group {
  display: flex;
  .card_news {
    .view {
      max-height: 170px !important;
    }
    .card_body {
      padding: 15px 0;
      .title {
        text-overflow: ellipsis;
        text-align: justify;
        text-justify: distribute;
        text-align-last: left;
        font-size: 0.875rem;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3; /* number of lines to show */
        -webkit-box-orient: vertical;
      }
      .tag {
        font-size: 0.75rem;
        color: #777;
        overflow: hidden;
        margin-bottom: 5px;
      }
    }
  }
}
@media #{$medium-and-down} {
  .card_group {
    display: table-row;
    .card_news {
      flex-direction: revert;
      height: 120px;
      margin-bottom: 1rem;
      .view {
        max-height: 120px !important;
        max-width: 150px;
      }
      .card_body {
        padding: 0 0 0 15px !important;
      }
    }
  }
}
