@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-mobile/form.scss";
@import "src/assets/scss/extend/placeholder.scss";

.list_hotline {
  display: flex;
  flex-wrap: wrap;
  margin-left: -30px;
  padding: 0 15px;
  h2 {
    color: #0e2b5c !important;
    font-size: 1.4rem;
    font-weight: 500;
    @media #{$medium-and-down} {
      font-size: 1rem;
    }
  }
}

.img_parallax {
  height: unset !important;
  padding-top: 60px !important;
  padding-bottom: 80px !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  // height: 300px !important;
  h1 {
    margin-bottom: 20px !important;
    color: #ffffff !important;
    span {
      font-size: 2.2rem;
    }
  }
  p {
    color: #fff;
  }
  @media #{$medium-and-down} {
    margin-top: 60px;
  }
}

.contact_page {
  position: relative;
  background: #fff;
  margin: -50px auto 120px;
  border-radius: 6px;
  min-height: 300px;
  padding: 30px;
  max-width: 1140px;
  @media #{$medium-and-down} {
    margin-top: 30px;
    padding: 0;
  }
}

.desc {
  margin: 0 0 60px 0;
  p {
    font-size: 1rem;
    margin-bottom: 0px;
    text-align: center;
    color: white;
  }
}

.form_contact_info {
  margin-bottom: 20px;
  &.form_contact_custom {
    display: flex;
    flex-wrap: wrap;
    > div {
      width: 50%;
      padding-right: 15px;
      margin-bottom: 15px;
      @media #{$medium-and-down} {
        width: 100%;
      }
    }
  }
}
.contact_column {
  .contact_inner_box {
    ul {
      padding: 0;
      list-style: none;
      li {
        font-size: 14px;
        line-height: 25px;
        color: #12263f;
        font-weight: 700;
        margin-bottom: 35px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        span {
          font-size: 30px;
          line-height: 25px;
          margin-right: 20px;
        }
        p {
          display: block;
          margin-bottom: 0px;
          font-size: 13px;
          line-height: 25px;
        }
      }
    }
    .addressLink {
      color: #12263f;
      border: none;
      text-decoration: none;
      font-weight: 400 !important;
    }
  }
}
span.text_info {
  position: relative;
  display: block;
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 700;
  margin-bottom: 20px;
}
h3.add_info {
  position: relative;
  line-height: 48px;
  color: #12263f;
  margin-bottom: 10px;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 20px;
}
.form_group {
  font-size: 0.875rem;
  position: relative;
  label {
    font-weight: 700;
  }
  input,
  textarea {
    font-size: 0.875rem;
    margin-bottom: 5px;
  }
  sup {
    color: #f62c2c;
  }
  textarea {
    &::placeholder {
      font-size: 0.875rem;
    }
  }
}

.form_select {
  select {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }
}
.form_button {
  width: 100%;
  font-size: 16px !important;
  background: #0352cc;
  border: 2px solid #fff !important;
  color: #fff !important;
  font-weight: 700;
  font-size: 0.875rem;
  padding: 10px 40px !important;
}

.input_warning {
  font-size: 14px;
  color: red;
}
.w_100 {
  width: 100% !important;
}
.btn_send_support {
  text-align: right;
  button {
    display: inline-block;
    vertical-align: middle;
    padding: 8px 60px;
  }
}

//  -------------------Minh anh
.bannerContact_minhanh {
  .list_hotline {
    & > div {
      & > div {
        h2,
        a {
          color: #db2233 !important;
        }
      }
    }
  }
  .contact_page {
    .contact_column {
      .contact_inner_box {
        .text_info {
          color: #db2233 !important;
        }
        ul {
          li {
            span {
              color: #db2032;
            }
          }
        }
      }
    }
    .form_column {
      .form_contact_info {
        .btn_send_support {
          button {
            background: #db2233 !important;
          }
        }
      }
    }
  }
}
