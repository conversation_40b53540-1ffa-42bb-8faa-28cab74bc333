/* eslint-disable no-case-declarations */
import { find } from "lodash";
import * as types from "./customerServiceTypes";

const initialState = {
  cskhToken: null,

  searchedPhone: "",
  transactionBill: null,

  phoneSearchResult: {},
  selectedBill: null,
  selectedPatient: null,
  supportingAccount: null,

  loading: false,
  loadingTicket: false,
  loadingPatient: false,

  messageGetTransactionError: "",
  historyBooking: null
};

const customerServiceReducer = (state = initialState, action) => {
  // console.log(action.type);
  switch (action.type) {
    case types.GET_HISTORY_BOOKING_SUCCESS:
      return {
        ...state,
        historyBooking: action.value
      };

    case types.RESET_BOOKING_INFO_CSKH:
      return {
        ...state,
        selectedBill: null
      };

    case types.RESET_BOOKING_BY_PATIENT_CS:
      return {
        ...state,
        phoneSearchResult: null,
        selectedBill: null
      };

    case types.RESET_ALL_CS:
      return {
        ...initialState,
        searchedPhone: state.searchedPhone
      };

    case types.SET_CS_TOKEN:
      return {
        ...state,
        cskhToken: action.value
      };

    case types.GET_BOOKING_INFO_CSKH:
      return {
        ...state,
        selectedBill: null
      };

    case types.REQUEST_CARE247_INDEPENDENT_CSKH:
      return {
        ...state,
        bookingId: null
      };
    case types.GET_REQUEST_CARE247_INDEPENDENT_CSKH_SUCCESS:
      return {
        ...state,
        dataIndependent: action.payload,
        loadingTicket: false
      };
    case types.GET_REQUEST_CARE247_INDEPENDENT_CSKH_FAIL:
      return {
        ...state,
        dataIndependent: {},
        loadingTicket: false
      };
    case types.GET_BOOKING_INFO_CSKH_SUCCESS:
      return {
        ...state,
        selectedBill: action.payload
      };

    case types.CHANGE_HOSPITAL_CSKH:
      return {
        ...state,
        selectedHospital: action.payload
      };
    case types.GET_PATIENTS_BY_PHONE_CSKH:
      return {
        ...state,
        searchedPhone: action.phone,
        phoneSearchResult: {},
        loadingPatient: true
      };
    case types.GET_PATIENTS_BY_PHONE_CSKH_SUCCESS:
      return {
        ...state,
        phoneSearchResult: action.payload,
        supportingAccount: action.payload.user,
        cskhToken: action.payload.secretKey,
        loadingPatient: false
      };
    case types.GET_PATIENTS_BY_PHONE_CSKH_FAIL:
      return {
        ...state,
        loadingPatient: false
      };

    case types.SELECT_PATIENT_CSKH_BY_ID:
      const patients = state?.phoneSearchResult?.patients;
      const findIdPatient = find(patients, { id: action.value });

      if (findIdPatient) {
        return {
          ...state,
          selectedPatient: findIdPatient
        };
      } else {
        return { ...state };
      }

    case types.SELECT_PATIENT_CSKH:
      return {
        ...state,
        selectedPatient: action.payload
      };
    case types.CLEAR_SELECTED_PATIENT:
      return {
        ...state,
        selectedPatient: null
      };
    case types.UPDATE_CANCEL_BILL:
      return {
        ...state,
        phoneSearchResult: {
          ...state.phoneSearchResult,
          bookings: action.booking
        }
      };
    case types.REQUEST_BOOKINGS_BY_PATIENT_CSKH_SUCCESS:
      return {
        ...state,
        phoneSearchResult: {
          ...state.phoneSearchResult,
          bookings: action.payload
        }
      };
    case types.GET_TRANSACTIONID_INFO_CSKH_SUCCESS:
      return {
        ...state,
        transactionBill: action.transactionBill,
        messageGetTransactionError: ""
      };

    case types.GET_TRANSACTIONID_INFO_CSKH_FAIL:
      return {
        ...state,
        messageGetTransactionError: action.messageGetTransactionError,
        transactionBill: null
      };

    case types.RESET_TRANSACTIONID_INFO_CSKH:
      return {
        ...state,
        transactionBill: null,
        messageGetTransactionError: ""
      };
    default:
      return state;
  }
};

export default customerServiceReducer;
