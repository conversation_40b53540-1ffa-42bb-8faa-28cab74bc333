/* eslint-disable react/no-did-update-set-state */
import React, { Component, Fragment } from "react";
import {
  MDBContainer,
  MDBRow,
  MDBCol,
  MDBCard,
  MDBCardBody,
  MDBListGroup,
  MDBListGroupItem,
  MDBTooltip
} from "mdbreact";
import { Link } from "react-router-dom";
import cx from "classnames";
import styles from "./style.module.scss";
import DetailedExaminationCard from "~/components/mobile/molecules/DetailedExaminationCard";
import MedicalBillOfPatientDetail from "~/components/mobile/molecules/MedicalBillOfPatientDetail";
import BottomNavigation from "~/components/mobile/atoms/BottomNavigation";
import { getInfoFollowHospital } from "~/utils/flowRouting";
import { getMedicalBillStatus } from "~/utils/func";
import { Facebook } from "react-content-loader";

class MedicalBillDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      pathname: ""
    };
  }

  componentDidUpdate(prevProps) {
    if (this.props.location.pathname !== prevProps.location.pathname) {
      this.setState({
        pathname: this.props.location.pathname
      });
    }
  }

  render() {
    const { medicalBillDetail, loading } = this.props;
    const {
      transaction_code_gd: billCode,
      hospital,
      booking_date: bookingDate,
      status,
      id,
      description
    } = medicalBillDetail;

    const hospitalID = hospital && hospital.id ? hospital.id : 0;
    const hospitalName = hospital && hospital.name ? hospital.name : "";

    const { logoInBill, mainColor } = getInfoFollowHospital(hospitalID);

    const correctedStatus = getMedicalBillStatus(status, bookingDate);

    const urlInPhieuKhamBenh = `${
      getInfoFollowHospital(hospitalID).router
    }/in-phieu-kham-benh/${id}`;

    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow>
            <MDBCol md="12" lg="4" className="mb-5">
              <MDBListGroup
                className={cx(styles.nav_tab_info, styles.nav_tab_info_detail)}
              >
                <MDBListGroupItem>
                  <Link
                    to={{
                      pathname: "/user",
                      state: { activeItem: 1 }
                    }}
                  >
                    <i className="far fa-address-book" />
                    Hồ sơ bệnh nhân
                  </Link>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <Link
                    className={styles.active}
                    to={{
                      pathname: "/user",
                      state: { activeItem: 2 }
                    }}
                  >
                    <i className="fas fa-file-medical" />
                    Phiếu khám bệnh
                  </Link>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <Link
                    to={{
                      pathname: "/user",
                      state: { activeItem: 3 }
                    }}
                  >
                    <i className="far fa-bell" />
                    Thông báo{" "}
                    {this.props.total_new ? (
                      <span className={styles.count}>
                        {this.props.total_new}
                      </span>
                    ) : null}
                  </Link>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <Link
                    to={{
                      pathname: "/user",
                      state: { activeItem: 4 }
                    }}
                  >
                    <i className="fal fa-credit-card" />
                    Lịch sử thanh toán viện phí
                  </Link>
                </MDBListGroupItem>
              </MDBListGroup>
            </MDBCol>
            <MDBCol md="12" lg="8">
              {loading ? (
                <Facebook />
              ) : (
                <Fragment>
                  <MDBRow>
                    <MDBCol>
                      <div className={styles.back_btn}>
                        <Link
                          to={{
                            pathname: "/user",
                            state: { activeItem: 2 }
                          }}
                          className={cx(styles.button, styles.back_children)}
                        >
                          Quay lại
                        </Link>
                      </div>
                    </MDBCol>
                  </MDBRow>
                  <div className={cx("md-5", styles.header)}>
                    <div className={styles.header_body}>
                      <MDBRow className={cx("align-items-center")}>
                        <MDBCol>
                          <h6 className={styles.header_pretitle}>
                            Thông tin phiếu khám bệnh
                          </h6>
                          <h1 className={styles.header_title}>
                            Mã phiếu {billCode}
                          </h1>
                        </MDBCol>
                        <MDBCol className={cx("col-auto")}>
                          <Link
                            className={cx(styles.button, styles.btn_invoice)}
                            target="_blank"
                            to={urlInPhieuKhamBenh}
                          >
                            Xem phiếu
                          </Link>
                        </MDBCol>
                      </MDBRow>
                    </div>
                  </div>
                  <MDBCard className={styles.card_invoice}>
                    <MDBCardBody>
                      <MDBRow>
                        <MDBCol className={cx("text-right", "mb-2")}>
                          <span
                            className={
                              correctedStatus === "Chưa khám"
                                ? styles.status_bill_notdone
                                : correctedStatus === "Đã khám"
                                ? styles.status_bill_done
                                : styles.status_bill_overtime
                            }
                          >
                            {correctedStatus}
                          </span>
                        </MDBCol>
                      </MDBRow>
                      <MDBRow>
                        <MDBCol className={cx("text-center")}>
                          <img
                            src={logoInBill}
                            className={cx("mb-2", styles.logo_brand)}
                            alt=""
                          />
                          <h2 className={cx("mb-1", styles.band_name)}>
                            Phiếu khám bệnh từ{" "}
                            <strong style={{ color: mainColor }}>
                              {hospitalName}
                            </strong>
                          </h2>
                          <p className={cx("mb-5", styles.text_muted)}>
                            Mã phiếu {billCode}{" "}
                            <MDBTooltip placement="top" domElement>
                              <span>
                                <i className="fas fa-question-circle" />
                              </span>
                              <span>Mã phiếu dùng để xác minh giao dịch</span>
                            </MDBTooltip>
                          </p>
                        </MDBCol>
                      </MDBRow>
                      <MDBRow>
                        <MDBCol>
                          {medicalBillDetail && (
                            <Fragment>
                              <DetailedExaminationCard
                                medicalBillDetail={medicalBillDetail}
                              />
                            </Fragment>
                          )}
                        </MDBCol>
                      </MDBRow>
                      <hr className={cx("my-5")} />
                      <div className={styles.text_note}>
                        <h6>Ghi chú</h6>
                        <p>{description}</p>
                      </div>
                    </MDBCardBody>
                  </MDBCard>
                  <MDBCard className={cx(styles.card_invoice, "mb-5")}>
                    <div className={cx(styles.sub_card_header, "mb-2")}>
                      Thông tin bệnh nhân
                    </div>
                    <MDBCardBody className={styles.card_body}>
                      <MedicalBillOfPatientDetail
                        medicalBillDetail={medicalBillDetail}
                      />
                    </MDBCardBody>
                    <BottomNavigation
                      onCancel={this.props.handleToggleCancelModal}
                      onResendSMS={this.props.handleToggleResendSMSModal}
                    />
                  </MDBCard>
                </Fragment>
              )}
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default MedicalBillDetail;
