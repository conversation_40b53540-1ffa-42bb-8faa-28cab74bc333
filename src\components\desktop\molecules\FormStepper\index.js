import cx from "classnames";
import { find, debounce } from "lodash";
import { MD<PERSON>ni<PERSON>, MDBSpinner } from "mdbreact";
import moment from "moment";
import { createForm, createForm<PERSON>ield } from "rc-form";
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import v from "validator";
import PKHBtn from "~/components/common/atoms/Button";
import Select from "~/components/common/atoms/Select";
import SelectSearch from "~/components/common/atoms/SelectSearch";
import TagName from "~/components/common/atoms/TagName";
import Modal from "~/components/common/molecules/Modal";
import * as p from "~/store/patientForm/patientFormAction";
import * as rs from "~/store/resource/resourceAction";
import { handleAge } from "~/utils/constants";
import { getInfoFollowHospital } from "~/utils/flowRouting";
import { client } from "~/utils/medproSDK";
import * as fnc from "~/utils/func";
import styles from "./style.module.scss";

class PatientFormStepper extends Component {
  constructor(props) {
    super(props);
    this.state = {
      sexs: [
        {
          name: "<PERSON>ọn giới tính",
          id: -1
        },
        {
          name: "Nam",
          id: 1
        },
        {
          name: "Nữ",
          id: 0
        }
      ],
      showModalConfirmPhone: false,
      isFunctionProcess: false,
      isMessageError: false,
      textMessageError: "",
      phone: "",
      isExactPhone: "",
      errorCheckPhone: "",
      scannedText: "",
      // Add fallback required fields
      fallbackRequiredFields: [
        "fullname",
        "birthdate",
        "sex",
        "city_id",
        "district_id",
        "ward_id",
        "address"
      ]
    };

    this.defaultCountry = getInfoFollowHospital().defaultCountry;
    this.debouncedHandleScannerChange = debounce(
      this.handleScannerChangeInternal,
      400
    );
    this.timeoutScannerChange = null;
  }

  validateName = (rule, value, callback) => {
    // Use the new required field validation
    this.validateRequiredField(
      "fullname",
      rule,
      value,
      callback,
      "Vui lòng nhập đúng họ tên!"
    );
  };

  validateCmndRequest = (rule, value, callback) => {
    this.validateRequiredField(
      "cmnd",
      rule,
      value,
      callback,
      "Vui lòng nhập mã định danh/CCCD/Passport!"
    );
  };

  validatePhone = (rule, value, callback) => {
   const {isChildren, isJunior} =   this.checkOverAge()
    if (!isChildren && !isJunior) {
      this.validateRequiredField(
        "mobile",
        rule,
        value,
        callback,
        "Vui lòng nhập số điện thoại!"
      );
    if (v.isMobilePhone(value, "vi-VN")) {
      callback();
    } else {
      callback("Vui lòng nhập đúng định dạng!");
    }
    }
  };

  validatorRelativePhone = (rule, value, callback) => {
    if (!value) {
      callback("Vui lòng nhập số điện thoại!");
    }
    if (v.isMobilePhone(value, "vi-VN")) {
      callback();
    } else {
      callback("Vui lòng nhập đúng định dạng!");
    }
  };

  validatorEmail = (rule, value, callback) => {
    if (!value) {
      callback();
    } else {
      if (!v.isEmail(value?.trim())) {
        callback("Vui lòng nhập đúng định dạng email !");
      } else callback();
    }
  };

  validatorCmnd = (rule, value, callback) => {
    if (value?.trim()) {
      const patternCmnd = /^[a-zA-Z0-9]+$/g;
      if (!patternCmnd.test(value?.trim())) {
        callback("Vui lòng nhập đúng định dạng CMND");
      } else {
        callback();
      }
    }
    callback();
  };

  validatorSex = (rule, value, callback) => {
    this.validateRequiredField(
      "sex",
      rule,
      value,
      callback,
      "Vui lòng chọn giới tính!"
    );
  };

  validatorCity = (rule, value, callback) => {
    this.validateRequiredField(
      "city_id",
      rule,
      value,
      callback,
      "Vui lòng chọn tỉnh thành!"
    );
  };

  validatorCareers = (rule, value, callback) => {
    const { isChildren, isJunior } = this.checkOverAge();
    if (isChildren && !isJunior) {
      callback();
    }

    if (Number(value) === 0 || !value || value === "Chọn nghề nghiệp") {
      callback("Vui lòng chọn nghề nghiệp!");
    } else {
      callback();
    }
  };

  validatorDistrict = (rule, value, callback) => {
    this.validateRequiredField(
      "district_id",
      rule,
      value,
      callback,
      "Vui lòng chọn quận huyện!"
    );
  };

  validatorWard = (rule, value, callback) => {
    this.validateRequiredField(
      "ward_id",
      rule,
      value,
      callback,
      "Vui lòng chọn phường xã!"
    );
  };

  validatorAddress = (rule, value, callback) => {
    this.validateRequiredField(
      "address",
      rule,
      value,
      callback,
      "Vui lòng nhập số nhà, đường, thôn (ấp), xóm!"
    );
  };

  validatorBirthDay = (rule, value, callback) => {
    const { form } = this.props;
    const birthyear = Number(form.getFieldValue("birthyear"));
    const birthmonth = Number(form.getFieldValue("birthmonth"));
    if (this.checkBirthdayIsValid(Number(value), birthmonth, birthyear)) {
      const currentYear = Number(moment().format("YYYY"));
      const yearOld = currentYear - birthyear;
      if (yearOld <= 6) {
        if (Number(value) === 0) {
          if (birthmonth === 0) {
            callback("Vui lòng chọn ngày tháng.");
          } else {
            callback("Vui lòng chọn ngày");
          }
        } else {
          callback();
        }
      } else callback();
    } else {
      callback("Vui lòng chọn ngày sinh đúng.");
    }
  };

  validatorBirthMonth = (rule, value, callback) => {
    const { form } = this.props;
    const birthyear = Number(form.getFieldValue("birthyear"));
    const birthday = Number(form.getFieldValue("birthday"));
    if (this.checkBirthdayIsValid(birthday, Number(value), birthyear)) {
      const currentYear = Number(moment().format("YYYY"));
      const yearOld = currentYear - birthyear;
      if (yearOld <= 3) {
        if (Number(value) === 0 && birthday !== 0) {
          callback("Vui lòng chọn tháng");
        } else {
          callback();
        }
      } else callback();
    } else {
      callback();
    }
  };

  validatorBirthYear = (rule, value, callback) => {
    if (Number(value) === 0) {
      callback("Vui lòng chọn năm sinh!");
    }
    callback();
  };

  validatorRelativeType = (rule, value, callback) => {
    if (this.checkOverAge()) {
      if (!!value && Number(value) !== 0) {
        callback();
      } else {
        callback("Vui lòng chọn quan hệ bệnh nhân");
      }
    } else {
      callback();
    }
  };

  getRequiredFields = () => {
    const { requiredFields } = this.props;
    const { fallbackRequiredFields } = this.state;

    // Use API-provided required fields if available, otherwise use fallback
    return requiredFields && requiredFields.length > 0
      ? requiredFields
      : fallbackRequiredFields;
  };

  isFieldRequired = fieldName => {
    const requiredFields = this.getRequiredFields();
    return requiredFields.includes(fieldName);
  };

  validateRequiredField = (fieldName, rule, value, callback, customMessage) => {
    if (this.isFieldRequired(fieldName)) {
      if (fieldName === "sex") {
        if (value === undefined || value === null) {
          callback(`Vui lòng nhập giới tính!`);
          return;
        }
      } else if (
        !value ||
        (typeof value === "number" && value === 0) ||
        (typeof value === "number" && value === -1)
      ) {
        callback(customMessage || `Vui lòng nhập ${fieldName}!`);
        return;
      }
    }
    callback();
  };

  handleCaseAge = () => {
    const {
      form,
      patientForm: {
        info: { relative_mobile, mobile }
      },
      patient: { selectedPatient },
      type
    } = this.props;
    const { isChildren, isJunior } = this.checkOverAge();
    if (!!isJunior === false && isChildren) {
      form.resetFields("mobile");

      form.setFieldsValue({
        profession_id: "medpro_952",
        mobile: mobile.value,
        relative_mobile: relative_mobile.value
      });
    } else {
      form.setFieldsValue({
        profession_id: type === "create" ? 0 : selectedPatient.profession_id,
        mobile: mobile.value
      });
    }
    // form.validateFields(["relative_mobile", "profession_id", "mobile"], {
    //   force: true
    // });
  };

  handleChangeYear = value => {
    const { form } = this.props;

    form.setFieldsValue({
      birthyear: value
    });

    form.validateFields(["birthday", "birthmonth", "birthyear"], {
      force: true
    });
    this.handleCaseAge();
  };

  handleChangeMonth = value => {
    const { form } = this.props;

    form.setFieldsValue({
      birthmonth: value
    });

    form.validateFields(["birthday"], {
      force: true
    });

    this.handleCaseAge();
  };

  handleChangeDay = value => {
    const { form } = this.props;

    form.setFieldsValue({
      birthday: value
    });
    form.validateFields(["birthmonth"], {
      force: true
    });

    this.handleCaseAge();
  };

  handleChangeCareer = id => {
    this.props.form.setFieldsValue({
      profession_id: id
    });
  };

  handleChangeCountry = id => {
    this.props.form.setFieldsValue({
      country_code: id,
      city_id: 0,
      district_id: 0,
      ward_id: 0,
      address: ""
    });
    const { allCountries } = this.props;
    const findSelectedCountry = find(allCountries, { id: id });
    if (typeof findSelectedCountry !== typeof undefined) {
      this.props.onChangeCountry({ ...findSelectedCountry });
    }
    if (id !== "VIE") {
      this.handleChangeNation("medpro_82");
      this.handleChangeCity(99);
    } else {
      this.handleChangeNation("medpro_1");
    }
  };

  handleChangeNation = id => {
    this.props.form.setFieldsValue({
      dantoc_id: id
    });
  };

  handleChangeCity = id => {
    const { allCities } = this.props;
    this.props.form.setFieldsValue({
      city_id: id,
      district_id: 0,
      ward_id: 0
    });
    const findSelectedCity = find(allCities, { id: id });
    if (typeof findSelectedCity !== typeof undefined) {
      this.props.onChangeCity({ ...findSelectedCity });
    }
  };

  handleChangeDistrict = id => {
    const { allDistricts } = this.props;
    this.props.form.setFieldsValue({
      district_id: id,
      ward_id: 0
    });
    const findSelectedDistrict = find(allDistricts, { id: id });
    if (typeof findSelectedDistrict !== typeof undefined) {
      this.props.onChangeDistrict({ ...findSelectedDistrict });
    }
  };

  handleChangeWard = id => {
    this.props.form.setFieldsValue({
      ward_id: id
    });
  };

  handleChangeSex = id => {
    this.props.form.setFieldsValue({
      sex: id
    });
  };

  handleChangeRelativeType = id => {
    this.props.form.setFieldsValue({
      relative_type_id: id
    });
  };

  handleGetCmnd = () => {
    const cmnd = this.props.form.getFieldValue("cmnd");
    const fullname = this.props.form.getFieldValue("fullname");
    const birthdate = this.props.form.getFieldValue("birthdate");
    if (!cmnd || !fullname || !birthdate) {
      this.setState({
        isMessageError: true,
        textMessageError:
          "Thông tin nhập chưa hợp lệ. Vui lòng kiểm tra lại và nhập đúng định dạng."
      });
      return;
    }
    this.props.getInfoPatientCMND({
      cmnd,
      fullname,
      birthdate
    });
  };

  xuLyTen = () => {
    const fullname = this.props.form.getFieldValue("fullname");
    const _fullname = fnc.removeNewlines(fullname);

    const relative_name = this.props.form.getFieldValue("relative_name");
    const _relative_name = fnc.removeNewlines(relative_name);

    this.props.form.setFieldsValue({
      fullname: _fullname,
      relative_name: _relative_name
    });
  };

  onCreatePatientInfo = event => {
    event.preventDefault();
    this.xuLyTen();
    this.props.form.validateFields((error, value) => {
      if (!error) {
        this.props.onCreatePatientInfo();
      }
    });
  };

  onUpdatePatientInfo = event => {
    event.preventDefault();
    this.xuLyTen();
    this.props.form.validateFields((error, value) => {
      if (!error) {
        this.props.onUpdatePatientInfo();
      }
    });
  };

  handleResetFormFields = () => {
    const { defaultCountry } = getInfoFollowHospital();
    this.props.resetPatientForm(); // làm trống form
    this.props.resetDefaultCountry(defaultCountry.id); // chọn quốc gia mặc định trong form là Việt nam
    this.props.onChangeCountry(defaultCountry); // chọn Selected Country là Việt Nam
    this.props.requestAllCity(); // request all cities của Việt Nam
  };

  onToggleModalConfirmPhone = () => {
    this.setState({ showModalConfirmPhone: !this.state.showModalConfirmPhone });
  };

  toggleModalFunctionProcess = () => {
    this.setState({ isFunctionProcess: !this.state.isFunctionProcess });
  };

  handleConfirmGetCMND = () => {
    this.props.getInfoPatientCMNDSuccess();
  };

  handleCancelGetCMND = () => {
    this.props.getInfoPatientCMNDConfirmCancel();
  };

  toggleModalMessageError = async () => {
    await this.props.getInfoPatientCMNDFailure({ message: false });
    await this.setState({ isMessageError: false });
  };

  // Modal confirm Phone
  handleChangePhone = e => {
    const patternPhone = /^[0-9]+$/;
    if (!patternPhone.test(e.target.value)) {
      this.setState({
        errorCheckPhone: "Vui lòng nhập đúng định dạng",
        isExactPhone: false
      });
    } else {
      this.setState({
        isExactPhone: true,
        errorCheckPhone: ""
      });
    }
    this.setState({
      phone: e.target.value
    });
  };

  handleCheckPhone = () => {
    const { phone } = this.state;
    const {
      patientForm: {
        info: {
          mobile: { value: oldMobile }
        }
      }
    } = this.props;
    if (fnc.hash(phone) === oldMobile) {
      this.props.onUpdatePatientInfo();
    } else {
      this.setState({
        isExactPhone: false,
        errorCheckPhone:
          "Số điện thoại không chính xác với số điện thoại ban đầu"
      });
    }
  };

  renderBodyModalConfirmMobile = () => {
    const { phone, isExactPhone, errorCheckPhone } = this.state;
    const {
      patientForm: {
        info: {
          mobile: { value: oldMobile }
        }
      }
    } = this.props;
    let message = `Bệnh nhân vui lòng cập nhật số điện thoại tại quầy chăm sóc khách hàng của bệnh viện Đại học Y Dược`;
    let isNoPhone = true;
    if (oldMobile !== null) {
      message = `Vui lòng nhập lại đầy đủ số điện thoại ${oldMobile}, để xác nhận hồ sơ bệnh nhân`;
      isNoPhone = false;
    }
    return (
      <Fragment>
        <p>{message}</p>
        {!isNoPhone && (
          <input
            type="text"
            placeholder="Nhập số điện thoại"
            className="form-control"
            onChange={this.handleChangePhone}
            value={phone}
          />
        )}
        {isExactPhone === false && (
          <p className={styles.red} style={{ fontSize: 11, paddingTop: 5 }}>
            {errorCheckPhone}
          </p>
        )}
      </Fragment>
    );
  };

  checkBirthdayIsValid = (birthDay, birthMonth, birthYear) => {
    if (birthDay && birthMonth && birthYear) {
      const dateTime = moment(
        `${birthDay}/${birthMonth}/${birthYear}`,
        "D/M/YYYY"
      );
      return dateTime.isValid() && dateTime.isSameOrBefore(moment());
    }
    return true;
  };

  checkOverAge = () => {
    const { form, partnerId } = this.props;
    const [splDay, splMonth, splYear] =
      form.getFieldValue("birthdate")?.split("/") || [];
    const namsinh = Number(splYear); // Number(form.getFieldValue("birthyear"));
    const thangsinh = Number(splMonth); // Number(form.getFieldValue("birthmonth"));
    const ngaysinh = Number(splDay); // Number(form.getFieldValue("birthday"));

    const { _ageChildren, _ageJunior } = handleAge(partnerId);
    if (namsinh === 0) {
      return { isChildren: false };
    } else {
      if (thangsinh === 0) {
        // thì tính năm hiện tại -  năm sinh
        const namhientai = Number(moment().format("YYYY"));
        const sotuoi = namhientai - namsinh;

        if (sotuoi < _ageChildren) {
          return { isChildren: true };
        } else {
          if (sotuoi < _ageJunior) {
            return { isChildren: true, isJunior: _ageChildren !== _ageJunior };
          } else {
            return { isChildren: false };
          }
        }
      } else {
        // tính tháng năm hiện tại - tháng năm sinh
        const day = ngaysinh === 0 ? 1 : ngaysinh;
        const date = `${day}/${thangsinh}/${namsinh}`;
        const sotuoi = moment().diff(moment(date, "DD/MM/YYYY"), "years", true);

        if (sotuoi < _ageChildren) {
          return { isChildren: true };
        } else {
          if (sotuoi < _ageJunior) {
            return { isChildren: true, isJunior: _ageChildren !== _ageJunior };
          } else {
            return { isChildren: false };
          }
        }
      }
    }
  };

  handleNumber = evt => {
    var ch = String.fromCharCode(evt.which);

    if (!/[0-9]/.test(ch)) {
      evt.preventDefault();
    }
  };

  convertGenderCCCD = (gender, birthYear) => {
    if (birthYear > 1900 && birthYear < 2000) {
      return gender === 0 ? 1 : 0;
    } else if (birthYear >= 2000 && birthYear < 2100) {
      return gender === 2 ? 1 : 0;
    } else if (birthYear > 2200 && birthYear < 2300) {
      return gender === 4 ? 1 : 0;
    } else if (birthYear > 2300 && birthYear < 2400) {
      return gender === 6 ? 1 : 0;
    } else if (birthYear > 2400 && birthYear < 2500) {
      return gender === 8 ? 1 : 0;
    }
  };

  extractInformation = async str => {
    try {
      let name, address, birthDate, insuranceCode, sex, identifyId;
      if (!str) {
        return null;
      }
      const arrayinfoqr = str.split("|");
      if (!arrayinfoqr.length) {
        return null;
      }
      if (arrayinfoqr[0].length === 12) {
        name = arrayinfoqr[2];
        address = arrayinfoqr[5];
        identifyId = arrayinfoqr[0];
        const birthDateFormat = moment(arrayinfoqr[3], "DDMMYYYY");
        birthDate = birthDateFormat.format("DD/MM/YYYY");
        sex = `${this.convertGenderCCCD(
          Number(identifyId[3]),
          birthDateFormat.year()
        )}`;
        insuranceCode = "";
      } else {
        name = decodeURIComponent(
          arrayinfoqr[1].replace(/\s+/g, "").replace(/[0-9a-f]{2}/g, "%$&")
        );
        address = decodeURIComponent(
          arrayinfoqr[4].replace(/\s+/g, "").replace(/[0-9a-f]{2}/g, "%$&")
        );
        birthDate = arrayinfoqr[2];
        insuranceCode = arrayinfoqr[0];
        sex = arrayinfoqr[3] - 1 === 1 ? "0" : "1";
      }
      const response = await client.parseAddress({ address });
      const data = response?.data;
      return {
        sex,
        name: name.split(" ")[name.split(" ").length - 1],
        surname: name
          .split(" ")
          .splice(0, name.split(" ").length - 1)
          .toString()
          .replace(/,/g, " "),
        fullname:
          name
            .split(" ")
            .splice(0, name.split(" ").length - 1)
            .toString()
            .replace(/,/g, " ") +
          " " +
          name.split(" ")[name.split(" ").length - 1],
        birthdate: moment(birthDate, "DD/MM/YYYY").isValid()
          ? birthDate
          : "01/01/" + birthDate,
        country_code: "VIE",
        country: "Việt Nam",
        city_id: data?.cityId,
        city: data?.cityId
          ? address.split(", ").length > 2
            ? address.split(", ")[address.split(", ").length - 1].split("_")[0]
            : ""
          : undefined,
        district_id: data?.districtId,
        district: data?.districtId
          ? address.split(", ").length > 2
            ? address.split(", ")[address.split(", ").length - 2]
            : ""
          : undefined,
        ward_id: data?.wardId,
        ward: data?.wardId
          ? address.split(", ").length > 2
            ? address.split(", ")[address.split(", ").length - 3]
            : ""
          : undefined,
        address: data.address || "",
        insuranceCode: insuranceCode.toUpperCase(),
        cmnd: identifyId
      };
    } catch (error) {
      console.log("@@@extractInformation:", error);
      return null;
    }
  };

  handleScannerChangeInternal = async e => {
    const extractInfo = await this.extractInformation(e.target.value);
    this.props.getInfoPatientCMNDSuccess({
      infoConfirm: {
        address: { value: extractInfo?.address },
        birthdate: { value: extractInfo?.birthdate },
        fullname: { value: extractInfo?.fullname },
        cmnd: { value: extractInfo?.cmnd },
        city_id: { value: extractInfo?.city_id },
        district_id: { value: extractInfo?.district_id },
        ward_id: { value: extractInfo?.ward_id },
        sex: { value: extractInfo?.sex }
      }
    });
  };

  handleScannerChange = e => {
    if (!e.target.value.includes("|")) {
      return;
    }
    this.debouncedHandleScannerChange(e);
    if (this.timeoutScannerChange) clearTimeout(this.timeoutScannerChange);
    this.setState({ scannedText: e.target.value });
    this.timeoutScannerChange = setTimeout(() => {
      if (e.target.value.trim()) {
        this.setState({ scannedText: "" });
      }
    }, 1000);
  };

  getFieldValidationRules = fieldName => {
    const validatorMap = {
      fullname: [{ validator: this.validateName }],
      cmnd: [{ validator: this.validateCmndRequest }],
      sex: [{ validator: this.validatorSex }],
      city_id: [{ validator: this.validatorCity }],
      district_id: [{ validator: this.validatorDistrict }],
      ward_id: [{ validator: this.validatorWard }],
      address: [{ validator: this.validatorAddress }],
      mobile: [{ validator: this.validatePhone }],
      email: [{ validator: this.validatorEmail }],
      profession_id: [{ validator: this.validatorCareers }],
      dantoc_id: this.isFieldRequired("dantoc_id")
        ? [{ required: true, message: "Vui lòng chọn Dân tộc" }]
        : [],
      relative_name: [{ validator: this.validateName }],
      relative_type_id: [{ validator: this.validatorRelativeType }],
      relative_mobile: [{ validator: this.validatorRelativePhone }],
      relative_email: [{ validator: this.validatorEmail }]
    };

    return validatorMap[fieldName] || [];
  };

  async componentDidUpdate(prevProps) {
    if (
      !this.props.patientForm.infoConfirm?.modalConfirm &&
      this.props.patientForm.infoConfirm?.city_id?.value &&
      prevProps.patientForm.infoConfirm?.city_id?.value !==
      this.props.patientForm.infoConfirm?.city_id?.value
    ) {
      await this.props.onChangeCity({
        id: this.props.patientForm.infoConfirm.city_id.value
      });
    }
    if (
      !this.props.patientForm.infoConfirm?.modalConfirm &&
      this.props.patientForm.infoConfirm?.district_id?.value &&
      prevProps.patientForm.infoConfirm?.district_id?.value !==
      this.props.patientForm.infoConfirm?.district_id?.value
    ) {
      await this.props.onChangeDistrict({
        id: this.props.patientForm.infoConfirm.district_id.value
      });
    }
  }

  render() {
    const {
      allCareers,
      allNation,
      allCities,
      allDistricts,
      allWards,
      allRelationShip,
      form: { getFieldProps, getFieldError },
      patientForm: { loading, disableCreate, infoConfirm, error },
      type
    } = this.props;
    const { scannedText } = this.state;

    const msbn = false;

    const { isChildren, isJunior } = this.checkOverAge();

    const { sexs, showModalConfirmPhone } = this.state;

    const id = getFieldProps("id").value;

    const errorMessage = ele => {
      const field = getFieldError(ele);

      const mess = field ? field.join(", ") : null;
      return <span className={styles.alertSpan}>{mess}</span>;
    };

    return (
      <Fragment>
        {loading ? (
          <div className="loading">
            <MDBSpinner big />
          </div>
        ) : null}
        <MDBAnimation type="fadeIn">
          <form className={cx(styles.form_medpro, styles.form_update)}>
            <div>
              <TagName
                element="h2"
                className={["title_component", "title_headline"]}
              >
                <span>Nhập thông tin bệnh nhân</span>
              </TagName>
              {type === "create" ? (
                <div className={styles.scannerTextarea}>
                  <div className={styles.label}>Dùng thiết bị quét CCCD</div>
                  <textarea
                    className="form-control"
                    rows="3"
                    style={{ resize: "none", caretColor: "transparent" }}
                    value={scannedText}
                    onChange={this.handleScannerChange}
                    onKeyDown={e => {
                      const now = Date.now();
                      if (!this.lastKeyTime || now - this.lastKeyTime > 30) {
                        e.preventDefault();
                      }
                      this.lastKeyTime = now;
                    }}
                  />
                </div>
              ) : null}
              <div className={styles.info_required}>
                <span
                  className={styles.alertSpan}
                  style={{ marginBottom: "15px" }}
                >
                  (*) Thông tin bắt buộc nhập
                </span>
              </div>
              <div className={styles.wapper_form_group}>
                <div
                  className={styles.form_group}
                  style={{ position: "relative" }}
                >
                  <label htmlFor="label_cmnd">
                    Mã định danh/CCCD/Passport
                    {this.isFieldRequired("cmnd") && <sup>*</sup>}
                  </label>
                  <div className={styles.inputCmnd}>
                    <input
                      {...getFieldProps("cmnd", {
                        rules: this.getFieldValidationRules("cmnd")
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Nhập mã định danh/CCCD/Passport"
                      disabled={msbn}
                    />
                  </div>
                  {errorMessage("cmnd")}
                </div>

                <div className={styles.form_group}>
                  <label htmlFor="label_hoten">
                    Họ và tên (có dấu)
                    {this.isFieldRequired("fullname") && <sup>*</sup>}
                  </label>
                  <input
                    {...getFieldProps("fullname", {
                      rules: this.getFieldValidationRules("fullname")
                    })}
                    type="text"
                    className="form-control text-uppercase"
                    placeholder="Ví dụ: Nguyễn Văn Bảo"
                    disabled={msbn}
                    maxLength="150"
                  />
                  {errorMessage("fullname")}
                </div>

                <div className={styles.form_group}>
                  <label htmlFor="label_birth">
                    Ngày tháng năm sinh
                    {this.isFieldRequired("birthdate") && <sup>*</sup>}
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Nhập ngày/tháng/năm"
                    {...getFieldProps("birthdate", {
                      rules: [
                        {
                          validator: (rule, value, callback) => {
                            // Check if birthdate is required
                            if (this.isFieldRequired("birthdate")) {
                              const pattern = /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/;
                              if (!value) {
                                callback("Vui lòng nhập ngày/tháng/năm");
                                return;
                              } else if (value && !pattern.test(value)) {
                                callback(
                                  "Vui lòng nhập đúng định dạng dd/mm/yyyy"
                                );
                                return;
                              } else {
                                const [day, month, year] = value
                                  .split("/")
                                  .map(Number);
                                const dateObj = new Date(year, month - 1, day);
                                const isValid =
                                  dateObj.getFullYear() === year &&
                                  dateObj.getMonth() === month - 1 &&
                                  dateObj.getDate() === day;

                                if (!isValid) {
                                  callback("Ngày tháng không hợp lệ");
                                  return;
                                }
                              }
                            }
                            callback();
                          }
                        }
                      ]
                    })}
                    onChange={e => {
                      if (e.target.value) {
                        let value = e.target.value.replace(/[^0-9]/g, "");
                        if (value.length > 2) {
                          value = value.slice(0, 2) + "/" + value.slice(2);
                        }
                        if (value.length > 5) {
                          value = value.slice(0, 5) + "/" + value.slice(5);
                        }
                        this.props.form.setFieldsValue({ birthdate: value });
                      } else {
                        this.props.form.setFieldsValue({ birthdate: "" });
                      }
                    }}
                  />
                  {errorMessage("birthdate")}
                </div>

                {type === "create" ? (
                  <div className={styles.form_group}>
                    <label
                      className={styles.labelGetcmnd}
                      htmlFor="label_getcmnd"
                    />
                    <div
                      className={cx(styles.btnGetCmnd)}
                      onClick={() => this.handleGetCmnd()}
                    >
                      <span>Lấy dữ liệu</span>
                    </div>
                  </div>
                ) : null}

                <div className={styles.form_group}>
                  <label htmlFor="label_sdt">
                    Số điện thoại{" "}
                    {!isChildren && !isJunior ? <sup>*</sup> : null}
                  </label>
                  <input
                    {...getFieldProps("mobile", {
                      rules: this.getFieldValidationRules("mobile")
                    })}
                    type="text"
                    onKeyPress={this.handleNumber}
                    id="label_sdt"
                    className="form-control"
                    placeholder="Nhập số điện thoại"
                  />
                  {errorMessage("mobile")}
                </div>

                <div className={cx(styles.form_group, styles.form_select)}>
                  <label htmlFor="label_gt">
                    Giới tính
                    {this.isFieldRequired("sex") && <sup>*</sup>}
                  </label>
                  <SelectSearch
                    {...getFieldProps("sex", {
                      trigger: ["onChange"],
                      valuePropName: "value",
                      rules: [{ validator: this.validatorSex }]
                    })}
                    data={sexs}
                    onChange={this.handleChangeSex}
                    disabled={msbn}
                    isSearch
                  />
                  {errorMessage("sex")}
                </div>

                <div className={cx(styles.form_group, styles.form_select)}>
                  <label htmlFor="label_job">
                    Nghề nghiệp
                    {!isChildren || isJunior ? <sup>*</sup> : null}
                  </label>
                  <SelectSearch
                    {...getFieldProps("profession_id", {
                      trigger: ["onChange"],
                      valuePropName: "value",
                      rules: this.getFieldValidationRules("profession_id")
                    })}
                    data={allCareers}
                    onChange={this.handleChangeCareer}
                    disabled={isChildren && !isJunior}
                    isSearch
                  />

                  {errorMessage("profession_id")}
                </div>

                <div className={styles.form_group}>
                  <label htmlFor="label_email">
                    Địa chỉ Email
                    <sup />
                  </label>
                  <input
                    {...getFieldProps("email", {
                      rules: this.getFieldValidationRules("email")
                    })}
                    type="text"
                    className="form-control"
                    placeholder="Nhập địa chỉ email để nhận phiếu khám"
                  />
                  {errorMessage("email")}
                </div>

                <div className={cx(styles.form_group, styles.form_select)}>
                  <label htmlFor="label_dt">Dân tộc</label>
                  <SelectSearch
                    {...getFieldProps("dantoc_id", {
                      trigger: ["onChange"],
                      valuePropName: "value",
                      rules: [
                        {
                          required: true,
                          message: "Vui lòng chọn Dân tộc"
                        }
                      ]
                    })}
                    data={allNation}
                    onChange={this.handleChangeNation}
                    isSearch
                  />
                </div>

                <div className={cx(styles.form_group, styles.form_select)}>
                  <label htmlFor="label_tp">
                    Tỉnh / Thành
                    {this.isFieldRequired("city_id") && <sup>*</sup>}
                  </label>
                  <SelectSearch
                    {...getFieldProps("city_id", {
                      trigger: ["onChange"],
                      valuePropName: "value",
                      rules: this.getFieldValidationRules("city_id")
                    })}
                    data={allCities}
                    onChange={this.handleChangeCity}
                    disabled={msbn}
                    isSearch
                  />
                  {errorMessage("city_id")}
                </div>

                <div className={cx(styles.form_group, styles.form_select)}>
                  <label htmlFor="label_district">
                    {this.props.partnerId === "nhidonghcm" ? "Thành /" : null}{" "}
                    Quận / Huyện
                    {this.isFieldRequired("district_id") && <sup>*</sup>}
                  </label>
                  <SelectSearch
                    {...getFieldProps("district_id", {
                      trigger: ["onChange"],
                      valuePropName: "value",
                      rules: this.getFieldValidationRules("district_id")
                    })}
                    data={allDistricts}
                    onChange={this.handleChangeDistrict}
                    disabled={msbn}
                    isSearch
                  />
                  {errorMessage("district_id")}
                </div>

                <div className={cx(styles.form_group, styles.form_select)}>
                  <label htmlFor="label_ward">
                    Phường / Xã
                    {this.isFieldRequired("ward_id") && <sup>*</sup>}
                  </label>
                  <SelectSearch
                    {...getFieldProps("ward_id", {
                      trigger: ["onChange"],
                      valuePropName: "value",
                      rules: this.getFieldValidationRules("ward_id")
                    })}
                    data={allWards}
                    onChange={this.handleChangeWard}
                    disabled={msbn}
                    isSearch
                  />
                  {errorMessage("ward_id")}
                </div>

                <div className={styles.form_group}>
                  <label htmlFor="label_address">
                    Số nhà/Tên đường/Ấp thôn xóm
                    {this.isFieldRequired("address") && <sup>*</sup>}
                  </label>
                  <input
                    {...getFieldProps("address", {
                      rules: this.getFieldValidationRules("address")
                    })}
                    type="text"
                    className="form-control"
                    placeholder="Nhập số nhà/Tên đường/Ấp thôn xóm"
                    disabled={msbn}
                    maxLength="100"
                  />
                  {errorMessage("address")}
                </div>

                {/* -------------------- Phần thân nhân --------------------- */}
                {isChildren && (
                  <Fragment>
                    <TagName
                      element="h2"
                      className={["title_component", "title_headline"]}
                    >
                      <span>Nhập thông tin thân nhân</span>
                    </TagName>

                    <div className={styles.form_group}>
                      <label htmlFor="label_relative_name">
                        Họ và tên người thân<sup>*</sup>
                      </label>
                      <input
                        {...getFieldProps("relative_name", {
                          rules: this.getFieldValidationRules("relative_name")
                        })}
                        type="text"
                        className="form-control text-uppercase"
                        placeholder="Nhập họ tên người thân"
                        disabled={msbn}
                        maxLength="150"
                      />
                      {errorMessage("relative_name")}
                    </div>
                    <div className={styles.form_group}>
                      <label htmlFor="label_relative_type_id">
                        Quan hệ với bệnh nhân<sup>*</sup>
                      </label>
                      <Select
                        {...getFieldProps("relative_type_id", {
                          trigger: ["onChange"],
                          valuePropName: "value",
                          rules: this.getFieldValidationRules(
                            "relative_type_id"
                          )
                        })}
                        data={allRelationShip}
                        onChange={this.handleChangeRelativeType}
                      />
                      {errorMessage("relative_type_id")}
                    </div>
                    <div className={styles.form_group}>
                      <label htmlFor="label_relative_mobile">
                        Số điện thoại<sup>*</sup>
                      </label>
                      <input
                        {...getFieldProps("relative_mobile", {
                          required: true,
                          rules: this.getFieldValidationRules("relative_mobile")
                        })}
                        type="text"
                        className="form-control"
                        onKeyPress={this.handleNumber}
                        placeholder="Nhập số điện thoại"
                      />
                      {errorMessage("relative_mobile")}
                    </div>
                    <div className={styles.form_group}>
                      <label htmlFor="label_address">Email</label>
                      <input
                        {...getFieldProps("relative_email", {
                          rules: this.getFieldValidationRules("relative_email")
                        })}
                        type="text"
                        className="form-control"
                        placeholder="Nhập địa chỉ email"
                      />
                      {errorMessage("relative_email")}
                    </div>
                  </Fragment>
                )}
                <div className={cx(styles.form_group, styles.action_form)}>
                  {id === 0 && (
                    <PKHBtn reset="reset" onClick={this.handleResetFormFields}>
                      Nhập lại
                    </PKHBtn>
                  )}

                  <PKHBtn
                    create_patient_form="create_patient_form"
                    onClick={
                      id === 0
                        ? this.onCreatePatientInfo
                        : this.onUpdatePatientInfo
                    }
                    disabled={disableCreate}
                  >
                    {id === 0 ? "Tạo mới" : "Cập nhật"}
                  </PKHBtn>
                </div>
              </div>
            </div>
          </form>
        </MDBAnimation>
        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={showModalConfirmPhone}
          toggle={this.onToggleModalConfirmPhone}
          title="Xác nhận số điện thoại"
          children={this.renderBodyModalConfirmMobile()}
          centered
          className="centered"
          footer
          footerConfirm
          cancelText="Đóng"
          okText="Xác nhận"
          onCancel={() => this.onToggleModalConfirmPhone()}
          onOk={this.handleCheckPhone}
        />
        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={
            this.state.isMessageError ? this.state.isMessageError : !!error
          }
          toggle={this.toggleModalMessageError}
          title="Thông báo"
          children={this.state.textMessageError || error}
          centered
          className="centered"
        />
        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={this.state.isFunctionProcess}
          toggle={this.toggleModalFunctionProcess}
          title="Thông báo"
          children={"Tính năng đang được cập nhật và phát triển!"}
          centered
          className="centered"
        />
        {infoConfirm?.modalConfirm && (
          <Modal
            iconTitle={<i className="fal fa-bell" />}
            modal
            toggle={this.handleCancelGetCMND}
            title="Xác nhận thông tin"
            centered
            className="centered"
          >
            <div className={styles.modalConfirmWrapper}>
              <h4>Thông tin từ mã định danh/CCCD/Passport</h4>
              <div className={styles.confirmItems}>
                <div className={styles.item}>
                  <div className={styles.label}>Họ và tên:</div>
                  <div className={styles.value}>
                    {infoConfirm?.fullname?.value || "-"}
                  </div>
                </div>
                <div className={styles.item}>
                  <div className={styles.label}>
                    Mã định danh/CCCD/Passport:
                  </div>
                  <div className={styles.value}>
                    {infoConfirm?.cccd?.value || "-"}
                  </div>
                </div>
                <div className={styles.item}>
                  <div className={styles.label}>Ngày tháng năm sinh:</div>
                  <div className={styles.value}>
                    {infoConfirm?.birthdate?.value || "-"}
                  </div>
                </div>
                <div className={styles.item}>
                  <div className={styles.label}>Giới tính:</div>
                  <div className={styles.value}>
                    {infoConfirm?.sex?.value === 1 ? "Nam" : "Nữ"}
                  </div>
                </div>
                <div className={styles.item}>
                  <div className={styles.label}>Tỉnh / Thành:</div>
                  <div className={styles.value}>
                    {infoConfirm?.city_name?.value || "Chưa xác định"}
                  </div>
                </div>
                <div className={styles.item}>
                  <div className={styles.label}>Quận / Huyện:</div>
                  <div className={styles.value}>
                    {infoConfirm?.district_name?.value || "Chưa xác định"}
                  </div>
                </div>
                <div className={styles.item}>
                  <div className={styles.label}>Phường / Xã:</div>
                  <div className={styles.value}>
                    {infoConfirm?.ward_name?.value || "Chưa xác định"}
                  </div>
                </div>
                <div className={styles.item}>
                  <div className={styles.label}>
                    Số nhà/Tên đường/Ấp thôn xóm:
                  </div>
                  <div className={styles.value}>
                    {infoConfirm?.address?.value || "Chưa xác định"}
                  </div>
                </div>
              </div>
              <div className={styles.actionGroup}>
                <div
                  onClick={() => this.handleCancelGetCMND()}
                  className={styles.btnCancel}
                >
                  Hủy bỏ
                </div>
                <div
                  onClick={() => this.handleConfirmGetCMND()}
                  className={styles.btnOk}
                >
                  Chấp nhận
                </div>
              </div>
            </div>
          </Modal>
        )}
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    patientForm,
    resource: {
      dataCareer: allCareers,
      dataNation: allNation,
      dataCountry: allCountries,
      dataCity: allCities,
      dataDistrict: allDistricts,
      dataWard: allWards,
      dataRelationShip: allRelationShip
    },
    totalData: { partnerId },
    patient
  } = state;
  const { resource } = state;
  return {
    patient,
    partnerId,
    resource,
    allCareers,
    allRelationShip,
    allNation,
    allCountries,
    allCities,
    allDistricts,
    allWards,
    patientForm
  };
};

const mapDispatchToProps = {
  onChangeCareer: rs.onChangeCareer,
  onChangeCountry: rs.onChangeCountry,
  onChangeNation: rs.onChangeNation,
  onChangeRelationShip: rs.onChangeRelationShip,
  onChangeCity: rs.onChangeCity,
  onChangeDistrict: rs.onChangeDistrict,
  onChangeWard: rs.onChangeWard,
  requestAllCity: rs.requestAllCity,
  onSaveField: p.saveField,
  onCreatePatientInfo: p.onCreatePatientInfo,
  onUpdatePatientInfo: p.onUpdatePatientInfo,
  resetPatientForm: p.resetPatientForm,
  resetDefaultCountry: p.resetDefaultCountry,
  getInfoPatientCMND: p.getInfoPatientCMND,
  getInfoPatientCMNDConfirmCancel: p.getInfoPatientCMNDConfirmCancel,
  getInfoPatientCMNDSuccess: p.getInfoPatientCMNDSuccess,
  getInfoPatientCMNDFailure: p.getInfoPatientCMNDFailure
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  createForm({
    mapPropsToFields: props => {
      const {
        patientForm: { info: patientForm },
        type,
        partnerId,
        allCareers
      } = props;
      const { _ageChildren } = handleAge(partnerId);
      const [splDay, splMonth, splYear] =
        patientForm?.birthdate?.value?.split("/") || [];
      const namsinh = Number(splYear);
      const thangsinh = Number(splMonth);
      const ngaysinh = Number(splDay);
      let dd = "1";
      if (patientForm?.birthday?.value && patientForm.birthday.value !== 0) {
        dd = String(patientForm.birthday.value);
      } else if (ngaysinh && ngaysinh !== 0) {
        dd = String(ngaysinh);
      }
      let mm = "1";
      if (
        patientForm?.birthmonth?.value &&
        patientForm.birthmonth.value !== 0
      ) {
        mm = String(patientForm.birthmonth.value);
      } else if (thangsinh && thangsinh !== 0) {
        mm = String(thangsinh);
      }
      let yy = moment().year();
      if (patientForm?.birthyear?.value && patientForm.birthyear.value !== 0) {
        yy = patientForm.birthyear.value;
      } else if (namsinh && namsinh !== 0) {
        yy = String(namsinh);
      }
      const date = `${dd.padStart(2, "0")}/${mm.padStart(2, "0")}/${yy}`;
      const sotuoi = moment().diff(moment(date, "DD/MM/YYYY"), "years", true);

      let ngheNghiep;

      if (type === "update") {
        if (sotuoi < _ageChildren) {
          ngheNghiep = {
            value: "medpro_952"
          };
        } else {
          if (patientForm.profession_id.value === "medpro_952") {
            ngheNghiep = {
              value: ""
            };
          } else {
            ngheNghiep = patientForm.profession_id;
          }
        }
      } else {
        const foundCareer = allCareers.find(item => item.name === "Khác");
        ngheNghiep =
          foundCareer?.id && patientForm?.profession_id.value === 0
            ? { value: foundCareer?.id }
            : patientForm.profession_id;
        if (sotuoi < _ageChildren) {
          ngheNghiep = patientForm.profession_id;
        }
      }
      patientForm.birthdate = patientForm?.birthdate
        ? patientForm?.birthdate
        : { value: date };
      return {
        id: createFormField(patientForm.id),
        fullname: createFormField(patientForm.fullname),
        birthdate: createFormField(patientForm?.birthdate),
        birthday: createFormField(patientForm.birthday),
        birthmonth: createFormField(patientForm.birthmonth),
        birthyear: createFormField(patientForm.birthyear),
        mobile: createFormField(patientForm.mobile),
        cmnd: createFormField(patientForm.cmnd),
        email: createFormField(patientForm.email),
        profession_id: createFormField(ngheNghiep),
        sex: createFormField(patientForm.sex),
        country_code: createFormField(patientForm.country_code),
        dantoc_id: createFormField(patientForm.dantoc_id),
        city_id: createFormField(patientForm.city_id),
        district_id: createFormField(patientForm.district_id),
        ward_id: createFormField(patientForm.ward_id),
        address: createFormField(patientForm.address),
        relative_name: createFormField(patientForm.relative_name),
        relative_type_id: createFormField(patientForm.relative_type_id),
        relative_email: createFormField(patientForm.relative_email),
        relative_mobile: createFormField(patientForm.relative_mobile)
      };
    },
    onFieldsChange(props, fields) {
      const newFields = { ...fields };
      if ("relative_email" in fields) {
        newFields.relative_email.value = fields.relative_email.value?.trim();
      } else if ("email" in fields) {
        newFields.email.value = fields.email.value?.trim();
      } else if ("cmnd" in fields) {
        newFields.cmnd.value = fields.cmnd.value?.trim();
      } else if ("relative_name" in fields) {
        newFields.relative_name.value = fnc.name(fields.relative_name.value);
      } else if ("fullname" in fields) {
        newFields.fullname.value = fnc.name(fields.fullname.value);
      }
      props.onSaveField(newFields);
    }
  })(PatientFormStepper)
);
