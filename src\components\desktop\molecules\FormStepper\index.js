import cx from "classnames";
import { find, isEmpty } from "lodash";
import { MDBAlert, MDBAnimation, MDBSpinner } from "mdbreact";
import moment from "moment";
import { createForm, createFormField } from "rc-form";
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import isEmail from "validator/lib/isEmail";
import PKHBtn from "~/components/common/atoms/Button";
import Select from "~/components/common/atoms/Select";
import TagName from "~/components/common/atoms/TagName";
import Modal from "~/components/common/molecules/Modal";
import {
  onCreatePatientInfo,
  onUpdatePatientInfo,
  resetDefaultCountry,
  resetPatientForm,
  saveField
} from "~/store/patientForm/patientFormAction";
import {
  onChangeCareer,
  onChangeCity,
  onChangeCountry,
  onChangeDistrict,
  onChangeNation,
  onChangeRelationShip,
  onChangeWard,
  requestAllCity
} from "~/store/resource/resourceAction";
import { patternPhone } from "~/utils/constants";
import { getInfoFollowHospital } from "~/utils/flowRouting";
import * as fnc from "~/utils/func";
import styles from "./style.module.scss";

class PatientFormStepper extends Component {
  constructor(props) {
    super(props);
    this.state = {
      sexs: [
        {
          name: "Chọn giới tính",
          id: -1
        },
        {
          name: "Nam",
          id: 1
        },
        {
          name: "Nữ",
          id: 0
        }
      ],
      showModalConfirmPhone: false,
      phone: "",
      isExactPhone: "",
      errorCheckPhone: ""
    };

    this.defaultCountry = getInfoFollowHospital().defaultCountry;
  }

  validateName = (rule, value, callback) => {
    const getFullname = value.split(" ");
    if (getFullname.length < 2) {
      callback("Phải bao gồm họ và tên!");
    } else if (getFullname.length > 6) {
      callback("Họ tên quá dài!");
    } else if (value) {
      if (getFullname[0].length < 2) {
        callback("Vui lòng nhập đúng họ tên!");
      }
      if (getFullname[0].length < 2 && getFullname[1].length < 2) {
        callback("Vui lòng nhập đúng họ tên!");
      }

      const str = fnc.validName(value);
      const patternName = /^[a-z0-9A-Z ]{4,}$/g;

      if (patternName.test(str)) {
        callback();
      } else {
        callback("Vui lòng nhập đúng họ tên!");
      }
    } else {
      callback("Vui lòng nhập họ tên có dấu!");
    }
  };

  validatePhone = (rule, value, callback) => {
    const phone = new RegExp(patternPhone);
    if (!value) {
      this.checkOverAge(18)
        ? callback()
        : callback("Vui lòng nhập số điện thoại!");
    }
    if (phone.test(value)) {
      callback();
    } else {
      callback("Vui lòng nhập đúng định dạng!");
    }
  };

  validatorRelativePhone = (rule, value, callback) => {
    const phone = new RegExp(patternPhone);
    if (!value) {
      callback("Vui lòng nhập số điện thoại!");
    }
    if (phone.test(value)) {
      callback();
    } else {
      callback("Vui lòng nhập đúng định dạng!");
    }
  };

  validatorEmail = (rule, value, callback) => {
    if (typeof value !== typeof undefined && !!value) {
      const trimValue = (value + "").trim();
      if (!isEmpty(trimValue)) {
        if (!isEmail(trimValue)) {
          callback("Vui lòng nhập đúng định dạng email !");
        } else callback();
      } else callback();
    } else callback();
  };

  validatorCmnd = (rule, value, callback) => {
    if (value.trim()) {
      const patternCmnd = /^[a-zA-Z0-9]+$/g;
      if (!patternCmnd.test(value.trim())) {
        callback("Vui lòng nhập đúng định dạng CMND");
      } else {
        callback();
      }
    }
    callback();
  };

  validatorSex = (rule, value, callback) => {
    if (Number(value) === -1) {
      callback("Vui lòng chọn giới tính!");
    } else {
      callback();
    }
  };

  validatorCity = (rule, value, callback) => {
    const countryCode = this.props.form.getFieldValue("country_code");
    if (countryCode !== this.defaultCountry.id) {
      callback();
    } else {
      if (Number(value) === 0) {
        callback("Vui lòng chọn tỉnh thành!");
      } else {
        callback();
      }
    }
    callback();
  };

  validatorCareers = (rule, value, callback) => {
    if (this.checkOverAge(18)) {
      callback();
    }

    if (Number(value) === 0 || !value || value === "Chọn nghề nghiệp") {
      callback("Vui lòng chọn nghề nghiệp!");
    } else {
      callback();
    }
  };

  validatorDistrict = (rule, value, callback) => {
    const countryCode = this.props.form.getFieldValue("country_code");
    if (countryCode !== this.defaultCountry.id) {
      callback();
    } else {
      if (Number(value) === 0) {
        callback("Vui lòng chọn quận huyện!");
      } else {
        callback();
      }
    }
  };

  validatorWard = (rule, value, callback) => {
    const countryCode = this.props.form.getFieldValue("country_code");
    if (countryCode !== this.defaultCountry.id) {
      callback();
    } else {
      if (Number(value) === 0) {
        callback("Vui lòng chọn phường xã!");
      } else {
        callback();
      }
    }
  };

  validatorAddress = (rule, value, callback) => {
    const countryCode = this.props.form.getFieldValue("country_code");
    if (countryCode !== this.defaultCountry.id) {
      callback();
    } else {
      if (Number(value) === 0) {
        callback("Vui lòng nhập số nhà, đường, thôn (ấp), xóm!");
      } else {
        callback();
      }
    }
  };

  validatorBirthDay = (rule, value, callback) => {
    const { form } = this.props;
    const birthyear = Number(form.getFieldValue("birthyear"));
    const birthmonth = Number(form.getFieldValue("birthmonth"));
    if (this.checkBirthdayIsValid(Number(value), birthmonth, birthyear)) {
      const currentYear = Number(moment().format("YYYY"));
      const yearOld = currentYear - birthyear;
      if (yearOld <= 6) {
        if (Number(value) === 0) {
          if (birthmonth === 0) {
            callback("Vui lòng chọn ngày tháng.");
          } else {
            callback("Vui lòng chọn ngày");
          }
        } else {
          callback();
        }
      } else callback();
    } else {
      callback("Vui lòng chọn ngày sinh đúng.");
    }
  };

  validatorBirthMonth = (rule, value, callback) => {
    const { form } = this.props;
    const birthyear = Number(form.getFieldValue("birthyear"));
    const birthday = Number(form.getFieldValue("birthday"));
    if (this.checkBirthdayIsValid(birthday, Number(value), birthyear)) {
      const currentYear = Number(moment().format("YYYY"));
      const yearOld = currentYear - birthyear;
      if (yearOld <= 3) {
        if (Number(value) === 0 && birthday !== 0) {
          callback("Vui lòng chọn tháng");
        } else {
          callback();
        }
      } else callback();
    } else {
      callback();
    }
  };

  validatorBirthYear = (rule, value, callback) => {
    if (Number(value) === 0) {
      callback("Vui lòng chọn năm sinh!");
    }
    callback();
  };

  validatorRelativeType = (rule, value, callback) => {
    if (this.checkOverAge(18)) {
      if (!!value && Number(value) !== 0) {
        callback();
      } else {
        callback("Vui lòng chọn quan hệ bệnh nhân");
      }
    } else {
      callback();
    }
  };

  validatorRelativeEmail = (rule, value, callback) => {
    if (typeof value !== typeof undefined && !!value) {
      const trimValue = (value + "").trim();
      if (!isEmpty(trimValue)) {
        if (!isEmail(trimValue)) {
          callback("Vui lòng nhập đúng định dạng email !");
        } else callback();
      } else callback();
    } else callback();
  };

  handleCaseAge = () => {
    const {
      form,
      patientForm: {
        info: { relative_mobile, mobile }
      },
      patient: { selectedPatient },
      type
    } = this.props;
    if (this.checkOverAge(18)) {
      form.resetFields("mobile");

      form.setFieldsValue({
        profession_id: "medpro_952",
        mobile: mobile.value,
        relative_mobile: relative_mobile.value
      });
    } else {
      form.setFieldsValue({
        profession_id: type === "create" ? 0 : selectedPatient.profession_id,
        mobile: mobile.value
      });
    }
    // form.validateFields(["relative_mobile", "profession_id", "mobile"], {
    //   force: true
    // });
  };

  handleChangeYear = value => {
    const { form } = this.props;

    form.setFieldsValue({
      birthyear: value
    });

    form.validateFields(["birthday", "birthmonth", "birthyear"], {
      force: true
    });
    this.handleCaseAge();
  };

  handleChangeMonth = value => {
    const { form } = this.props;

    form.setFieldsValue({
      birthmonth: value
    });

    form.validateFields(["birthday"], {
      force: true
    });

    this.handleCaseAge();
  };

  handleChangeDay = value => {
    const { form } = this.props;

    form.setFieldsValue({
      birthday: value
    });
    form.validateFields(["birthmonth"], {
      force: true
    });

    this.handleCaseAge();
  };

  handleChangeCareer = id => {
    this.props.form.setFieldsValue({
      profession_id: id
    });
  };

  handleChangeCountry = id => {
    this.props.form.setFieldsValue({
      country_code: id,
      city_id: 0,
      district_id: 0,
      ward_id: 0,
      address: ""
    });
    const { allCountries } = this.props;
    const findSelectedCountry = find(allCountries, { id: id });
    if (typeof findSelectedCountry !== typeof undefined) {
      this.props.onChangeCountry({ ...findSelectedCountry });
    }
    if (id !== "VIE") {
      this.handleChangeNation("medpro_82");
      this.handleChangeCity(99);
    } else {
      this.handleChangeNation("medpro_1");
    }
  };

  handleChangeNation = id => {
    this.props.form.setFieldsValue({
      dantoc_id: id
    });
  };

  handleChangeCity = id => {
    const { allCities } = this.props;
    this.props.form.setFieldsValue({
      city_id: id,
      district_id: 0,
      ward_id: 0
    });
    const findSelectedCity = find(allCities, { id: id });
    if (typeof findSelectedCity !== typeof undefined) {
      this.props.onChangeCity({ ...findSelectedCity });
    }
  };

  handleChangeDistrict = id => {
    const { allDistricts } = this.props;
    this.props.form.setFieldsValue({
      district_id: id,
      ward_id: 0
    });
    const findSelectedDistrict = find(allDistricts, { id: id });
    if (typeof findSelectedDistrict !== typeof undefined) {
      this.props.onChangeDistrict({ ...findSelectedDistrict });
    }
  };

  handleChangeWard = id => {
    this.props.form.setFieldsValue({
      ward_id: id
    });
  };

  handleChangeSex = id => {
    this.props.form.setFieldsValue({
      sex: id
    });
  };

  handleChangeRelativeType = id => {
    this.props.form.setFieldsValue({
      relative_type_id: id
    });
  };

  xuLyTen = () => {
    const fullname = this.props.form.getFieldValue("fullname");
    const _fullname = fnc.removeNewlines(fullname);

    const relative_name = this.props.form.getFieldValue("relative_name");
    const _relative_name = fnc.removeNewlines(relative_name);

    this.props.form.setFieldsValue({
      fullname: _fullname,
      relative_name: _relative_name
    });
  };

  onCreatePatientInfo = event => {
    event.preventDefault();
    this.xuLyTen();
    this.props.form.validateFields((error, value) => {
      if (!error) {
        this.props.onCreatePatientInfo();
      }
    });
  };

  onUpdatePatientInfo = event => {
    event.preventDefault();
    this.xuLyTen();
    this.props.form.validateFields((error, value) => {
      if (!error) {
        this.props.onUpdatePatientInfo();
      }
    });
  };

  handleResetFormFields = () => {
    const { defaultCountry } = getInfoFollowHospital();
    this.props.resetPatientForm(); // làm trống form
    this.props.resetDefaultCountry(defaultCountry.id); // chọn quốc gia mặc định trong form là Việt nam
    this.props.onChangeCountry(defaultCountry); // chọn Selected Country là Việt Nam
    this.props.requestAllCity(); // request all cities của Việt Nam
  };

  onToggleModalConfirmPhone = () => {
    this.setState({ showModalConfirmPhone: !this.state.showModalConfirmPhone });
  };

  // Modal confirm Phone
  handleChangePhone = e => {
    const patternPhone = /^[0-9]+$/;
    if (!patternPhone.test(e.target.value)) {
      this.setState({
        errorCheckPhone: "Vui lòng nhập đúng định dạng",
        isExactPhone: false
      });
    } else {
      this.setState({
        isExactPhone: true,
        errorCheckPhone: ""
      });
    }
    this.setState({
      phone: e.target.value
    });
  };

  handleCheckPhone = () => {
    const { phone } = this.state;
    const {
      patientForm: {
        info: {
          mobile: { value: oldMobile }
        }
      }
    } = this.props;
    if (fnc.hash(phone) === oldMobile) {
      this.props.onUpdatePatientInfo();
    } else {
      this.setState({
        isExactPhone: false,
        errorCheckPhone:
          "Số điện thoại không chính xác với số điện thoại ban đầu"
      });
    }
  };

  renderBodyModalConfirmMobile = () => {
    const { phone, isExactPhone, errorCheckPhone } = this.state;
    const {
      patientForm: {
        info: {
          mobile: { value: oldMobile }
        }
      }
    } = this.props;
    let message = `Bệnh nhân vui lòng cập nhật số điện thoại tại quầy chăm sóc khách hàng của bệnh viện Đại học Y Dược`;
    let isNoPhone = true;
    if (oldMobile !== null) {
      message = `Vui lòng nhập lại đầy đủ số điện thoại ${oldMobile}, để xác nhận hồ sơ bệnh nhân`;
      isNoPhone = false;
    }
    return (
      <Fragment>
        <p>{message}</p>
        {!isNoPhone && (
          <input
            type="text"
            placeholder="Nhập số điện thoại"
            className="form-control"
            onChange={this.handleChangePhone}
            value={phone}
          />
        )}
        {isExactPhone === false && (
          <p className={styles.red} style={{ fontSize: 11, paddingTop: 5 }}>
            {errorCheckPhone}
          </p>
        )}
      </Fragment>
    );
  };

  checkBirthdayIsValid = (birthDay, birthMonth, birthYear) => {
    if (birthDay && birthMonth && birthYear) {
      const dateTime = moment(
        `${birthDay}/${birthMonth}/${birthYear}`,
        "D/M/YYYY"
      );
      return dateTime.isValid() && dateTime.isSameOrBefore(moment());
    }
    return true;
  };

  checkOverAge = age => {
    const { form, partnerId } = this.props;

    const namsinh = Number(form.getFieldValue("birthyear"));
    const thangsinh = Number(form.getFieldValue("birthmonth"));
    const ngaysinh = Number(form.getFieldValue("birthday"));

    let _age = 0;

    switch (partnerId) {
      case "nhidong1":
        _age = 16;
        break;

      case "dalieuhcm":
        _age = 6;
        break;

      case "nhidonghcm":
        _age = 18;
        break;

      default:
        _age = 16;
        break;
    }

    if (namsinh === 0) {
      return false;
    } else {
      if (thangsinh === 0) {
        // thì tính năm hiện tại -  năm sinh
        const namhientai = Number(moment().format("YYYY"));
        const sotuoi = namhientai - namsinh;

        if (sotuoi < _age) {
          return true;
        } else {
          return false;
        }
      } else {
        // tính tháng năm hiện tại - tháng năm sinh
        const day = ngaysinh === 0 ? 1 : ngaysinh;
        const date = `${day}/${thangsinh}/${namsinh}`;
        const sotuoi = moment().diff(moment(date, "DD/MM/YYYY"), "years", true);

        if (sotuoi < _age) {
          return true;
        } else {
          return false;
        }
      }
    }
  };

  handleNumber = evt => {
    var ch = String.fromCharCode(evt.which);

    if (!/[0-9]/.test(ch)) {
      evt.preventDefault();
    }
  };

  render() {
    const {
      allCareers,
      allNation,
      allCities,
      allDistricts,
      allWards,
      allRelationShip,
      form: { getFieldProps, getFieldError },
      patientForm: { loading, disableCreate }
    } = this.props;

    const msbn = false;

    const isChildren = this.checkOverAge(18);

    const { sexs, showModalConfirmPhone } = this.state;

    const id = getFieldProps("id").value;

    const errorMessage = ele => {
      const field = getFieldError(ele);

      const mess = field ? field.join(", ") : null;
      return <span className={styles.alertSpan}>{mess}</span>;
    };

    return (
      <Fragment>
        {loading ? (
          <div className="loading">
            <MDBSpinner big />
          </div>
        ) : (
          <MDBAnimation type="fadeIn">
            <form className={cx(styles.form_medpro, styles.form_update)}>
              <div>
                <TagName
                  element="h2"
                  className={["title_component", "title_headline"]}
                >
                  <span>Nhập thông tin bệnh nhân</span>
                </TagName>
                <MDBAlert color="primary">
                  Vui lòng cung cấp thông tin chính xác để được phục vụ tốt
                  nhất. Trong trường hợp cung cấp sai thông tin bệnh nhân & điện
                  thoại, việc xác nhận cuộc hẹn sẽ không hiệu lực trước khi đặt
                  khám.
                </MDBAlert>
                <div className={styles.info_required}>
                  <span
                    className={styles.alertSpan}
                    style={{ marginBottom: "15px" }}
                  >
                    (*) Thông tin bắt buộc nhập
                  </span>
                </div>
                <div className={styles.wapper_form_group}>
                  <div className={styles.form_group}>
                    <label htmlFor="label_hoten">
                      Họ và tên (có dấu)<sup>*</sup>
                    </label>
                    <input
                      {...getFieldProps("fullname", {
                        rules: [{ validator: this.validateName }]
                      })}
                      type="text"
                      className="form-control text-uppercase"
                      placeholder="Ví dụ: Nguyễn Văn Bảo"
                      disabled={msbn}
                      maxLength="28"
                    />
                    {errorMessage("fullname")}
                  </div>

                  <div className={styles.form_group}>
                    <label htmlFor="label_birth">
                      Ngày tháng năm sinh<sup>*</sup>
                    </label>
                    <div className={styles.form_group_select}>
                      <Select
                        {...getFieldProps("birthday", {
                          trigger: ["onChange"],
                          valuePropName: "value",
                          rules: [{ validator: this.validatorBirthDay }]
                        })}
                        data={fnc.dayList}
                        onChange={this.handleChangeDay}
                        disabled={msbn}
                      />
                      <Select
                        {...getFieldProps("birthmonth", {
                          trigger: ["onChange"],
                          valuePropName: "value",
                          rules: [{ validator: this.validatorBirthMonth }]
                        })}
                        data={fnc.monthList}
                        onChange={this.handleChangeMonth}
                        disabled={msbn}
                      />
                      <Select
                        {...getFieldProps("birthyear", {
                          trigger: ["onChange"],
                          valuePropName: "value",
                          rules: [{ validator: this.validatorBirthYear }]
                        })}
                        data={fnc.yearList}
                        onChange={this.handleChangeYear}
                        disabled={msbn}
                      />
                    </div>
                    {errorMessage("birthday")}
                    {errorMessage("birthmonth")}
                    {errorMessage("birthyear")}
                  </div>

                  <div className={styles.form_group}>
                    <label htmlFor="label_sdt">
                      Số điện thoại {isChildren ? null : <sup>*</sup>}
                    </label>
                    <input
                      {...getFieldProps("mobile", {
                        rules: [
                          {
                            validator: this.validatePhone
                          }
                        ]
                      })}
                      type="text"
                      onKeyPress={this.handleNumber}
                      id="label_sdt"
                      className="form-control"
                      placeholder="Nhập số điện thoại"
                    />
                    {errorMessage("mobile")}
                  </div>

                  <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_gt">
                      Giới tính<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("sex", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorSex }]
                      })}
                      data={sexs}
                      onChange={this.handleChangeSex}
                      disabled={msbn}
                    />
                    {errorMessage("sex")}
                  </div>

                  <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_job">
                      Nghề nghiệp {isChildren ? null : <sup>*</sup>}
                    </label>
                    <Select
                      {...getFieldProps("profession_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorCareers }]
                      })}
                      data={allCareers}
                      onChange={this.handleChangeCareer}
                      disabled={isChildren}
                    />

                    {errorMessage("profession_id")}
                  </div>

                  <div className={styles.form_group}>
                    <label htmlFor="label_cmnd">
                      Số CMND/Passport
                      <sup />
                    </label>
                    <input
                      {...getFieldProps("cmnd", {
                        // rules: [{ validator: this.validatorCmnd }]
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Nhập số CMND"
                      disabled={msbn}
                    />
                    {errorMessage("cmnd")}
                  </div>

                  <div className={styles.form_group}>
                    <label htmlFor="label_email">
                      Địa chỉ Email
                      <sup />
                    </label>
                    <input
                      {...getFieldProps("email", {
                        rules: [{ validator: this.validatorEmail }]
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Nhập địa chỉ email để nhận phiếu khám"
                    />
                    {errorMessage("email")}
                  </div>

                  <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_dt">Dân tộc</label>
                    <Select
                      {...getFieldProps("dantoc_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [
                          {
                            required: true,
                            message: "Vui lòng chọn Dân tộc"
                          }
                        ]
                      })}
                      data={allNation}
                      onChange={this.handleChangeNation}
                    />
                  </div>

                  <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_tp">
                      Tỉnh / Thành<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("city_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorCity }]
                      })}
                      data={allCities}
                      onChange={this.handleChangeCity}
                      disabled={msbn}
                    />
                    {errorMessage("city_id")}
                  </div>
                  <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_district">
                      {this.props.partnerId === "nhidonghcm" ? "Thành /" : null}{" "}
                      Quận / Huyện<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("district_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorDistrict }]
                      })}
                      data={allDistricts}
                      onChange={this.handleChangeDistrict}
                      disabled={msbn}
                    />
                    {errorMessage("district_id")}
                  </div>
                  <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_ward">
                      Phường / Xã<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("ward_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorWard }]
                      })}
                      data={allWards}
                      onChange={this.handleChangeWard}
                      disabled={msbn}
                    />
                    {errorMessage("ward_id")}
                  </div>

                  <div className={styles.form_group}>
                    <label htmlFor="label_address">
                      Địa chỉ<sup>*</sup>
                    </label>
                    <input
                      {...getFieldProps("address", {
                        rules: [{ validator: this.validatorAddress }]
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Nhập địa chỉ"
                      disabled={msbn}
                      maxLength="100"
                    />
                    {errorMessage("address")}
                  </div>

                  {/* -------------------- Phần thân nhân --------------------- */}
                  {isChildren && (
                    <Fragment>
                      <TagName
                        element="h2"
                        className={["title_component", "title_headline"]}
                      >
                        <span>Nhập thông tin thân nhân</span>
                      </TagName>

                      <div className={styles.form_group}>
                        <label htmlFor="label_relative_name">
                          Họ và tên người thân<sup>*</sup>
                        </label>
                        <input
                          {...getFieldProps("relative_name", {
                            rules: [{ validator: this.validateName }]
                          })}
                          type="text"
                          className="form-control text-uppercase"
                          placeholder="Nhập họ tên người thân"
                          disabled={msbn}
                          maxLength="28"
                        />
                        {errorMessage("relative_name")}
                      </div>
                      <div className={styles.form_group}>
                        <label htmlFor="label_relative_type_id">
                          Quan hệ với bệnh nhân<sup>*</sup>
                        </label>
                        <Select
                          {...getFieldProps("relative_type_id", {
                            trigger: ["onChange"],
                            valuePropName: "value",
                            rules: [{ validator: this.validatorRelativeType }]
                          })}
                          data={allRelationShip}
                          onChange={this.handleChangeRelativeType}
                        />
                        {errorMessage("relative_type_id")}
                      </div>
                      <div className={styles.form_group}>
                        <label htmlFor="label_relative_mobile">
                          Số điện thoại<sup>*</sup>
                        </label>
                        <input
                          {...getFieldProps("relative_mobile", {
                            required: true,
                            rules: [
                              {
                                validator: this.validatorRelativePhone
                              }
                            ]
                          })}
                          type="text"
                          className="form-control"
                          onKeyPress={this.handleNumber}
                          placeholder="Nhập số điện thoại"
                        />
                        {errorMessage("relative_mobile")}
                      </div>
                      <div className={styles.form_group}>
                        <label htmlFor="label_address">Email</label>
                        <input
                          {...getFieldProps("relative_email", {
                            rules: [{ validator: this.validatorRelativeEmail }]
                          })}
                          type="text"
                          className="form-control"
                          placeholder="Nhập địa chỉ email"
                        />
                        {errorMessage("relative_email")}
                      </div>
                    </Fragment>
                  )}
                  <div className={cx(styles.form_group, styles.action_form)}>
                    {id === 0 && (
                      <PKHBtn
                        reset="reset"
                        onClick={this.handleResetFormFields}
                      >
                        Nhập lại
                      </PKHBtn>
                    )}

                    <PKHBtn
                      create_patient_form="create_patient_form"
                      onClick={
                        id === 0
                          ? this.onCreatePatientInfo
                          : this.onUpdatePatientInfo
                      }
                      disabled={disableCreate}
                    >
                      {id === 0 ? "Tạo mới" : "Cập nhật"}
                    </PKHBtn>
                  </div>
                </div>
              </div>
            </form>
          </MDBAnimation>
        )}
        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={showModalConfirmPhone}
          toggle={this.onToggleModalConfirmPhone}
          title="Xác nhận số điện thoại"
          children={this.renderBodyModalConfirmMobile()}
          centered
          className="centered"
          footer
          footerConfirm
          cancelText="Đóng"
          okText="Xác nhận"
          onCancel={() => this.onToggleModalConfirmPhone()}
          onOk={this.handleCheckPhone}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    patientForm,
    resource: {
      dataCareer: allCareers,
      dataNation: allNation,
      dataCountry: allCountries,
      dataCity: allCities,
      dataDistrict: allDistricts,
      dataWard: allWards,
      dataRelationShip: allRelationShip
    },
    totalData: { partnerId },
    patient
  } = state;
  const { resource } = state;
  return {
    patient,
    partnerId,
    resource,
    allCareers,
    allRelationShip,
    allNation,
    allCountries,
    allCities,
    allDistricts,
    allWards,
    patientForm
  };
};

const mapDispatchToProps = dispatch => ({
  onChangeCareer: selectedCareer => {
    dispatch(onChangeCareer(selectedCareer));
  },
  onChangeCountry: selectedCountry => {
    dispatch(onChangeCountry(selectedCountry));
  },
  onChangeNation: selectedNation => {
    dispatch(onChangeNation(selectedNation));
  },
  onChangeRelationShip: selectedRelationShip => {
    dispatch(onChangeRelationShip(selectedRelationShip));
  },
  onChangeCity: selectedCity => {
    dispatch(onChangeCity(selectedCity));
  },
  onChangeDistrict: selectedDistrict => {
    dispatch(onChangeDistrict(selectedDistrict));
  },
  onChangeWard: selectedWard => {
    dispatch(onChangeWard(selectedWard));
  },
  onSaveField: fields => {
    dispatch(saveField(fields));
  },
  onCreatePatientInfo: () => {
    dispatch(onCreatePatientInfo());
  },
  onUpdatePatientInfo: () => {
    dispatch(onUpdatePatientInfo());
  },
  resetPatientForm: () => {
    dispatch(resetPatientForm());
  },
  resetDefaultCountry: id => {
    dispatch(resetDefaultCountry(id));
  },
  requestAllCity: () => {
    dispatch(requestAllCity());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  createForm({
    mapPropsToFields: props => {
      const {
        patientForm: { info: patientForm },
        type,
        partnerId
      } = props;

      let _age = 0;

      switch (partnerId) {
        case "nhidong1":
          _age = 16;
          break;

        case "dalieuhcm":
          _age = 6;
          break;

        case "nhidonghcm":
          _age = 18;
          break;

        default:
          _age = 16;
          break;
      }

      const date = `${
        patientForm.birthday.value === 0 ? 1 : patientForm.birthday.value
      }/${
        patientForm.birthmonth.value === 0 ? 1 : patientForm.birthmonth.value
      }/${
        patientForm.birthyear.value === 0
          ? moment().year()
          : patientForm.birthyear.value
      }`;

      const sotuoi = moment().diff(moment(date, "DD/MM/YYYY"), "years", true);

      let ngheNghiep;

      if (type === "update") {
        if (sotuoi < _age) {
          ngheNghiep = {
            value: "medpro_952"
          };
        } else {
          ngheNghiep = patientForm.profession_id;
        }
      } else {
        ngheNghiep = patientForm.profession_id;
      }

      return {
        id: createFormField(patientForm.id),
        fullname: createFormField(patientForm.fullname),
        birthdate: createFormField(patientForm.birthdate),
        birthday: createFormField(patientForm.birthday),
        birthmonth: createFormField(patientForm.birthmonth),
        birthyear: createFormField(patientForm.birthyear),
        mobile: createFormField(patientForm.mobile),
        cmnd: createFormField(patientForm.cmnd),
        email: createFormField(patientForm.email),
        profession_id: createFormField(ngheNghiep),
        sex: createFormField(patientForm.sex),
        country_code: createFormField(patientForm.country_code),
        dantoc_id: createFormField(patientForm.dantoc_id),
        city_id: createFormField(patientForm.city_id),
        district_id: createFormField(patientForm.district_id),
        ward_id: createFormField(patientForm.ward_id),
        address: createFormField(patientForm.address),
        relative_name: createFormField(patientForm.relative_name),
        relative_type_id: createFormField(patientForm.relative_type_id),
        relative_email: createFormField(patientForm.relative_email),
        relative_mobile: createFormField(patientForm.relative_mobile)
      };
    },
    onFieldsChange(props, fields) {
      const newFields = { ...fields };
      if ("relative_email" in fields) {
        newFields.relative_email.value = fields.relative_email.value.trim();
      } else if ("cmnd" in fields) {
        newFields.cmnd.value = fields.cmnd.value.trim();
      } else if ("relative_name" in fields) {
        newFields.relative_name.value = fnc.name(fields.relative_name.value);
      } else if ("fullname" in fields) {
        newFields.fullname.value = fnc.name(fields.fullname.value);
      }
      props.onSaveField(newFields);
    }
  })(PatientFormStepper)
);
