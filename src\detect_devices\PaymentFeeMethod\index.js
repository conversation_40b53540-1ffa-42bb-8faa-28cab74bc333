import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import { Redirect } from "react-router-dom";
import Loadable from "react-loadable";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import { MDBSpinner, MDBInput } from "mdbreact";
import {
  getAllFeePaymentMethod,
  selectedPaymentMethod,
  togglePaymentMethod,
  hideModalPaymentMethod,
  resetPaymentMethod
} from "~/store/payment/paymentAction";
import {
  submitPayment,
  resetAlertSubmitFail
} from "~/actions/umc/submitPayment";
import styles from "./style.module.scss";
import { reduce, get, find } from "lodash";
import Modal from "~/components/common/molecules/Modal";
import Alert from "~/components/common/atoms/Alert";
import { reserveBooking, repaymentBooking } from "~/store/totalData/actions";
import { onChangeEmail, paymentFee } from "~/store/fee/feeActions";
import { getRouteFollowPartnerId } from "~/utils/func";
import isEmail from "validator/lib/isEmail";
import { partnerInfo } from "~/configs/partnerDetails";
const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const PaymentMethodPageMobile = Loadable({
  loader: () =>
    import(/* mobile */ "~/components/desktop/pages/PaymentFeeMethod"),
  loading: LoadableLoading
});
const PaymentMethodPageDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/PaymentFeeMethod"),
  loading: LoadableLoading
});

class DetectPaymentMethod extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowAlert: false,
      isShowPaymentConfirm: false,
      loadingSubmitBooking: false,
      errorMissEmail: ""
    };
  }

  methods = {
    calculate: () => {
      const { schedulesSelected, selectedMethod } = this.props;
      const total = reduce(
        schedulesSelected,
        function(sum, item) {
          const price = Number(item.service.price);
          return sum + price;
        },
        0
      );
      const medpro = selectedMethod.medproFee
        ? Number(selectedMethod.medproFee)
        : 0;
      const fee = selectedMethod.rate ? Number(selectedMethod.rate) : 0;
      const feeMore = selectedMethod.constRate
        ? Number(selectedMethod.constRate)
        : 0;
      const phi = parseInt((total + medpro) * fee + feeMore);
      const phiThanhToan = phi;
      const phiThanhToanGiaoDich = phiThanhToan || 0;
      const totalPhi = medpro + phiThanhToanGiaoDich;
      return {
        total,
        totalPhi
      };
    },
    toggleAlertFail: () => {
      this.props.OnResetErrorMessgeSubmitPayment();
      this.setState(state => ({
        loadingSubmitBooking: false
      }));
    },
    handleDoPaymentFee: () => {
      const { email, paymentFee } = this.props;
      if (email !== "" && !isEmail(email)) {
        this.setState({
          errorMissEmail: "Vui lòng nhập đúng định dạng email."
        });
      } else {
        paymentFee();
        this.setState({
          errorMissEmail: "",
          isShowPaymentConfirm: false
        });
      }
    },

    toggleShowPaymentConfirm: () => {
      this.setState(state => ({
        isShowPaymentConfirm: !state.isShowPaymentConfirm
      }));
    },

    handleSelectedMethod: (method, methodId) => {
      this.props.selectedPaymentMethod(method, methodId);
    },
    toggleAlert: () => {
      this.setState({
        isShowAlert: !this.state.isShowAlert
      });
    },
    handleGoBack: () => {
      const { partnerId } = this.props;
      const route = getRouteFollowPartnerId(
        "/chi-tiet-ma-thanh-toan",
        partnerId
      );
      this.props.history.push(route);
    },
    handleChangeEmail: e => {
      const email = e.target.value;
      this.props.onChangeEmail(email);
      if (email !== "" && !isEmail(email)) {
        this.setState({
          errorMissEmail: "Vui lòng nhập đúng định dạng email."
        });
      } else {
        this.setState({
          errorMissEmail: ""
        });
      }
    }
  };

  styles = {
    loading: {
      position: "fixed",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)"
    },

    loading_submit_booking: {
      position: "fixed",
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      zIndex: 1000,
      backgroundColor: "black",
      opacity: 0.5
    },

    body_modal: {
      fontSize: "1rem"
    },
    body_modal_image: {
      margin: "auto",
      display: "block",
      objectFit: "cover",
      marginBottom: "10px",
      maxHeight: "100px"
    },
    alert: {
      color: "red",
      fontSize: "0.8rem"
    }
  };

  renderBodyModalConfirmPayment = () => {
    const { errorMissEmail } = this.state;
    const { selectedMethod, selectedFee, email } = this.props;
    const selectedMethodName =
      Object.keys(selectedMethod).length > 0
        ? selectedMethod.name
        : "Chọn phương thức thanh toán";

    const selectedMethodImage = get(selectedMethod, "paymentIcon.path", "");

    return (
      <Fragment>
        <div style={this.styles.body_modal}>
          {selectedMethodImage && (
            <img
              style={this.styles.body_modal_image}
              src={selectedMethodImage}
              alt={selectedMethodName}
            />
          )}
          <p>
            {selectedMethodName} số tiền{" "}
            <strong>
              {Number(selectedFee.amount).toLocaleString("vi-VN")}đ
            </strong>{" "}
            (mã phiếu {selectedFee.fee_code})
          </p>
          <MDBInput
            label="Nhập email để nhận thông tin hóa đơn"
            type="text"
            id="text"
            onChange={this.methods.handleChangeEmail}
            value={email}
          />
          {errorMissEmail && (
            <span style={this.styles.alert}>{errorMissEmail}</span>
          )}
        </div>
      </Fragment>
    );
  };

  componentDidMount() {
    this.props.getAllFeePaymentMethod();
    this.props.hideModalPaymentMethod();
    this.props.resetPaymentMethod();
  }

  componentWillUnmount() {
    this.props.OnResetErrorMessgeSubmitPayment();
  }

  render() {
    const {
      device,
      isRedirectToPaymentSupportPage,
      loading,
      IsAuthenticated
    } = this.props;

    if (!IsAuthenticated) {
      return <Redirect to="/login" />;
    }

    const { isShowAlert, isShowPaymentConfirm } = this.state;

    if (isRedirectToPaymentSupportPage)
      return <Redirect to="/ho-tro-thanh-toan" />;

    if (loading) {
      return (
        <div className={styles.loading_spinner}>
          <div className={styles.loading}>
            <MDBSpinner big crazy tag="div" />
          </div>
        </div>
      );
    }

    return (
      <Fragment>
        {device === "mobile" ? (
          <PaymentMethodPageMobile {...this.props} {...this.methods} />
        ) : (
          <PaymentMethodPageDesktop {...this.props} {...this.methods} />
        )}

        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={isShowPaymentConfirm}
          title="Xác nhận thanh toán"
          children={this.renderBodyModalConfirmPayment()}
          centered
          footer
          footerConfirm
          className="centered"
          toggle={this.methods.toggleShowPaymentConfirm}
          cancelText="Quay lại"
          okText="Đồng ý"
          onCancel={this.methods.toggleShowPaymentConfirm}
          onOk={this.methods.handleDoPaymentFee}
        />

        <Alert
          isModal={isShowAlert}
          message="Vui lòng chọn phương thức thanh toán"
          toggleAlert={this.methods.toggleAlert}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    payment: { selectedMethod, data, showModal, price },
    doctorAndTime: { sumaryInfo },
    features: { selectedFeature, selectedFeatureBooking },
    totalData: {
      schedulesSelected,
      bookingTree,
      loadingReserveBooking,
      loading,
      isRepayment,
      partnerId
    },
    hospital: { selectedHospital },
    user: { IsAuthenticated },
    fee: { selectedFee, patient, email, loading: loadingPaymentFee }
  } = state;
  return {
    device: type,
    selectedMethod,
    sumaryInfo,
    showModal,
    data,
    selectedFeature,
    selectedFeatureBooking,
    schedulesSelected,
    bookingTree,
    loadingReserveBooking,
    price,
    loading,
    isRepayment,
    selectedHospital,
    partnerId,
    IsAuthenticated,
    selectedFee,
    patient,
    email,
    loadingPaymentFee
  };
};

const mapDispatchToProps = dispatch => ({
  getAllFeePaymentMethod: () => {
    dispatch(getAllFeePaymentMethod());
  },
  OnTogglePaymentMethod: () => {
    dispatch(togglePaymentMethod());
  },
  OnSubmitPayment: () => {
    dispatch(submitPayment());
  },
  OnResetErrorMessgeSubmitPayment: () => {
    dispatch(resetAlertSubmitFail());
  },
  selectedPaymentMethod: (method, methodId) => {
    dispatch(selectedPaymentMethod(method, methodId));
  },
  hideModalPaymentMethod: () => {
    dispatch(hideModalPaymentMethod());
  },
  resetPaymentMethod: () => dispatch(resetPaymentMethod()),
  reserveBooking: () => dispatch(reserveBooking()),
  repaymentBooking: () => dispatch(repaymentBooking()),
  onChangeEmail: value => dispatch(onChangeEmail(value)),
  paymentFee: () => dispatch(paymentFee())
});

const PaymentMethodHelmet = withTitle({
  component: DetectPaymentMethod,
  title: `${hospitalName.value} | Chọn hình thức thanh toán`
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PaymentMethodHelmet);
