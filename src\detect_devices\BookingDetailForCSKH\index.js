import { first, isEmpty, get } from "lodash";
import {
  MDBBtn,
  MDBNav,
  MDBNavItem,
  MDBSpinner,
  MDBTabContent,
  MDBTabPane
} from "mdbreact";
import queryString from "query-string";
import React, { Component } from "react";
import { connect } from "react-redux";
import ReactToPrint from "react-to-print";
import { openToast } from "~/components/common/molecules/ToastNotification";
import MedicalBillPrinter from "~/components/desktop/molecules/MedicalBillPrinter";
import MedicalCoupon from "~/components/desktop/molecules/MedicalCoupon";
import ModalSendBooking from "~/components/desktop/pages/CustomerService/components/ModalSendBooking";
import history from "~/history";
import { requestPaymentInfoCSKH } from "~/store/customerService/customerServiceActions";
import { redirectToMedproId } from "~/store/login/actions";
import {
  checkRepayment,
  getBookingInfo,
  setPaymentInfomationByCSKH
} from "~/store/totalData/actions";
import styles from "./style.module.scss";
import MedicalCouponCare from "~/components/desktop/molecules/MedicalCouponCare";
import cx from "classnames";

class MedicalBillPrintForCSKH extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showSendBookingModal: false,
      selectingBill: null,
      billPrint: this.props.location?.state?.printState,
      loading: false,
      activeItemBooking: "NormalMedpro"
    };
  }

  handleChangeBill = () => {
    if (history.location.pathname === "/user/booking") {
      history.goBack();
    }
    this.setState(v => ({
      loading: !v.loading
    }));
    setTimeout(() => {
      this.setState(v => ({
        billPrint: !v.billPrint,
        loading: false
      }));
    }, 1000);
  };

  componentDidMount() {
    const { mpTransaction, transactions } = queryString.parse(
      this.props.location.search
    );
    if (!history.location.pathname.includes("/in-phieu-kham-cskh")) {
      this.setState({
        billPrint: this.props.location?.state?.printState || false
      });
    }

    if (transactions) {
      this.props.getBookingInfo({ transactionId: transactions });
    }
    if (mpTransaction) {
      this.props.requestPaymentInfoCSKH({ transactionId: mpTransaction });
    }
    if (
      this.state.billPrint &&
      history.location.pathname.includes("/user/booking")
    ) {
      this.usePrintRef.handleClick();
    }
  }

  UNSAFE_componentWillMount() {
    if (history.location.pathname.includes("/in-phieu-kham-cskh")) {
      this.setState(v => ({
        billPrint: true,
        loading: false
      }));
    }
  }

  toggleModalSendBooking = billInfo => {
    this.setState({
      showSendBookingModal: !this.state.showSendBookingModal,
      selectingBill: billInfo
    });
  };

  toggleTabs = value => {
    this.setState({ activeItemBooking: value });
  };

  render() {
    const { paymentInformation, partnerId } = this.props;
    const { billPrint, loading, activeItemBooking } = this.state;
    const medproCare = get(first(paymentInformation), "bookingInfo.medproCare");
    const showMedproCare = !isEmpty(medproCare);
    if (loading) {
      return (
        <div className="loading">
          <MDBSpinner big />
        </div>
      );
    }
    return (
      <div className={styles.wrapper_bill}>
        <div className={styles.groupBtn}>
          {!billPrint && (
            <MDBBtn
              color="success"
              size="sm"
              onClick={() => {
                this.toggleModalSendBooking(first(paymentInformation));
              }}
            >
              Gửi phiếu khám
            </MDBBtn>
          )}
          {!["umc"].includes(partnerId) && (
            <>
              {!billPrint ? (
                <MDBBtn
                  color="primary"
                  size="sm"
                  onClick={this.handleChangeBill}
                >
                  Lấy phiếu in
                </MDBBtn>
              ) : (
                <ReactToPrint
                  ref={el => (this.usePrintRef = el)}
                  trigger={() => {
                    return (
                      <MDBBtn color="primary" size="sm">
                        In phiếu khám
                      </MDBBtn>
                    );
                  }}
                  onAfterPrint={this.handleChangeBill}
                  content={() => this.componentRef}
                  pageStyle="print"
                  onPrintError={() => openToast("In phiếu thất bại :>> ")}
                  suppressErrors
                />
              )}
            </>
          )}
        </div>

        {billPrint ? (
          <>
            <div
              className={styles.printer}
              ref={el => (this.componentRef = el)}
            >
              <MedicalBillPrinter type="CSKH" />
            </div>
          </>
        ) : (
          <div className={styles.BookingCoupon}>
            {showMedproCare && (
              <>
                <MDBNav className={cx(styles.tabsBooking)}>
                  <MDBNavItem
                    className={cx(
                      activeItemBooking === "NormalMedpro"
                        ? styles.NormalMedpro
                        : ""
                    )}
                    active={activeItemBooking === "NormalMedpro"}
                    onClick={() => this.toggleTabs("NormalMedpro")}
                  >
                    <span>Phiếu khám</span>
                  </MDBNavItem>
                  <MDBNavItem
                    className={cx(
                      activeItemBooking === "MedproCare"
                        ? styles.MedproCare
                        : ""
                    )}
                    active={activeItemBooking === "MedproCare"}
                    onClick={() => this.toggleTabs("MedproCare")}
                  >
                    <span>Dịch vụ Care247</span>
                  </MDBNavItem>
                </MDBNav>
              </>
            )}
            <MDBTabContent activeItem={activeItemBooking}>
              <MDBTabPane tabId="NormalMedpro">
                <MedicalCoupon type="CSKH" />
              </MDBTabPane>
              <MDBTabPane tabId="MedproCare">
                <MedicalCouponCare {...this.props} />
              </MDBTabPane>
            </MDBTabContent>
          </div>
        )}

        <ModalSendBooking
          toggle={this.toggleModalSendBooking}
          show={this.state.showSendBookingModal}
          itemCoupon={this.state.selectingBill}
        />
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    totalData: { paymentInformation, partnerId },
    customerService: { selectedBill }
  } = state;
  return {
    selectedBill,
    partnerId,
    paymentInformation
  };
};

const mergeProps = (stateProps, dispatchProps, ownProps) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps
});

export default connect(
  mapStateToProps,
  {
    requestPaymentInfoCSKH,
    checkRepayment,
    setPaymentInfomationByCSKH,
    getBookingInfo,
    redirectToMedproId
  },
  mergeProps
)(MedicalBillPrintForCSKH);
