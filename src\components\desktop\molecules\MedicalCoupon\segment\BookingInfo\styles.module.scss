.BookingInfo {
  margin-top: 20px;
  margin-bottom: 10px;
.infoName{
  display: flex;
  flex-direction: column;
  gap: 7px;
  width: 100%;
  border-radius: 8px;
  padding: 8px;
  border: #11a2f3 1px solid;
  margin-bottom: 4px;
  .infoContent{
    display: flex;
    justify-content: space-between;
    .column_left{
        text-align: left;
        width: 50%;
      }
      .value{
          text-align: right;
      }
  }
}
  .listInfo {
    padding: 0;
    list-style: none;
    width: 100%;

    li {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      align-content: center;
      justify-content: space-between;

      &:last-child {
        margin-bottom: 0;
      }

      span {
        display: block;
        min-width: 160px;
      }

      b {
        text-align: right;
      }
    }
  }
}

.green {
  color: #6dec7b;
}
.column_right {
  text-align: right;
}
