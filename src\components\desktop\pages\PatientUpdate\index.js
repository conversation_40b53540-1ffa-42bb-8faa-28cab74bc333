import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MDBRow } from "mdbreact";
import React, { useEffect } from "react";
import { connect } from "react-redux";
import TagName from "~/components/common/atoms/TagName";
import FormPatientStepper from "~/components/desktop/molecules/FormStepper";
import {
  onChangeCity,
  onChangeCountry,
  onChangeDistrict,
  requestAllCareers,
  requestAllCountries,
  requestAllNation,
  requestAllRelationShip
} from "~/store/resource/resourceAction";
import styles from "./style.module.scss";
const PatientUpdate = ({
  requestAllCountries,
  requestAllCareers,
  requestAllRelationShip,
  onChangeCity,
  onChangeDistrict,
  requestAllNation,
  history,
  selectedPatient,
  dataDistrict
}) => {
  useEffect(() => {
    requestAllCountries();
    requestAllCareers();
    requestAllRelationShip();
    requestAllNation();
    onChangeCity({
      id: selectedPatient.city_id
    });
  }, [
    requestAllCare<PERSON>,
    requestAllCountries,
    requestAllNation,
    requestAllRelationShip,
    selectedPatient,
    onChangeCity
  ]);

  useEffect(() => {
    onChangeDistrict({
      id: selectedPatient.district_id
    });
  }, [dataDistrict, onChangeDistrict, selectedPatient]);

  return (
    <div className={styles.wapper_page_desktop}>
      <MDBContainer>
        <MDBRow>
          <MDBCol md="12">
            <div className={styles.wapper_page_inner}>
              <TagName
                element="h1"
                className={[
                  "title_component",
                  "title_line",
                  "title_header_mobile"
                ]}
              >
                <span> Cập nhật thông tin</span>
              </TagName>
              <FormPatientStepper goBack={history.goBack} type="update" />
            </div>
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    </div>
  );
};

const mapStateToProps = state => {
  const {
    patient: { selectedPatient },
    resource: { dataDistrict }
  } = state;
  return { selectedPatient, dataDistrict };
};

const mapDispatchToProps = {
  requestAllCountries: requestAllCountries,
  requestAllCareers: requestAllCareers,
  requestAllRelationShip: requestAllRelationShip,
  requestAllNation: requestAllNation,
  onChangeCountry: onChangeCountry,
  onChangeCity: onChangeCity,
  onChangeDistrict: onChangeDistrict
};

export default connect(mapStateToProps, mapDispatchToProps)(PatientUpdate);
