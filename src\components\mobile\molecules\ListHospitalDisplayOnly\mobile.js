import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import LazyLoad from 'react-lazyload';
import { MDBSpinner } from 'mdbreact';
import { Facebook } from 'react-content-loader';
import styles from './style.module.scss';
// import { HOME} from "~/utils/urlApi";

class ListHospitalDisplayOnly extends Component {
  // componentDidMount() {
  //   const { data } = this.props;
  //   if (data.length === 0) this.props.requestHospitalList();
  // }

  renderHospitalList = () => {
    const { data } = this.props;
    const hospitalListNode = data.map(item => {
      return (
        <div key={`hospital${item.id}`} className={styles.hospital_item}>
          <Link to="/" onClick={this.props.toggleChooseHospital}>
            <LazyLoad
              height={60}
              once={item.once}
              key={item.id}
              offset={[-150, 0]}
              // placeholder={<MDBSpinner className={styles.loading} />}
              debounce={500}>
              <img src={item.image} alt="" />
            </LazyLoad>
          </Link>
        </div>
      );
    });
    return <div className={styles.hospital}>{hospitalListNode}</div>;
  };

  render() {
    const { loading } = this.props;
    return (
      <Fragment>{loading ? <Facebook /> : this.renderHospitalList()}</Fragment>
    );
  }
}
const mapStateToProps = state => {
  const {
    hospital: {
      hospitalList: { data, loading },
    },
  } = state;
  return {
    data,
    loading,
  };
};

const mapDispatchToProps = dispatch => ({
  // requestHospitalList: () => {
  //   dispatch({ type: hospitalListTypes.HOSPITAL_LIST_REQUEST });
  // }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ListHospitalDisplayOnly);
