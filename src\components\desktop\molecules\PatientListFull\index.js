import React, { Component, Fragment } from "react";
import {
  MDBListGroup,
  MDBListGroupItem,
  MDBCard,
  MDBAnimation
} from "mdbreact";
import { connect } from "react-redux";
import { withRouter, <PERSON> } from "react-router-dom";
import PKHBtn from "~/components/common/atoms/Button";
import cx from "classnames";
import styles from "./style.module.scss";
import * as actions from "~/store/patient/patientAction";
import { getInfoPatient } from "~/utils/flowRouting";
import { Facebook } from "react-content-loader";
import partnerId from "~/utils/partner";
class PatientListFull extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  handleShowFullPatientDetail = id => {
    if (this.props.selectedPatient.id === id) {
      this.props.onSelectPatientFromList(0);
    } else {
      this.props.onSelectPatientFromList(id);
    }
  };

  renderContent = () => {
    const {
      data,
      selectedPatient,
      handleToggleModalConfirm,
      handleGetPatientDetail,
      handleContinueAction
    } = this.props;
    const url = "/cap-nhat-thong-tin";
    const objCheckNextStep = Object.create(null);

    if (Object.keys(selectedPatient).length > 0) {
      objCheckNextStep.nextStep = true;
    }

    return data?.map((patient, index) => {
      const {
        fullName,
        msbn,
        sex,
        birthdate,
        dantoc,
        mobile,
        fullAddress
      } = getInfoPatient(patient);

      return (
        <Fragment key={index}>
          <MDBCard
            className={cx(
              styles.card,
              msbn && styles.has_msbn,
              styles["card_" + partnerId],
              selectedPatient.id === patient.id ? styles.active : ""
            )}
            onClick={() => this.handleShowFullPatientDetail(patient.id)}
          >
            {msbn && (
              <div className={styles.msbn}>
                <span className={styles.msbn_style}>{msbn}</span>
              </div>
            )}
            <MDBListGroup className={styles.list_group}>
              <MDBListGroupItem>
                <div className={styles.fullname}>
                  <i className="fal fa-user-circle" />
                  <strong>{fullName}</strong>
                </div>
              </MDBListGroupItem>

              <MDBListGroupItem>
                <div className={styles.column1}>
                  <i className="fal fa-birthday-cake" />
                  <span>Ngày sinh</span>
                </div>
                <div className={styles.column2}> {birthdate}</div>
              </MDBListGroupItem>

              <MDBListGroupItem>
                <div className={styles.column1}>
                  {" "}
                  <i className="fal fa-mobile" />
                  <span>Số điện thoại</span>
                </div>
                <div className={styles.column2}> {mobile}</div>
              </MDBListGroupItem>

              {selectedPatient.id === patient.id && (
                <Fragment>
                  <MDBListGroupItem>
                    <div className={styles.column1}>
                      <i className="fal fa-venus-mars" />
                      Giới tính
                    </div>
                    <div className={styles.column2}>{sex}</div>
                  </MDBListGroupItem>

                  {dantoc !== "" && (
                    <MDBListGroupItem>
                      <div className={styles.column1}>
                        <i className="fal fa-id-card" />
                        Dân tộc
                      </div>
                      <div className={styles.column2}>{dantoc}</div>
                    </MDBListGroupItem>
                  )}

                  <MDBListGroupItem>
                    <div className={styles.column1}>
                      <i className="fal fa-map-marker" />
                      Địa chỉ
                    </div>
                    <div className={styles.column2}>{fullAddress}</div>
                  </MDBListGroupItem>
                </Fragment>
              )}
            </MDBListGroup>

            {selectedPatient.id === patient.id && (
              <div className={styles.action_edit_remove}>
                <div className={styles.action_left}>
                  <PKHBtn
                    remove="remove"
                    onClick={e => {
                      e.stopPropagation();
                      handleToggleModalConfirm();
                    }}
                  >
                    <i className="fal fa-trash-alt" />
                    Xoá
                  </PKHBtn>
                  <Link to={url}>
                    <PKHBtn
                      edit="edit"
                      onClick={() => handleGetPatientDetail(selectedPatient.id)}
                    >
                      <i className="fal fa-edit" />
                      Sửa
                    </PKHBtn>
                  </Link>
                </div>
                <div className={styles.action_right}>
                  <PKHBtn
                    color={partnerId === "minhanh" ? "red" : ""}
                    {...objCheckNextStep}
                    buttonArrow="buttonArrow"
                    create="create"
                    onClick={e => {
                      e.stopPropagation();
                      handleContinueAction();
                    }}
                  >
                    Tiếp tục
                  </PKHBtn>
                </div>
              </div>
            )}
          </MDBCard>
        </Fragment>
      );
    });
  };

  render() {
    const { loading } = this.props;
    if (loading) {
      return (
        <React.Fragment>
          <Facebook height={117} />
        </React.Fragment>
      );
    }

    return (
      <Fragment>
        <MDBAnimation type="fadeIn">{this.renderContent()}</MDBAnimation>
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    patient: { data, loading, selectedPatient },
    hospital: { selectedHospital }
  } = state;
  return {
    data,
    loading,
    selectedPatient,
    selectedHospital
  };
};

const mapDispatchToProps = dispatch => ({
  onSelectPatientFromList: id => {
    dispatch(actions.selectPatientFromList(id));
  }
});

export default withRouter(
  connect(mapStateToProps, mapDispatchToProps)(PatientListFull)
);
