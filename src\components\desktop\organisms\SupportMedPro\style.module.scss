@import "src/assets/scss/custom-variables.scss";
.supportMedPro {
  background-color: #fff;
  padding: 60px 0;
  @media #{$medium-and-down} {
    padding: 30px 0;
  }
}
.supportMedPro_inner {
  text-align: center;
}
.supportMedPro_inner_h3 {
  text-transform: uppercase;
  color: #12263f;
  font-size: 1.875rem;
  margin-bottom: 2.5rem;
  span {
    background: #0352cc;
    padding: 4px 24px;
    border-radius: 3px;
    color: #ffffff;
  }
}
.cardSupport {
  padding: 40px 0;
  transition: all 0.3s ease-in-out;
  box-shadow: unset;
  margin-bottom: 15px;
  border: 1px solid #dfe3eb;
  img {
    width: 80px;
    height: 80px;
    display: inline-block;
    margin: 0 auto;
  }
  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.09);
    transform: translateY(-0.5rem);
  }
}
.new_body {
  .wrapper_title {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30px;
    margin: 5px 0;
  }
  h4 {
    font-size: 1rem;
  }
  a {
    line-height: 1;
  }
}

//  ---------------------------Minh anh
.supportMedPro_minhanh {
  a {
    &:hover {
      color: #db2233 !important;
    }
  }
}
