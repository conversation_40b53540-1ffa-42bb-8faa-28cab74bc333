import React, { Component, Fragment } from "react";
import { MDBAlert, MDBBtn } from "mdbreact";
import styles from "./style.module.scss";

class SubBodyPage extends Component {
  render() {
    return (
      <Fragment>
        <div className={styles.page_subBody}>
          <div className={styles.alert_info}>
            <MDBAlert color="primary">
              Tính năng lọc/ sắp xếp phiếu khám đang được cập nhật, thông báo
              này chỉ tạm thời!{" "}
              {/* <strong>{this.props.resourceName}</strong> */}
              {/* <strong>Bênh viện chấn thương chỉnh hình</strong> */}
              {/* <br />
              Để sử tải dữ liệu hồ sơ trong tài khoản, vui lòng chọn tính năng
              bên cạnh để tiến hành đặt khám cho khách hàng. */}
            </MDBAlert>
          </div>
          <div className={styles.function_box}>
            <label className={styles.box_label}>Ch<PERSON><PERSON> năng</label>
            <MDBBtn color="info">T<PERSON><PERSON> hồ sơ trong tài khoản</MDBBtn>
            {/* <MDBBtn>Tải hồ sơ trong tài khoản</MDBBtn>
            <MDBBtn>Tải hồ sơ trong tài khoản</MDBBtn> */}
          </div>
        </div>
      </Fragment>
    );
  }
}

export default SubBodyPage;
