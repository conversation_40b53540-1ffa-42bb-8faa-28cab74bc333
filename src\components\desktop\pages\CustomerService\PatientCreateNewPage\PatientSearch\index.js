/* eslint-disable react/jsx-handler-names */
import { MDBBtn, MDBIcon } from "mdbreact";
import React, { Component, Fragment } from "react";
import ResultBox from "~/src/PatientList";
// actions
import styles from "./style.module.scss";

class PatientSearch extends Component {
  // false data
  state = {
    inputPatientName: "",
    inputPatientPhone: "",
    inputPatientId: "",
    patient: [{}]
  };

  onSubmit = event => {
    event.preventDefault();
    console.log(this.state);
  };

  handleInputPatientPhone = event => {
    this.setState({ inputPatientPhone: event.target.value });
  };

  handleInputPatientName = event => {
    this.setState({ inputPatientName: event.target.value });
  };

  handleInputPatientId = event => {
    this.setState({ inputPatientId: event.target.value });
  };

  render() {
    // const dumbData = {
    //   fullName: "<PERSON><PERSON>",
    //   birthdate: "20/10/1998",
    //   mobile: "01239684521"
    // };
    // const { fullName, birthdate, mobile } = dumbData;

    return (
      <Fragment>
        <div className={styles.Search_Box}>
          <form className={styles.form_class} onSubmit={this.onSubmit}>
            <div className={styles.input_item}>
              <label htmlFor="patientPhone">Số điện thoại:</label>
              <input
                type="text"
                placeholder="Số điện thoại"
                name="patientPhone"
                autoComplete="off"
                onChange={event => this.handleInputPatientPhone(event)}
              />
            </div>
            <div className={styles.input_item}>
              <label htmlFor="patientName">Tên bệnh nhân:</label>
              <input
                type="text"
                placeholder="Tên bệnh nhân"
                name="patientName"
                autoComplete="off"
                onChange={event => this.handleInputPatientName(event)}
              />
              <br />
            </div>
            <div className={styles.input_item}>
              <label htmlFor="patientId">Mã hồ sơ:</label>
              <input
                type="text"
                placeholder="Mã hồ sơ"
                name="patientId"
                autoComplete="off"
                onChange={event => this.handleInputPatientId(event)}
              />
              <br />
            </div>
            <MDBBtn color="info" type="submit">
              <MDBIcon icon="search" /> TÌM KIẾM
            </MDBBtn>
            {/* <MDBInput type="text" placeholder="Tên bệnh nhân" /> */}
          </form>
        </div>
        <div className={styles.Result_Box}>
          <ResultBox />
        </div>
      </Fragment>
    );
  }
}

export default PatientSearch;
