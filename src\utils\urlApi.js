// patient
const hospitalId = 2;

export const PATIENT = {
  // GETBYMSBN_URL: "/patient/getbymsbn",
  GETBYMSBN_URL: "/patient/umc/getbymsbn",
  GETBYUSERID_URL: "/patient/getbyuserid",
  // SEARCHBYINFO_URL: "/patient/searchbyinfo",
  SEARCHBYINFO_URL: "/patient/umc/find-patient-by-extra-info",
  INSERT_URL: "/patient/insert",
  // DELETE_URL: "/patient/delete",
  DELETE_URL: "/patient/umc/delete",
  // UMC_URL_REQUEST_CHOOSE_PROFILE_LIST: "/patient/getbyuserid",
  UMC_URL_REQUEST_CHOOSE_PROFILE_LIST: "/patient/umc/getbyuserid",
  GET_LIST_PATIENT_BY_USER_ID: "/mongo/patient/getbyuserid",
  CHECK_INSURANCE: "/patient/check_insurrance",
  VERIFY_PHONE_PATIENT: "/patient/umc/verify-phone",
  VERIFY_INFO_PATIENT: "/patient/umc/add-patient-to-user",
  CHECK_REQUIRE_PATIENT: "/mongo/patient/check-required-insert"
};

// rebooking
export const CONTACT = {
  POST_URL: "/cmessage/insert"
};

export const FAQ = {
  GET_ALL_FAQ: "https://api.pkh.vn/api/faq/getall"
};

export const USER = {
  LOGIN_SMS_FB: "/user/fbsmslogin",
  LOGIN_BY_FIREBASE: "​/user​/firebase-login",
  LOGIN_BY_ZALO: "/user/zalo-login"
};

export const FEATURES = {
  // REQUEST_ALL_FEATURES_URL: "/hospital/get_feature"
  REQUEST_ALL_FEATURES_URL: "/feature/list-feature-by-partner"
};

export const NOTIFICATIONS = {
  GET_NOTIFICATION_URL: "/noti/getall",
  UPDATE_READ_NOTIFICATION_URL: "/noti/update",
  DELETE_ALL_NOTIFICATION_URL: "/noti/delete"
};

export const HOSPITAL = {
  REQUEST_HOSPITAL_LIST_URL: "/hospital/getall",
  REQUEST_HOSPITAL_LIST_BY_FEATURE_ID_URL: id =>
    `/hospital/getbyfeature?feature_id=${id}`,
  REQUEST_FEATURE_LIST_BY_HOSPITAL_ID_URL: id =>
    `/hospital/get_feature_by_hospital?hospital_id=${id}`
};

export const FEE = {
  SEARCH: "/fee/search",
  GET_BANK: "/fee/getbank",
  GET_ORDER: "/fee/getorder",
  GET_ORDER_OFFLINE: "/fee/getorderoffline",
  GET_HISTORY: "/fee/history",
  GET_ALL_PAYMENT_METHOD: "/payment_fee/getallpayment"
};

export const PRESCRIBE = {
  GET_ALL: "/prescribe/getall",
  GET_DETAIL_BY_DATE: "/prescribe/detail_by_date",
  GET_PDF: "/prescribe/pdf"
};

export const CLINIC = {
  GET_ALL: "/clinic/getall",
  GET_DETAIL_BY_DATE: "/clinic/detail_by_date",
  GET_PDF: "/clinic/pdf"
};

export const ROOM = {
  ROOM_LIST_API_REQUEST: "/room/getlist",
  SELECTED_ROOM_AND_DOCTOR_API_REQUEST: "/room/detail",
  DOCTOR_TIME_SLOT_API_REQUEST: "/room/detail_from_doctor"
};

export const RESOURCE = {
  GET_TERM_OF_SERVICE: "/resource/getarticle?sub_id=1",
  GET_INTRODUCTION: "/resource/getarticle?sub_id=2",
  GET_PRIVATE_POLICY: "/resource/getarticle?sub_id=3",
  GET_USAGE_RULES: "/resource/getarticle?sub_id=4",
  GET_TERM_BEFORE_LOGIN: "/resource/getarticle?sub_id=5",
  // GET_HOLIDAY: "/resource/getholiday",
  GET_HOLIDAY: `/resource/getholiday?hospital_id=${hospitalId}`,
  GET_ALL_BANNER: "/resource/getallbanner",
  // GET_ALL_PROFESSION: "/resource/getallprofession",
  GET_ALL_PROFESSION: "/profession-mongo/get-all-by-partner",
  // GET_CITY: "/resource/getcity?country_code=",
  GET_CITY: "/city-mongo/get-all-by-partner",
  // GET_ALL_COUNTRY: "/resource/getallcountry",
  GET_ALL_COUNTRY: "/country-mongo/get-all-by-partner",
  // GET_DISTRICT: "/resource/getdistrict?city_id=",
  GET_DISTRICT: "/district-mongo/get-all-by-partner",
  // GET_NATION: "/resource/getdantoc",
  GET_NATION: "/nation-mongo/get-all-by-partner",
  // GET_WARD: "/resource/getward?district_id=",
  GET_WARD: "/ward-mongo/get-all-by-partner",
  // GET_FEATURE: "/resource/getfeature",
  // GET_ALL_RELATIONSHIP: "/relative/getalltype",
  GET_ALL_RELATIONSHIP: "/resource/umc/getalltype"
};

export const BOOKING = {
  GETBYUSERID_URL: "/booking/getbyuserid",
  DETAIL_URL: "/booking/detail",
  CANCEL_URL: "/booking/cancel",
  RESEND_CODE_URL: "/booking/resend_code",
  INSERT_MULTI_URL: "/booking/insertmulti",
  RESERVE: "/booking-gateway/reserve",
  GET_INFO: "/booking-gateway/booking-info",
  GET_PAYMENT_INFO: "/booking-gateway/get-payment-info",
  RESERVE_NO_AUTHEN: "/booking-gateway/reserve-by-transation",
  GET_MEDPRO_CARE: "/his-gateway/medpro-care",
  GET_MEDPRO_CARE_ROOM: "booking-gateway/telemed/meeting/rooms",
  GET_HISTORY_BOOKING_CSKH: "booking-gateway/telemed/meeting/rooms/history"
};

export const PAYMENT = {
  GET_INFO_BOOKING_API_REQUEST: "/payment/getinfobooking",
  // GET_ALL_PAYMENT_METHOD_API_REQUEST: "/payment/getallpayment",
  GET_ALL_PAYMENT_METHOD_API_REQUEST: "/payment-method/getallpayment"
};

export const SUPPORTER = {
  GET_USER_DETAIL: "/user/detail",
  GET_PATIENT: "/patient/getbyuserid",
  GET_SUPPORTER: "/supporter/getall",
  FOLLOW_SUPPORTER: "/supporter/follow",
  GET_FOLLOW_SUPPORTER: "/supporter/getfollow",
  GET_HISTORY_CHAT: "/supporter/gethistory",
  GET_PATIENT_CHATED: "/supporter/getchatlist"
};

export const REBOOKING = {
  GET_ALL_PATIENT: "/rebooking/getallpatient",
  GET_ALL: "/rebooking/getall",
  GET_REBOOKING_BY_PATIENT_ID: "/rebooking/get_by_patient_id",
  GET_ALL_FROM_HISTORY: "/rebooking/getallfromhistory"
};

export const TIME = {
  GET_TIME_SLOT: "/subject/detail"
};

export const PATIENT_FORM = {
  // POST_INSERT_URL: "/patient/insert",
  POST_INSERT_URL: "/mongo/patient/insert",
  // POST_UPDATE_URL: "/patient/update",
  POST_UPDATE_URL: "/patient/umc/update",
  // GET_DETAIL_URL: "/patient/detail"
  GET_DETAIL_URL: "/patient/umc/detail-for-update"
};

export const SUBJECT = {
  GET_SERVICE_BY_SUBJECT: "/subject/service_by_subject",
  GET_SCHEDULE_BY_SUBJECT: "/subject/schedule_by_subject"
};

export const DOCTOR = {
  GET_DOCTOR_LIST_BY_FILTER: "/doctor/search2",
  GET_DOCTOR_LIST_BY_FILTER_MA: "/doctor/search"
};

export const DATE_AND_SPECIALIST = {
  GET_ALL_SPECIALIST: "/subject/getall"
};

export const HOME = {
  // URL_REQUEST_HOSPITAL_LIST: "/hospital/getall",
  URL_REQUEST_HOSPITAL_LIST_BY_SERVICE: "/hospital/getbyfeature"
};

export const SCHEDULE = {
  GET_BY_DOCTOR: "/schedule/get_by_doctor",
  GET_SCHEDULE_DETAIL: "/schedule/detail",
  GET_BOOKING_TREE: "/his-gateway/booking-tree"
};
