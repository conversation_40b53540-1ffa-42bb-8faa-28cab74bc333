//  đường line chuột gặm
.line {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-between;
  margin: 0 -35px;

  // padding: 15px 0;
  .circle {
    width: 35px;
    height: 35px;
    background-color: whitesmoke;
    border-radius: 50%;
  }

  .dashed {
    width: 80%;
    border-top: 3px dashed whitesmoke;
  }
}

.top {
  padding: 0;
  margin-top: -28px;

  .dashed {
    border: none;
  }
}

.bottom {
  padding: 0;
  margin-bottom: -28px;

  .dashed {
    border: none;
  }
}

//  đường line chuột gặm
