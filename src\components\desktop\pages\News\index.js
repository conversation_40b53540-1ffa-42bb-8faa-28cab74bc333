import cx from "classnames";
import {
  MDBCard,
  MDBCardBody,
  MDBCardImage,
  MDBCol,
  MDBContainer,
  MDBRow,
  MDBSpinner,
  MDBView
} from "mdbreact";
import React, { useEffect } from "react";
import { connect, useDispatch } from "react-redux";
import { Link } from "react-router-dom";
import TagName from "~/components/common/atoms/TagName";
import { apiNews } from "~/utils/constants";
import { getListNewBanners } from "../../../../store/news/newsAction";
import ListNews from "../../molecules/ListNews";
import styles from "./style.module.scss";

const News = ({ loading, listNewsBanner }) => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getListNewBanners());
  }, [dispatch]);

  if (loading) {
    return (
      <div className="loading">
        <MDBSpinner big />
      </div>
    );
  }

  return (
    <>
      <HeadBanner />
      <MDBContainer className="mt-4">
        <MDBRow>
          <MDBCol md="6">
            <CardBannersRight data={listNewsBanner[0]} />
          </MDBCol>
          <MDBCol md="6">
            <ListNewBanners list={listNewsBanner.splice(1, 2)} />
          </MDBCol>
        </MDBRow>
        <MDBRow className="mt-4">
          <MDBCol sm="12" md="12" lg="8">
            <ListNews />
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    </>
  );
};

const mapStateToProps = state => {
  const {
    news: { listNewsBanner, loading }
  } = state;
  return { listNewsBanner, loading };
};

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(News);

export const HeadBanner = () => {
  return (
    <MDBContainer fluid className="p-0">
      <MDBView className={styles.img_parallax} fixed>
        <MDBContainer>
          <MDBRow>
            <MDBCol>
              <div className={styles.wapper_page_head}>
                <TagName
                  element="h1"
                  className={[
                    "title_component",
                    "title_line",
                    "title_contact",
                    "sub_title_section"
                  ]}
                >
                  <span className={styles.titleNews}>Tin tức và sự kiện</span>
                </TagName>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </MDBView>
    </MDBContainer>
  );
};

export const CardBannersRight = ({ data }) => {
  return (
    <MDBCard className={styles.card_news}>
      <MDBView className={styles.view}>
        <Link to={"/tin-tuc/" + data?.slug}>
          <MDBCardImage
            className="img-fluid rounded"
            src={apiNews + data?.image[0].url}
            waves
          />
        </Link>
      </MDBView>

      <MDBCardBody className={styles.card_body}>
        <div className={styles.title}>
          <Link to={"/tin-tuc/" + data?.slug}>{data?.title}</Link>
        </div>
        <div className={cx(styles.author, data?.author ? "" : "d-none")}>
          {data?.author}
        </div>
        <div className={styles.description}>
          <Link to={"/tin-tuc/" + data?.slug}>{data?.description}</Link>
        </div>
      </MDBCardBody>
    </MDBCard>
  );
};

export const ListNewBanners = ({ list }) => {
  return (
    <>
      {list?.map(({ title, author, description, slug }, i) => {
        return (
          <MDBCard className={styles.card_news} key={i}>
            <MDBCardBody className={styles.card_body}>
              <div className={styles.title}>
                <Link to={"/tin-tuc/" + slug}>{title}</Link>
              </div>
              <div className={cx(styles.author, author ? "" : "d-none")}>
                {author}
              </div>
              <div className={styles.description}>
                <Link to={"/tin-tuc/" + slug}>{description}</Link>
              </div>
            </MDBCardBody>
          </MDBCard>
        );
      })}
    </>
  );
};
