import {
  MDBBtn,
  MDBInput,
  MDBModal,
  MDBModalBody,
  MDBModalFooter,
  MDBModalHeader
} from "mdbreact";
import React, { useState } from "react";
import { getFormatMoney } from "~/utils/getFormatMoney";
import Line from "../segment/Line";
import MedicalCouponCare from "../segment/MedicalCouponCare";
import styles from "./style.module.scss";

const CouponNoPayment = ({
  listCare247,
  care247Available,
  onPaymentCare247
}) => {
  const [checkedMedproCare, setCheckedMedproCare] = useState("");
  const [showModalBookingCare, setShowModalBookingCare] = useState(false);

  const renderLabelMedproCare = item => {
    const comparePrice =
      getFormatMoney(item?.originalPrice) > getFormatMoney(item?.price);
    return (
      <div className={styles.radioCare247}>
        <p className={styles.careItemName}>
          <span>
            {item.name} {item?.subname && `(${item.subname})`}
          </span>
        </p>

        <p className={styles.careItemPrice}>
          <span className={styles.price}>
            Giá: {getFormatMoney(item?.price)}
            {item.currency} / {item.duration}
          </span>
          {comparePrice && (
            <span className={styles.originalPrice}>
              {getFormatMoney(item?.originalPrice)}
              {item.currency}
            </span>
          )}
        </p>
      </div>
    );
  };

  const toggleModal = () => {
    setShowModalBookingCare(false);
    setCheckedMedproCare("");
  };

  return (
    <>
      <div className={styles.coupon}>
        <Line top />
        <MedicalCouponCare care247Available={care247Available} />
        <Line bottom />
      </div>
      <div className={styles.groupButton}>
        <MDBBtn
          className={styles.buttonTele}
          color=""
          size="sm"
          href="tel:19002115"
        >
          Nhận tư vấn dịch vụ
        </MDBBtn>
        <MDBBtn
          className={styles.buttonBooking}
          color=""
          size="sm"
          onClick={() => setShowModalBookingCare(true)}
        >
          Đặt dịch vụ
        </MDBBtn>
      </div>
      <MDBModal
        isOpen={showModalBookingCare}
        centered
        className={styles.modal}
        toggle={toggleModal}
      >
        <MDBModalHeader className={styles.headerModal} toggle={toggleModal}>
          Chọn gói dịch vụ
        </MDBModalHeader>
        <div className={styles.numbericalOrderNext}>
          <p className={styles.time}>
            Giờ đồng hành: <span>{care247Available.time}</span>
          </p>
          <p>Địa chỉ: {care247Available?.partner?.name}</p>
          <p>{care247Available?.partner?.address}</p>
        </div>
        <MDBModalBody>
          {listCare247?.addonServices?.map(item => {
            return (
              <div key={item?.id} className={styles.medproCareItem}>
                <MDBInput
                  key={item.id}
                  id={item.id}
                  type="radio"
                  checked={checkedMedproCare === item.id}
                  onClick={e => {
                    if (item.id === checkedMedproCare) {
                      setCheckedMedproCare("");
                    } else {
                      setCheckedMedproCare(item.id);
                    }
                  }}
                  label={renderLabelMedproCare(item)}
                />
              </div>
            );
          })}
        </MDBModalBody>
        <MDBModalFooter>
          <MDBBtn
            color="primary"
            size="sm"
            onClick={() => {
              onPaymentCare247(checkedMedproCare);
            }}
          >
            Thanh toán
          </MDBBtn>
        </MDBModalFooter>
      </MDBModal>
    </>
  );
};

export default CouponNoPayment;
