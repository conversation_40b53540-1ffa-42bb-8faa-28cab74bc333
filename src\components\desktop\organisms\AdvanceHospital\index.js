import React from "react";
import { connect } from "react-redux";
import { MDBContainer, MDBRow, MDBCol, MDBAnimation, MDBCard } from "mdbreact";
import { Link } from "react-router-dom";
import styles from "./style.module.scss";
import search from "~/assets/img/common/search.png";
import troubles from "~/assets/img/common/troubles.png";
import PKHBtn from "~/components/common/atoms/Button";
import { redirectToMedproId } from "~/store/login/actions";
import partnerId from "~/utils/partner";
import cx from "classnames";

const AdvanceHospital = ({ history, redirectToMedproId, user }) => {
  const goToUserOrLogin = () => {
    if (user.IsAuthenticated) {
      history.push("/user");
    } else {
      redirectToMedproId();
    }
  };

  return (
    <MDBAnimation className={styles.advance_medpro}>
      <div className={styles.advance_medpro_2}>
        <MDBContainer>
          <MDBRow>
            <MDBCol size={12}>
              <div
                className={cx(
                  styles.box_advance_medpro,
                  styles["box_advance_medpro_" + partnerId]
                )}
              >
                <MDBRow>
                  <MDBCol col="6" className={styles.col}>
                    <MDBCard className={styles.mdb_card}>
                      <div className={styles.mdb_card_img}>
                        <img src={search} alt="" />
                      </div>
                      <div
                        className={styles.mdb_card_content}
                        onClick={goToUserOrLogin}
                      >
                        <h2>Tra cứu thông tin</h2>
                        <p>
                          Tra cứu thông tin đặt chỗ, thông tin bệnh nhân, lịch
                          khám ...
                        </p>
                        <p className="text-right">
                          <PKHBtn>Tra cứu</PKHBtn>
                        </p>
                      </div>
                    </MDBCard>
                  </MDBCol>
                  <MDBCol col="6" className={styles.col}>
                    <MDBCard className={styles.mdb_card}>
                      <div className={styles.mdb_card_img}>
                        <img src={troubles} alt="" />
                      </div>
                      <div
                        className={styles.mdb_card_content}
                        onClick={() => {
                          history.push("/thac-mac");
                        }}
                      >
                        <h2>Những vấn đề thường gặp</h2>
                        <div className="des">
                          Những vấn đề thường gặp, trong quá trình khám &amp;
                          tái khám
                        </div>
                        <ul>
                          <li>Quản lý thông tin bệnh nhân</li>
                          <li>Quy trình khám bệnh & nhận phiếu khám bệnh</li>
                          <li>Hoàn tất thanh toán</li>
                        </ul>
                        <p className={styles.view_all}>
                          <Link to="thac-mac">Xem thêm</Link>
                        </p>
                      </div>
                    </MDBCard>
                  </MDBCol>
                </MDBRow>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    </MDBAnimation>
  );
};

const mapStateToProps = state => ({
  user: state.user
});

export default connect(mapStateToProps, { redirectToMedproId })(
  AdvanceHospital
);
