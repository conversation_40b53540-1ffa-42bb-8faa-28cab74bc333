/* eslint-disable react/jsx-handler-names */
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import { Redirect } from "react-router-dom";
import Loadable from "react-loadable";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import { MDBSpinner, MDBAlert } from "mdbreact";
import {
  getAllPaymentMethod,
  selectedPaymentMethod,
  togglePaymentMethod,
  hideModalPaymentMethod,
  resetPaymentMethod
} from "~/store/payment/paymentAction";
import {
  submitPayment,
  resetAlertSubmitFail
} from "~/actions/umc/submitPayment";
import { reduce } from "lodash";
import Modal from "~/components/common/molecules/Modal";
import Alert from "~/components/common/atoms/Alert";

const PaymentMethodPageMobile = Loadable({
  loader: () =>
    import(/* mobile */ "~/components/mobile/pages/umc/PaymentMethod"),
  loading: LoadableLoading
});
const PaymentMethodPageDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/umc/PaymentMethod"),
  loading: LoadableLoading
});

class DetectPaymentMethod extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowAlert: false,
      isShowPaymentConfirm: false,
      loadingSubmitBooking: false
    };
  }

  methods = {
    calculate: () => {
      const { sumaryInfo, selectedMethod } = this.props;
      const total = reduce(
        sumaryInfo,
        function(sum, item) {
          const price = Number(item.selectedSpecialist.price);
          return sum + price;
        },
        0
      );
      const medpro = selectedMethod.medpro_fee
        ? Number(selectedMethod.medpro_fee)
        : 0;
      const fee = selectedMethod.rate ? Number(selectedMethod.rate) : 0;
      const feeMore = selectedMethod.const_rate
        ? Number(selectedMethod.const_rate)
        : 0;
      const phi = parseInt((total + medpro) * fee + feeMore);
      const phiThanhToan = phi;
      const phiThanhToanGiaoDich = phiThanhToan || 0;
      const totalPhi = medpro + phiThanhToanGiaoDich;
      return {
        total,
        totalPhi
      };
    },
    toggleAlertFail: () => {
      this.props.OnResetErrorMessgeSubmitPayment();
      this.setState(state => ({
        loadingSubmitBooking: false
      }));
    },
    handleDoPayment: () => {
      this.props.OnSubmitPayment();
      this.setState(state => ({
        isShowPaymentConfirm: false,
        loadingSubmitBooking: true
      }));
    },

    toggleShowPaymentConfirm: () => {
      this.setState(state => ({
        isShowPaymentConfirm: !state.isShowPaymentConfirm
      }));
    },

    handleSelectedMethod: (method, methodId) => {
      this.props.selectedPaymentMethod(method, methodId);
    },
    toggleAlert: () => {
      this.setState({
        isShowAlert: !this.state.isShowAlert
      });
    }
  };

  styles = {
    loading: {
      position: "fixed",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)"
    },

    loading_submit_booking: {
      position: "fixed",
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      zIndex: 1000,
      backgroundColor: "black",
      opacity: 0.5
    },

    body_modal: {
      fontSize: "1rem"
    },
    body_modal_image: {
      margin: "auto",
      display: "block",
      objectFit: "cover",
      marginBottom: "10px",
      maxHeight: "100px"
    }
  };

  renderBodyModalConfirmPayment = () => {
    const { selectedMethod } = this.props;
    const selectedMethodName =
      Object.keys(selectedMethod).length > 0
        ? selectedMethod.name
        : "Chọn phương thức thanh toán";
    const selectedMethodImage =
      Object.keys(selectedMethod).length > 0 ? selectedMethod.image : "";
    const { total, totalPhi } = this.methods.calculate();
    return (
      <Fragment>
        <div style={this.styles.body_modal}>
          {selectedMethodImage && (
            <img
              style={this.styles.body_modal_image}
              src={selectedMethodImage}
              alt={selectedMethodName}
            />
          )}
          <p>
            Thanh toán số tiền{" "}
            <strong>{Number(total + totalPhi).toLocaleString("vi-VN")}đ</strong>{" "}
            bằng {selectedMethodName}
          </p>
          <MDBAlert color="primary">
            Bạn sẽ nhận được phiếu khám bệnh ngay khi{" "}
            <strong>thanh toán thành công</strong>. Trường hợp không nhận được
            phiếu khám bệnh, vui lòng liên hệ <strong>19002115</strong>.
          </MDBAlert>
        </div>
      </Fragment>
    );
  };

  componentDidMount() {
    this.props.OnRequestAllPaymentMethod();
    this.props.hideModalPaymentMethod();
    this.props.resetPaymentMethod();
  }

  componentWillUnmount() {
    this.props.OnResetErrorMessgeSubmitPayment();
  }

  render() {
    const {
      device,
      errorMsg,
      errorBooking,
      isRedirectToPaymentSupportPage
    } = this.props;
    const {
      isShowAlert,
      isShowPaymentConfirm,
      loadingSubmitBooking
    } = this.state;

    const errorMessage =
      errorBooking.error_code === 1011
        ? `Chuyên khoa ${errorBooking.booking_info.subject.name} 
        đã hết số khung giờ này. Vui lòng chọn lại khung giờ khác!`
        : errorBooking.error_code;

    if (isRedirectToPaymentSupportPage)
      return <Redirect to="/ho-tro-thanh-toan" />;
    return (
      <Fragment>
        {device === "mobile" ? (
          <PaymentMethodPageMobile {...this.props} {...this.methods} />
        ) : (
          <PaymentMethodPageDesktop {...this.props} {...this.methods} />
        )}

        {loadingSubmitBooking && !errorMsg && (
          <div style={this.styles.loading_submit_booking}>
            <div style={this.styles.loading}>
              <MDBSpinner big crazy tag="div" />
            </div>
          </div>
        )}

        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={isShowPaymentConfirm}
          title="Xác nhận thanh toán"
          children={this.renderBodyModalConfirmPayment()}
          centered
          footer
          footerConfirm
          className="centered"
          toggle={this.methods.toggleShowPaymentConfirm}
          cancelText="Quay lại"
          okText="Đồng ý"
          onCancel={this.methods.toggleShowPaymentConfirm}
          onOk={this.methods.handleDoPayment}
        />

        <Alert
          isModal={!!errorBooking}
          message={errorMessage || ""}
          toggleAlert={this.methods.toggleAlertFail}
        />
        <Alert
          isModal={isShowAlert}
          message="Vui lòng chọn phương thức thanh toán"
          toggleAlert={this.methods.toggleAlert}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    allPaymentMethod: { selectedMethod, loading, data, showModal },
    doctorAndTime: { sumaryInfo },
    umcSubmitPayment: {
      isRedirectToPaymentSupportPage,
      errorMsg,
      errorBooking
    },
    features: { selectedFeature, selectedFeatureBooking }
  } = state;
  return {
    device: type,
    selectedMethod,
    sumaryInfo,
    isRedirectToPaymentSupportPage,
    errorMsg,
    errorBooking,
    loading,
    showModal,
    data,
    selectedFeature,
    selectedFeatureBooking
  };
};

const mapDispatchToProps = dispatch => ({
  OnRequestAllPaymentMethod: () => {
    dispatch(getAllPaymentMethod());
  },
  OnTogglePaymentMethod: () => {
    dispatch(togglePaymentMethod());
  },
  OnSubmitPayment: () => {
    dispatch(submitPayment());
  },
  OnResetErrorMessgeSubmitPayment: () => {
    dispatch(resetAlertSubmitFail());
  },
  selectedPaymentMethod: (method, methodId) => {
    dispatch(selectedPaymentMethod(method, methodId));
  },
  hideModalPaymentMethod: () => {
    dispatch(hideModalPaymentMethod());
  },
  resetPaymentMethod: () => dispatch(resetPaymentMethod())
});

const PaymentMethodHelmet = withTitle({
  component: DetectPaymentMethod,
  title: "Medpro | Chọn hình thức thanh toán"
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PaymentMethodHelmet);
