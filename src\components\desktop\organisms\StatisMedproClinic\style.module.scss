.Container{
  padding: 64px 0;
  background-image: url('../../../../assets/img/desktop/HompageClinic/BG_CLINIC.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;

  min-width: 100%;

  display: flex;
  flex-direction: column;
  align-items: center;
  .Label{
    text-align: center;
    padding-bottom: 24px;
    text-transform: uppercase;
    p{
      font-size: 28px;
      font-weight: 700;
      color: #1E56C5;
    }
  }
  .rowStatis{
    max-width: 1440px;
    .colStatis{
      .listCard{
        list-style: none;
        display: flex;
        justify-content: space-around;
        padding: 0;
        li{
          width: 193px;
          .card{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .img{
              max-width: 112px;
              max-height: 112px;
            }
            p{
              display: flex;
              flex-direction: column;
              text-align: center;
              .statis{
                color: #1E56C5;
                font-size: 28px;
                font-weight: 600;
              }
              .subtitle{
                font-size: 14px;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}
@media (max-width: 600px){
  .Label{
    p{
      font-size: 18px !important;
    }
  }
  .listCard{
    flex-wrap: wrap !important;
    justify-content: flex-start !important;
    li{
      min-width: calc(100% / 3) !important;
      @media (max-width: 425px){
        min-width: calc(100% / 2) !important;
      }
    }
  }
}
@media (max-width: 1024px){
  .rowStatis{
    min-width: 100%;
  }
  .Label{
    p{
      font-size: 22px !important;
    }
  }
  .listCard{
    li{
      width: calc(100% / 5) !important;
      .card{
        .img{
          max-width: 80px !important;
          max-height: 80px !important;
        }
        p{
          .statis{
            font-size: 18px !important;
          }
          .subtitle{
            font-size: 12px !important;
          }
        }
      }
    }
  }
}
