import { get, size } from "lodash";
import { MDBSpinner } from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import Slider from "react-slick";
import history from "~/history";
import { resetPayment } from "~/store/payment/paymentAction";
import {
  addScheduleRepayment,
  checkRepayment
} from "~/store/totalData/actions";
import { getRouteFollowPartnerId } from "~/utils/func";
import Coupon from "./Coupon";
import CardDownload from "./segment/CardDownload";
import styles from "./style.module.scss";

class MedicalCoupon extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  handleRePayment = () => {
    const {
      paymentInformation: { bookingInfo },
      IsAuthenticated
    } = this.props;

    this.props.checkRepayment(true);
    this.props.addScheduleRepayment(bookingInfo);

    if (IsAuthenticated) {
      this.props.resetPayment();
      const route = getRouteFollowPartnerId(
        "/xac-nhan-thong-tin",
        bookingInfo.partnerId
      );
      history.push(route);
    } else {
      history.push(`/${bookingInfo.partnerId}/hinh-thuc-thanh-toan`);
    }
  };

  renderCoupon = () => {
    const { paymentInformation } = this.props;
    return paymentInformation?.map((item, i) => {
      const status = get(item, "bookingInfo.status", 1);
      const layout = get(item, "layout", "");
      return (
        <div key={i} className={styles.item}>
          {size(paymentInformation) > 1 && (
            <p
              style={{
                textAlign: "center",
                fontSize: "18px",
                fontWeight: "bold"
              }}
            >
              Phiếu khám {i + 1}
            </p>
          )}
          {[1].includes(status) && layout !== "INFORM" && (
            <CardDownload global={this.props} />
          )}
          <Coupon item={item} key={i} keys={i} />
        </div>
      );
    });
  };

  render() {
    const { paymentInformation, loading = true, type } = this.props;
    console.log("type :>> ", type);
    if (loading) {
      return (
        <div className={styles.loading}>
          <MDBSpinner big crazy tag="div" />
        </div>
      );
    }
    function NextArrow(props) {
      const { className, style, onClick } = props;
      return (
        <button
          className={className}
          style={{
            ...style,
            border: "1px solid #1da1f2",
            borderRadius: "50%",
            width: "35px",
            height: "35px",
            margin: "0 15px ",
            marginBottom: "30px",
            color: "#1da1f2"
          }}
          onClick={onClick}
        >
          <i className="fa fa-chevron-right" />
        </button>
      );
    }
    function PrevArrow(props) {
      const { className, style, onClick } = props;
      return (
        <button
          className={className}
          style={{
            ...style,
            border: "1px solid #1da1f2",
            color: "#1da1f2",
            borderRadius: "50%",
            width: "35px",
            height: "35px",
            margin: "0 15px ",
            marginBottom: "30px"
          }}
          onClick={onClick}
        >
          <i className="fa fa-chevron-left" />
        </button>
      );
    }

    const settings = {
      arrows: false,
      infinite: false,
      slidesToShow: 3,
      slidesToScroll: 1,
      lazyLoad: true,
      dots: false,
      variableWidth: true,
      centerMode: true,
      swipe: false,
      nextArrow: <NextArrow />,
      prevArrow: <PrevArrow />,
      responsive: [
        {
          breakpoint: 480,
          settings: {
            arrows: true,
            infinite: true,
            slidesToShow: 1,
            slidesToScroll: 1
          }
        }
      ]
    };

    const renderCoupon = this.renderCoupon();

    return (
      <React.Fragment>
        <div className={styles.listCoupon}>
          {size(paymentInformation) > 1 ? (
            <Slider {...settings} className={styles.sliderCoupon}>
              {renderCoupon}
            </Slider>
          ) : (
            <>{renderCoupon}</>
          )}
        </div>
      </React.Fragment>
    );
  }
}
const mapStateToProps = state => {
  const {
    global,
    user: { IsAuthenticated },
    totalData: { paymentInformation, loading, reviewBooking }
  } = state;
  return {
    global,
    IsAuthenticated,
    paymentInformation,
    loading,
    reviewBooking
  };
};
const mergeProps = (stateProps, dispatchProps, ownProps) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps
});
export default connect(
  mapStateToProps,
  {
    checkRepayment,
    addScheduleRepayment,
    resetPayment
  },
  mergeProps
)(MedicalCoupon);
