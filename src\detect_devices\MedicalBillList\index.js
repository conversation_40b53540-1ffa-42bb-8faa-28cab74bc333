/* eslint-disable react/no-did-update-set-state */
import { find, get } from "lodash";
import { MDBInput } from "mdbreact";
import React, { Component, Fragment } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withRout<PERSON> } from "react-router-dom";
import Modal from "~/components/common/molecules/Modal";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import { client } from "~/utils/medproSDK";
import {
  cancelMedicalBill,
  clearCanceledBookingMessage,
  resendSMS
} from "~/store/booking/bookingAction";
import { hideAlertSMSOK } from "~/store/payment/paymentAction";
import {
  addScheduleRepayment,
  getBookingInfo,
  getPaymentInfo
} from "~/store/totalData/actions";
import {
  CONTENT_CANCEL_MEDICAL_BILL,
  NO,
  OTHER_NUMBER,
  TITLE_CANCEL_MEDICAL_BILL,
  YES
} from "~/utils/constants";
import { getMedicalBillStatus } from "~/utils/func";
const info = get(partnerInfo, "info");

const hospitalName = find(info, { key: "name" });

const Desktop = Loadable({
  loader: () => import("~/components/desktop/pages/MedicalBillList"),
  loading: LoadableLoading
});
class DetectMedicalBillList extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isOpenCancelModal: false,
      isOpenResendSMSModal: false,
      selectedIndex: 2,
      otherNumber: "",
      errorMessage: "",
      shareToPay: false,
      isOpenThankyouModal: false,
      cancelBookingGuild: ""
    };
  }

  method = {
    handleToggleCancelModal: () => {
      const { isOpenCancelModal } = this.state;
      this.setState({ isOpenCancelModal: !isOpenCancelModal });
    },
    handleToggleResendSMSModal: () => {
      const { isOpenResendSMSModal } = this.state;
      this.setState({ isOpenResendSMSModal: !isOpenResendSMSModal });
    },
    canShowPrintButton: () =>
      this.props.data.length === 1 &&
      getMedicalBillStatus(
        this.props.medicalBillDetail.status,
        this.props.medicalBillDetail.booking_date
      ) === "Chưa khám"
  };

  hideAlert = () => {
    this.props.hideAlertSMSOK();
  };

  closeCancelMedicalBillModal = () => {
    this.setState({
      isOpenCancelModal: false
    });
  };

  closeThankyouModal = () => {
    this.setState({
      isOpenThankyouModal: false
    });
  };

  cancelMedicalBill = () => {
    const { handleCancelMedicalBill, canceledMedicalBill } = this.props;

    const textCancel = get(canceledMedicalBill, "cancelBookingGuide", "");
    handleCancelMedicalBill();
    this.method.handleToggleCancelModal();
    this.setState({
      isOpenThankyouModal: textCancel !== ""
    });
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  closeResendSMSModal = () => {
    this.setState({ isOpenResendSMSModal: false });
  };

  handleInputChange = otherNumber => {
    this.setState({ otherNumber, errorMessage: "" });
  };

  handleOnChange = selectedIndex => () => {
    this.setState({ selectedIndex });
    if (selectedIndex === 3)
      setTimeout(() => {
        this.input.focus();
      }, 200);
  };

  resendSMS = number => {
    const { handleResendSMS, user, data: phieuKham } = this.props;
    const data = {
      user_id: user.user_id,
      access_token: user.access_token,
      booking_id: phieuKham[0].id,
      mobile: number
    };

    handleResendSMS(data);
    this.closeResendSMSModal();
  };

  handleSend = () => {
    const { selectedIndex, otherNumber } = this.state;
    const {
      medicalBillDetail: { user: userInfo, patient: patientInfo },
      selectedHospital
    } = this.props;

    const userNumber = userInfo ? userInfo.phone : "";

    // BV Nhi đồng: patient number sử dụng relative mobile
    // Sử dụng mobile hoặc realativeMobile để gửi tin nhắn (ko dùng censored)
    let patientNumber = "";
    if (selectedHospital.id === 4) {
      patientNumber =
        patientInfo && patientInfo.relative ? patientInfo.relative.mobie : "";
    } else {
      patientNumber = patientInfo ? patientInfo.mobile : "";
    }

    let sendNumber = 0;
    switch (selectedIndex) {
      case 1:
        sendNumber = userNumber;
        break;
      case 2:
        sendNumber = patientNumber;
        break;
      case 3:
        sendNumber = otherNumber;
        break;
      default:
        sendNumber = userNumber;
    }
    this.resendSMS(sendNumber);
  };

  Repayment = PaymentMethod => {
    switch (PaymentMethod) {
      case "Thẻ khám bệnh" &&
        "Thanh toán bằng Thẻ ATM nội địa/Internete Banking":
        return "1 đến 5";
      case "Thanh toán bằng Ví MoMo" && "Thanh toán bằng Ví SmartPay":
        return "1 đến 3";
      case "Thanh toán bằng Thẻ quốc tế Visa, Master, JCB":
        return "5 đến 45";
      case "test":
        return "testing ngày";
      default:
        return "Chưa xác định";
    }
  };

  renderPopupCancelSuccess = () => {
    const { canceledMedicalBill } = this.props;
    const textCancel = get(canceledMedicalBill, "cancelBookingGuide", "");

    return (
      <div
        dangerouslySetInnerHTML={{
          __html: textCancel
        }}
      />
    );
  };

  renderBodyModalResendSMS = () => {
    const { selectedIndex, errorMessage } = this.state;
    const {
      medicalBillDetail: { user: userInfo }
    } = this.props;

    const userNumber = userInfo ? userInfo.phone : "";

    // BV Nhi đồng: patient number sử dụng relative mobile
    const patientNumber = "";

    return (
      <Fragment>
        {userInfo && userInfo.phone !== null && (
          <MDBInput
            gap
            onChange={this.handleOnChange(1)}
            checked={selectedIndex === 1}
            label={userNumber}
            type="radio"
            id="user"
          />
        )}
        <MDBInput
          gap
          onChange={this.handleOnChange(2)}
          checked={selectedIndex === 2}
          label={patientNumber}
          type="radio"
          id="patient"
        />
        <MDBInput
          gap
          onChange={this.handleOnChange(3)}
          checked={selectedIndex === 3}
          label={OTHER_NUMBER}
          type="radio"
          id="other"
        />
        <div
          style={
            selectedIndex === 3 ? { display: "block" } : { display: "none" }
          }
        >
          <MDBInput
            type="number"
            hint="Vui lòng nhập số điện thoại"
            getValue={() => this.handleInputChange()}
            inputRef={el => (this.input = el)}
          />
        </div>
        {errorMessage ? <span>{errorMessage}</span> : []}
      </Fragment>
    );
  };

  componentDidMount() {
    const urlParams = new URLSearchParams(window.location.search);
    const {
      match: {
        params: { code }
      }
    } = this.props;
    const mpTransaction = urlParams.get("mpTransaction");
    console.log("object", mpTransaction, this.props);
    if (mpTransaction !== null) {
      this.props.getPaymentInfo(mpTransaction, "payment");
    } else if (code !== undefined) {
      this.props.getBookingInfo(code);
    }
  }

  componentDidUpdate(prevProps) {
    const {
      match: {
        params: { code }
      }
    } = this.props;
    const {
      match: {
        params: { code: prevCode }
      }
    } = prevProps;
    if (code !== prevCode) {
      if (code !== undefined) {
        this.props.getBookingInfo(code);
      }
    }
  }

  // componentWillUnmount() {
  //   this.props.clearCanceledBookingMessage();
  // }

  render() {
    const { isOpenCancelModal, isOpenThankyouModal } = this.state;

    return (
      <React.Fragment>
        <Desktop {...this.props} {...this.method} />

        <Modal
          modal={isOpenThankyouModal}
          title="Thông báo"
          toggle={this.closeThankyouModal}
          centered
          children={this.renderPopupCancelSuccess()}
        />

        <Modal
          modal={isOpenCancelModal}
          title={TITLE_CANCEL_MEDICAL_BILL}
          toggle={this.closeCancelMedicalBillModal}
          centered
          footer
          footerConfirm
          children={CONTENT_CANCEL_MEDICAL_BILL}
          cancelText={NO}
          okText={YES}
          onCancel={() => this.closeCancelMedicalBillModal()}
          onOk={() => this.cancelMedicalBill()}
        />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { info },
    hospital: { selectedHospital },
    totalData: { paymentInformation, service, loading },
    booking: { canceledMedicalBill }
  } = state;

  return {
    canceledMedicalBill,
    device: type,
    user: info,
    selectedHospital,
    paymentInformation,
    service,
    loading
  };
};

const mapDispatchToProps = dispatch => ({
  handleCancelMedicalBill: data => {
    dispatch(cancelMedicalBill(data));
  },
  addScheduleRepayment: info => {
    dispatch(addScheduleRepayment(info));
  },
  handleResendSMS: data => {
    dispatch(resendSMS(data));
  },
  hideAlertSMSOK: () => {
    dispatch(hideAlertSMSOK());
  },
  clearCanceledBookingMessage: () => dispatch(clearCanceledBookingMessage()),
  getPaymentInfo: (id, text) => dispatch(getPaymentInfo(id, text)),
  getBookingInfo: (id, text) => dispatch(getBookingInfo(id))
});

const MedicalBillListHelmet = withTitle({
  component: DetectMedicalBillList,
  title: `${hospitalName.value} | Kết quả đặt phiếu khám bệnh`
});

export default withRouter(
  connect(mapStateToProps, mapDispatchToProps)(MedicalBillListHelmet)
);
