import {
  MDBAnimation,
  MDBCard,
  MDBCardBody,
  MDBCardTitle,
  MDBCol,
  MDBContainer,
  MDBRow
} from "mdbreact";
import React from "react";
import LazyLoad from "react-lazyload";
import facebook from "~/assets/img/desktop/icon/facebook.svg";
import qrmed from "~/assets/img/desktop/icon/qrmed.jpg";
import qrcode from "~/assets/img/desktop/icon/qrthuong.png";
import support from "~/assets/img/desktop/icon/support.svg";
import TagName from "~/components/common/atoms/TagName";
import styles from "./style.module.scss";
import partnerId from "~/utils/partner";
import cx from "classnames";

const SupportMedPro = props => {
  return (
    <MDBAnimation
      reveal="true"
      type="fadeIn"
      className={cx(styles.supportMedPro, styles["supportMedPro_" + partnerId])}
    >
      <div className={styles.supportMedPro_inner}>
        <MDBContainer>
          <MDBRow>
            <MDBCol size="12">
              <div>
                <TagName element="span" className={["title_section"]}>
                  Hỗ trợ
                </TagName>
              </div>
              <TagName
                element="h3"
                className={["sub_title_section", "title_featureMedpro"]}
              >
                Các hình thức hỗ trợ
              </TagName>
            </MDBCol>
          </MDBRow>
          <MDBRow>
            <MDBCol size={6} md="3">
              <MDBCard className={styles.cardSupport}>
                <LazyLoad height={100}>
                  <img src={support} alt="" />
                </LazyLoad>
                <MDBCardBody className={styles.new_body}>
                  <div className={styles.wrapper_title}>
                    <MDBCardTitle>Hỗ trợ đặt khám</MDBCardTitle>
                  </div>
                  <div className={styles.wrapper_title}>
                    <a href="tel:19002115" title="19002115">
                      1900-2115
                    </a>
                  </div>
                </MDBCardBody>
              </MDBCard>
            </MDBCol>
            <MDBCol size={6} md="3">
              <MDBCard className={styles.cardSupport}>
                <a
                  href="https://www.facebook.com/medpro.vn"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <LazyLoad height={100}>
                    <img src={facebook} alt="" />
                  </LazyLoad>
                </a>
                <MDBCardBody className={styles.new_body}>
                  <div className={styles.wrapper_title}>
                    <MDBCardTitle>Fanpage Facebook</MDBCardTitle>
                  </div>
                  <div className={styles.wrapper_title}>
                    <a
                      href="https://www.facebook.com/medpro.vn"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Bấm vào đây
                    </a>
                  </div>
                </MDBCardBody>
              </MDBCard>
            </MDBCol>
            <MDBCol size={6} md="3">
              <MDBCard className={styles.cardSupport}>
                <a
                  href="https://zalo.me/*******************"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <LazyLoad height={100}>
                    <img src={qrmed} alt="" />
                  </LazyLoad>
                </a>

                <MDBCardBody className={styles.new_body}>
                  <div className={styles.wrapper_title}>
                    <MDBCardTitle>Hỗ trợ ZALO</MDBCardTitle>
                  </div>
                  <div className={styles.wrapper_title}>
                    <a
                      href="https://zalo.me/*******************"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Bấm vào đây
                    </a>
                  </div>
                </MDBCardBody>
              </MDBCard>
            </MDBCol>
            <MDBCol size={6} md="3">
              <MDBCard className={styles.cardSupport}>
                <a
                  href="https://m.me/medpro.vn"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <LazyLoad height={100}>
                    <img src={qrcode} alt="" />
                  </LazyLoad>
                </a>
                <MDBCardBody className={styles.new_body}>
                  <div className={styles.wrapper_title}>
                    <MDBCardTitle>Chat Facebook</MDBCardTitle>
                  </div>
                  <div className={styles.wrapper_title}>
                    <a
                      href="https://m.me/medpro.vn"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Bấm vào đây
                    </a>
                  </div>
                </MDBCardBody>
              </MDBCard>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    </MDBAnimation>
  );
};

export default SupportMedPro;
