/*
 * MDBootstrap Accordion Extended
 * Learn more: https://mdbootstrap.com/docs/jquery/javascript/accordion/
 * About MDBootstrap: https://mdbootstrap.com/
 */
.accordion-gradient-bcg {
  background: linear-gradient(45deg, rgba(234, 21, 129, 0.6), rgba(10, 23, 187, 0.6) 100%); }

.accordion.md-accordion.accordion-1 p, .accordion.md-accordion.accordion-2 p, .accordion.md-accordion.accordion-3 p, .accordion.md-accordion.accordion-4 p, .accordion.md-accordion.accordion-5 p {
  font-size: 1rem; }

.accordion.md-accordion.accordion-1 .card, .accordion.md-accordion.accordion-2 .card, .accordion.md-accordion.accordion-4 .card, .accordion.md-accordion.accordion-5 .card {
  border: 0; }
  .accordion.md-accordion.accordion-1 .card .card-header, .accordion.md-accordion.accordion-2 .card .card-header, .accordion.md-accordion.accordion-4 .card .card-header, .accordion.md-accordion.accordion-5 .card .card-header {
    border: 0; }

.accordion.md-accordion.accordion-1 .card .card-body {
  line-height: 1.4; }

.accordion.md-accordion.accordion-2 .card {
  background-color: transparent; }
  .accordion.md-accordion.accordion-2 .card .card-body {
    border: 0;
    border-radius: 3px; }

.accordion.md-accordion.accordion-3 {
  border-radius: 3px; }
  .accordion.md-accordion.accordion-3 .fas.fa-angle-down, .accordion.md-accordion.accordion-3 .fab.fa-angle-down, .accordion.md-accordion.accordion-3 .far.fa-angle-down {
    margin-top: -10px; }

.accordion.md-accordion.accordion-4 .card:last-of-type .card-body {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px; }

.accordion.md-accordion.accordion-5 .card {
  background-color: transparent; }
  .accordion.md-accordion.accordion-5 .card .card-header {
    background-color: #f44336;
    transition: .3s; }
    .accordion.md-accordion.accordion-5 .card .card-header:hover {
      transition: .3s;
      background-color: #455a64; }
    .accordion.md-accordion.accordion-5 .card .card-header .fas, .accordion.md-accordion.accordion-5 .card .card-header .fab, .accordion.md-accordion.accordion-5 .card .card-header .far {
      background-color: #fff;
      border-top-left-radius: 3px; }
  .accordion.md-accordion.accordion-5 .card .card-body {
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px; }

.accordion.md-accordion.accordion-blocks .card {
  margin-bottom: 1.2rem;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); }
  .accordion.md-accordion.accordion-blocks .card .card-body {
    border-top: 1px solid #eee; }
