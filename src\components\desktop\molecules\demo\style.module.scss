@import "src/assets/scss/custom-variables.scss";

.loading {
  min-height: 70vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  align-content: center;
  opacity: 0.5;
}

.banner {
  min-width: 360px;
  max-width: 360px;
  background-image: url("/assets/img/bannerBillPrint.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;

  .title {
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    color: white;
    margin-bottom: 5px;
  }
  .subTitle {
    font-size: 13px;
    text-align: center;
    color: white;
    margin-bottom: 5px;
  }
  .group {
    display: flex;
    justify-content: space-around;
    .QR {
      padding: 5px 5px 0;
      border-radius: 5px;
      background-color: white;
    }
    .linkDown {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      align-content: center;
      p {
        font-style: italic;
        color: white;
        font-size: 13px;
        font-weight: bolder;
        margin: 0 0 0 10px;
      }
    }
  }
}
.print {
  background-color: white;
  padding: 15px;
  max-width: 360px;
  color: #12263f;
  font-size: 14px;
  @media screen and (max-width: 576px) {
    margin: 0px;
    max-width: unset;
    width: 100%;
  }
  ul {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
    li {
      b,
      strong {
        font-weight: 500;
        text-align: right;
      }
      .nbold {
        font-weight: normal;
      }
      p {
        margin-bottom: 0;
      }
      .column_left {
        font-weight: 500;
      }
    }
  }

  .totalPaymentMessage {
    color: orangered;
    margin-top: 20px;
    text-align: center;
  }

  .green {
    color: black;
  }

  .title_hospital {
    margin-bottom: 10px;
    text-align: center;
    img {
      width: 70%;
      height: auto;
      margin: 15px 0;
    }
    .sub_title {
      font-size: 18px;
      text-align: center;
      font-weight: bold;
    }
    .nameParner {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    .addressParner {
      font-size: 13px;
    }
  }

  .bar_code {
    margin-bottom: 20px;
    text-align: center;

    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    p {
      font-weight: 500;
      margin-bottom: 5px;
    }
    svg {
      max-width: 100%;
      margin: 0 0 !important;
    }
  }

  .form_code {
    text-align: center;
    margin-bottom: 15px;
  }
  .info_examination {
    text-align: center;
    margin-bottom: 20px;
    ul {
      li {
        font-weight: 500;
      }
    }
  }

  .txtNum {
    font-size: 13px !important;
    font-weight: 500 !important;
  }
  .number,
  .gray {
    font-family: Roboto;
    font-size: 60px;
    color: black;
    font-weight: bold;
    text-align: center;
    margin: 0;
  }
  .gray {
    color: #c6cace;
  }

  .top_note {
    text-align: center;
    font-weight: 500;
    margin-bottom: 5px;
    font-size: 0.9rem;
  }
  .attention {
    color: #df0000;
    font-style: italic;
    font-size: 14px;
    font-weight: bold;
  }
  .time_note {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;

    span {
      padding: 7px 25px;
      border-radius: 15px;
      color: white;
      font-size: 14px;
    }

    .timeNoteCancel {
      color: #c6cace;
    }
    .greenNote {
      background-color: black;
    }
    .redNote {
      background-color: black;
    }
    .greyNote {
      background-color: #c6cace;
    }
    .btnShare {
      margin-left: 0.5rem;
      display: inline-block;
      padding: 7px 25px;
      border-radius: 15px;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border: 1px solid snow;
      box-shadow: 0 2.8px 2.2px rgba(0, 0, 0, 0.034),
        0 12.5px 10px rgba(0, 0, 0, 0.06), 0 100px 80px rgba(0, 0, 0, 0.12);
      a {
        color: blue;
      }
    }
  }
  .status_number {
    text-align: center;
    span {
      display: inline-block;
      padding: 0.25em 0.4em;
      font-size: 75%;
      font-weight: 700;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25rem;
      background-color: #6e84a3;
      color: #ffffff;
    }
  }
  .list_detail {
    margin-top: 20px;
    margin-bottom: 10px;
    ul {
      li {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        align-content: center;
        justify-content: space-between;

        &:last-child {
          margin-bottom: 0;
        }
        span {
          display: block;
          min-width: 160px;
        }
      }
    }
  }
  .note {
    margin-bottom: 10px;
    font-style: italic;
  }
  .profile_number,
  .note_time {
    margin-bottom: 10px;
  }

  .organization {
    margin-bottom: 15px;
    a {
      width: 100%;
      display: flex;
      align-items: center;
      align-content: center;
      justify-content: center;

      p {
        margin-right: 5px;
        margin-bottom: 0;
      }
      img {
        width: 80px;
      }
    }
  }
  .thanhToanLai {
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: center;
    justify-content: center;
    .noti_unpayment {
      text-align: center;
      color: red;
      margin: 15px 0;
    }
  }
  .awaitMessage {
    color: #ffb340;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    flex-direction: column;
    margin-bottom: 15px;
    p {
      padding: 0 35px;
    }
    .spin {
      display: block;
    }
  }

  //  đường line chuột gặm
  .line {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    margin: 0 -35px;
    // padding: 15px 0;
    .circle {
      width: 35px;
      height: 35px;
      background-color: whitesmoke;
      border-radius: 50%;
    }
    .dashed {
      width: 80%;
      border-top: 3px dashed whitesmoke;
    }
  }
  .top {
    padding: 0;
    margin-top: -28px;
    .dashed {
      border: none;
    }
  }
  .bottom {
    padding: 0;
    margin-bottom: -28px;
    .dashed {
      border: none;
    }
  }
  //  đường line chuột gặm
}

@media print {
  body {
    width: 210mm;
    height: 297mm;
  }
  @page {
    margin: 0;
  }
}
