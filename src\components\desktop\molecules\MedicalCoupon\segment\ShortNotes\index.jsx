import cx from "classnames";
import { get } from "lodash";
import React from "react";
import styles from "./style.module.scss";

const ShortNotes = ({ bookingInfo }) => {
  const status = get(bookingInfo, "status", 1);
  const cancelMessage = get(bookingInfo, "cancelMessage", "");
  const totalPaymentMessage = get(bookingInfo, "totalPaymentMessage", "");
  const totalMessageExtra = get(bookingInfo, "totalMessageExtra", "");

  const service = get(bookingInfo, "service");

  return (
    <div className={cx(styles.note)}>
      {cancelMessage && (
        <span className={styles.cancelMessage}>{cancelMessage}</span>
      )}
      {bookingInfo.shortDescription && (
        <div
          dangerouslySetInnerHTML={{ __html: bookingInfo.shortDescription }}
        />
      )}
      {service?.serviceShortDescription && ![-2, 2, 0, 6].includes(status) && (
        <div
          className={styles.cancelMessage}
          dangerouslySetInnerHTML={{
            __html: service?.serviceShortDescription
          }}
        />
      )}

      {/* text: số tiền bệnh viện thanh toán giúp */}
      {[2, 1, -2].includes(status) && totalPaymentMessage && (
        <div className={styles.totalPaymentMessage}>
          <span>{totalPaymentMessage}</span>
          <span className={styles.MessageExtra}>{totalMessageExtra}</span>
        </div>
      )}
    </div>
  );
};

export default ShortNotes;
