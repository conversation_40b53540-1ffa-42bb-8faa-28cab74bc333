import { find, get } from "lodash";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import { persistor } from "~/store";
import { setCSToken } from "~/store/customerService/customerServiceActions";
import { resetListHospital } from "~/store/hospital/hospitalActions";
import { redirectToMedproId } from "~/store/login/actions";
import { getExtraConfig } from "~/store/totalData/actions";
import { logOut } from "~/store/user/userAction";
import { client } from "~/utils/medproSDK";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const CustomerServicePageDesktop = Loadable({
  loader: () => import("~/components/desktop/pages/CustomerService"),
  loading: LoadableLoading,
  delay: 500
});

class CustomerServicePage extends Component {
  componentDidMount() {
    const {
      user: { info },
      cs: { cskhToken }
    } = this.props;

    if (info) {
      // đã đăng nhập thì kiểm tra thông tin người dùng
      if (info.isCS === "true") {
        const _csToken =
          cskhToken || window.localStorage.getItem("cskhToken") || null;
        const _token =
          info?.token || window.localStorage.getItem("jwt") || null;

        client.setCskhToken(_csToken);
        client.setToken(_token);
        this.props.setCSToken(_csToken);
        this.props.resetListHospital();
        this.props.getExtraConfig();
      } else {
        window.location.href = "https://medpro.vn/";
        persistor.purge();
      }
    } else {
      this.props.redirectToMedproId();
    }
  }

  render() {
    const { user } = this.props;
    if (user?.info?.isCS === "false" || !user?.info) {
      return null;
    }

    return (
      <React.Fragment>
        <CustomerServicePageDesktop
          {...this.props}
          menuBarBottomRef={this.menuBarBottomRef}
        />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user,
    customerService: cs
  } = state;
  return {
    cs,
    user,
    device: type
  };
};

const mapDispatchToProps = {
  logOut: logOut,
  setCSToken,
  resetListHospital,
  getExtraConfig,
  redirectToMedproId
};

const CustomerServicePageHelmet = withTitle({
  component: CustomerServicePage,
  title: `${hospitalName.value} | Cham soc khach hang`
});

export default withRouter(
  connect(mapStateToProps, mapDispatchToProps)(CustomerServicePageHelmet)
);
