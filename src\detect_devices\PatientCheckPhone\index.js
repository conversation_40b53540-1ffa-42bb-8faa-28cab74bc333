import React, { Component } from "react";
import { connect } from "react-redux";
import Loadable from "react-loadable";
import { get, find } from "lodash";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import {
  selectedPatient,
  toggleModalConfirm,
  resetModalConfirmPatient,
  verifyPhonePatient,
  verifyPatientWithoutPhone
} from "~/store/patient/patientAction";
import { partnerInfo } from "~/configs/partnerDetails";
import { patternPhone } from "~/utils/constants";
const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const PatientCheckPhoneDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/PatientCheckPhone"),
  loading: LoadableLoading
});

class DetectPatientCheckPhone extends Component {
  state = {
    phone: "",
    errorPhone: ""
  };

  methods = {
    submitPhone: () => {
      console.log(this.state.phone);
      let phone = this.state.phone;
      if (phone !== "") {
        this.setState({ errorPhone: "" });
        if (phone.length === 9 && !phone.startsWith("0")) {
          phone = `0${phone}`;
        }
        this.props.verifyPhonePatient(phone);
      } else {
        this.setState({ errorPhone: "Vui lòng nhập số điện thoại" });
      }
    },

    changePhone: e => {
      if (!patternPhone.test(e.target.value)) {
        this.setState({
          errorPhone: "Vui lòng nhập đúng định dạng"
        });
      } else {
        this.setState({
          errorPhone: ""
        });
      }
      this.setState({ phone: e.target.value });
    },

    handleToggleModalConfirm: () => {
      this.setState({
        errorPhone: "",
        phone: ""
      });
      this.props.toggleModalConfirm();
    },

    handleSelectPatient: (id, key) => {
      const {
        selectedPatient,
        handleSelectedPatient,
        history,
        verifyPatientWithoutPhone
      } = this.props;
      const isVerifiedByPhone = get(selectedPatient, "isVerifiedByPhone");
      if (isVerifiedByPhone) {
        handleSelectedPatient(id);
      } else {
        verifyPatientWithoutPhone(key);
        history.push("/xac-nhan-thong-tin-benh-nhan");
      }
    }
  };

  componentDidMount() {
    this.props.resetModalConfirmPatient();
  }

  render() {
    return (
      <React.Fragment>
        <PatientCheckPhoneDesktop
          {...this.props}
          {...this.state}
          {...this.methods}
        />
        {this.renderModalCheckphone}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    patient: {
      selectedPatient,
      showModalConfirm,
      loading,
      errorMessage,
      showLoading
    },
    hospital: { selectedHospital }
  } = state;
  return {
    device: type,
    selectedPatient,
    showModalConfirm,
    loading,
    selectedHospital,
    errorMessage,
    showLoading
  };
};

const PatientCheckPhoneHelmet = withTitle({
  component: DetectPatientCheckPhone,
  title: `${hospitalName.value} |  Xác thực điện thoại bệnh nhân`
});

export default connect(mapStateToProps, {
  verifyPhonePatient,
  handleSelectedPatient: selectedPatient,
  toggleModalConfirm,
  resetModalConfirmPatient,
  verifyPatientWithoutPhone
})(PatientCheckPhoneHelmet);
