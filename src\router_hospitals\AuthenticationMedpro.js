import React, { Fragment } from "react";
import { connect } from "react-redux";
import { redirectToMedproId } from "~/store/login/actions";
import Breadcrumb from "~/components/desktop/atoms/Breadcrumb";

const AuthenticationMedpro = ({
  ChildComponent,
  IsAuthenticated,
  breadcrumb,
  ...rest
}) => {
  class NewComponent extends React.Component {
    render() {
      if (!this.props.IsAuthenticated) {
        setTimeout(() => {
          this.props.redirectToMedproId();
        }, 500);
        return "";
      }
      return (
        <Fragment>
          {breadcrumb && <Breadcrumb className="head" />}
          <ChildComponent {...this.props} />
        </Fragment>
      );
    }
  }

  const mapStateToProps = state => {
    const {
      user: { info: IsAuthenticated }
    } = state;
    return {
      IsAuthenticated
    };
  };

  const Abc = connect(mapStateToProps, { redirectToMedproId })(NewComponent);

  return <Abc {...rest} />;
};

export default AuthenticationMedpro;
