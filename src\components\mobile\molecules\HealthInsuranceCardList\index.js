import cx from "classnames";
import { get } from "lodash";
import {
  MD<PERSON>ni<PERSON>,
  MDBCard,
  MDBCollapse,
  MDBCollapseHeader,
  MDBListGroup
} from "mdbreact";
import React, { Component, Fragment } from "react";
import { Facebook } from "react-content-loader";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import MedicalBillCard from "~/components/mobile/molecules/MedicalBillCard";
import { selectHospital } from "~/store/hospital/hospitalActions";
import { selectedToShowModal } from "~/store/payment/paymentAction";
import {
  addScheduleRepayment,
  getBookingTree,
  resetPaymentInformation,
  reviewBooking,
  selectMedicalBill
} from "~/store/totalData/actions";
import styles from "./style.module.scss";
import partnerId from "~/utils/partner";
class HealthInsuranceCardList extends Component {
  state = {
    collapseID: ""
  };

  toggleCollapse = collapseID => () =>
    this.setState(prevState => ({
      collapseID: prevState.collapseID !== collapseID ? collapseID : ""
    }));

  onSelectedToShowModal = item => {
    if (item.hospital.id === 2) this.props.onSelectedToShowModal(item);
  };

  onWatchBookingDetail = (id, hospital) => {
    this.props.selectMedicalBill(id, hospital);
  };

  onRepayment = data => {
    const hospitalId = get(data, "partner.partnerId");
    this.props.selectHospital(hospitalId);

    this.props.getBookingTree("not_redirect");

    const date = get(data, "date", null);
    const subject = get(data, "subject", null);
    const service = get(data, "service", null);
    const doctor = get(data, "doctor", null);
    const room = get(data, "room", null);
    const bookingSlot = get(data, "bookingSlot", null);
    this.props.addScheduleRepayment({
      date,
      subject,
      service,
      doctor,
      room,
      time: bookingSlot
    });
  };

  renderBillsDataIntoCards = (bills = []) => {
    if (!bills.length)
      return (
        <p className={styles.no_bills_found}>
          Bệnh nhân này chưa có phiếu khám bệnh
        </p>
      );

    return bills.map((item, i) => {
      return (
        <MedicalBillCard
          key={i}
          data={item}
          handleReviewBooking={() => this.props.onReviewBooking()}
          handleWatchBookingDetail={this.onWatchBookingDetail}
          handleRepayment={this.onRepayment}
          resetPaymentInformation={() => this.props.onResetPaymentInformation()}
        />
      );
    });
  };

  renderHealthInsuranceCard = () => {
    const { bookingList } = this.props;
    const { collapseID } = this.state;

    return bookingList.map((patient, i) => {
      const surname = get(patient, "surname", "");
      const name = get(patient, "name", "");
      const bookings = get(patient, "bookings", []);
      return (
        <MDBCard
          className={cx(
            styles.card_collapse,
            styles["card_collapse_" + partnerId]
          )}
          key={i}
        >
          <MDBCollapseHeader
            onClick={this.toggleCollapse(`${i}`)}
            className={
              collapseID === `${i}`
                ? styles.card_header_selected
                : styles.card_header
            }
          >
            {surname + " " + name}
            <i
              className={
                collapseID === `${i}`
                  ? "fa fa-angle-down rotate-icon ml-1"
                  : "fa fa-angle-down ml-1"
              }
            />
          </MDBCollapseHeader>
          <MDBCollapse id={`${i}`} isOpen={collapseID}>
            <MDBListGroup
              className={cx(styles.list_group, styles.list_group_parent)}
            >
              {this.renderBillsDataIntoCards(bookings)}
            </MDBListGroup>
          </MDBCollapse>
        </MDBCard>
      );
    });
  };

  render() {
    const { loading } = this.props;
    return (
      <Fragment>
        {loading ? (
          <React.Fragment>
            <Facebook height={117} />
          </React.Fragment>
        ) : (
          <MDBAnimation type="fadeIn" className={styles.list_medical_user}>
            {this.renderHealthInsuranceCard()}
          </MDBAnimation>
        )}
      </Fragment>
    );
  }
}

const mapDispatchToProps = dispatch => ({
  onReviewBooking: () => {
    dispatch(reviewBooking());
  },
  onResetPaymentInformation: () => {
    dispatch(resetPaymentInformation());
  },
  onSelectedToShowModal: item => {
    dispatch(selectedToShowModal(item));
  },

  selectMedicalBill: (id, hospital) => {
    dispatch(selectMedicalBill(id, hospital));
  },

  addScheduleRepayment: info => {
    dispatch(addScheduleRepayment(info));
  },
  getBookingTree: text => {
    dispatch(getBookingTree(text));
  },
  selectHospital: id => {
    dispatch(selectHospital(id));
  }
});

export default withRouter(
  connect(null, mapDispatchToProps)(HealthInsuranceCardList)
);
