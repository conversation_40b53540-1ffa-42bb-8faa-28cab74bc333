import cx from "classnames";
import {
  MDBBtn,
  MDBCard,
  MDBCardBody,
  MDBCardImage,
  MDBCol,
  MDBContainer,
  MDBRow,
  MDBView
} from "mdbreact";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import TagName from "~/components/common/atoms/TagName";
import { apiNews } from "~/utils/constants";
import styles from "./style.module.scss";

export const NewsAndEvents = () => {
  const [listNews, setlistNews] = useState();
  const [listNewsPin, setlistNewsPin] = useState();

  const getListNews = async () => {
    const url = `https://cms.medpro.com.vn/posts?&categories.slug=tin-tuc&_limit=4&_sort=updated_at:desc`;
    const rs = await fetch(url);
    const data = await rs.json();
    setlistNews(data);
  };
  const getListNewsPin = async () => {
    const url = `https://cms.medpro.com.vn/posts?pin_in=true&categories.slug=tin-tuc&_limit=1&_sort=updated_at:desc`;
    const rs = await fetch(url);
    const data = await rs.json();
    setlistNewsPin(data[0]);
  };

  useEffect(() => {
    getListNews();
    getListNewsPin();
  }, []);

  return (
    <MDBContainer className="my-5">
      <MDBRow center>
        <MDBCol className="text-center mb-3">
          <TagName
            element="h3"
            className={["sub_title_section", "title_featureMedpro"]}
          >
            Tin tức & sự kiện
          </TagName>
        </MDBCol>
      </MDBRow>
      <MDBRow>
        <MDBCol sm="12" md="12" lg="5" className={cx(styles.colNews)}>
          <MDBCard news className={cx(styles.cardNewsLeft)}>
            <MDBView className={styles.view}>
              <Link to={"/tin-tuc/" + listNewsPin?.slug}>
                <MDBCardImage
                  className="img-fluid"
                  src={apiNews + listNewsPin?.image[0]?.url}
                  waves
                />
              </Link>
            </MDBView>
            <MDBCardBody className={cx(styles.cardBody)}>
              <p className={styles.title}>
                <Link to={"/tin-tuc/" + listNewsPin?.slug}>
                  {listNewsPin?.title}
                </Link>
              </p>

              <p className={styles.tag}>
                {moment(listNewsPin?.updated_at).format("DD/MM/YYYY, hh:mm")}
              </p>

              <p className={styles.author}>{listNewsPin?.author}</p>

              <p className={styles.description}>
                <Link to={"/tin-tuc/" + listNewsPin?.slug}>
                  {listNewsPin?.description}
                </Link>
              </p>
            </MDBCardBody>
          </MDBCard>
        </MDBCol>

        <MDBCol sm="12" md="12" lg="7" className={styles.colNews}>
          <MDBRow>
            {listNews?.map(({ title, image, updated_at, slug }, i) => {
              return (
                <MDBCol md="6" key={i}>
                  <MDBCard className={cx(styles.cardNews)}>
                    <MDBView className={styles.view}>
                      <Link to={"/tin-tuc/" + slug}>
                        <MDBCardImage
                          className={cx(
                            styles.imgCard,
                            "card-img-top img-fluid"
                          )}
                          src={apiNews + image[0]?.url}
                        />
                      </Link>
                    </MDBView>
                    <MDBCardBody className={cx(styles.cardBody)}>
                      <div className={styles.title}>
                        <Link to={"/tin-tuc/" + slug}>{title}</Link>
                      </div>
                      <div className={styles.tag}>
                        {moment(updated_at).format("DD/MM/YYYY, hh:mm")}
                      </div>
                    </MDBCardBody>
                  </MDBCard>
                </MDBCol>
              );
            })}
          </MDBRow>
        </MDBCol>
      </MDBRow>
      <MDBRow center>
        <MDBBtn outline color="primary" className="text-capitalize">
          <Link to="/tin-tuc">Xem thêm các bài viết khác {`>>`}</Link>
        </MDBBtn>
      </MDBRow>
    </MDBContainer>
  );
};
