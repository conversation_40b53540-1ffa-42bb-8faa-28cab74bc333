import { find, get } from "lodash";
import { MD<PERSON>ol, MD<PERSON><PERSON>r, MDBRow } from "mdbreact";
import React from "react";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import BHYTTraiTuyenDalieuhcm from "~/components/mobile/molecules/BHYTTraiTuyenDalieuhcm";
import { partnerInfo } from "~/configs/partnerDetails";
import { getNextStep } from "~/store/filter/actions";
import { uploadFile } from "~/store/totalData/actions";
const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });
class InstructionIncorrectBHYTDaLieuHCM extends React.Component {
  render() {
    return (
      <div style={{ marginTop: "calc(-7vh)" }}>
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="12">
              <BHYTTraiTuyenDalieuhcm type="app" {...this.props} />
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

const InstructionIncorrectBHYTHelmet = withTitle({
  component: InstructionIncorrectBHYTDaLieuHCM,
  title: `${hospitalName.value} | Hướng dẫn BHYT`
});

export default connect(null, { getNextStep, uploadFile })(
  InstructionIncorrectBHYTHelmet
);
