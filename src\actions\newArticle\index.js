import * as types from "./types";

export const getFeaturedArticle = () => {
  return {
    type: types.GET_FEATURED_ARTICLE
  };
};

export const getNewArticle = () => {
  return {
    type: types.GET_NEW_ARTICLE
  };
};

export const searchArticle = searchField => {
  return {
    type: types.SEARCH_ARTICLE,
    payload: searchField
  };
};

export const clearArticle = () => {
  return {
    type: types.CLEAR_ARTICLE
  };
};

export const clearSearchField = () => {
  return {
    type: types.CLEAR_SEARCH_FIELD
  };
};

export const getNewsDetail = payload => {
  return {
    type: types.GET_NEWS_DETAIL,
    payload
  };
};
