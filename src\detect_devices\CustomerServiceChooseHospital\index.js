import { find, get } from "lodash";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import MenuBarBottom from "~/components/mobile/molecules/MenuBar";
// import AppId from "~/utils/partner";
import { partnerInfo } from "~/configs/partnerDetails";
import * as featuresActions from "~/store/features/featuresActions";
import {
  requestHospitalList,
  resetFeatureListByHospital
} from "~/store/hospital/hospitalActions";
import * as rebooking from "~/store/rebooking/rebookingAction";
import { requestAllCity } from "~/store/resource/resourceAction";
import { resetData } from "~/store/totalData/actions";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const CustomerServiceChooseHospitalPageMobile = Loadable({
  loader: () =>
    import(
      /* webpackChunkName: "home_mobile" */ "~/components/desktop/pages/CustomerServiceChooseHospital"
    ),
  loading: LoadableLoading,
  delay: 500
});
const CustomerServiceChooseHospitalPageDesktop = Loadable({
  loader: () =>
    import(
      /* webpackChunkName: "home_desktop" */ "~/components/desktop/pages/CustomerServiceChooseHospital"
    ),
  loading: LoadableLoading,
  delay: 500
});

class CustomerServiceChooseHospitalPage extends Component {
  // constructor(props) {
  //   super(props);
  //   this.scrollTop = 0;
  //   this.props.getFeaturedArticle();
  //   this.menuBarBottomRef = React.createRef();
  //   if (this.props.IsAuthenticated) {
  //     this.props.getNotificationOnPage({
  //       category: 1
  //     });
  //   }
  // }

  componentDidMount() {
    this.props.requestHospitalList();
    this.props.clearFollowupState();
    this.props.resetFeatureListByHospital();
    this.props.resetData();
    this.props.requestAllCity();
  }

  render() {
    const { device } = this.props;
    // if (AppId === "momo") {
    //   return <Redirect to="/chon-benh-vien" />;
    // }
    return (
      <React.Fragment>
        {device === "mobile" ? (
          <CustomerServiceChooseHospitalPageMobile
            {...this.props}
            menuBarBottomRef={this.menuBarBottomRef}
          />
        ) : (
          <CustomerServiceChooseHospitalPageDesktop
            {...this.props}
            menuBarBottomRef={this.menuBarBottomRef}
          />
        )}
        <MenuBarBottom
          menuBarBottomRef={this.props.menuBarBottomRef}
          currentLocation={this.props.location.pathname}
        />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    hospital: {
      selectedHospital,
      featureList: { data: featureList },
      hospitalList: { data: hospitalList }
    },
    features: { selectedFeature, featureListBooking },
    user: { IsAuthenticated },
    resource: { dataCity: allCities }
  } = state;
  return {
    device: type,
    selectedHospital,
    selectedFeature,
    IsAuthenticated,
    featureList,
    featureListBooking,
    hospitalList,
    allCities
  };
};

const mapDispatchToProps = dispatch => ({
  clearFollowupState: () => dispatch(rebooking.clearState()),
  resetFeatureListByHospital: () => {
    dispatch(resetFeatureListByHospital());
  },
  selectFeatureBookingById: id =>
    dispatch(featuresActions.selectFeatureBookingById(id)),

  requestHospitalList: () => {
    dispatch(requestHospitalList());
  },
  resetData: () => {
    dispatch(resetData());
  },
  requestAllCity: () => {
    dispatch(requestAllCity());
  }
});

const CustomerServiceChooseHospitalPageHelmet = withTitle({
  component: CustomerServiceChooseHospitalPage,
  title: `${hospitalName.value} | Cham soc khach hang`
});

export default withRouter(
  connect(
    mapStateToProps,
    mapDispatchToProps
  )(CustomerServiceChooseHospitalPageHelmet)
);
