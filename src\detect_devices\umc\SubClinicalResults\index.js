import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
const SubClinicalResultsPageMobile = Loadable({
  loader: () =>
    import(/* mobile */ "~/components/mobile/pages/umc/SubClinicalResults"),
  loading: LoadableLoading
});
const SubClinicalResultsPageDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/mobile/pages/umc/SubClinicalResults"),
  loading: LoadableLoading
});

class DetectSubClinicalResults extends Component {
  render() {
    const { device } = this.props;
    return (
      <React.Fragment>
        {device === "mobile" ? (
          <SubClinicalResultsPageMobile />
        ) : (
          <SubClinicalResultsPageDesktop />
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    }
  } = state;
  return {
    device: type
  };
};

const SubClinicalResultsHelmet = withTitle({
  component: DetectSubClinicalResults,
  title: "Medpro | Kết quả cận lâm sàng"
});

export default connect(mapStateToProps)(SubClinicalResultsHelmet);
