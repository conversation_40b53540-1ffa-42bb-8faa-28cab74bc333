/* eslint-disable react/jsx-handler-names */
import React, { Component, Fragment } from "react";
import { MDBInput } from "mdbreact";
import { connect } from "react-redux";
import Loadable from "react-loadable";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import {
  YES,
  NO,
  SEND,
  CLOSE,
  OTHER_NUMBER,
  TITLE_CANCEL_MEDICAL_BILL,
  CONTENT_CANCEL_MEDICAL_BILL,
  TITLE_RESEND_SMS,
  patternPhone
} from "~/utils/constants";
import Modal from "~/components/common/molecules/Modal";
import Alert from "~/components/common/atoms/Alert";
import {
  getMedicalBillDetail,
  cancelMedicalBill,
  resendSMS,
  clearCanceledBookingMessage
} from "~/store/booking/bookingAction";
import { hideAlertSMSOK } from "~/store/payment/paymentAction";
import { withTitle } from "~/components/common/molecules/TitleHelmet";

const MedicalBillDetailPageMobile = Loadable({
  loader: () =>
    import(/* mobile */ "~/components/desktop/pages/MedicalBillDetail"),
  loading: LoadableLoading
});
const MedicalBillDetailPageDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/MedicalBillDetail"),
  loading: LoadableLoading
});

class DetectMedicalBillDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpenCancelModal: false,
      isOpenResendSMSModal: false,
      selectedIndex: 2,
      otherNumber: "",
      errorMessage: ""
    };
  }

  method = {
    handleToggleCancelModal: () => {
      const { isOpenCancelModal } = this.state;
      this.setState({ isOpenCancelModal: !isOpenCancelModal });
    },

    handleToggleResendSMSModal: () => {
      const { isOpenResendSMSModal } = this.state;
      this.setState({ isOpenResendSMSModal: !isOpenResendSMSModal });
    }
  };

  handleInputChange = otherNumber => {
    this.setState({ otherNumber, errorMessage: "" });
  };

  hideAlert = () => {
    this.props.hideAlertSMSOK();
  };

  cancelMedicalBill = () => {
    const {
      handleCancelMedicalBill,
      user,
      match: {
        params: { code }
      }
    } = this.props;
    const data = {
      user_id: user.user_id,
      access_token: user.access_token,
      booking_id: code
    };
    handleCancelMedicalBill(data);
    this.method.handleToggleCancelModal();
  };

  checkPhone = value => {
    if (value) {
      if (patternPhone.test(value)) {
        return true;
      } else {
        if (patternPhone.test(value)) {
          return true;
        } else {
          return false;
        }
      }
    } else {
      return false;
    }
  };

  checkBeforeSend = callback => {
    const { selectedIndex, otherNumber } = this.state;
    if (selectedIndex === 3) {
      if (this.checkPhone(otherNumber)) {
        callback();
      } else {
        this.setState({
          errorMessage: "Vui lòng nhập số điện thoại chính xác."
        });
      }
    } else {
      callback();
    }
  };

  handleSend = () => {
    const { selectedIndex, otherNumber } = this.state;
    const {
      medicalBillDetail: { user: userInfo, patient: patientInfo },
      selectedHospital
    } = this.props;
    const userNumber = userInfo ? userInfo.phone : "";

    // BV Nhi đồng: patient number sử dụng relative mobile
    // Sử dụng mobile hoặc realativeMobile để gửi tin nhắn (ko dùng censored)
    let patientNumber = "";
    if (selectedHospital.id === 4) {
      patientNumber =
        patientInfo && patientInfo.relative ? patientInfo.relative.mobie : "";
    } else {
      patientNumber = patientInfo ? patientInfo.mobile : "";
    }

    let sendNumber = 0;
    switch (selectedIndex) {
      case 1:
        sendNumber = userNumber;
        break;
      case 2:
        sendNumber = patientNumber;
        break;
      case 3:
        sendNumber = otherNumber;
        break;
      default:
        sendNumber = userNumber;
    }
    this.resendSMS(sendNumber);
  };

  closeResendSMSModal = () => {
    this.setState({ isOpenResendSMSModal: false });
  };

  resendSMS = number => {
    const {
      handleResendSMS,
      user,
      match: {
        params: { code }
      }
    } = this.props;

    const data = {
      user_id: user.user_id,
      access_token: user.access_token,
      booking_id: code,
      mobile: number // === mobileCensored ? mobile : number
    };
    handleResendSMS(data);
    this.closeResendSMSModal();
  };

  closeCancelMedicalBillModal = () => {
    this.setState({ isOpenCancelModal: false });
  };

  getMedicalBillDetail = () => {
    const {
      user,
      handleGetMedicalBillDeltail,
      match: {
        params: { code }
      }
    } = this.props;
    const data = {
      user_id: user.user_id,
      access_token: user.access_token,
      booking_id: code
    };
    handleGetMedicalBillDeltail(data);
  };

  handleOnChange = selectedIndex => () => {
    this.setState({ selectedIndex });
    if (selectedIndex === 3)
      setTimeout(() => {
        this.input.focus();
      }, 200);
  };

  renderBodyModalResendSMS = () => {
    const {
      medicalBillDetail: { user: userInfo, patient: patientInfo },
      selectedHospital
    } = this.props;
    const userNumber = userInfo ? userInfo.phone : "";

    // BV Nhi đồng: patient number sử dụng relative mobile
    let patientNumber = "";
    if (selectedHospital.id === 4) {
      patientNumber =
        patientInfo && patientInfo.relative
          ? patientInfo.relative.mobile_censored
          : "";
    } else {
      patientNumber = patientInfo ? patientInfo.mobile_censored : "";
    }

    const { selectedIndex, errorMessage } = this.state;
    return (
      <Fragment>
        {userInfo && userInfo.phone !== null && (
          <MDBInput
            gap
            onChange={this.handleOnChange(1)}
            checked={selectedIndex === 1}
            label={userNumber}
            type="radio"
            id="user"
          />
        )}
        <MDBInput
          gap
          onChange={this.handleOnChange(2)}
          checked={selectedIndex === 2}
          label={patientNumber}
          type="radio"
          id="patient"
        />
        <MDBInput
          gap
          onChange={this.handleOnChange(3)}
          checked={selectedIndex === 3}
          label={OTHER_NUMBER}
          type="radio"
          id="other"
        />
        <div
          style={
            selectedIndex === 3 ? { display: "block" } : { display: "none" }
          }
        >
          <MDBInput
            type="number"
            hint="Vui lòng nhập số điện thoại"
            getValue={this.handleInputChange}
            inputRef={el => (this.input = el)}
          />
        </div>
        {errorMessage ? <span>{errorMessage}</span> : []}
      </Fragment>
    );
  };

  componentDidMount() {
    const { user } = this.props;
    if (user) {
      this.getMedicalBillDetail();
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.match.url !== prevProps.match.url) {
      this.getMedicalBillDetail();
    }
  }

  componentWillReceiveProps(prevProps) {
    const { canceledMedicalBill } = this.props;
    if (prevProps.cancelMedicalBill !== canceledMedicalBill) {
      this.closeCancelMedicalBillModal();
    }
  }

  componentWillUnmount() {
    this.props.clearCanceledBookingMessage();
  }

  render() {
    const {
      device,
      medicalBillDetail,
      clearCanceledBookingMessage,
      canceledMedicalBill
    } = this.props;
    const { showResendSMSOK, resendSMSMessage } = this.props;
    const { isOpenCancelModal, isOpenResendSMSModal } = this.state;

    const cancelMessage =
      medicalBillDetail &&
      medicalBillDetail.related &&
      (medicalBillDetail.related.length === 0
        ? CONTENT_CANCEL_MEDICAL_BILL
        : `Bạn có chắc là muốn hủy ${medicalBillDetail.related.length +
            1} phiếu khám bệnh (${
            medicalBillDetail.transaction_code_gd
          }, ${medicalBillDetail.related.join(",")}) cùng lúc không?`);

    return (
      <React.Fragment>
        {device === "mobile" ? (
          <MedicalBillDetailPageMobile {...this.props} {...this.method} />
        ) : (
          <MedicalBillDetailPageDesktop {...this.props} {...this.method} />
        )}

        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={isOpenCancelModal}
          title={TITLE_CANCEL_MEDICAL_BILL}
          toggle={this.closeCancelMedicalBillModal}
          centered
          className="centered"
          footer
          footerConfirm
          children={cancelMessage}
          cancelText={NO}
          okText={YES}
          onCancel={this.closeCancelMedicalBillModal}
          onOk={this.cancelMedicalBill}
        />
        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={isOpenResendSMSModal}
          title={TITLE_RESEND_SMS}
          children={this.renderBodyModalResendSMS()}
          toggle={this.closeResendSMSModal}
          centered
          className="centered"
          footer
          footerConfirm
          cancelText={CLOSE}
          okText={SEND}
          onCancel={this.closeResendSMSModal}
          onOk={() => this.checkBeforeSend(this.handleSend)}
        />
        <Alert
          isModal={showResendSMSOK}
          message={resendSMSMessage}
          toggleAlert={this.hideAlert}
        />
        <Alert
          isModal={canceledMedicalBill && canceledMedicalBill.message}
          message={canceledMedicalBill && canceledMedicalBill.message}
          toggleAlert={clearCanceledBookingMessage}
        />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    booking: { medicalBillDetail, canceledMedicalBill, loading },
    user: { info },
    healthInsuranceCard: { showResendSMSOK, resendSMSMessage },
    hospital: { selectedHospital }
  } = state;
  return {
    device: type,
    user: info,
    medicalBillDetail,
    canceledMedicalBill,
    showResendSMSOK,
    resendSMSMessage,
    loading,
    selectedHospital
  };
};

const mapDispatchToProps = dispatch => ({
  handleGetMedicalBillDeltail: data => {
    dispatch(getMedicalBillDetail(data));
  },
  handleCancelMedicalBill: data => {
    dispatch(cancelMedicalBill(data));
  },
  handleResendSMS: data => {
    dispatch(resendSMS(data));
  },
  hideAlertSMSOK: () => {
    dispatch(hideAlertSMSOK());
  },
  clearCanceledBookingMessage: () => dispatch(clearCanceledBookingMessage())
});

const MedicalBillDetailHelmet = withTitle({
  component: DetectMedicalBillDetail,
  title: "Medpro | Chi tiết phiếu khám bệnh"
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(MedicalBillDetailHelmet);
