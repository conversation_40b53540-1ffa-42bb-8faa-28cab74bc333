{"name": "medpro", "version": "0.1.0", "private": true, "dependencies": {"axios": "0.21.1", "classnames": "^2.2.6", "compress": "^0.99.0", "core-js": "^3.1.4", "cross-env": "^5.2.1", "current-device": "^0.8.1", "customize-cra": "^0.2.14", "file-saver": "^2.0.2", "fingerprintjs2": "^2.1.0", "firebase": "^7.6.1", "firebaseui": "^4.4.0", "history": "^4.9.0", "html-react-parser": "^0.11.1", "is-html": "2.0.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.15", "md5": "^2.2.1", "mdbreact": "./mdbreact-4.20.0.tgz", "medpro-sdk": "git+https://gitlab+deploy-token-840807:<EMAIL>/medpro/medpro-sdk.git#develop", "moment": "^2.24.0", "prop-types": "^15.7.2", "qrcode.react": "1.0.1", "qs": "^6.7.0", "query-string": "^6.12.1", "rc-form": "2.4.12", "react": "17.0.2", "react-app-rewired": "^2.1.8", "react-avatar": "^3.6.0", "react-barcode": "1.4.0", "react-bootstrap-4-pagination": "1.0.4", "react-content-loader": "6.0.3", "react-device-detect": "^1.13.1", "react-dom": "17.0.2", "react-firebaseui": "5.0.2", "react-helmet": "6.1.0", "react-image-lightbox": "^5.1.0", "react-infinite-scroll-component": "^4.5.3", "react-input-mask": "^2.0.4", "react-lazyload": "3.2.0", "react-loadable": "^5.5.0", "react-markdown": "5.0.3", "react-meta-tags": "1.0.1", "react-multi-carousel": "2.6.2", "react-otp-input": "^2.0.2", "react-paginate": "^6.3.0", "react-perfect-scrollbar": "^1.5.3", "react-phone-input-2": "^2.13.7", "react-redux": "^7.1.0", "react-router-dom": "^5.0.1", "react-scripts": "3.0.1", "react-slick": "^0.24.0", "react-transition-group": "^4.2.1", "react-watermark-component": "^2.2.1", "redux": "^4.0.1", "redux-persist": "^5.10.0", "redux-saga": "^1.0.3", "semantic-ui-react": "2.0.3", "slick-carousel": "^1.8.1", "svgo": "1.3.0", "swiper": "^5.2.1", "validator": "^11.1.0"}, "scripts": {"start": "cross-env REACT_APP_STAGE=development PORT=3000 react-app-rewired start", "start:testing": "cross-env REACT_APP_STAGE=testing PORT=3000 react-app-rewired start", "start:staging": "cross-env REACT_APP_STAGE=staging PORT=3000 react-app-rewired start", "start:production": "cross-env REACT_APP_STAGE=production PORT=3000 react-app-rewired start", "start-https": "cross-env HTTPS=true PORT=3001 react-scripts start", "build": "cross-env REACT_APP_STAGE=development react-app-rewired build", "build:testing": "cross-env REACT_APP_STAGE=testing react-app-rewired build", "build:staging": "cross-env REACT_APP_STAGE=staging react-app-rewired build", "build:production": "cross-env REACT_APP_STAGE=production react-app-rewired build", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "serve build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"babel-eslint": "10.0.1", "babel-plugin-dynamic-import-node": "^2.3.0", "babel-plugin-import": "^1.12.1", "babel-plugin-root-import": "^6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-config-standard": "^14.1.0", "eslint-config-standard-react": "^9.1.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-node": "^9.1.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.14.3", "eslint-plugin-react-hooks": "^2.0.1", "eslint-plugin-standard": "^4.0.1", "node-sass": "^4.14.1", "prettier": "^1.18.2", "redux-devtools-extension": "^2.13.8", "standard": "^14.1.0"}}