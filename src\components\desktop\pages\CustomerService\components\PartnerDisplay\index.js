import React, { Component, Fragment } from "react";
import styles from "./style.module.scss";
import { MDBIcon } from "mdbreact";

class PartnerDisplay extends Component {
  render() {
    // const image = this.props.hospital.image || "";

    return (
      <Fragment>
        <input
          className={styles.phone_input}
          placeholder="Số điện thoại"
          value={this.props.inputPhone}
          onChange={event => this.props.inputPhoneChangeHandler(event)}
          onKeyDown={event => {
            if (event.keyCode === 13) {
              event.preventDefault();
              this.props.requestInfoByPhone();
            }
          }}
        ></input>
        <MDBIcon
          icon="phone-square-alt"
          className={styles.phone_icon}
          onClick={() => this.props.requestInfoByPhone()}
        />
        {/* <div className={styles.right_part}>
          <span className={styles.hospital_name}>
            {this.props.hospital.name}
          </span>
          <br />
        </div> */}
      </Fragment>
    );
  }
}

export default PartnerDisplay;
