import { get } from "lodash";
import React from "react";
import styles from "./styles.module.scss";

const Attention = ({ bookingInfo }) => {
  const status = get(bookingInfo, "status", 1);
  const checkInRoom = get(bookingInfo, "checkInRoom", "");
  const bookingNote = get(bookingInfo, "bookingNote", "");

  if ([-2, 0, 6].includes(status)) return null;
  return (
    <>
      {(bookingNote || checkInRoom?.description) && (
        <p className={styles.attention}>Lưu ý:</p>
      )}

      {checkInRoom?.description && (
        <div className={styles.note}>
          <div
            dangerouslySetInnerHTML={{
              __html: checkInRoom?.description
            }}
          />
        </div>
      )}

      {bookingNote && (
        <div
          dangerouslySetInnerHTML={{
            __html: bookingNote
          }}
        />
      )}
    </>
  );
};

export default Attention;
