@import "src/assets/scss/custom-variables.scss";
.box_service {
  width: 100%;
  padding: 0 15px;
  position: absolute;
  bottom: 30px;
  top: 50px;
  max-width: 1140px;
  margin: 0 auto;
  left: 0;
  right: 0;

  .box_service_item {
    .btnVacxin {
      display: block;
      text-align: center;
      margin-bottom: 1.2rem;
      text-transform: uppercase;
      width: 100%;
    }
    width: 30%;
    float: right;
    padding: 0 15px;
    color: $main_black;
    // min-height: 250px;
    h2 {
      margin: 30px 0 45px;
      font-size: 20px;
      font-weight: 500;
      color: $main_black;
      span {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
  @media #{$medium-and-down} {
    .box_service_item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 0.25rem;
      width: 60%;

      h2 {
        margin: 15px 0 30px;
        font-size: 20px;
      }

      .btnVacxin {
        display: block;
        max-width: 300px;
        div {
          text-align: center;
        }
      }
    }
  }
  @media (max-width: 480px) {
    top: 15px;
    .box_service_item {
      width: 100%;
      h2 {
        font-size: 15px;
      }
    }
  }
}

//  responsive box_service
@media #{$medium-and-down} {
  .box_service {
    position: unset;
    display: flex;
    justify-content: center;
  }
  .box_service_item {
    margin-top: 35px !important;
    min-height: 180px !important;
    background-color: #f8fafd !important;
  }
}

.non_active {
  background-color: rgba(3, 121, 204, 0.06);
}

.active {
  background-color: rgba(255, 255, 255, 0.9);
  @media #{$medium-and-down} {
    background-color: rgba(255, 255, 255, 0.5);
  }
}

.modal {
  .modalBody {
    ul {
      text-transform: uppercase;
      list-style-type: none;
      padding: 0;
      margin: 0;
      li {
        text-align: center;
        margin-bottom: 1rem;
        .subTitle {
          text-transform: initial;
          font-weight: 500;
          font-size: 0.95rem;
        }
        .title {
          font-weight: 500;
          font-size: 1.2rem;
        }

        button {
          width: 100%;
          display: block;
          div {
            text-transform: uppercase;
            text-align: center;
          }
        }
      }
    }
  }
}
