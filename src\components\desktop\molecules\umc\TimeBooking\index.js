import cx from "classnames";
import { MDBAlert } from "mdbreact";
import React, { Component, Fragment } from "react";
import { Facebook } from "react-content-loader";
import { connect } from "react-redux";
import { selectedTime } from "~/store/room/roomAction";
import { genSlotTime, maxTimeSlot } from "~/utils/genSlotTime";
import styles from "./style.module.scss";

class TimeBooking extends Component {
  constructor(props) {
    super(props);
    this.state = {
      message_error:
        "Khung giờ trong ngày hiện tại đã hết, vui lòng chọn khung giờ khác"
    };
  }

  onSelectedTime = time => {
    this.props.onSelectedTime(time);
  };

  renderPickTimeListByRoomAndDoctor = (from = 5, to = 18) => {
    const { pickTimeListByRoomAndDoctor, selectedTime } = this.props;
    return pickTimeListByRoomAndDoctor.map(time => {
      const isFull = maxTimeSlot(time.id, time.max_slot, time.used_slot) === 1;
      // eslint-disable-next-line camelcase
      const cls_time = styles.time;
      // eslint-disable-next-line camelcase
      const cls_full = styles.full;
      // eslint-disable-next-line camelcase
      const cls_active = styles.active;
      const cls = cx({
        [cls_time]: true,
        [cls_full]: isFull,
        [cls_active]: selectedTime.id === time.id
      });
      const objEvent = {};
      if (!isFull) {
        objEvent.onClick = () => this.onSelectedTime(time);
      }
      return time.from > from && time.from < to ? (
        <div key={time.id} className={cls} {...objEvent}>
          {time.from ? genSlotTime(time.from) : ""} -{" "}
          {time.to ? genSlotTime(time.to) : ""}
        </div>
      ) : (
        ""
      );
    });
  };

  checkIsFull = time => {
    return maxTimeSlot(time.id, time.max_slot, time.used_slot) === 1;
    // return true;
  };

  render() {
    const {
      selectedRoomAndDoctor,
      pickTimeListByRoomAndDoctor,
      pickTimeLoading
    } = this.props;

    // eslint-disable-next-line camelcase
    const { message_error } = this.state;
    let buoi =
      selectedRoomAndDoctor.id > 0 ? selectedRoomAndDoctor.ptime.buoi : 1;
    if (
      pickTimeListByRoomAndDoctor[0] &&
      pickTimeListByRoomAndDoctor[0].from > 12
    ) {
      buoi = 2;
    }
    const isFullAll = pickTimeListByRoomAndDoctor.every(this.checkIsFull);
    if (
      pickTimeListByRoomAndDoctor[0] &&
      pickTimeListByRoomAndDoctor[0].from < 12 &&
      pickTimeListByRoomAndDoctor[pickTimeListByRoomAndDoctor.length - 1] &&
      pickTimeListByRoomAndDoctor[pickTimeListByRoomAndDoctor.length - 1].from >
        12
    )
      return pickTimeLoading ? (
        <Facebook />
      ) : (
        <div className={styles.list_time}>
          <div className={styles.list_time_session}>Buổi Sáng :</div>
          <div className={styles.list_time_detail}>
            {this.renderPickTimeListByRoomAndDoctor(5, 12)}
          </div>

          <div className={styles.list_time_session}>Buổi chiều :</div>
          <div className={styles.list_time_detail}>
            {this.renderPickTimeListByRoomAndDoctor(12, 18)}
          </div>

          {isFullAll ? (
            <MDBAlert color="warning">{message_error}</MDBAlert>
          ) : (
            []
          )}
        </div>
      );

    return (
      <div className={styles.list_time}>
        <div className={styles.list_time_session}>
          {buoi === 1 ? "Buổi Sáng :" : "Buổi Chiều :"}
        </div>
        <div className={styles.list_time_detail}>
          {pickTimeLoading ? (
            <Facebook />
          ) : isFullAll ? (
            <Fragment>
              {this.renderPickTimeListByRoomAndDoctor()}
              <MDBAlert color="warning">{message_error}</MDBAlert>
            </Fragment>
          ) : (
            this.renderPickTimeListByRoomAndDoctor()
          )}
        </div>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    doctorAndTime: {
      selectedRoomAndDoctor,
      pickTimeListByRoomAndDoctor,
      selectedTime,
      pickTimeLoading
    },
    features: { selectedFeature }
  } = state;
  return {
    selectedRoomAndDoctor,
    pickTimeListByRoomAndDoctor,
    selectedTime,
    pickTimeLoading,
    selectedFeature
  };
};

const mapDispatchToProps = dispatch => ({
  onSelectedTime: time => {
    dispatch(selectedTime(time));
  }
  // onSelectedDate: date => {
  //   dispatch(selectedDate(date));
  // },
  // onSelectedMonth: month => {
  //   dispatch(selectedMonth(month));
  // },
});

export default connect(mapStateToProps, mapDispatchToProps)(TimeBooking);
