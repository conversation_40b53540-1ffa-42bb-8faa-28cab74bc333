import * as types from "./hospitalTypes";

export const requestHospitalList = (typeFind = 1) => {
  return {
    type: types.REQUEST_HOSPITAL_LIST,
    typeFind
  };
};

export const loadingHospitalList = () => {
  return {
    type: types.HOSPITAL_LIST_IS_LOADING
  };
};

export const requestHospitalListSuccess = (data = []) => {
  return {
    type: types.REQUEST_HOSPITAL_LIST_SUCCESS,
    data
  };
};

export const requestHospitalListFail = (error = "") => {
  return {
    type: types.REQUEST_HOSPITAL_LIST_FAIL,
    error
  };
};

export const selectHospital = id => {
  return {
    type: types.SELECT_HOSPITAL_BY_ID,
    id
  };
};

export const selectHospitalBefore = id => {
  return {
    type: types.SELECT_HOSPITAL_BEFORE_BY_ID,
    id
  };
};

export const resetSelectedHospital = () => {
  return {
    type: types.RESET_SELECTED_HOSPITAL
  };
};

export const requestHospitalListByFeatureId = id => {
  return {
    type: types.REQUEST_HOSPITAL_LIST_BY_FEATURE_ID,
    id
  };
};

export const loadingHospitalListByFeatureId = id => {
  return {
    type: types.HOSPITAL_LIST_BY_FEATURE_ID_IS_LOADING,
    id
  };
};

export const requestHospitalListByFeatureIdSuccess = (id, data = []) => {
  return {
    type: types.REQUEST_HOSPITAL_LIST_BY_FEATURE_ID_SUCCESS,
    id,
    data
  };
};

export const requestHospitalListByFeatureIdFail = (id, error = "") => {
  return {
    type: types.REQUEST_HOSPITAL_LIST_BY_FEATURE_ID_FAIL,
    id,
    error
  };
};

export const searchHospital = pattern => {
  return {
    type: types.SET_SEARCH_PATTERN_HOSPITAL,
    pattern
  };
};

// dat09 04-02-2020
export const requestFeatureByHospitalId = id => {
  return {
    type: types.REQUEST_FEATURE_LIST_BY_HOSPITAL_ID,
    id
  };
};

export const requestFeatureByHospitalIdSuccess = (id, data = []) => {
  return {
    type: types.REQUEST_FEATURE_LIST_BY_HOSPITAL_ID_SUCCESS,
    id,
    data
  };
};

export const requestFeatureByHospitalIdFail = (id, error = "") => {
  return {
    type: types.REQUEST_FEATURE_LIST_BY_HOSPITAL_ID_FAIL,
    id,
    error
  };
};

export const resetFeatureListByHospital = () => {
  return {
    type: types.RESET_FEATURE_LIST_BY_HOSPITAL
  };
};

export const switchSelectFlow = status => {
  return {
    type: types.SWITCH_SELECT_FLOW,
    payload: status
  };
};

export const resetListHospital = () => {
  return {
    type: types.RESET_LIST_HOSPITAL
  };
};
