import moment from "moment";
import React, { Component } from "react";
import { connect } from "react-redux";
import TagName from "~/components/common/atoms/TagName";
import * as action from "~/store/resource/resourceAction";
import styles from "./style.module.scss";

class BannerContact extends Component {
  constructor() {
    super();
    this.state = {
      isInWorkingTime: ""
    };
  }

  timerID;
  componentDidMount() {
    this.props.getHoliday();
    if (this.props.footer.checkWorkingTime) {
      const startTime = (8 * 60 + 30) * 60;
      const endTime = (17 * 60 + 30) * 60;
      const now = moment();
      const thisTime = (now.hour() * 60 + now.minute()) * 60 + now.second();

      if (thisTime < startTime) {
        this.setState({ isInWorkingTime: false });
        this.timerID = setTimeout(() => {
          this.setState({ isInWorkingTime: true });
        }, (startTime - thisTime) * 1000);
      } else if (thisTime <= endTime) {
        this.setState({ isInWorkingTime: true });
        this.timerID = setTimeout(() => {
          this.setState({ isInWorkingTime: false });
        }, (endTime - thisTime) * 1000);
      } else {
        this.setState({ isInWorkingTime: false });
      }
    }
  }

  render() {
    const { title, desc, imgSrcArr, footer, isTodayWorkingDay } = this.props;
    const { isInWorkingTime } = this.state;
    return (
      <div className={styles.list_group}>
        <div className={styles.list_group_item}>
          <TagName element="h2" className={["title_component"]}>
            <span>{title}</span>
          </TagName>
          {desc.map((d, i) => (
            <p key={i} className={styles.desc}>
              {d}
            </p>
          ))}
          <div className={styles.list_group_img}>
            {imgSrcArr
              ? imgSrcArr.map((imgSrc, i) => {
                  return (
                    <img className={styles.icon} src={imgSrc} alt="" key={i} />
                  );
                })
              : ""}
          </div>
          <div className={styles.list_group_footer}>
            {footer.link ? (
              <span
                className={styles.link}
                dangerouslySetInnerHTML={{ __html: footer.link }}
              />
            ) : footer.phone ? (
              <a className={styles.phone} href={`tel:${footer.phone}`}>
                {footer.phone}
              </a>
            ) : footer.checkWorkingTime ? (
              isTodayWorkingDay && isInWorkingTime ? (
                <span className={styles.in_working_hours}>
                  Đang trong giờ hành chính
                </span>
              ) : (
                <span className={styles.out_working_hours}>
                  Ngoài giờ hành chính
                </span>
              )
            ) : (
              ""
            )}
          </div>
        </div>
      </div>
    );
  }

  componentWillUnmount() {
    clearTimeout(this.timerID);
  }
}

const mapStateToProps = state => {
  let isTodayHoliday;
  // if (state.holiday.data) {
  //   isTodayHoliday = state.holiday.data.some(
  //     d => moment().diff(d, "days") === 0
  //   );
  // }
  if (state.resource.dataHoliday) {
    isTodayHoliday = state.resource.dataHoliday.some(
      d => moment().diff(d, "days") === 0
    );
  }
  const isTodayWeekend = moment().isoWeekday() === 7;
  return {
    isTodayWorkingDay: !isTodayHoliday && !isTodayWeekend
  };
};
const mapDispatchToProps = dispatch => {
  return {
    getHoliday: () => dispatch(action.requestHoliday())
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(BannerContact);
