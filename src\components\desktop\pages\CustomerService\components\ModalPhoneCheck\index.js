import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import {
  MDBModal,
  MDBModalBody,
  MDBBtn,
  MDBModalHeader,
  MDBIcon,
  MDBAlert
} from "mdbreact";
import { getPatientsByPhoneCSKH } from "~/store/customerService/customerServiceActions";

import styles from "./style.module.scss";
import history from "~/history";

class ModalPhoneCheck extends Component {
  state = {
    currentStep: 1,
    inputPhone: "",
    phoneValidate: false,
    phone_regexp: /((09|03|07|08|05)+([0-9]{8})\b)/
  };

  continueToNextStep = () => {
    this.props.disableAutoClose();
    this.setState({ currentStep: this.state.currentStep + 1 });
  };

  inputPhoneChangeHandler = event => {
    const phone = event.target.value;
    // do not allow user to enter text
    if (!this.state.phone_regexp.test(phone) || phone.length > 15) {
      this.setState({ phoneValidate: true, inputPhone: phone });
    } else {
      this.setState({ phoneValidate: false, inputPhone: phone });
    }
  };

  getAccountInfoByPhone = () => {
    const phone = this.state.inputPhone;
    if (!this.state.phone_regexp.test(phone) || phone.length > 15) {
      return;
    }
    this.props.requestPatientsInfoByPhone(phone);
  };

  renderStep = () => {
    switch (this.state.currentStep) {
      case 1:
        return (
          <div className={styles.pair_select}>
            <MDBBtn color="warning" onClick={() => history.push("/")}>
              Không!
            </MDBBtn>
            <MDBBtn color="success" onClick={() => this.continueToNextStep()}>
              OK!
            </MDBBtn>
          </div>
        );
      case 2:
        return (
          <div className={styles.search_phone}>
            <div className={styles.input_group}>
              <input
                type="text"
                placeholder="số điện thoại"
                value={this.state.inputPhone}
                onChange={event => this.inputPhoneChangeHandler(event)}
                onKeyDown={event => {
                  if (event.keyCode === 13) {
                    event.preventDefault();
                    this.getAccountInfoByPhone();
                  }
                }}
              />
              <button
                onClick={() => this.getAccountInfoByPhone()}
                style={{
                  backgroundImage:
                    "linear-gradient(90deg, #0352cc 0%, #0b7df1 100%)"
                }}
              >
                <MDBIcon icon="search" /> Tìm kiếm
              </button>
            </div>
            <div className={styles.error_message}>
              <label>
                {this.state.phoneValidate
                  ? "Số điện thoại không đúng định dạng!"
                  : ""}
              </label>
            </div>
            {this.props.supportingAccount ? (
              <Fragment>
                <div className={styles.search_result}>
                  <strong>Đã tìm thấy tài khoản:</strong>
                  <div className={styles.account_info}>
                    <div className={styles.col_30}>
                      <MDBIcon icon="user" />
                      <br />
                      <MDBIcon icon="phone" />
                    </div>
                    <div className={styles.col_70}>
                      {this.props.supportingAccount.fullname}
                      <br />
                      {this.props.supportingAccount.username}
                    </div>
                  </div>
                </div>
                <div className={styles.button_wrapper}>
                  <MDBBtn color="success" onClick={() => this.props.toggle()}>
                    Tiếp tục
                  </MDBBtn>
                </div>
              </Fragment>
            ) : null}
          </div>
        );
      default:
        return null;
    }
  };

  render() {
    return (
      <Fragment>
        <MDBModal
          isOpen={this.props.show}
          toggle={() => this.props.toggle()}
          size="lg"
        >
          <MDBModalHeader
            className={styles.modal_header}
            toggle={this.props.toggle}
          >
            <strong>Kiểm tra số điện thoại</strong>
          </MDBModalHeader>
          <MDBModalBody>
            <div className={styles.modal_container}>
              <div className={styles.error_header}>
                Opps! Không tìm thấy tài khoản đang hỗ trợ!
              </div>
              <MDBAlert color="warning">
                Có vẻ như bạn chưa thực hiện tìm kiếm tài khoản theo số điện
                thoại. Hệ thống chăm sóc khách hàng cần xác định tài khoản mà
                bạn đang hỗ trợ để thực hiện tính năng này.
              </MDBAlert>
              {this.renderStep()}
            </div>
          </MDBModalBody>
        </MDBModal>
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { supportingAccount }
  } = state;
  return { supportingAccount };
};

const mapDispatchToProps = dispatch => ({
  requestPatientsInfoByPhone: phone => {
    dispatch(getPatientsByPhoneCSKH(phone));
  }
});

export default connect(mapStateToProps, mapDispatchToProps)(ModalPhoneCheck);
