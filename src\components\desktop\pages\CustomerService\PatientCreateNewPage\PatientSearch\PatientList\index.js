import React, { Component, Fragment } from "react";

import { MDBDataTable } from "mdbreact";
import { patientListColumns } from "~/store/customerService/customreServiceMetaData";

class ResultBox extends Component {
  render() {
    const data = {
      columns: patientListColumns,
      rows: []
    };
    return (
      <Fragment>
        <MDBDataTable
          hover
          displayEntries={false}
          searching={false}
          data={data}
          scrollX
          fixed
          responsive
          bordered
          theadColor=""
          btn
        />
      </Fragment>
    );
  }
}

export default ResultBox;
