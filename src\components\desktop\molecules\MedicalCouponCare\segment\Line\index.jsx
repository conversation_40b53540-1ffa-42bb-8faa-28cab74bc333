import React from "react";
import styles from "./style.module.scss";
import cx from "classnames";

const Line = ({ top, bottom, normal = true }) => {
  return (
    <div
      className={cx(
        styles.line,
        top && styles.top,
        bottom && styles.bottom,
        normal && styles.normal
      )}
    >
      <div className={styles.circle} />
      <div className={styles.dashed} />
      <div className={styles.circle} />
    </div>
  );
};

export default Line;
