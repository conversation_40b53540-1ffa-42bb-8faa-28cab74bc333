import cx from "classnames";
import { MD<PERSON>ol, MDBContainer, MD<PERSON>ow } from "mdbreact";
import React from "react";
import { Image } from "semantic-ui-react";
import styles from "./style.module.scss";

export const Ads = () => {
  return (
    <MDBContainer className={cx(styles.image_ads, "p-0")}>
      {listAds.map(({ image }, i) => {
        return (
          <MDBRow key={i}>
            <MDBCol>
              <Image
                style={{ height: "700px", marginTop: "2rem" }}
                className="img-fluid rounded"
                src={image}
                alt={image}
              />
            </MDBCol>
          </MDBRow>
        );
      })}
    </MDBContainer>
  );
};

const listAds = [
  {
    image: "https://mdbootstrap.com/img/Photos/Others/images/93.jpg"
  }
];
