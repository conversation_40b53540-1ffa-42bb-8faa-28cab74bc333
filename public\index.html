<!DOCTYPE html>
<html lang="vi">

<head>
    <title id="title-page">Medpro - Đặt khám trự<PERSON> tuyến</title>

    <meta charset="utf-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=0" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <meta property="fb:page_id" content="683405612247779" />

    <meta name="description" id='description'
        content="Medpro, đặt khám trực tuyến, thanh toán viện phí, nh<PERSON><PERSON> u<PERSON>ng thuốc, kh<PERSON><PERSON> chuyên khoa, bác s<PERSON>, tư vấn sức khoẻ từ xa" />
    <meta name="keywords" id="keywords"
        content="medpro, đặt khám trự<PERSON> tuyến, thanh to<PERSON> viện phí, kh<PERSON><PERSON> chuyên kho<PERSON>, khá<PERSON> theo bá<PERSON> s<PERSON>" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="copyright" content="Medpro" />
    <meta name="author" content="Medpro" />
    <meta name="robots" content="noindex, nofollow" />
    <meta name="geo.placename" content="Ho Chi Minh, Viet Nam" />
    <meta name="geo.region" content="VN-HCM" />

    <meta name="revisit-after" content="3 days" />
    <meta property="og:type" content="website" />
    <meta property="fb:pages" content="???" />
    <meta property="og:image" id="image" content="%PUBLIC_URL%/assets/img/medpro.jpeg" />
    <meta property="og:image:width" content="1280" />
    <meta property="og:image:height" content="640" />
    <meta property="og:url" content="https://medpro.vn" itemprop="url" />
    <meta property="og:title" content="Medpro - Đặt khám trực tuyến" itemprop="headline" />

    <script src="https://zjs.zdn.vn/zalo/sdk.js" async></script>
    <script src='https://resource.medpro.com.vn/static/js/gtag.js' async></script>
    <script src='https://cms.medpro.com.vn/uploads/wechat_89290fe81a.js' async></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.min.js" defer></script>
    <script src="https://www.gstatic.com/firebasejs/8.1.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.1.1/firebase-analytics.js"></script>

    <link rel="shortcut icon" id="favicon" href="%PUBLIC_URL%/favicon.ico" />

    <!-- Google tag (gtag.js) -->
    <script src="https://www.googletagmanager.com/gtag/js?id=G-1ZFLFLEBBF" async></script>
    <script> 
        window.dataLayer = window.dataLayer || []; 
        function gtag(){
            dataLayer.push(arguments);
        } 
        gtag('js', new Date()); 
        gtag('config', 'G-1ZFLFLEBBF'); 
    </script>

    <!-- Facebook Pixel Code -->
    <script>
        !(function (f, b, e, v, n, t, s) {
            if (f.fbq) return;
            n = f.fbq = function () {
                n.callMethod
                    ? n.callMethod.apply(n, arguments)
                    : n.queue.push(arguments);
            };
            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = "2.0";
            n.queue = [];
            t = b.createElement(e);
            t.async = !0;
            t.src = v;
            s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s);
        })(
            window,
            document,
            "script",
            "https://connect.facebook.net/en_US/fbevents.js"
        );
        fbq("init", "471477723570821");
        fbq("track", "PageView");
    </script>

    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=471477723570821&ev=PageView&noscript=1" /></noscript>

    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.min.js"></script>


</head>

<body>
    <div id="root"></div>

    <!-- Facebook Pixel Code -->
    <script>
        !(function (f, b, e, v, n, t, s) {
            if (f.fbq) return;
            n = f.fbq = function () {
                n.callMethod
                    ? n.callMethod.apply(n, arguments)
                    : n.queue.push(arguments);
            };
            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = "2.0";
            n.queue = [];
            t = b.createElement(e);
            t.async = !0;
            t.src = v;
            s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s);
        })(
            window,
            document,
            "script",
            "https://connect.facebook.net/en_US/fbevents.js"
        );
        fbq("init", "397504298371447");
        fbq("track", "PageView");
    </script>
    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=397504298371447&ev=PageView&noscript=1" /></noscript>
    <!-- End Facebook Pixel Code -->
</body>

</html>