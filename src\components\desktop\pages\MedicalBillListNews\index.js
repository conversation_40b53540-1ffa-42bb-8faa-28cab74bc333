/* eslint-disable prettier/prettier */
import cx from "classnames";
import { get } from "lodash";
import {
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBContainer,
  MDBRow
} from "mdbreact";
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import { Link, withRouter } from "react-router-dom";
import { getBookingInfo } from "~/store/totalData/actions";
import partnerId from "~/utils/partner";
import MedicalCoupon from "../../molecules/MedicalCoupon";
import styles from "./style.module.scss";
class MedicalBillListNews extends Component {
  constructor(props) {
    super(props);
    this.state = { reload: false, activeItemBooking: "NormalMedpro" };
  }

  handleReloadBills = params => {
    const {
      match: {
        params: { code }
      }
    } = this.props;
    this.setState({ reload: true });
    this.props.getBookingInfo({ transactionId: code });

    setTimeout(() => {
      this.setState({ reload: false });
    }, 1500);
  };

  toggleTabs = value => {
    this.setState({ activeItemBooking: value });
  };

  render() {
    const { paymentInformation } = this.props;
    const { reload } = this.state;

    const status = get(paymentInformation, "bookingInfo.status");
    const invoiceUrl = get(paymentInformation, "bookingInfo.invoiceUrl");

    return (
      <div
        className={cx(
          styles.wapper_page_desktop,
          styles["wapper_page_desktop_" + partnerId]
        )}
      >
        <MDBContainer>
          <MDBRow>
            <MDBCol md="12" lg="12">
              <MDBCard className={styles.panels}>
                <div className={styles.back_btn}>
                  <Link
                    to={{
                      pathname: "/user",
                      state: { activeItem: 2 }
                    }}
                    className={cx(styles.button, styles.back_children)}
                  >
                    Danh sách phiếu khám
                  </Link>
                </div>
                <MDBCardHeader
                  className={cx(
                    styles.panels_header,
                    styles.panels_header_printbill
                  )}
                >
                  <div className={styles.sub_card_header}>
                    <span>Thông tin phiếu khám bệnh</span>
                    {status === 6 && (
                      <div className={styles.reload}>
                        <button
                          onClick={this.handleReloadBills}
                          className={reload && styles.active}
                        >
                          <span>Làm mới </span>{" "}
                          <i className="far fa-sync-alt" />
                        </button>
                      </div>
                    )}
                  </div>
                </MDBCardHeader>
                <MDBCardBody className={styles.card_body}>
                  {paymentInformation !== null && (
                    <Fragment>
                      <MedicalCoupon {...this.props} />
                      {!!invoiceUrl && (
                        <p>
                          Xem hóa đơn{" "}
                          <a
                            href={invoiceUrl}
                            target={"_blank" || "_self"}
                            rel="noopener noreferrer"
                          >
                            tại đây
                          </a>
                        </p>
                      )}
                    </Fragment>
                  )}
                </MDBCardBody>
              </MDBCard>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

const mapStateToProps = state => { };

const mapDispatchToProps = {
  getBookingInfo
};

export default withRouter(
  connect(mapStateToProps, mapDispatchToProps)(MedicalBillListNews)
);
