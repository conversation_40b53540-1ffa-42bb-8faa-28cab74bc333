@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/listgroup.scss";
.bg_service {
  display: flex;
}
.line {
  height: 2px;
  background: #e3e6f0;
  margin: 50px 0;
  &.line2 {
    margin: 30px 0;
  }
}
.section_download_app {
  padding: 7.5rem 0;
  h3 {
    line-height: 1.125;
    margin-bottom: 1.5rem;
  }
}

mark {
  color: inherit;
  padding: 0;
  background: none;
  background-image: linear-gradient(120deg, #9bf4cf 0, #9bf4cf 100%);
  background-repeat: no-repeat;
  background-size: 50% 0.3em;
  background-position: 0 90%;
  position: relative;
  animation-delay: 1s;
  display: block;
  &[data-aos="highlight-text"] {
    &.aos-animate {
      background-size: 100% 0.3em;
    }
  }
  &:not([data-aos="highlight-text"]) {
    background-size: 100% 0.3em;
  }
}

.carousel_slide {
  opacity: 0 !important;
}

.swiper-slide {
  opacity: 0.5;
}

.swiper-slide-next {
  opacity: 0.5;
}
.swiper-slide-prev {
  opacity: 0.5;
}
