import {
  MDBBtn,
  MDBInput,
  MDBModal,
  MDBModalBody,
  MDBModalFooter,
  MDBModalHeader
} from "mdbreact";
import React, { useState } from "react";
import { getFormatMoney } from "~/utils/getFormatMoney";
import Line from "../segment/Line";
import MedicalCouponCare from "../segment/MedicalCouponCare";
import styles from "./style.module.scss";

const Coupon = ({
  item,
  keys,
  listAddon,
  onPaymentAddon,
  care247Available
}) => {
  const [isAddTime, setIsAddTime] = useState(false);
  const [timeId, setTimeId] = useState("");

  const renderRadioItem = item => {
    const comparePrice =
      getFormatMoney(item?.originalPrice) > getFormatMoney(item?.price);
    return (
      <div className={styles.radioCare247}>
        <p className={styles.careItemName}>
          <span>{item.name}</span>
        </p>

        <p className={styles.careItemPrice}>
          <span className={styles.price}>
            Giá: {getFormatMoney(item?.price)}
            {item.currency} / {item.duration}
          </span>
          {comparePrice && (
            <span className={styles.originalPrice}>
              {getFormatMoney(item?.originalPrice)}
              {item.currency}
            </span>
          )}
        </p>
      </div>
    );
  };

  const toggleModal = () => {
    setIsAddTime(false);
    setTimeId("");
  };

  return (
    <div className={styles.container}>
      <div className={styles.coupon} key={keys}>
        <Line top />
        <MedicalCouponCare care247Available={care247Available} />
        <Line bottom />
      </div>
      <MDBBtn
        color="primary"
        size="xxl"
        className={styles.btnAddonTime}
        onClick={() => setIsAddTime(true)}
      >
        Đặt thêm giờ
      </MDBBtn>
      <MDBModal
        isOpen={isAddTime}
        centered
        className={styles.modal}
        toggle={toggleModal}
      >
        <MDBModalHeader className={styles.headerModal} toggle={toggleModal}>
          Đặt thêm giờ
        </MDBModalHeader>
        <MDBModalBody>
          {listAddon?.map(item => {
            return (
              <div key={item?.id} className={styles.medproCareItem}>
                <MDBInput
                  key={item.id}
                  id={item.id}
                  type="radio"
                  checked={timeId === item.id}
                  onClick={e => {
                    console.log("item.id", item.id);
                    if (item.id === timeId) {
                      setTimeId("");
                    } else {
                      setTimeId(item.id);
                    }
                  }}
                  label={renderRadioItem(item)}
                />
              </div>
            );
          })}
        </MDBModalBody>
        <MDBModalFooter>
          <MDBBtn
            color="primary"
            size="sm"
            onClick={() => {
              onPaymentAddon(timeId);
            }}
          >
            Thanh toán
          </MDBBtn>
        </MDBModalFooter>
      </MDBModal>
    </div>
  );
};

export default Coupon;
