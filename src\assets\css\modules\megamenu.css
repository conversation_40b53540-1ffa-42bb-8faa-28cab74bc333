/*
 * MDBootstrap Mega Menu
 * Learn more: https://mdbootstrap.com/docs/jquery/navigation/mega-menu/
 * About MDBootstrap: https://mdbootstrap.com/
 */
.navbar .mega-dropdown {
  position: static !important; }
  .navbar .mega-dropdown .dropdown-menu.mega-menu {
    width: 100%;
    border: none;
    -webkit-border-radius: 0;
    border-radius: 0; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu .sub-menu .news-title {
      font-size: 1.1rem;
      -webkit-transition: .2s;
      -o-transition: .2s;
      transition: .2s; }
      .navbar .mega-dropdown .dropdown-menu.mega-menu .sub-menu .news-title.smaller {
        font-weight: 400;
        font-size: 1rem;
        line-height: 1.4; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu .sub-menu .sub-title {
      border-bottom: 1px solid #e0e0e0; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu .sub-menu ul li a {
      width: 100%;
      -webkit-transition: .3s;
      -o-transition: .3s;
      transition: .3s; }
      .navbar .mega-dropdown .dropdown-menu.mega-menu .sub-menu ul li a:hover {
        background-color: rgba(0, 0, 0, 0.2);
        -webkit-transition: .3s;
        -o-transition: .3s;
        transition: .3s; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu.v-1 .sub-menu .news-single {
      border-bottom: 1px solid #e0e0e0; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu.v-1 .sub-menu .news-title {
      color: #4f4f4f !important; }
      .navbar .mega-dropdown .dropdown-menu.mega-menu.v-1 .sub-menu .news-title:hover {
        color: #2196f3 !important; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu.v-1 .sub-menu .m-sm {
      margin-bottom: -6px;
      font-size: .9rem;
      color: #2196f3 !important; }
      .navbar .mega-dropdown .dropdown-menu.mega-menu.v-1 .sub-menu .m-sm:hover {
        color: #2196f3 !important; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu.v-2 .sub-menu .news-title {
      color: #fff !important; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu.v-2 .sub-menu ul li a {
      color: #fff !important; }
      .navbar .mega-dropdown .dropdown-menu.mega-menu.v-2 .sub-menu ul li a:hover {
        color: #fff !important; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu.v-2 .sub-menu .sub-title {
      padding-bottom: 1rem;
      margin-bottom: 1rem; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu.v-3 .sub-menu ul li a {
      color: #fff !important; }
      .navbar .mega-dropdown .dropdown-menu.mega-menu.v-3 .sub-menu ul li a:hover {
        color: #fff !important; }
    .navbar .mega-dropdown .dropdown-menu.mega-menu.v-3 .sub-menu .news-title {
      color: #fff !important; }
      .navbar .mega-dropdown .dropdown-menu.mega-menu.v-3 .sub-menu .news-title:hover {
        color: #e0e0e0 !important; }
    @media (max-width: 1024px) {
      .navbar .mega-dropdown .dropdown-menu.mega-menu {
        max-height: 300px;
        overflow-x: hidden;
        overflow-y: auto; } }

.navbar .dropdown.multi-level-dropdown .dropdown-menu .dropdown-submenu {
  position: relative; }
  .navbar .dropdown.multi-level-dropdown .dropdown-menu .dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%; }
    .navbar .dropdown.multi-level-dropdown .dropdown-menu .dropdown-submenu .dropdown-menu.r-100 {
      right: 100%; }
    .navbar .dropdown.multi-level-dropdown .dropdown-menu .dropdown-submenu .dropdown-menu.l-auto {
      left: auto; }

.navbar .dropdown.multi-level-dropdown .dropdown-menu .dropdown-item {
  width: 100%;
  -webkit-transition: .3s;
  -o-transition: .3s;
  transition: .3s; }
  .navbar .dropdown.multi-level-dropdown .dropdown-menu .dropdown-item:hover {
    background-color: rgba(0, 0, 0, 0.2) !important;
    -webkit-transition: .3s;
    -o-transition: .3s;
    transition: .3s;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-border-radius: 0;
    border-radius: 0; }
