import cx from "classnames";
import { get } from "lodash";
import React from "react";
import { connect } from "react-redux";
import PKHBtn from "~/components/common/atoms/Button";
import styles from "./style.module.scss";

const BottomBill = ({ onCancel, bookingInfo, IsAuthenticated }) => {
  if (!bookingInfo) return null;

  const status = get(bookingInfo, "status", 1);

  if (!IsAuthenticated) return null;

  return (
    [1].includes(status) && (
      <div className={styles.bottom_navigation}>
        <PKHBtn
          flat
          className={cx(styles.button, styles.cancel)}
          onClick={onCancel}
        >
          <div className={styles.text}>
            <i className="fal fa-times" /> Hủy phiếu
          </div>
        </PKHBtn>
      </div>
    )
  );
};

const mapStateToProps = state => {
  const {
    user: { IsAuthenticated }
  } = state;

  return {
    IsAuthenticated
  };
};

export default connect(mapStateToProps, {})(BottomBill);
