import cx from "classnames";
import {
  MDBAnimation,
  MDBCol,
  MDBContainer,
  MDBListGroup,
  MDBListGroupItem,
  MDBRow
} from "mdbreact";
import React from "react";
import LazyLoad from "react-lazyload";
import chat from "~/assets/img/desktop/icon/chat.svg";
import android from "~/assets/img/desktop/icon/google-play.svg";
import hoadon from "~/assets/img/desktop/icon/hoadon.svg";
import ios from "~/assets/img/desktop/icon/ios.svg";
import news from "~/assets/img/desktop/icon/new.svg";
import payment from "~/assets/img/desktop/icon/payment.svg";
import register from "~/assets/img/desktop/icon/register.svg";
import result from "~/assets/img/desktop/icon/result.svg";
import TagName from "~/components/common/atoms/TagName";
import {
  LINK_ANDROID_APP,
  LINK_IOS_APP,
  urlSlide
} from "~/utils/manageResource";
import styles from "./style.module.scss";

const FeatureMedPro = () => {
  return (
    <div className={styles.feature_medpro} id="download_application">
      <MDBContainer>
        <MDBRow>
          <MDBCol size={12}>
            <MDBAnimation
              reveal="true"
              type="fadeIn"
              className={styles.feature_medpro_inner}
            >
              <TagName element="span" className={["title_section"]}>
                download
              </TagName>
              <TagName
                element="h3"
                className={["sub_title_section", "title_featureMedpro"]}
              >
                Tải ứng dụng <span>MedPro</span>
              </TagName>
              <MDBListGroup className={styles.list_group}>
                <MDBListGroupItem>
                  <a
                    href={LINK_IOS_APP}
                    alt="apple-app-icon"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <LazyLoad height={45}>
                      <img src={ios} alt="" />
                    </LazyLoad>
                  </a>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <a
                    href={LINK_ANDROID_APP}
                    alt="google-app-icon"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <LazyLoad height={45}>
                      <img src={android} alt="" />
                    </LazyLoad>
                  </a>
                </MDBListGroupItem>
              </MDBListGroup>
            </MDBAnimation>
          </MDBCol>
        </MDBRow>
        <MDBRow>
          <MDBCol size={12}>
            <MDBAnimation
              reveal="true"
              type="fadeIn"
              className={styles.feature_medpro_list}
            >
              <MDBListGroup className={cx(styles.list_group, styles.list_left)}>
                <MDBListGroupItem>
                  <LazyLoad height={80}>
                    <img src={register} alt="" />
                  </LazyLoad>
                  <h4>Đăng ký khám bệnh</h4>
                  <p>Đăng ký khám bệnh theo ngày</p>
                  <p>Đăng ký khám bệnh theo bác sĩ</p>
                  <p> Tái khám theo lịch hẹn</p>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <LazyLoad height={80}>
                    <img src={chat} alt="" />
                  </LazyLoad>
                  <h4>Tư vấn sức khoẻ trực tuyến </h4>
                  <p>Tư vấn sức khỏe trực tuyến với các</p>
                  <p>bác sĩ đầu ngành & chuyên gia</p>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <LazyLoad height={80}>
                    <img src={result} alt="" />
                  </LazyLoad>
                  <h4>Kết quả cận lâm sàng </h4>
                  <p>Kết quả Cận Lâm Sàng sẽ được cập nhật</p>
                  <p>trực tiếp trên phần mềm</p>
                </MDBListGroupItem>
              </MDBListGroup>
              <div className={styles.dt}>
                <LazyLoad height={585}>
                  <img src={urlSlide} alt="" className={styles.img_fluid} />
                </LazyLoad>
              </div>
              <MDBListGroup
                className={cx(styles.list_group, styles.list_right)}
              >
                <MDBListGroupItem>
                  <LazyLoad height={80}>
                    <img src={payment} alt="" />
                  </LazyLoad>
                  <h4>Thanh toán viện phí</h4>
                  <p>Đăng ký khám bệnh theo ngày</p>
                  <p>Đăng ký khám bệnh theo bác sĩ</p>
                  <p> Tái khám theo lịch hẹn</p>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <LazyLoad height={80}>
                    <img src={news} alt="" />
                  </LazyLoad>
                  <h4>Tin tức từ các bệnh viện </h4>
                  <p>Tin tức về sức khỏe, cập nhật kiến thức</p>
                  <p>chăm sóc sức khỏe từ các chuyên gia</p>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <LazyLoad height={80}>
                    <img src={hoadon} alt="" />
                  </LazyLoad>
                  <h4>Hoá đơn điện tử</h4>
                  <p>Tra cứu hóa đơn điện tử chính xác và nhanh chóng</p>
                </MDBListGroupItem>
              </MDBListGroup>
            </MDBAnimation>
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    </div>
  );
};

export default FeatureMedPro;
