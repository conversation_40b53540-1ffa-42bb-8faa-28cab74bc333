import { MDBContainer } from "mdbreact";
import React, { Component } from "react";
import BookingCard from "../../../components/BookingCard";
import styles from "./style.module.scss";

class PatientBoxBody extends Component {
  renderBookingList = (list, loading = false) => {
    if (!list || loading || list.length < 1) {
      return (
        <MDBContainer className={styles.LoadingInBox}>
          {/* <MDBSpinner crazy /> */}
          <span>
            <em><PERSON>ui lòng chọn hồ sơ để hiển thị phiếu khám bệnh! </em>
          </span>
        </MDBContainer>
      );
    }

    if (list.length > 0 && list[0]?.error) {
      return (
        <MDBContainer className={styles.LoadingInBox}>
          <span style={{ color: "red" }}>
            <em>{list[0]?.message} </em>
          </span>
        </MDBContainer>
      );
    }

    return list?.map(bill => (
      <BookingCard key={bill.id} billInfo={bill} {...this.props} />
    ));
  };

  render() {
    const { billList, loading } = this.props;
    return this.renderBookingList(billList, loading);
  }
}
export default PatientBoxBody;
