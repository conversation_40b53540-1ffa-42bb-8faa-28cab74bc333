import { VERSION } from "lodash";
import { applyMiddleware, createStore } from "redux";

import { composeWithDevTools } from "redux-devtools-extension";
import { persistReducer, persistStore } from "redux-persist";
import autoMergeLevel2 from "redux-persist/lib/stateReconciler/autoMergeLevel2";
import storage from "redux-persist/lib/storage";
import createSagaMiddleware from "redux-saga";
import rootReducer from "~/reducers";
import rootSaga from "~/sagas";

const sagaMiddleware = createSagaMiddleware();
const composeEnhancers = composeWithDevTools({});

const persistConfig = {
  key: "root",
  storage: storage,
  VERSION,
  stateReconciler: autoMergeLevel2, // Xem thêm tại mục "Quá trình merge".
  whitelist: [
    "user",
    "global",
    "totalData",
    "patientForm",
    "patient",
    "notification",
    "hospital",
    "followUpExam",
    "fee",
    "payment",
    "features",
    "invoice",
    "login",
    "filter",
    "customerService"
  ]
};

const pReducer = persistReducer(persistConfig, rootReducer);

export const store = createStore(
  pReducer,
  composeEnhancers(applyMiddleware(sagaMiddleware))
);

export const persistor = persistStore(store);

sagaMiddleware.run(rootSaga);
