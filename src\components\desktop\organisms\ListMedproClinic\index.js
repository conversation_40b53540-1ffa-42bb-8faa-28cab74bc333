import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MDBRow } from "mdbreact";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import * as featuresActions from "~/store/features/featuresActions";
import {
  requestHospitalList,
  resetFeatureListByHospital
} from "~/store/hospital/hospitalActions";
import * as rebooking from "~/store/rebooking/rebookingAction";
import { requestAllCity } from "~/store/resource/resourceAction";
import { resetData } from "~/store/totalData/actions";
import styles from "./style.module.scss";

const ChooseClinicDesktop = Loadable({
  loader: () => import("~/components/desktop/pages/ChooseHospital"),
  loading: LoadableLoading,
  delay: 10
});
class ChooseClinic extends Component {
  componentDidMount() {
    this.props.requestHospitalList();
    this.props.clearFollowupState();
    this.props.resetFeatureListByHospital();
    this.props.resetData();
    this.props.requestAllCity();
  }

  render() {
    return (
      <MDBContainer className={styles.Container}>
        <div className={styles.Label}>
          <p>Danh sách phòng khám/Phòng mạch</p>
        </div>
        <MDBRow className={styles.rowClinic}>
          <MDBCol xl={12} className={styles.colClinic}>
            <ChooseClinicDesktop {...this.props} {...this.methods} />
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    hospital: {
      selectedHospital,
      featureList: { data: featureList },
      hospitalList: { data: hospitalList }
    },
    features: { selectedFeature, featureListBooking },
    user: { IsAuthenticated },
    resource: { dataCity: allCities }
  } = state;
  return {
    device: type,
    selectedHospital,
    selectedFeature,
    IsAuthenticated,
    featureList,
    featureListBooking,
    hospitalList,
    allCities
  };
};

const mapDispatchToProps = dispatch => ({
  clearFollowupState: () => dispatch(rebooking.clearState()),
  resetFeatureListByHospital: () => {
    dispatch(resetFeatureListByHospital());
  },
  selectFeatureBookingById: id =>
    dispatch(featuresActions.selectFeatureBookingById(id)),
  requestHospitalList: () => {
    dispatch(requestHospitalList());
  },
  resetData: () => {
    dispatch(resetData());
  },
  requestAllCity: () => {
    dispatch(requestAllCity());
  }
});

const ChooseClinicHelmet = withTitle({
  component: ChooseClinic,
  title: "Medpro | Chọn phòng khám"
});

export default withRouter(
  connect(mapStateToProps, mapDispatchToProps)(ChooseClinicHelmet)
);
