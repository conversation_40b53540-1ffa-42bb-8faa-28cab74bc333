@charset "UTF-8";
/*
 * MDBootstrap Cards Extended
 * Learn more: https://mdbootstrap.com/components/extended-cards/
 * About MDBootstrap: https://mdbootstrap.com/
 */
/*** Global ***/
.mdb-color.lighten-5 {
  background-color: #d0d6e2 !important; }

.mdb-color.lighten-4 {
  background-color: #b1bace !important; }

.mdb-color.lighten-3 {
  background-color: #929fba !important; }

.mdb-color.lighten-2 {
  background-color: #7283a7 !important; }

.mdb-color.lighten-1 {
  background-color: #59698d !important; }

.mdb-color {
  background-color: #45526e !important; }

.mdb-color-text {
  color: #45526e !important; }

.rgba-mdb-color-slight,
.rgba-mdb-color-slight:after {
  background-color: rgba(69, 82, 110, 0.1); }

.rgba-mdb-color-light,
.rgba-mdb-color-light:after {
  background-color: rgba(69, 82, 110, 0.3); }

.rgba-mdb-color-strong,
.rgba-mdb-color-strong:after {
  background-color: rgba(69, 82, 110, 0.7); }

.mdb-color.darken-1 {
  background-color: #3b465e !important; }

.mdb-color.darken-2 {
  background-color: #2e3951 !important; }

.mdb-color.darken-3 {
  background-color: #1c2a48 !important; }

.mdb-color.darken-4 {
  background-color: #1c2331 !important; }

.red.lighten-5 {
  background-color: #ffebee !important; }

.red.lighten-4 {
  background-color: #ffcdd2 !important; }

.red.lighten-3 {
  background-color: #ef9a9a !important; }

.red.lighten-2 {
  background-color: #e57373 !important; }

.red.lighten-1 {
  background-color: #ef5350 !important; }

.red {
  background-color: #f44336 !important; }

.red-text {
  color: #f44336 !important; }

.rgba-red-slight,
.rgba-red-slight:after {
  background-color: rgba(244, 67, 54, 0.1); }

.rgba-red-light,
.rgba-red-light:after {
  background-color: rgba(244, 67, 54, 0.3); }

.rgba-red-strong,
.rgba-red-strong:after {
  background-color: rgba(244, 67, 54, 0.7); }

.red.darken-1 {
  background-color: #e53935 !important; }

.red.darken-2 {
  background-color: #d32f2f !important; }

.red.darken-3 {
  background-color: #c62828 !important; }

.red.darken-4 {
  background-color: #b71c1c !important; }

.red.accent-1 {
  background-color: #ff8a80 !important; }

.red.accent-2 {
  background-color: #ff5252 !important; }

.red.accent-3 {
  background-color: #ff1744 !important; }

.red.accent-4 {
  background-color: #d50000 !important; }

.pink.lighten-5 {
  background-color: #fce4ec !important; }

.pink.lighten-4 {
  background-color: #f8bbd0 !important; }

.pink.lighten-3 {
  background-color: #f48fb1 !important; }

.pink.lighten-2 {
  background-color: #f06292 !important; }

.pink.lighten-1 {
  background-color: #ec407a !important; }

.pink {
  background-color: #e91e63 !important; }

.pink-text {
  color: #e91e63 !important; }

.rgba-pink-slight,
.rgba-pink-slight:after {
  background-color: rgba(233, 30, 99, 0.1); }

.rgba-pink-light,
.rgba-pink-light:after {
  background-color: rgba(233, 30, 99, 0.3); }

.rgba-pink-strong,
.rgba-pink-strong:after {
  background-color: rgba(233, 30, 99, 0.7); }

.pink.darken-1 {
  background-color: #d81b60 !important; }

.pink.darken-2 {
  background-color: #c2185b !important; }

.pink.darken-3 {
  background-color: #ad1457 !important; }

.pink.darken-4 {
  background-color: #880e4f !important; }

.pink.accent-1 {
  background-color: #ff80ab !important; }

.pink.accent-2 {
  background-color: #ff4081 !important; }

.pink.accent-3 {
  background-color: #f50057 !important; }

.pink.accent-4 {
  background-color: #c51162 !important; }

.purple.lighten-5 {
  background-color: #f3e5f5 !important; }

.purple.lighten-4 {
  background-color: #e1bee7 !important; }

.purple.lighten-3 {
  background-color: #ce93d8 !important; }

.purple.lighten-2 {
  background-color: #ba68c8 !important; }

.purple.lighten-1 {
  background-color: #ab47bc !important; }

.purple {
  background-color: #9c27b0 !important; }

.purple-text {
  color: #9c27b0 !important; }

.rgba-purple-slight,
.rgba-purple-slight:after {
  background-color: rgba(156, 39, 176, 0.1); }

.rgba-purple-light,
.rgba-purple-light:after {
  background-color: rgba(156, 39, 176, 0.3); }

.rgba-purple-strong,
.rgba-purple-strong:after {
  background-color: rgba(156, 39, 176, 0.7); }

.purple.darken-1 {
  background-color: #8e24aa !important; }

.purple.darken-2 {
  background-color: #7b1fa2 !important; }

.purple.darken-3 {
  background-color: #6a1b9a !important; }

.purple.darken-4 {
  background-color: #4a148c !important; }

.purple.accent-1 {
  background-color: #ea80fc !important; }

.purple.accent-2 {
  background-color: #e040fb !important; }

.purple.accent-3 {
  background-color: #d500f9 !important; }

.purple.accent-4 {
  background-color: #aa00ff !important; }

.deep-purple.lighten-5 {
  background-color: #ede7f6 !important; }

.deep-purple.lighten-4 {
  background-color: #d1c4e9 !important; }

.deep-purple.lighten-3 {
  background-color: #b39ddb !important; }

.deep-purple.lighten-2 {
  background-color: #9575cd !important; }

.deep-purple.lighten-1 {
  background-color: #7e57c2 !important; }

.deep-purple {
  background-color: #673ab7 !important; }

.deep-purple-text {
  color: #673ab7 !important; }

.rgba-deep-purple-slight,
.rgba-deep-purple-slight:after {
  background-color: rgba(103, 58, 183, 0.1); }

.rgba-deep-purple-light,
.rgba-deep-purple-light:after {
  background-color: rgba(103, 58, 183, 0.3); }

.rgba-deep-purple-strong,
.rgba-deep-purple-strong:after {
  background-color: rgba(103, 58, 183, 0.7); }

.deep-purple.darken-1 {
  background-color: #5e35b1 !important; }

.deep-purple.darken-2 {
  background-color: #512da8 !important; }

.deep-purple.darken-3 {
  background-color: #4527a0 !important; }

.deep-purple.darken-4 {
  background-color: #311b92 !important; }

.deep-purple.accent-1 {
  background-color: #b388ff !important; }

.deep-purple.accent-2 {
  background-color: #7c4dff !important; }

.deep-purple.accent-3 {
  background-color: #651fff !important; }

.deep-purple.accent-4 {
  background-color: #6200ea !important; }

.indigo.lighten-5 {
  background-color: #e8eaf6 !important; }

.indigo.lighten-4 {
  background-color: #c5cae9 !important; }

.indigo.lighten-3 {
  background-color: #9fa8da !important; }

.indigo.lighten-2 {
  background-color: #7986cb !important; }

.indigo.lighten-1 {
  background-color: #5c6bc0 !important; }

.indigo {
  background-color: #3f51b5 !important; }

.indigo-text {
  color: #3f51b5 !important; }

.rgba-indigo-slight,
.rgba-indigo-slight:after {
  background-color: rgba(63, 81, 181, 0.1); }

.rgba-indigo-light,
.rgba-indigo-light:after {
  background-color: rgba(63, 81, 181, 0.3); }

.rgba-indigo-strong,
.rgba-indigo-strong:after {
  background-color: rgba(63, 81, 181, 0.7); }

.indigo.darken-1 {
  background-color: #3949ab !important; }

.indigo.darken-2 {
  background-color: #303f9f !important; }

.indigo.darken-3 {
  background-color: #283593 !important; }

.indigo.darken-4 {
  background-color: #1a237e !important; }

.indigo.accent-1 {
  background-color: #8c9eff !important; }

.indigo.accent-2 {
  background-color: #536dfe !important; }

.indigo.accent-3 {
  background-color: #3d5afe !important; }

.indigo.accent-4 {
  background-color: #304ffe !important; }

.blue.lighten-5 {
  background-color: #e3f2fd !important; }

.blue.lighten-4 {
  background-color: #bbdefb !important; }

.blue.lighten-3 {
  background-color: #90caf9 !important; }

.blue.lighten-2 {
  background-color: #64b5f6 !important; }

.blue.lighten-1 {
  background-color: #42a5f5 !important; }

.blue {
  background-color: #2196f3 !important; }

.blue-text {
  color: #2196f3 !important; }

.rgba-blue-slight,
.rgba-blue-slight:after {
  background-color: rgba(33, 150, 243, 0.1); }

.rgba-blue-light,
.rgba-blue-light:after {
  background-color: rgba(33, 150, 243, 0.3); }

.rgba-blue-strong,
.rgba-blue-strong:after {
  background-color: rgba(33, 150, 243, 0.7); }

.blue.darken-1 {
  background-color: #1e88e5 !important; }

.blue.darken-2 {
  background-color: #1976d2 !important; }

.blue.darken-3 {
  background-color: #1565c0 !important; }

.blue.darken-4 {
  background-color: #0d47a1 !important; }

.blue.accent-1 {
  background-color: #82b1ff !important; }

.blue.accent-2 {
  background-color: #448aff !important; }

.blue.accent-3 {
  background-color: #2979ff !important; }

.blue.accent-4 {
  background-color: #2962ff !important; }

.light-blue.lighten-5 {
  background-color: #e1f5fe !important; }

.light-blue.lighten-4 {
  background-color: #b3e5fc !important; }

.light-blue.lighten-3 {
  background-color: #81d4fa !important; }

.light-blue.lighten-2 {
  background-color: #4fc3f7 !important; }

.light-blue.lighten-1 {
  background-color: #29b6f6 !important; }

.light-blue {
  background-color: #03a9f4 !important; }

.light-blue-text {
  color: #03a9f4 !important; }

.rgba-light-blue-slight,
.rgba-light-blue-slight:after {
  background-color: rgba(3, 169, 244, 0.1); }

.rgba-light-blue-light,
.rgba-light-blue-light:after {
  background-color: rgba(3, 169, 244, 0.3); }

.rgba-light-blue-strong,
.rgba-light-blue-strong:after {
  background-color: rgba(3, 169, 244, 0.7); }

.light-blue.darken-1 {
  background-color: #039be5 !important; }

.light-blue.darken-2 {
  background-color: #0288d1 !important; }

.light-blue.darken-3 {
  background-color: #0277bd !important; }

.light-blue.darken-4 {
  background-color: #01579b !important; }

.light-blue.accent-1 {
  background-color: #80d8ff !important; }

.light-blue.accent-2 {
  background-color: #40c4ff !important; }

.light-blue.accent-3 {
  background-color: #00b0ff !important; }

.light-blue.accent-4 {
  background-color: #0091ea !important; }

.cyan.lighten-5 {
  background-color: #e0f7fa !important; }

.cyan.lighten-4 {
  background-color: #b2ebf2 !important; }

.cyan.lighten-3 {
  background-color: #80deea !important; }

.cyan.lighten-2 {
  background-color: #4dd0e1 !important; }

.cyan.lighten-1 {
  background-color: #26c6da !important; }

.cyan {
  background-color: #00bcd4 !important; }

.cyan-text {
  color: #00bcd4 !important; }

.rgba-cyan-slight,
.rgba-cyan-slight:after {
  background-color: rgba(0, 188, 212, 0.1); }

.rgba-cyan-light,
.rgba-cyan-light:after {
  background-color: rgba(0, 188, 212, 0.3); }

.rgba-cyan-strong,
.rgba-cyan-strong:after {
  background-color: rgba(0, 188, 212, 0.7); }

.cyan.darken-1 {
  background-color: #00acc1 !important; }

.cyan.darken-2 {
  background-color: #0097a7 !important; }

.cyan.darken-3 {
  background-color: #00838f !important; }

.cyan.darken-4 {
  background-color: #006064 !important; }

.cyan.accent-1 {
  background-color: #84ffff !important; }

.cyan.accent-2 {
  background-color: #18ffff !important; }

.cyan.accent-3 {
  background-color: #00e5ff !important; }

.cyan.accent-4 {
  background-color: #00b8d4 !important; }

.teal.lighten-5 {
  background-color: #e0f2f1 !important; }

.teal.lighten-4 {
  background-color: #b2dfdb !important; }

.teal.lighten-3 {
  background-color: #80cbc4 !important; }

.teal.lighten-2 {
  background-color: #4db6ac !important; }

.teal.lighten-1 {
  background-color: #26a69a !important; }

.teal {
  background-color: #009688 !important; }

.teal-text {
  color: #009688 !important; }

.rgba-teal-slight,
.rgba-teal-slight:after {
  background-color: rgba(0, 150, 136, 0.1); }

.rgba-teal-light,
.rgba-teal-light:after {
  background-color: rgba(0, 150, 136, 0.3); }

.rgba-teal-strong,
.rgba-teal-strong:after {
  background-color: rgba(0, 150, 136, 0.7); }

.teal.darken-1 {
  background-color: #00897b !important; }

.teal.darken-2 {
  background-color: #00796b !important; }

.teal.darken-3 {
  background-color: #00695c !important; }

.teal.darken-4 {
  background-color: #004d40 !important; }

.teal.accent-1 {
  background-color: #a7ffeb !important; }

.teal.accent-2 {
  background-color: #64ffda !important; }

.teal.accent-3 {
  background-color: #1de9b6 !important; }

.teal.accent-4 {
  background-color: #00bfa5 !important; }

.green.lighten-5 {
  background-color: #e8f5e9 !important; }

.green.lighten-4 {
  background-color: #c8e6c9 !important; }

.green.lighten-3 {
  background-color: #a5d6a7 !important; }

.green.lighten-2 {
  background-color: #81c784 !important; }

.green.lighten-1 {
  background-color: #66bb6a !important; }

.green {
  background-color: #4caf50 !important; }

.green-text {
  color: #4caf50 !important; }

.rgba-green-slight,
.rgba-green-slight:after {
  background-color: rgba(76, 175, 80, 0.1); }

.rgba-green-light,
.rgba-green-light:after {
  background-color: rgba(76, 175, 80, 0.3); }

.rgba-green-strong,
.rgba-green-strong:after {
  background-color: rgba(76, 175, 80, 0.7); }

.green.darken-1 {
  background-color: #43a047 !important; }

.green.darken-2 {
  background-color: #388e3c !important; }

.green.darken-3 {
  background-color: #2e7d32 !important; }

.green.darken-4 {
  background-color: #1b5e20 !important; }

.green.accent-1 {
  background-color: #b9f6ca !important; }

.green.accent-2 {
  background-color: #69f0ae !important; }

.green.accent-3 {
  background-color: #00e676 !important; }

.green.accent-4 {
  background-color: #00c853 !important; }

.light-green.lighten-5 {
  background-color: #f1f8e9 !important; }

.light-green.lighten-4 {
  background-color: #dcedc8 !important; }

.light-green.lighten-3 {
  background-color: #c5e1a5 !important; }

.light-green.lighten-2 {
  background-color: #aed581 !important; }

.light-green.lighten-1 {
  background-color: #9ccc65 !important; }

.light-green {
  background-color: #8bc34a !important; }

.light-green-text {
  color: #8bc34a !important; }

.rgba-light-green-slight,
.rgba-light-green-slight:after {
  background-color: rgba(139, 195, 74, 0.1); }

.rgba-light-green-light,
.rgba-light-green-light:after {
  background-color: rgba(139, 195, 74, 0.3); }

.rgba-light-green-strong,
.rgba-light-green-strong:after {
  background-color: rgba(139, 195, 74, 0.7); }

.light-green.darken-1 {
  background-color: #7cb342 !important; }

.light-green.darken-2 {
  background-color: #689f38 !important; }

.light-green.darken-3 {
  background-color: #558b2f !important; }

.light-green.darken-4 {
  background-color: #33691e !important; }

.light-green.accent-1 {
  background-color: #ccff90 !important; }

.light-green.accent-2 {
  background-color: #b2ff59 !important; }

.light-green.accent-3 {
  background-color: #76ff03 !important; }

.light-green.accent-4 {
  background-color: #64dd17 !important; }

.lime.lighten-5 {
  background-color: #f9fbe7 !important; }

.lime.lighten-4 {
  background-color: #f0f4c3 !important; }

.lime.lighten-3 {
  background-color: #e6ee9c !important; }

.lime.lighten-2 {
  background-color: #dce775 !important; }

.lime.lighten-1 {
  background-color: #d4e157 !important; }

.lime {
  background-color: #cddc39 !important; }

.lime-text {
  color: #cddc39 !important; }

.rgba-lime-slight,
.rgba-lime-slight:after {
  background-color: rgba(205, 220, 57, 0.1); }

.rgba-lime-light,
.rgba-lime-light:after {
  background-color: rgba(205, 220, 57, 0.3); }

.rgba-lime-strong,
.rgba-lime-strong:after {
  background-color: rgba(205, 220, 57, 0.7); }

.lime.darken-1 {
  background-color: #c0ca33 !important; }

.lime.darken-2 {
  background-color: #afb42b !important; }

.lime.darken-3 {
  background-color: #9e9d24 !important; }

.lime.darken-4 {
  background-color: #827717 !important; }

.lime.accent-1 {
  background-color: #f4ff81 !important; }

.lime.accent-2 {
  background-color: #eeff41 !important; }

.lime.accent-3 {
  background-color: #c6ff00 !important; }

.lime.accent-4 {
  background-color: #aeea00 !important; }

.yellow.lighten-5 {
  background-color: #fffde7 !important; }

.yellow.lighten-4 {
  background-color: #fff9c4 !important; }

.yellow.lighten-3 {
  background-color: #fff59d !important; }

.yellow.lighten-2 {
  background-color: #fff176 !important; }

.yellow.lighten-1 {
  background-color: #ffee58 !important; }

.yellow {
  background-color: #ffeb3b !important; }

.yellow-text {
  color: #ffeb3b !important; }

.rgba-yellow-slight,
.rgba-yellow-slight:after {
  background-color: rgba(255, 235, 59, 0.1); }

.rgba-yellow-light,
.rgba-yellow-light:after {
  background-color: rgba(255, 235, 59, 0.3); }

.rgba-yellow-strong,
.rgba-yellow-strong:after {
  background-color: rgba(255, 235, 59, 0.7); }

.yellow.darken-1 {
  background-color: #fdd835 !important; }

.yellow.darken-2 {
  background-color: #fbc02d !important; }

.yellow.darken-3 {
  background-color: #f9a825 !important; }

.yellow.darken-4 {
  background-color: #f57f17 !important; }

.yellow.accent-1 {
  background-color: #ffff8d !important; }

.yellow.accent-2 {
  background-color: #ffff00 !important; }

.yellow.accent-3 {
  background-color: #ffea00 !important; }

.yellow.accent-4 {
  background-color: #ffd600 !important; }

.amber.lighten-5 {
  background-color: #fff8e1 !important; }

.amber.lighten-4 {
  background-color: #ffecb3 !important; }

.amber.lighten-3 {
  background-color: #ffe082 !important; }

.amber.lighten-2 {
  background-color: #ffd54f !important; }

.amber.lighten-1 {
  background-color: #ffca28 !important; }

.amber {
  background-color: #ffc107 !important; }

.amber-text {
  color: #ffc107 !important; }

.rgba-amber-slight,
.rgba-amber-slight:after {
  background-color: rgba(255, 193, 7, 0.1); }

.rgba-amber-light,
.rgba-amber-light:after {
  background-color: rgba(255, 193, 7, 0.3); }

.rgba-amber-strong,
.rgba-amber-strong:after {
  background-color: rgba(255, 193, 7, 0.7); }

.amber.darken-1 {
  background-color: #ffb300 !important; }

.amber.darken-2 {
  background-color: #ffa000 !important; }

.amber.darken-3 {
  background-color: #ff8f00 !important; }

.amber.darken-4 {
  background-color: #ff6f00 !important; }

.amber.accent-1 {
  background-color: #ffe57f !important; }

.amber.accent-2 {
  background-color: #ffd740 !important; }

.amber.accent-3 {
  background-color: #ffc400 !important; }

.amber.accent-4 {
  background-color: #ffab00 !important; }

.orange.lighten-5 {
  background-color: #fff3e0 !important; }

.orange.lighten-4 {
  background-color: #ffe0b2 !important; }

.orange.lighten-3 {
  background-color: #ffcc80 !important; }

.orange.lighten-2 {
  background-color: #ffb74d !important; }

.orange.lighten-1 {
  background-color: #ffa726 !important; }

.orange {
  background-color: #ff9800 !important; }

.orange-text {
  color: #ff9800 !important; }

.rgba-orange-slight,
.rgba-orange-slight:after {
  background-color: rgba(255, 152, 0, 0.1); }

.rgba-orange-light,
.rgba-orange-light:after {
  background-color: rgba(255, 152, 0, 0.3); }

.rgba-orange-strong,
.rgba-orange-strong:after {
  background-color: rgba(255, 152, 0, 0.7); }

.orange.darken-1 {
  background-color: #fb8c00 !important; }

.orange.darken-2 {
  background-color: #f57c00 !important; }

.orange.darken-3 {
  background-color: #ef6c00 !important; }

.orange.darken-4 {
  background-color: #e65100 !important; }

.orange.accent-1 {
  background-color: #ffd180 !important; }

.orange.accent-2 {
  background-color: #ffab40 !important; }

.orange.accent-3 {
  background-color: #ff9100 !important; }

.orange.accent-4 {
  background-color: #ff6d00 !important; }

.deep-orange.lighten-5 {
  background-color: #fbe9e7 !important; }

.deep-orange.lighten-4 {
  background-color: #ffccbc !important; }

.deep-orange.lighten-3 {
  background-color: #ffab91 !important; }

.deep-orange.lighten-2 {
  background-color: #ff8a65 !important; }

.deep-orange.lighten-1 {
  background-color: #ff7043 !important; }

.deep-orange {
  background-color: #ff5722 !important; }

.deep-orange-text {
  color: #ff5722 !important; }

.rgba-deep-orange-slight,
.rgba-deep-orange-slight:after {
  background-color: rgba(255, 87, 34, 0.1); }

.rgba-deep-orange-light,
.rgba-deep-orange-light:after {
  background-color: rgba(255, 87, 34, 0.3); }

.rgba-deep-orange-strong,
.rgba-deep-orange-strong:after {
  background-color: rgba(255, 87, 34, 0.7); }

.deep-orange.darken-1 {
  background-color: #f4511e !important; }

.deep-orange.darken-2 {
  background-color: #e64a19 !important; }

.deep-orange.darken-3 {
  background-color: #d84315 !important; }

.deep-orange.darken-4 {
  background-color: #bf360c !important; }

.deep-orange.accent-1 {
  background-color: #ff9e80 !important; }

.deep-orange.accent-2 {
  background-color: #ff6e40 !important; }

.deep-orange.accent-3 {
  background-color: #ff3d00 !important; }

.deep-orange.accent-4 {
  background-color: #dd2c00 !important; }

.brown.lighten-5 {
  background-color: #efebe9 !important; }

.brown.lighten-4 {
  background-color: #d7ccc8 !important; }

.brown.lighten-3 {
  background-color: #bcaaa4 !important; }

.brown.lighten-2 {
  background-color: #a1887f !important; }

.brown.lighten-1 {
  background-color: #8d6e63 !important; }

.brown {
  background-color: #795548 !important; }

.brown-text {
  color: #795548 !important; }

.rgba-brown-slight,
.rgba-brown-slight:after {
  background-color: rgba(121, 85, 72, 0.1); }

.rgba-brown-light,
.rgba-brown-light:after {
  background-color: rgba(121, 85, 72, 0.3); }

.rgba-brown-strong,
.rgba-brown-strong:after {
  background-color: rgba(121, 85, 72, 0.7); }

.brown.darken-1 {
  background-color: #6d4c41 !important; }

.brown.darken-2 {
  background-color: #5d4037 !important; }

.brown.darken-3 {
  background-color: #4e342e !important; }

.brown.darken-4 {
  background-color: #3e2723 !important; }

.blue-grey.lighten-5 {
  background-color: #eceff1 !important; }

.blue-grey.lighten-4 {
  background-color: #cfd8dc !important; }

.blue-grey.lighten-3 {
  background-color: #b0bec5 !important; }

.blue-grey.lighten-2 {
  background-color: #90a4ae !important; }

.blue-grey.lighten-1 {
  background-color: #78909c !important; }

.blue-grey {
  background-color: #607d8b !important; }

.blue-grey-text {
  color: #607d8b !important; }

.rgba-blue-grey-slight,
.rgba-blue-grey-slight:after {
  background-color: rgba(96, 125, 139, 0.1); }

.rgba-blue-grey-light,
.rgba-blue-grey-light:after {
  background-color: rgba(96, 125, 139, 0.3); }

.rgba-blue-grey-strong,
.rgba-blue-grey-strong:after {
  background-color: rgba(96, 125, 139, 0.7); }

.blue-grey.darken-1 {
  background-color: #546e7a !important; }

.blue-grey.darken-2 {
  background-color: #455a64 !important; }

.blue-grey.darken-3 {
  background-color: #37474f !important; }

.blue-grey.darken-4 {
  background-color: #263238 !important; }

.grey.lighten-5 {
  background-color: #fafafa !important; }

.grey.lighten-4 {
  background-color: #f5f5f5 !important; }

.grey.lighten-3 {
  background-color: #eeeeee !important; }

.grey.lighten-2 {
  background-color: #e0e0e0 !important; }

.grey.lighten-1 {
  background-color: #bdbdbd !important; }

.grey {
  background-color: #9e9e9e !important; }

.grey-text {
  color: #9e9e9e !important; }

.rgba-grey-slight,
.rgba-grey-slight:after {
  background-color: rgba(158, 158, 158, 0.1); }

.rgba-grey-light,
.rgba-grey-light:after {
  background-color: rgba(158, 158, 158, 0.3); }

.rgba-grey-strong,
.rgba-grey-strong:after {
  background-color: rgba(158, 158, 158, 0.7); }

.grey.darken-1 {
  background-color: #757575 !important; }

.grey.darken-2 {
  background-color: #616161 !important; }

.grey.darken-3 {
  background-color: #424242 !important; }

.grey.darken-4 {
  background-color: #212121 !important; }

.black {
  background-color: #000 !important; }

.black-text {
  color: #000 !important; }

.rgba-black-slight,
.rgba-black-slight:after {
  background-color: rgba(0, 0, 0, 0.1); }

.rgba-black-light,
.rgba-black-light:after {
  background-color: rgba(0, 0, 0, 0.3); }

.rgba-black-strong,
.rgba-black-strong:after {
  background-color: rgba(0, 0, 0, 0.7); }

.white {
  background-color: #fff !important; }

.white-text {
  color: #fff !important; }

.rgba-white-slight,
.rgba-white-slight:after {
  background-color: rgba(255, 255, 255, 0.1); }

.rgba-white-light,
.rgba-white-light:after {
  background-color: rgba(255, 255, 255, 0.3); }

.rgba-white-strong,
.rgba-white-strong:after {
  background-color: rgba(255, 255, 255, 0.7); }

.rgba-stylish-slight {
  background-color: rgba(62, 69, 81, 0.1); }

.rgba-stylish-light {
  background-color: rgba(62, 69, 81, 0.3); }

.rgba-stylish-strong {
  background-color: rgba(62, 69, 81, 0.7); }

.primary-color {
  background-color: #0352CC !important; }

.primary-color-dark {
  background-color: #0d47a1 !important; }

.secondary-color {
  background-color: #aa66cc !important; }

.secondary-color-dark {
  background-color: #9933cc !important; }

.default-color {
  background-color: #2bbbad !important; }

.default-color-dark {
  background-color: #00695c !important; }

.info-color {
  background-color: #33b5e5 !important; }

.info-color-dark {
  background-color: #0099cc !important; }

.success-color {
  background-color: #00c851 !important; }

.success-color-dark {
  background-color: #007e33 !important; }

.warning-color {
  background-color: #ffbb33 !important; }

.warning-color-dark {
  background-color: #ff8800 !important; }

.danger-color {
  background-color: #ff3547 !important; }

.danger-color-dark {
  background-color: #cc0000 !important; }

.elegant-color {
  background-color: #2e2e2e !important; }

.elegant-color-dark {
  background-color: #212121 !important; }

.stylish-color {
  background-color: #4b515d !important; }

.stylish-color-dark {
  background-color: #3e4551 !important; }

.unique-color {
  background-color: #3f729b !important; }

.unique-color-dark {
  background-color: #1c2331 !important; }

.special-color {
  background-color: #37474f !important; }

.special-color-dark {
  background-color: #263238 !important; }

.purple-gradient {
  background: linear-gradient(40deg, #ff6ec4, #7873f5) !important; }

.peach-gradient {
  background: linear-gradient(40deg, #FFD86F, #FC6262) !important; }

.aqua-gradient {
  background: linear-gradient(40deg, #2096ff, #05ffa3) !important; }

.blue-gradient {
  background: linear-gradient(40deg, #45cafc, #303f9f) !important; }

.purple-gradient-rgba {
  background: linear-gradient(40deg, rgba(255, 110, 196, 0.9), rgba(120, 115, 245, 0.9)) !important; }

.peach-gradient-rgba {
  background: linear-gradient(40deg, rgba(255, 216, 111, 0.9), rgba(252, 98, 98, 0.9)) !important; }

.aqua-gradient-rgba {
  background: linear-gradient(40deg, rgba(32, 150, 255, 0.9), rgba(5, 255, 163, 0.9)) !important; }

.blue-gradient-rgba {
  background: linear-gradient(40deg, rgba(69, 202, 252, 0.9), rgba(48, 63, 159, 0.9)) !important; }

.dark-grey-text {
  color: #4f4f4f !important; }
  .dark-grey-text:hover, .dark-grey-text:focus {
    color: #4f4f4f !important; }

.hoverable {
  box-shadow: none;
  transition: all 0.55s ease-in-out; }
  .hoverable:hover {
    box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    transition: all 0.55s ease-in-out; }

.z-depth-0 {
  box-shadow: none !important; }

.z-depth-1, .card.gradient-card:focus-within .card-image {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12) !important; }

.z-depth-1-half {
  box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15) !important; }

.z-depth-2 {
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important; }

.z-depth-3 {
  box-shadow: 0 12px 15px 0 rgba(0, 0, 0, 0.24), 0 17px 50px 0 rgba(0, 0, 0, 0.19) !important; }

.z-depth-4 {
  box-shadow: 0 16px 28px 0 rgba(0, 0, 0, 0.22), 0 25px 55px 0 rgba(0, 0, 0, 0.21) !important; }

.z-depth-5 {
  box-shadow: 0 27px 24px 0 rgba(0, 0, 0, 0.2), 0 40px 77px 0 rgba(0, 0, 0, 0.22) !important; }

.disabled,
:disabled {
  pointer-events: none !important; }

a {
  cursor: pointer;
  text-decoration: none;
  color: #0352CC;
  transition: all 0.2s ease-in-out; }
  a:hover {
    text-decoration: none;
    color: #014c8c;
    transition: all 0.2s ease-in-out; }
  a.disabled:hover, a:disabled:hover {
    color: #0352CC; }

a:not([href]):not([tabindex]), a:not([href]):not([tabindex]):focus, a:not([href]):not([tabindex]):hover {
  color: inherit;
  text-decoration: none; }

.card.promoting-card .fa {
  transition: .4s; }
  .card.promoting-card .fa[class*="fa-"]:hover {
    transition: .4s;
    cursor: pointer; }

.card.weather-card .collapse-content a.collapsed:after {
  content: 'Expand'; }

.card.weather-card .collapse-content a:not(.collapsed):after {
  content: 'Collapse'; }

.card.weather-card .degree:after {
  content: "°C";
  position: absolute;
  font-size: 3rem;
  margin-top: .9rem;
  font-weight: 400; }

.card.gradient-card {
  transition: all .5s ease-in-out; }
  .card.gradient-card .first-content .card-title {
    font-weight: 500; }
  .card.gradient-card .second-content {
    display: none; }
  .card.gradient-card .third-content {
    display: none; }
  .card.gradient-card .card-body {
    transition: all .5s ease-in-out;
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    height: 0;
    padding-bottom: 0;
    padding-top: 0; }
  .card.gradient-card .card-image {
    border-radius: .25rem; }
    .card.gradient-card .card-image .mask {
      border-radius: .25rem; }
  .card.gradient-card:focus-within {
    margin-top: 3rem;
    transition: all .5s ease-in-out; }
    .card.gradient-card:focus-within .card-image {
      transition: all .5s ease-in-out;
      width: 7rem;
      height: 7rem;
      margin-top: -2rem;
      margin-left: 1rem;
      border-radius: .25rem;
      margin-bottom: 2rem; }
      .card.gradient-card:focus-within .card-image .mask {
        border-radius: .25rem; }
    .card.gradient-card:focus-within .card-body {
      transition: all .7s ease-in-out;
      visibility: visible;
      opacity: 1;
      overflow: visible;
      padding-bottom: 1.25rem;
      padding-top: 1.25rem;
      height: auto;
      border-radius: .25rem; }
      .card.gradient-card:focus-within .card-body .progress {
        height: .4rem; }
        .card.gradient-card:focus-within .card-body .progress .progress-bar {
          height: .4rem; }
    .card.gradient-card:focus-within .first-content {
      display: none; }
    .card.gradient-card:focus-within .second-content {
      display: block; }
    .card.gradient-card:focus-within .third-content {
      display: block;
      margin-top: -6rem; }
  @media (max-device-width: 1025px) {
    .card.gradient-card:hover {
      margin-top: 3rem;
      transition: all .5s ease-in-out; }
      .card.gradient-card:hover .card-image {
        transition: all .5s ease-in-out;
        width: 7rem;
        height: 7rem;
        margin-top: -2rem;
        margin-left: 1rem;
        border-radius: .25rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); }
        .card.gradient-card:hover .card-image .mask {
          border-radius: .25rem; }
      .card.gradient-card:hover .card-body {
        transition: all .7s ease-in-out;
        visibility: visible;
        opacity: 1;
        overflow: visible;
        padding-bottom: 1.25rem;
        padding-top: 1.25rem;
        height: auto;
        border-radius: .25rem; }
        .card.gradient-card:hover .card-body .progress {
          height: .4rem; }
          .card.gradient-card:hover .card-body .progress .progress-bar {
            height: .4rem; }
      .card.gradient-card:hover .first-content {
        display: none; }
      .card.gradient-card:hover .second-content {
        display: block; }
      .card.gradient-card:hover .third-content {
        display: block;
        margin-top: -6rem; } }

.card.booking-card .rating {
  font-size: .7rem; }

.card.chart-card .classic-tabs .nav li a.active {
  border-bottom: 2px solid;
  transition: width .5s ease, background-color .5s ease; }

.card.chart-card .classic-tabs .nav.tabs-white li a {
  color: #757575;
  font-weight: 500; }
  .card.chart-card .classic-tabs .nav.tabs-white li a.active {
    color: #673ab7; }

.card.chart-card .btn-deep-purple-accent {
  background-color: #b388ff;
  margin-top: -65px; }
  .card.chart-card .btn-deep-purple-accent i {
    color: #000 !important; }

.card.chart-card .btn-teal-accent {
  background-color: #1de9b6;
  margin-top: -65px; }
  .card.chart-card .btn-teal-accent i {
    color: #000 !important; }

.card.colorful-card .indigo-accent-text {
  color: #304ffe; }

.card.colorful-card .btn-indigo-accent {
  background-color: #304ffe; }

.card.colorful-card .yellow-darken-text {
  color: #fdd835; }

.card.colorful-card .testimonial-card .avatar {
  width: 55px;
  margin-top: -30px;
  border: 3px solid #fff; }
  .card.colorful-card .testimonial-card .avatar img {
    width: 50px;
    height: 50px; }

.card.colorful-card .brown-darken-text {
  color: #3e2723; }

.card.colorful-card .btn-red-lighten {
  background-color: #ffcdd2; }

.card-wrapper.card-action {
  min-height: 640px; }
  @media (max-width: 450px) {
    .card-wrapper.card-action {
      min-height: 790px; } }

.card-form .md-form input[type=text]:focus:not([readonly]),
.card-form .md-form input[type=email]:focus:not([readonly]),
.card-form .md-form input[type=password]:focus:not([readonly]) {
  box-shadow: 0 1px 0 0 #fff;
  border-bottom: 1px solid #fff; }

.card-form .card-form-2 {
  border-top-left-radius: 21px;
  border-top-right-radius: 21px;
  margin-top: -35px; }
  .card-form .card-form-2 .form-check-input[type=checkbox].filled-in:checked + label:after,
  .card-form .card-form-2 label.btn input[type=checkbox].filled-in:checked + label:after {
    background-color: #e53935;
    border: 2px solid #e53935; }
  .card-form .card-form-2 .btn-outline-red-accent {
    border: 2px solid #e53935;
    background-color: transparent;
    color: #e53935; }
  .card-form .card-form-2 .pink-accent-text {
    color: #c51162; }

.z-depth-1-bottom {
  box-shadow: 0 5px 5px -2px rgba(0, 0, 0, 0.16); }
