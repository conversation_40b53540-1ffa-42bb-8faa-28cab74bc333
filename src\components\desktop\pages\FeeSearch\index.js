import React, { Component } from "react";
import {
  MDBContainer,
  MDBRow,
  MDBCol,
  MDBListGroup,
  MDBListGroupItem,
  MDBSpinner
} from "mdbreact";
import TagName from "~/components/common/atoms/TagName";
import styles from "./style.module.scss";
import cx from "classnames";
import Button from "~/components/common/atoms/Button";
class PaymentInpatient extends Component {
  render() {
    const {
      changeFeeCode,
      searchFeeCode,
      toggleModalViewImage,
      loading,
      hosNumber
    } = this.props;

    if (loading) {
      return (
        <div className={styles.loading_spinner}>
          <div className={styles.loading}>
            <MDBSpinner big crazy tag="div" />
          </div>
        </div>
      );
    }
    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow>
            <MDBCol md="12">
              <div className={styles.wapper_page_inner}>
                <TagName
                  element="h1"
                  className={[
                    "title_component",
                    "title_line",
                    "title_header_mobile"
                  ]}
                >
                  <span><PERSON>h toán viện phí</span>
                </TagName>
                <div className={cx(styles.search_fee_wrapper, styles.bg_inner)}>
                  <MDBContainer>
                    <MDBRow>
                      <MDBCol size={12}>
                        <div className={styles.tabcontent}>
                          <div>
                            <div className={styles.form_group}>
                              <input
                                type="text"
                                className="form-control"
                                placeholder="Nhập mã thanh toán"
                                onChange={e => changeFeeCode(e.target.value)}
                              />
                            </div>
                            <div className={styles.form_group}>
                              <Button
                                create="create"
                                block="block"
                                className={styles.button}
                                onClick={searchFeeCode}
                                disabled={hosNumber === ""}
                              >
                                Xác nhận
                              </Button>
                            </div>
                            <MDBListGroup className={styles.list_group}>
                              <MDBListGroupItem onClick={toggleModalViewImage}>
                                <i className="fas fa-caret-right" />
                                Xem cách tìm mã số thanh toán
                              </MDBListGroupItem>
                            </MDBListGroup>
                          </div>
                        </div>
                      </MDBCol>
                    </MDBRow>
                  </MDBContainer>
                </div>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default PaymentInpatient;
