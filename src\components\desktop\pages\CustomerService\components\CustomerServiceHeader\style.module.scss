@import "src/assets/scss/custom-variables.scss";
.wapper_Header {
  padding: 0;
  background-image: linear-gradient(45deg, #6a78d1, #00a4bd);

  .rowMenu{
    background-color: whitesmoke;
    .colMenu{
      .listMenu{
        list-style: none;
        padding: 0;margin: 0;
        display: flex;
        li{
          background-color: #33b5e5;
          padding:0.3rem 1rem;
          margin: 0.5rem;
          border-radius: 5px;
            transition: all 0.3s;
          &:hover{
              transform: translateY(-0.1rem);
          }
          a{
            width: 100%;
            color: white;
          }
        }

      }
    }
  }
}

.account_info {
  background-color: white;
  width: auto;
  padding: 2px 15px;
  border-radius: 5px;
}
@media #{$medium-and-down} {
  .account_info {
    text-align: center;
    width: fit-content;
  }
}

.logoBrand {
  width: 150px;
  background: white;
  border-radius: 10px;
  margin: 15px 0;
}
