@import "src/assets/scss/custom-variables.scss";
%main_content {
  // margin-top: 60px;
  height: calc(100vh - 60px);
  overflow: auto;
  position: relative;
  &.mt_0 {
    margin-top: 0;
  }
  .wapper_page_mobile {
    padding-top: 15px;
    padding-bottom: 60px;
  }
}
%line {
  height: 3px;
  background: $line_color;
  width: 100%;
}
%footer_handle {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0;
  justify-content: space-between;
  align-items: center;
  border-top: 3px solid #dfe3eb;
  z-index: 20;
  padding: 15px;
  background: #fff;
}
%next_prev {
  text-align: right;
  padding: 15px 0;
  display: flex;
  justify-content: space-between;
  border-top: 3px solid #dfe3eb;
}
%svg_loader {
  @media (min-width: $small-screen-up) {
    width: 50%;
  }
}
