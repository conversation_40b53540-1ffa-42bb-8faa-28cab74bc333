import { find, get } from "lodash";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const UsageRulesPageMobile = Loadable({
  loader: () => import(/* mobile */ "~/components/desktop/pages/UsageRules"),
  loading: LoadableLoading
});
const UsageRulesPageDesktop = Loadable({
  loader: () => import(/* desktop */ "~/components/desktop/pages/UsageRules"),
  loading: LoadableLoading
});

class DetectUsageRules extends Component {
  render() {
    const { device } = this.props;
    return (
      <React.Fragment>
        {device === "mobile" ? (
          <UsageRulesPageMobile />
        ) : (
          <UsageRulesPageDesktop />
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    }
  } = state;
  return {
    device: type
  };
};

const UsageRulesHelmet = withTitle({
  component: DetectUsageRules,
  title: `${hospitalName.value} | Quy định sử dụng`
});

export default connect(mapStateToProps)(UsageRulesHelmet);
