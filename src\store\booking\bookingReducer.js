import * as types from "~/store/booking/bookingType";
import { openToast } from "~/components/common/molecules/ToastNotification";

const initialState = {
  loading: false,
  duplicateBooking: false,
  medicalBills: [],
  medicalBillDetail: false,
  canceledMedicalBill: false,
  isResendSMS: false,
  submitBooking: {
    loading: false,
    data: {},
    isRedirectToPaymentSupportPage: false,
    isRedirectToPaymentOfflinePage: false,
    error: ""
  }
};

export default function locale(state = initialState, action = {}) {
  switch (action.type) {
    case types.GET_MEDICAL_BILLS_SUCCESS:
      return {
        ...state,
        medicalBills: action.data
      };
    case types.GET_MEDICAL_BILLS_FAILURE:
      return {
        ...state,
        medicalBills: []
      };

    case types.GET_MEDICAL_BILL_DETAIL:
      return {
        ...state,
        loading: true
      };

    case types.GET_MEDICAL_BILL_DETAIL_SUCCESS:
      return {
        ...state,
        medicalBillDetail: action.data,
        loading: false
      };
    case types.GET_MEDICAL_BILL_DETAIL_FAILURE:
      return {
        ...state,
        medicalBillDetail: false,
        loading: false
      };

    case types.CANCEL_MEDICAL_BILL_SUCCESS:
      openToast("Bạn đã hủy phiếu khám thành công!");
      return {
        ...state,
        canceledMedicalBill: action.data
      };
    case types.CANCEL_MEDICAL_BILL_FAILURE:
      return {
        ...state,
        canceledMedicalBill: false
      };
    case types.CLEAR_CANCELED_BOOKING_MESSAGE:
      return {
        ...state,
        canceledMedicalBill: false
      };

    case types.RESEND_SMS_SUCCESS:
      return {
        ...state,
        isResendSMS: action.data.status
      };
    case types.RESEND_SMS_FAILURE:
      return {
        ...state,
        isResendSMS: false
      };

    case types.LOADING_SUBMIT_BOOKING:
      return {
        ...state,
        submitBooking: { ...state.submitBooking, loading: true }
      };
    case types.SUBMIT_BOOKING_SUCCESS:
      return {
        ...state,
        submitBooking: {
          loading: false,
          data: action.data,
          error: "",
          isRedirectToPaymentSupportPage: action.data.payment.method_id === 6,
          isRedirectToPaymentOfflinePage: action.data.payment.method_id === 5
        }
      };
    case types.SUBMIT_BOOKING_FAIL:
      return {
        ...state,
        submitBooking: {
          ...state.submitBooking,
          loading: false,
          error: action.error
        }
      };
    case types.RESET_ERROR_SUBMIT_BOOKING:
      return {
        ...state,
        submitBooking: {
          ...state.submitBooking,
          error: ""
        }
      };
    case types.RESET_IS_REDIRECT_TO_PAYMENT_SUPPORT_AND_PAYMENT_OFFLINE_PAGE:
      return {
        ...state,
        submitBooking: {
          ...state.submitBooking,
          isRedirectToPaymentSupportPage: false,
          isRedirectToPaymentOfflinePage: false
        }
      };
    case types.CHECK_DUPLICATE_BOOKING:
      return {
        ...state,
        duplicateBooking: true
      };
    case types.RESET_DUPLICATE_BOOKING:
      return {
        ...state,
        duplicateBooking: false
      };
    default:
      return state;
  }
}
