import { find, get } from "lodash";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { Redirect } from "react-router-dom";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import { getNotificationOnPage } from "~/store/notifications/notificationsActions";
import { changeUrlRedirectAfterCreatePatient } from "~/store/patientForm/patientFormAction";
import { checkRepayment } from "~/store/totalData/actions";
const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const UserPageDesktop = Loadable({
  loader: () => import("~/components/desktop/pages/User"),
  loading: LoadableLoading
});

class DetectUser extends Component {
  methods = {
    handleCreateNewPatient: () => {
      this.props.history.push("/tao-moi-ho-so");
      this.props.changeUrlRedirectAfterCreatePatient("/user");
    }
  };

  componentDidMount() {
    this.props.getNotificationOnPage({
      category: 1
    });
    this.props.checkRepayment(false);
  }

  render() {
    const { infoUser } = this.props;
    if (infoUser === null) {
      return <Redirect to="/" />;
    }
    return (
      <React.Fragment>
        <UserPageDesktop {...this.props} {...this.methods} />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { info: infoUser }
  } = state;
  return {
    device: type,
    infoUser
  };
};

const DetectUserHelmet = withTitle({
  component: DetectUser,
  title: `${hospitalName.value} | Thông tin tài khoản`
});

export default connect(mapStateToProps, {
  changeUrlRedirectAfterCreatePatient,
  getNotificationOnPage,
  checkRepayment
})(DetectUserHelmet);
