import {
  MDBBtn,
  MDBIcon,
  MDBInputGroup,
  MDBModal,
  MDBModalBody,
  MDBModalHeader
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { openToast } from "~/components/common/molecules/ToastNotification";
import {
  sendBookingViaMail,
  sendBookingViaSms
} from "~/store/customerService/customerServiceActions";
import { validatePhoneNumber } from "~/utils/func";
import { currentEnv } from "~/configs";
import styles from "./style.module.scss";

class ModalSendBooking extends Component {
  state = {
    inputEmail: "",
    inputPhone: "",
    emailValidate: false
  };

  componentWillUpdate(nextProps) {
    if (nextProps.searchedPhone !== this.props.searchedPhone) {
      this.setState({ inputPhone: nextProps.searchedPhone });
    }
  }

  inputPhoneChangeHandler = event => {
    this.setState({ inputPhone: event.target.value });
  };

  copyTexthandler = url => {
    openToast("Đường dẫn đã được sao chép.");
    console.log("navigator.clipboard");
    var textArea = document.createElement("textarea");
    textArea.value = url;
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand("copy");
    } catch (err) { }

    document.body.removeChild(textArea);
  };

  sendSms = () => {
    const { itemCoupon } = this.props;
    const { inputPhone } = this.state;

    if (validatePhoneNumber(this.state.inputPhone)) {
      this.props.sendBookingViaSms(itemCoupon?.bookingInfo.smsCode, inputPhone);
      this.props.toggle();
    } else {
      openToast("Số điện thoại không hợp lệ vui lòng kiểm tra lại", "error");
    }
  };

  handlerCoppyBooking = () => {
    const { itemCoupon } = this.props;

    if (itemCoupon) {
      if (itemCoupon.bookingInfo.bookingOrder) {
        return `${currentEnv.MEDPRO_WEB}/transactions/${itemCoupon.bookingInfo.transactionId}`;
      } else {
        return `${currentEnv.MEDPRO_WEB}/booking/${itemCoupon.bookingInfo.smsCode}`;
      }
    } else {
      return "Phiếu không tồn tại";
    }
  };

  render() {
    return (
      <MDBModal
        isOpen={this.props.show}
        toggle={this.props.toggle}
        size="lg"
        className={styles.modalShareBill}
      >
        <MDBModalHeader tag="p">Gửi phiếu khám</MDBModalHeader>

        <MDBModalBody className={styles.modal_container}>
          <div className={styles.method_wrapper}>
            <label className={styles.method_label}>
              <MDBIcon icon="link" /> Đường dẫn phiếu khám:
            </label>
            <div className={styles.method_content}>
              <MDBInputGroup
                disabled
                size="sm"
                value={this.handlerCoppyBooking()}
                containerClassName=""
                append={
                  <MDBBtn
                    style={{ width: "120px" }}
                    color="primary"
                    className="m-0 px-3 py-2 z-depth-0"
                    onClick={() => {
                      this.copyTexthandler(this.handlerCoppyBooking());
                      this.props.toggle();
                    }}
                  >
                    <MDBIcon icon="copy" /> Sao Chép
                  </MDBBtn>
                }
              />
            </div>
          </div>

          <div className={styles.method_wrapper}>
            <label className={styles.method_label}>
              <MDBIcon icon="mobile-alt" /> Gửi phiếu qua SMS:
            </label>

            <div className={styles.method_content}>
              <MDBInputGroup
                size="sm"
                value={this.state.inputPhone}
                containerClassName=""
                hint="Nhập số điện thoại"
                onChange={event => this.inputPhoneChangeHandler(event)}
                onKeyDown={event => {
                  if (event.keyCode === 13) {
                    event.preventDefault();
                  }
                }}
                append={
                  <MDBBtn
                    style={{ width: "120px" }}
                    color="primary"
                    className="m-0 px-3 py-2 z-depth-0"
                    onClick={() => {
                      this.sendSms();
                    }}
                  >
                    <MDBIcon far icon="share-square" /> Gửi
                  </MDBBtn>
                }
              />
            </div>
          </div>
        </MDBModalBody>
      </MDBModal>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { searchedPhone }
  } = state;
  return { searchedPhone };
};

const mapDispatchToProps = dispatch => {
  return {
    sendBookingViaMail: (id, email) => {
      dispatch(sendBookingViaMail(id, email));
    },
    sendBookingViaSms: (BookingCode, mobile) => {
      dispatch(sendBookingViaSms(BookingCode, mobile));
    }
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ModalSendBooking);
