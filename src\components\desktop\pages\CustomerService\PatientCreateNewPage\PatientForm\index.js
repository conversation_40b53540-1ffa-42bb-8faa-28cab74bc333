/* eslint-disable max-len */
/* eslint-disable spaced-comment */
/* eslint-disable standard/no-callback-literal */
/* eslint-disable react/jsx-handler-names */
import React, { Component, Fragment } from "react";
import { createForm, createForm<PERSON>ield } from "rc-form";
import { find } from "lodash";
import moment from "moment";
import { connect } from "react-redux";
import { MDBAlert, MDBSpinner, MDBAnimation } from "mdbreact";
import PKHBtn from "~/components/common/atoms/Button";
import isEmail from "validator/lib/isEmail";
import isEmpty from "validator/lib/isEmpty";
import styles from "./style.module.scss";
import cx from "classnames";
import Select from "~/components/common/atoms/Select";
import {
  onChangeCareer,
  onChangeCountry,
  onChangeNation,
  onChangeCity,
  onChangeDistrict,
  onChangeWard,
  onChangeRelationShip,
  requestAllCity
} from "~/store/resource/resourceAction";
import {
  saveField,
  onCreatePatientInfo,
  onUpdatePatientInfo,
  resetPatientForm,
  resetDefaultCountry
} from "~/store/patientForm/patientFormAction";
import { getInfoFollowHospital } from "~/utils/flowRouting";
import Modal from "~/components/common/molecules/Modal";
import { hash } from "~/utils/func";
import TagName from "~/components/common/atoms/TagName";
class PatientFormStepper extends Component {
  constructor(props) {
    super(props);
    this.state = {
      sexs: [
        {
          name: "Chọn giới tính",
          id: -1
        },
        {
          name: "Nam",
          id: 1
        },
        {
          name: "Nữ",
          id: 0
        }
      ],
      showModalConfirmPhone: false,
      phone: "",
      isExactPhone: "",
      errorCheckPhone: ""
    };

    this.defaultCountry = getInfoFollowHospital().defaultCountry;
  }

  validatorName = (rule, value, callback) => {
    if (value) {
      value = value.toLowerCase();
      value = value.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
      value = value.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
      value = value.replace(/ì|í|ị|ỉ|ĩ/g, "i");
      value = value.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
      value = value.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
      value = value.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
      value = value.replace(/đ/g, "d");
      const patternName = /^[a-zA-Z ]+$/;
      if (patternName.test(value)) {
        callback();
      } else {
        callback("Họ tên chỉ bao gồm chữ cái!");
      }
    } else {
      callback();
    }
  };

  validatePhone = (rule, value, callback) => {
    const {
      patientForm: {
        info: {
          mobile: { value: oldMobile }
        }
      }
    } = this.props;

    if (value) {
      if (!oldMobile || (oldMobile && value !== oldMobile)) {
        const patternMobile = /^((0)(86|96|97|98|32|33|34|35|36|37|38|39|89|90|93|70|79|77|76|78|88|91|94|83|84|85|81|82|92|52|56|58|99|59))[0-9]{7}$/;
        if (patternMobile.test(value)) {
          callback();
        } else {
          const patternPhone = /^0((((28|24)[0-9])|(290|291|299|293|297|292|296|270|294|275|277|273|272|254|251|276|271|252|263|259|261|262|257|269|256|260|255|235|236|234|233|232|239|238|237|229|228|227|226|221|225|220|222|211|210|218|212|215|213|214|207|216|209|219|203|204|205|206))[0-9]{7})$/;
          if (patternPhone.test(value)) {
            callback();
          } else callback("Vui lòng nhập đúng định dạng!");
        }
      } else callback();
    } else {
      callback("Vui lòng nhập số điện thoại!");
    }
  };

  validatorEmail = (rule, value, callback) => {
    if (typeof value !== typeof undefined && !!value) {
      const trimValue = (value + "").trim();
      if (!isEmpty(trimValue)) {
        if (!isEmail(trimValue)) {
          callback("Vui lòng nhập đúng định dạng email !");
        } else callback();
      } else callback();
    } else callback();
  };

  validatorCmnd = (rule, value, callback) => {
    if (value) {
      const patternCmnd = /^[a-zA-Z0-9]+$/;
      if (!patternCmnd.test(value)) {
        callback("Vui lòng nhập đúng định dạng CMND");
      } else {
        callback();
      }
    }
    callback();
  };

  validatorSex = (rule, value, callback) => {
    if (Number(value) === -1) {
      callback("Vui lòng chọn giới tính!");
    }
    callback();
  };

  validatorCity = (rule, value, callback) => {
    const countryCode = this.props.form.getFieldValue("country_code");
    if (countryCode !== this.defaultCountry.id) {
      callback();
    } else {
      if (Number(value) === 0) {
        callback("Vui lòng chọn tỉnh thành!");
      } else {
        callback();
      }
    }
    callback();
  };

  validatorDistrict = (rule, value, callback) => {
    const countryCode = this.props.form.getFieldValue("country_code");
    if (countryCode !== this.defaultCountry.id) {
      callback();
    } else {
      if (Number(value) === 0) {
        callback("Vui lòng chọn quận huyện!");
      } else {
        callback();
      }
    }
  };

  validatorWard = (rule, value, callback) => {
    const countryCode = this.props.form.getFieldValue("country_code");
    if (countryCode !== this.defaultCountry.id) {
      callback();
    } else {
      if (Number(value) === 0) {
        callback("Vui lòng chọn phường xã!");
      } else {
        callback();
      }
    }
  };

  validatorAddress = (rule, value, callback) => {
    const countryCode = this.props.form.getFieldValue("country_code");
    if (countryCode !== this.defaultCountry.id) {
      callback();
    } else {
      if (Number(value) === 0) {
        callback("Vui lòng nhập số nhà, đường, thôn (ấp), xóm!");
      } else {
        callback();
      }
    }
  };

  validatorBirthDay = (rule, value, callback) => {
    const { form } = this.props;
    const birthyear = Number(form.getFieldValue("birthyear"));
    const birthmonth = Number(form.getFieldValue("birthmonth"));
    if (this.checkBirthdayIsValid(Number(value), birthmonth, birthyear)) {
      const currentYear = Number(moment().format("YYYY"));
      const yearOld = currentYear - birthyear;
      if (yearOld <= 3) {
        if (Number(value) === 0) {
          if (birthmonth === 0) {
            callback("Vui lòng chọn ngày tháng.");
          } else {
            callback("Vui lòng chọn ngày");
          }
        } else {
          callback();
        }
      } else callback();
    } else {
      callback("Vui lòng chọn ngày sinh đúng.");
    }
  };

  validatorBirthMonth = (rule, value, callback) => {
    const { form } = this.props;
    const birthyear = Number(form.getFieldValue("birthyear"));
    const birthday = Number(form.getFieldValue("birthday"));
    if (this.checkBirthdayIsValid(birthday, Number(value), birthyear)) {
      const currentYear = Number(moment().format("YYYY"));
      const yearOld = currentYear - birthyear;
      if (yearOld <= 3) {
        if (Number(value) === 0 && birthday !== 0) {
          callback("Vui lòng chọn tháng");
        } else {
          callback();
        }
      } else callback();
    } else {
      callback("Vui lòng chọn ngày sinh đúng.");
    }
  };

  validatorBirthYear = (rule, value, callback) => {
    if (Number(value) === 0) {
      callback("Vui lòng chọn năm sinh!");
    }
    callback();
  };

  validatorRelativeName = (rule, value, callback) => {
    if (this.checkOverAge(16)) {
      if (value) {
        value = value.toLowerCase();
        value = value.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
        value = value.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
        value = value.replace(/ì|í|ị|ỉ|ĩ/g, "i");
        value = value.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
        value = value.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
        value = value.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
        value = value.replace(/đ/g, "d");
        const patternName = /^[a-zA-Z ]+$/;
        if (patternName.test(value)) {
          callback();
        } else {
          callback("Họ tên chỉ bao gồm chữ cái!");
        }
      } else {
        callback("Vui lòng nhập họ tên người thân");
      }
    } else {
      callback();
    }
  };

  validatorRelativeType = (rule, value, callback) => {
    if (this.checkOverAge(16)) {
      if (!!value && Number(value) !== 0) {
        callback();
      } else {
        callback("Vui lòng chọn quan hệ bệnh nhân");
      }
    } else {
      callback();
    }
  };

  validatorRelativePhone = (rule, value, callback) => {
    const {
      patientForm: {
        info: {
          mobile: { value: oldMobile }
        }
      }
    } = this.props;

    if (this.checkOverAge(16)) {
      if (value) {
        if (!oldMobile || (oldMobile && value !== oldMobile)) {
          const patternMobile = /^((0)(86|96|97|98|32|33|34|35|36|37|38|39|89|90|93|70|79|77|76|78|88|91|94|83|84|85|81|82|92|52|56|58|99|59))[0-9]{7}$/;
          if (patternMobile.test(value)) {
            callback();
          } else {
            const patternPhone = /^0((((28|24)[0-9])|(290|291|299|293|297|292|296|270|294|275|277|273|272|254|251|276|271|252|263|259|261|262|257|269|256|260|255|235|236|234|233|232|239|238|237|229|228|227|226|221|225|220|222|211|210|218|212|215|213|214|207|216|209|219|203|204|205|206))[0-9]{7})$/;
            if (patternPhone.test(value)) {
              callback();
            } else callback("Vui lòng nhập đúng định dạng!");
          }
        } else callback();
      } else {
        callback("Vui lòng nhập số điện thoại!");
      }
    } else {
      callback();
    }
  };

  validatorRelativeEmail = (rule, value, callback) => {
    if (typeof value !== typeof undefined && !!value) {
      const trimValue = (value + "").trim();
      if (!isEmpty(trimValue)) {
        if (!isEmail(trimValue)) {
          callback("Vui lòng nhập đúng định dạng email !");
        } else callback();
      } else callback();
    } else callback();
  };

  onChangeDay = value => {
    this.props.form.setFieldsValue({
      birthday: value
    });
    this.props.form.validateFields(["birthmonth"], {
      force: true
    });
  };

  onChangeMonth = value => {
    this.props.form.setFieldsValue({
      birthmonth: value
    });
    this.props.form.validateFields(["birthday"], {
      force: true
    });
  };

  onChangeYear = value => {
    this.props.form.setFieldsValue({
      birthyear: value
    });
    this.props.form.validateFields(["birthday", "birthmonth", "birthyear"], {
      force: true
    });
  };

  onChangeCareer = id => {
    this.props.form.setFieldsValue({
      profession_id: id
    });
  };

  onChangeCountry = id => {
    this.props.form.setFieldsValue({
      country_code: id,
      city_id: 0,
      district_id: 0,
      ward_id: 0,
      address: ""
    });
    const { allCountries } = this.props;
    const findSelectedCountry = find(allCountries, { id: id });
    if (typeof findSelectedCountry !== typeof undefined) {
      this.props.onChangeCountry({ ...findSelectedCountry });
    }
    if (id !== "VIE") {
      this.onChangeNation("medpro_82");
      this.onChangeCity(99);
    } else {
      this.onChangeNation("medpro_1");
    }
  };

  onChangeNation = id => {
    this.props.form.setFieldsValue({
      dantoc_id: id
    });
  };

  onChangeCity = id => {
    const { allCities } = this.props;
    this.props.form.setFieldsValue({
      city_id: id,
      district_id: 0,
      ward_id: 0
    });
    const findSelectedCity = find(allCities, { id: id });
    if (typeof findSelectedCity !== typeof undefined) {
      this.props.onChangeCity({ ...findSelectedCity });
    }
  };

  onChangeDistrict = id => {
    const { allDistricts } = this.props;
    this.props.form.setFieldsValue({
      district_id: id,
      ward_id: 0
    });
    const findSelectedDistrict = find(allDistricts, { id: id });
    if (typeof findSelectedDistrict !== typeof undefined) {
      this.props.onChangeDistrict({ ...findSelectedDistrict });
    }
  };

  onChangeWard = id => {
    this.props.form.setFieldsValue({
      ward_id: id
    });
  };

  onChangeSex = id => {
    this.props.form.setFieldsValue({
      sex: id
    });
  };

  onChangeRelativeType = id => {
    this.props.form.setFieldsValue({
      relative_type_id: id
    });
  };

  onCreatePatientInfo = event => {
    event.preventDefault();
    this.props.form.validateFields((error, value) => {
      if (!error) {
        this.props.onCreatePatientInfo();
      }
    });
  };

  onUpdatePatientInfo = event => {
    event.preventDefault();
    const {
      patientForm: {
        info: {
          msbn: { value: msbn },
          mobile: { value: oldMobile }
        }
      }
    } = this.props;

    this.props.form.validateFields((error, value) => {
      if (!error) {
        if (msbn) {
          const { mobile } = value;
          if (mobile === oldMobile) {
            this.props.onUpdatePatientInfo();
          } else {
            this.handleToggleModalConfirmPhone();
          }
        } else {
          this.props.onUpdatePatientInfo();
        }
      }
    });
  };

  componentDidMount() {
    this.props.resetPatientForm();
  }

  handleResetFormFields = () => {
    const { defaultCountry } = getInfoFollowHospital();
    this.props.resetPatientForm(); // làm trống form
    this.props.resetDefaultCountry(defaultCountry.id); // chọn quốc gia mặc định trong form là Việt nam
    this.props.onChangeCountry(defaultCountry); // chọn Selected Country là Việt Nam
    this.props.requestAllCity(); // request all cities của Việt Nam
  };

  handleToggleModalConfirmPhone = () => {
    this.setState({ showModalConfirmPhone: !this.state.showModalConfirmPhone });
  };

  // Modal confirm Phone
  handleChangePhone = e => {
    const patternPhone = /^[0-9]+$/;
    if (!patternPhone.test(e.target.value)) {
      this.setState({
        errorCheckPhone: "Vui lòng nhập đúng định dạng",
        isExactPhone: false
      });
    } else {
      this.setState({
        isExactPhone: true,
        errorCheckPhone: ""
      });
    }
    this.setState({
      phone: e.target.value
    });
  };

  handleCheckPhone = () => {
    const { phone } = this.state;
    const {
      patientForm: {
        info: {
          mobile: { value: oldMobile }
        }
      }
    } = this.props;
    if (hash(phone) === oldMobile) {
      this.props.onUpdatePatientInfo();
    } else {
      this.setState({
        isExactPhone: false,
        errorCheckPhone:
          "Số điện thoại không chính xác với số điện thoại ban đầu"
      });
    }
  };

  renderBodyModalConfirmMobile = () => {
    const { phone, isExactPhone, errorCheckPhone } = this.state;
    const {
      patientForm: {
        info: {
          mobile: { value: oldMobile }
        }
      }
    } = this.props;
    let message = `Bệnh nhân vui lòng cập nhật số điện thoại tại quầy chăm sóc khách hàng của bệnh viện Đại học Y Dược`;
    let isNoPhone = true;
    if (oldMobile !== null) {
      message = `Vui lòng nhập lại đầy đủ số điện thoại ${oldMobile}, để xác nhận hồ sơ bệnh nhân`;
      isNoPhone = false;
    }
    return (
      <Fragment>
        <p>{message}</p>
        {!isNoPhone && (
          <input
            type="text"
            placeholder="Nhập số điện thoại"
            className="form-control"
            onChange={this.handleChangePhone}
            value={phone}
          />
        )}
        {isExactPhone === false && (
          <p className={styles.red} style={{ fontSize: 11, paddingTop: 5 }}>
            {errorCheckPhone}
          </p>
        )}
      </Fragment>
    );
  };

  checkBirthdayIsValid = (birthDay, birthMonth, birthYear) => {
    if (birthDay && birthMonth && birthYear) {
      const dateTime = moment(
        `${birthDay}/${birthMonth}/${birthYear}`,
        "D/M/YYYY"
      );
      return dateTime.isValid() && dateTime.isSameOrBefore(moment());
    }
    return true;
  };

  checkOverAge = age => {
    const { form } = this.props;

    const birthyear = Number(form.getFieldValue("birthyear"));
    const birthmonth = Number(form.getFieldValue("birthmonth"));
    const birthday = Number(form.getFieldValue("birthday"));

    if (birthyear === 0) {
      return false;
    } else {
      const day = birthday === 0 ? 1 : birthday;
      const month = birthmonth === 0 ? 1 : birthmonth;
      const birthdate = `${month}/${day}/${birthyear}`;

      const validDate = moment()
        .subtract(age, "years")
        .calendar();

      return moment(validDate).isBefore(birthdate);
    }
  };

  render() {
    const {
      allCareers,
      // allCountries,
      allNation,
      allCities,
      allDistricts,
      allWards,
      allRelationShip,
      form: { getFieldProps, getFieldError, getFieldValue },
      patientForm: {
        info: {
          msbn: { value: msbn }
        },
        loading,
        disableCreate
      }
    } = this.props;

    const showRelative = this.checkOverAge(16);

    const dayList = [{ id: 0, name: "Ngày" }];
    for (let i = 1; i <= 31; i++) {
      dayList.push({ id: i, name: i });
    }
    const monthList = [{ id: 0, name: "Tháng" }];
    for (let i = 1; i <= 12; i++) {
      monthList.push({ id: i, name: i });
    }
    const yearList = [{ id: 0, name: "Năm" }];
    for (let i = new Date().getFullYear(); i >= 1900; i--) {
      yearList.push({ id: i, name: i });
    }
    const { sexs, showModalConfirmPhone } = this.state;

    const fieldSurname = getFieldError("surname");
    const surnameMessage = fieldSurname ? fieldSurname.join(", ") : null;

    const fieldName = getFieldError("name");
    const nameMessage = fieldName ? fieldName.join(", ") : null;

    const fieldMobile = getFieldError("mobile");
    const mobileMessage = fieldMobile ? fieldMobile.join(", ") : null;

    const fieldCmnd = getFieldError("cmnd");
    const cmndMessage = fieldCmnd ? fieldCmnd.join(",") : null;

    const fieldSex = getFieldError("sex");
    const sexMessage = fieldSex ? fieldSex.join(", ") : null;

    const fieldCityId = getFieldError("city_id");
    const cityIdMessage = fieldCityId ? fieldCityId.join(", ") : null;

    const fieldDistrictId = getFieldError("district_id");
    const districtIdMessage = fieldDistrictId
      ? fieldDistrictId.join(", ")
      : null;

    const fieldWardId = getFieldError("ward_id");
    const wardIdMessage = fieldWardId ? fieldWardId.join(", ") : null;

    const fieldAddress = getFieldError("address");
    const addressMessage = fieldAddress ? fieldAddress.join(", ") : null;

    const fieldEmail = getFieldError("email");
    const emailMessage = fieldEmail ? fieldEmail.join(", ") : null;

    const id = getFieldProps("id").value;

    const fieldBirhtday = getFieldError("birthday");
    const birthdayMessage = fieldBirhtday ? fieldBirhtday.join(", ") : null;

    const fieldBirthmonth = getFieldError("birthmonth");
    const birthmonthMessage = fieldBirthmonth
      ? fieldBirthmonth.join(", ")
      : null;

    const fieldBirthyear = getFieldError("birthyear");
    const birthyearMessage = fieldBirthyear ? fieldBirthyear.join(", ") : null;

    // thân nhân
    const fieldRelativeName = getFieldError("relative_name");
    const relativeNameMessage = fieldRelativeName
      ? fieldRelativeName.join(", ")
      : null;

    const fieldRelativeType = getFieldError("relative_type_id");
    const relativeTypeMessage = fieldRelativeType
      ? fieldRelativeType.join(", ")
      : null;

    const fieldRelativeMobile = getFieldError("relative_mobile");
    const relativeMobileMessage = fieldRelativeMobile
      ? fieldRelativeMobile.join(", ")
      : null;

    const fieldRelativeEmail = getFieldError("relative_email");
    const relativeEmailMessage = fieldRelativeEmail
      ? fieldRelativeEmail.join(", ")
      : null;

    const isHiddenObj =
      getFieldValue("country_code") !== "VIE" ? { display: "none" } : {};

    // const isDisabled = getFieldValue("country_code") !== "VIE";

    return (
      <Fragment>
        {loading ? (
          <div className="loading">
            <MDBSpinner big />
          </div>
        ) : (
          <MDBAnimation type="fadeIn">
            <form className={styles.form_medpro}>
              <div className={styles.form_stepper}>
                <TagName
                  element="h2"
                  className={["title_component", "title_headline"]}
                >
                  <span>Nhập thông tin bệnh nhân</span>
                </TagName>
                {this.props.flow !== "cskh" ? (
                  <MDBAlert color="primary">
                    Vui lòng cung cấp thông tin chính xác để được phục vụ tốt
                    nhất. Trong trường hợp cung cấp sai thông tin bệnh nhân &
                    điện thoại, việc xác nhận cuộc hẹn sẽ không hiệu lực trước
                    khi đặt khám.
                  </MDBAlert>
                ) : null}
                <div className={styles.info_required}>
                  <span
                    className={styles.alertSpan}
                    style={{ marginBottom: "15px" }}
                  >
                    (*) Thông tin bắt buộc nhập
                  </span>
                </div>
                <div className={styles.wapper_form_group}>
                  <div className={styles.form_group}>
                    <label htmlFor="label_hoten">
                      Họ và tên lót (có dấu)<sup>*</sup>
                    </label>
                    <input
                      {...getFieldProps("surname", {
                        rules: [
                          {
                            required: true,
                            message: "Vui lòng nhập Họ và tên lót (có dấu)"
                          },
                          { validator: this.validatorName }
                        ]
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Ví dụ: Nguyễn Văn"
                      disabled={msbn}
                      maxLength="30"
                    />
                    {surnameMessage ? (
                      <span className={styles.alertSpan}>{surnameMessage}</span>
                    ) : (
                      []
                    )}
                  </div>
                  <div className={styles.form_group}>
                    <label htmlFor="label_ten">
                      Tên bệnh nhân (có dấu)<sup>*</sup>
                    </label>
                    <input
                      {...getFieldProps("name", {
                        rules: [
                          {
                            required: true,
                            message: "Vui lòng nhập Tên bệnh nhân"
                          },
                          { validator: this.validatorName }
                        ]
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Ví dụ: Bảo"
                      disabled={msbn}
                      maxLength="20"
                    />
                    {nameMessage ? (
                      <span className={styles.alertSpan}>{nameMessage}</span>
                    ) : (
                      []
                    )}
                  </div>
                  <div className={styles.form_group}>
                    <label htmlFor="label_birth">
                      Ngày tháng năm sinh<sup>*</sup>
                    </label>
                    <div className={styles.form_group_select}>
                      <Select
                        {...getFieldProps("birthday", {
                          trigger: ["onChange"],
                          valuePropName: "value",
                          rules: [{ validator: this.validatorBirthDay }]
                        })}
                        data={dayList}
                        onChange={this.onChangeDay}
                        disabled={msbn}
                      />
                      <Select
                        {...getFieldProps("birthmonth", {
                          trigger: ["onChange"],
                          valuePropName: "value",
                          rules: [{ validator: this.validatorBirthMonth }]
                        })}
                        data={monthList}
                        onChange={this.onChangeMonth}
                        disabled={msbn}
                      />
                      <Select
                        {...getFieldProps("birthyear", {
                          trigger: ["onChange"],
                          valuePropName: "value",
                          rules: [{ validator: this.validatorBirthYear }]
                        })}
                        data={yearList}
                        onChange={this.onChangeYear}
                        disabled={msbn}
                      />
                    </div>
                    {birthdayMessage ||
                    birthmonthMessage ||
                    birthyearMessage ? (
                      <span className={styles.alertSpan}>
                        {birthdayMessage || ""}{" "}
                        {birthdayMessage !== birthmonthMessage
                          ? birthmonthMessage
                          : ""}{" "}
                        {birthyearMessage || ""}
                      </span>
                    ) : (
                      []
                    )}
                    <MDBAlert color="info" className={styles.warning}>
                      Đối với bệnh nhân từ 3 tuổi trở xuống, vui lòng nhập đầy
                      đủ <strong>ngày tháng năm sinh</strong>
                    </MDBAlert>
                  </div>

                  <div className={styles.form_group}>
                    <label htmlFor="label_sdt">
                      Số điện thoại<sup>*</sup>
                    </label>
                    <input
                      {...getFieldProps("mobile", {
                        rules: [
                          {
                            validator: this.validatePhone
                          }
                        ]
                      })}
                      type="text"
                      id="label_sdt"
                      className="form-control"
                      placeholder="Nhập số điện thoại"
                    />
                    {mobileMessage ? (
                      <span className={styles.alertSpan}>{mobileMessage}</span>
                    ) : (
                      []
                    )}
                  </div>

                  <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_gt">
                      Giới tính<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("sex", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorSex }]
                      })}
                      data={sexs}
                      onChange={this.onChangeSex}
                      disabled={msbn}
                    />
                    {sexMessage ? (
                      <span className={styles.alertSpan}>{sexMessage}</span>
                    ) : (
                      []
                    )}
                  </div>

                  <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_job">Nghề nghiệp</label>
                    <Select
                      {...getFieldProps("profession_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: []
                      })}
                      data={allCareers}
                      onChange={this.onChangeCareer}
                      disabled={msbn}
                    />
                  </div>

                  <div className={styles.form_group}>
                    <label htmlFor="label_cmnd">
                      Số CMND/Passport
                      <sup />
                    </label>
                    <input
                      {...getFieldProps("cmnd", {
                        rules: [{ validator: this.validatorCmnd }]
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Nhập số CMND"
                      disabled={msbn}
                    />
                    {cmndMessage ? (
                      <span className={styles.alertSpan}>{cmndMessage}</span>
                    ) : (
                      []
                    )}
                  </div>

                  <div className={styles.form_group}>
                    <label htmlFor="label_email">
                      Địa chỉ Email
                      <sup />
                    </label>
                    <input
                      {...getFieldProps("email", {
                        rules: [{ validator: this.validatorEmail }]
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Nhập địa chỉ email để nhận phiếu khám"
                    />
                    {emailMessage ? (
                      <span className={styles.alertSpan}>{emailMessage}</span>
                    ) : (
                      []
                    )}
                  </div>

                  {/* <div className={cx(styles.form_group, styles.form_select)}>
                    <label htmlFor="label_qg">
                      Chọn quốc gia<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("country_code", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [
                          {
                            required: true,
                            message: "Vui lòng chọn Quốc gia"
                          }
                        ]
                      })}
                      data={allCountries}
                      onChange={this.onChangeCountry}
                      disabled={msbn}
                    />
                  </div> */}
                  <div
                    // style={isHiddenObj}
                    className={cx(styles.form_group, styles.form_select)}
                  >
                    <label htmlFor="label_dt">Dân tộc</label>
                    <Select
                      {...getFieldProps("dantoc_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [
                          {
                            required: true,
                            message: "Vui lòng chọn Dân tộc"
                          }
                        ]
                      })}
                      data={allNation}
                      onChange={this.onChangeNation}
                      // disabled={msbn || isDisabled}
                    />
                  </div>

                  <div
                    style={isHiddenObj}
                    className={cx(styles.form_group, styles.form_select)}
                  >
                    <label htmlFor="label_tp">
                      Tỉnh / Thành<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("city_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorCity }]
                      })}
                      data={allCities}
                      onChange={this.onChangeCity}
                      disabled={msbn}
                    />
                    {cityIdMessage ? (
                      <span className={styles.alertSpan}>{cityIdMessage}</span>
                    ) : (
                      []
                    )}
                  </div>
                  <div
                    style={isHiddenObj}
                    className={cx(styles.form_group, styles.form_select)}
                  >
                    <label htmlFor="label_district">
                      Quận / Huyện<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("district_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorDistrict }]
                      })}
                      data={allDistricts}
                      onChange={this.onChangeDistrict}
                      disabled={msbn}
                    />
                    {districtIdMessage ? (
                      <span className={styles.alertSpan}>
                        {districtIdMessage}
                      </span>
                    ) : (
                      []
                    )}
                  </div>
                  <div
                    style={isHiddenObj}
                    className={cx(styles.form_group, styles.form_select)}
                  >
                    <label htmlFor="label_ward">
                      Phường / Xã<sup>*</sup>
                    </label>
                    <Select
                      {...getFieldProps("ward_id", {
                        trigger: ["onChange"],
                        valuePropName: "value",
                        rules: [{ validator: this.validatorWard }]
                      })}
                      data={allWards}
                      onChange={this.onChangeWard}
                      disabled={msbn}
                    />
                    {wardIdMessage ? (
                      <span className={styles.alertSpan}>{wardIdMessage}</span>
                    ) : (
                      []
                    )}
                  </div>

                  <div style={isHiddenObj} className={styles.form_group}>
                    <label htmlFor="label_address">
                      Địa chỉ<sup>*</sup>
                    </label>
                    <input
                      {...getFieldProps("address", {
                        rules: [{ validator: this.validatorAddress }]
                      })}
                      type="text"
                      className="form-control"
                      placeholder="Nhập địa chỉ"
                      disabled={msbn}
                      maxLength="100"
                    />
                    {addressMessage ? (
                      <span className={styles.alertSpan}>{addressMessage}</span>
                    ) : (
                      []
                    )}
                  </div>

                  {/* -------------------- Phần thân nhân ---------------------*/}
                  {showRelative && (
                    <Fragment>
                      <TagName
                        element="h2"
                        className={["title_component", "title_headline"]}
                      >
                        <span>Nhập thông tin thân nhân</span>
                      </TagName>

                      <div style={isHiddenObj} className={styles.form_group}>
                        <label htmlFor="label_relative_name">
                          Họ và tên người thân<sup>*</sup>
                        </label>
                        <input
                          {...getFieldProps("relative_name", {
                            rules: [{ validator: this.validatorRelativeName }]
                          })}
                          type="text"
                          className="form-control"
                          placeholder="Nhập họ tên người thân"
                          disabled={msbn}
                          maxLength="100"
                        />
                        {relativeNameMessage ? (
                          <span className={styles.alertSpan}>
                            {relativeNameMessage}
                          </span>
                        ) : (
                          []
                        )}
                      </div>
                      <div style={isHiddenObj} className={styles.form_group}>
                        <label htmlFor="label_relative_type_id">
                          Quan hệ với bệnh nhân<sup>*</sup>
                        </label>
                        <Select
                          {...getFieldProps("relative_type_id", {
                            trigger: ["onChange"],
                            valuePropName: "value",
                            rules: [{ validator: this.validatorRelativeType }]
                          })}
                          data={allRelationShip}
                          onChange={this.onChangeRelativeType}
                        />
                        {relativeTypeMessage ? (
                          <span className={styles.alertSpan}>
                            {relativeTypeMessage}
                          </span>
                        ) : (
                          []
                        )}
                      </div>
                      <div style={isHiddenObj} className={styles.form_group}>
                        <label htmlFor="label_relative_mobile">
                          Số điện thoại<sup>*</sup>
                        </label>
                        <input
                          {...getFieldProps("relative_mobile", {
                            required: true,
                            rules: [
                              {
                                validator: this.validatorRelativePhone
                              }
                            ]
                          })}
                          type="text"
                          className="form-control"
                          placeholder="Nhập số điện thoại"
                        />
                        {relativeMobileMessage ? (
                          <span className={styles.alertSpan}>
                            {relativeMobileMessage}
                          </span>
                        ) : (
                          []
                        )}
                      </div>
                      <div style={isHiddenObj} className={styles.form_group}>
                        <label htmlFor="label_address">Email</label>
                        <input
                          {...getFieldProps("relative_email", {
                            rules: [{ validator: this.validatorRelativeEmail }]
                          })}
                          type="text"
                          className="form-control"
                          placeholder="Nhập địa chỉ email"
                        />
                        {relativeEmailMessage ? (
                          <span className={styles.alertSpan}>
                            {relativeEmailMessage}
                          </span>
                        ) : (
                          []
                        )}
                      </div>
                    </Fragment>
                  )}
                  <div className={cx(styles.form_group, styles.action_form)}>
                    {id === 0 && (
                      <PKHBtn
                        reset="reset"
                        onClick={this.handleResetFormFields}
                      >
                        Nhập lại
                      </PKHBtn>
                    )}

                    <PKHBtn
                      block={id === 0 ? "" : "block"}
                      create_patient_form="create_patient_form"
                      onClick={
                        id === 0
                          ? this.onCreatePatientInfo
                          : this.onUpdatePatientInfo
                      }
                      disabled={disableCreate}
                    >
                      {id === 0 ? "Tạo mới" : "Cập nhật"}
                    </PKHBtn>
                  </div>
                </div>
              </div>
            </form>
          </MDBAnimation>
        )}
        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={showModalConfirmPhone}
          toggle={this.handleToggleModalConfirmPhone}
          title="Xác nhận số điện thoại"
          children={this.renderBodyModalConfirmMobile()}
          centered
          className="centered"
          footer
          footerConfirm
          cancelText="Đóng"
          okText="Xác nhận"
          onCancel={this.handleToggleModalConfirmPhone}
          onOk={this.handleCheckPhone}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    patientForm,
    resource: {
      dataCareer: allCareers,
      dataNation: allNation,
      dataCountry: allCountries,
      dataCity: allCities,
      dataDistrict: allDistricts,
      dataWard: allWards,
      dataRelationShip: allRelationShip
    },
    totalData: { flow }
  } = state;
  const { resource } = state;
  return {
    resource,
    allCareers,
    allRelationShip,
    allNation,
    allCountries,
    allCities,
    allDistricts,
    allWards,
    patientForm,
    flow
  };
};

const mapDispatchToProps = dispatch => ({
  onChangeCareer: selectedCareer => {
    dispatch(onChangeCareer(selectedCareer));
  },
  onChangeCountry: selectedCountry => {
    dispatch(onChangeCountry(selectedCountry));
  },
  onChangeNation: selectedNation => {
    dispatch(onChangeNation(selectedNation));
  },
  onChangeRelationShip: selectedRelationShip => {
    dispatch(onChangeRelationShip(selectedRelationShip));
  },
  onChangeCity: selectedCity => {
    dispatch(onChangeCity(selectedCity));
  },
  onChangeDistrict: selectedDistrict => {
    dispatch(onChangeDistrict(selectedDistrict));
  },
  onChangeWard: selectedWard => {
    dispatch(onChangeWard(selectedWard));
  },
  onSaveField: fields => {
    dispatch(saveField(fields));
  },
  onCreatePatientInfo: () => {
    dispatch(onCreatePatientInfo());
  },
  onUpdatePatientInfo: () => {
    dispatch(onUpdatePatientInfo());
  },
  resetPatientForm: () => {
    dispatch(resetPatientForm());
  },
  resetDefaultCountry: id => {
    dispatch(resetDefaultCountry(id));
  },
  requestAllCity: () => {
    dispatch(requestAllCity());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  createForm({
    mapPropsToFields: props => {
      const {
        patientForm: { info: patientForm }
      } = props;
      return {
        id: createFormField(patientForm.id),
        name: createFormField(patientForm.name),
        surname: createFormField(patientForm.surname),
        birthdate: createFormField(patientForm.birthdate),
        birthday: createFormField(patientForm.birthday),
        birthmonth: createFormField(patientForm.birthmonth),
        birthyear: createFormField(patientForm.birthyear),
        mobile: createFormField(patientForm.mobile),
        cmnd: createFormField(patientForm.cmnd),
        email: createFormField(patientForm.email),
        profession_id: createFormField(patientForm.profession_id),
        sex: createFormField(patientForm.sex),
        country_code: createFormField(patientForm.country_code),
        dantoc_id: createFormField(patientForm.dantoc_id),
        city_id: createFormField(patientForm.city_id),
        district_id: createFormField(patientForm.district_id),
        ward_id: createFormField(patientForm.ward_id),
        address: createFormField(patientForm.address),
        relative_name: createFormField(patientForm.relative_name),
        relative_type_id: createFormField(patientForm.relative_type_id),
        relative_email: createFormField(patientForm.relative_email),
        relative_mobile: createFormField(patientForm.relative_mobile)
      };
    },
    onFieldsChange(props, fields) {
      const newFields = { ...fields };
      if ("name" in fields) {
        newFields.name.value = fields.name.value.toUpperCase();
      } else if ("surname" in fields) {
        newFields.surname.value = fields.surname.value.toUpperCase();
      }
      props.onSaveField(newFields);
    }
  })(PatientFormStepper)
);
