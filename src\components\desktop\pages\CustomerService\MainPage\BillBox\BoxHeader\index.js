import React, { Component } from "react";
import styles from "./style.module.scss";

class BoxHeader extends Component {
  render() {
    return (
      <div className={styles.box_header}>
        <div className={styles.box_header_title}>
          <p className={styles.title}>{this.props.title}</p>
        </div>
        {/* <button
          onClick={() => this.props.toggleSearchBox()}
          style={{
            backgroundImage: "linear-gradient(90deg, #11ece1 0%, #0cd5a9 100%)"
          }}
        >
          <MDBIcon icon="plus-circle" />
          <span className={styles.button_text}>lọc</span>
        </button> */}
      </div>
    );
  }
}

export default BoxHeader;
