/*!
 *  Material Design Hierarchical Display by <PERSON> (@zavoloklom) - https://github.com/zavoloklom/material-design-hierarchical-display/
 *  License - https://github.com/zavoloklom/material-design-hierarchical-display/blob/master/LICENSE (MIT License)
 */.zmd-hierarchical-display{visibility:hidden}.zmd-hierarchical-display.in{visibility:visible}.zmd-hierarchical-displaying{visibility:visible}.animation{-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:both;animation-fill-mode:both}.animation.zoomedIn,.animation.zoomedOut{-webkit-animation-timing-function:cubic-bezier(.55,0,.1,1);animation-timing-function:cubic-bezier(.55,0,.1,1)}@-webkit-keyframes zoomedIn{from{-webkit-transform:scale(0);transform:scale(0)}to{-webkit-transform:scale(1);transform:scale(1)}}@keyframes zoomedIn{from{-webkit-transform:scale(0);transform:scale(0)}to{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes zoomedOut{from{-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:scale(0);transform:scale(0)}}@keyframes zoomedOut{from{-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:scale(0);transform:scale(0)}}.zoomedIn{-webkit-animation-name:zoomedIn;animation-name:zoomedIn}.zoomedOut{-webkit-animation-name:zoomedOut;animation-name:zoomedOut}