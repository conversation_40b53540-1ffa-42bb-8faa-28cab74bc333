import { <PERSON><PERSON>ard, MDBCardBody, MDBView } from "mdbreact";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Pagination from "react-bootstrap-4-pagination";
import { connect, useDispatch } from "react-redux";
import { Link } from "react-router-dom";
import { apiNews } from "~/utils/constants";
import styles from "./style.module.scss";
import {
  getTotalListNews,
  getListNews
} from "../../../../store/news/newsAction";

const ListNews = props => {
  const dispatch = useDispatch();
  const { listNews, totalData } = props;

  const [page, setpage] = useState(1);

  useEffect(() => {
    dispatch(getTotalListNews());
    dispatch(getListNews(page));
  }, [page, dispatch]);

  const paginationConfig = {
    activeBgColor: "#00BFFF",
    activeColor: "white",
    disabledColor: "green",
    totalPages: totalData || 1,
    currentPage: page,
    showMax: 5,
    size: "lg",
    threeDots: true,
    prevNext: true,
    onClick: (page = 1) => {
      setpage(page);
    }
  };

  return (
    <div className={styles["list-news-vertical"]}>
      {listNews?.map(
        ({ id, title, description, updated_at: updatedAt, image, slug }) => {
          return (
            <MDBCard className={styles.card_news} key={id}>
              <Link to={"/tin-tuc/" + slug}>
                <MDBView
                  className={styles.view}
                  src={apiNews + image?.[0]?.url}
                />
              </Link>

              <MDBCardBody className={styles.card_body}>
                <p className={styles.title}>
                  <Link to={"/tin-tuc/" + slug}>{title}</Link>
                </p>
                <p className={styles.tag}>
                  {moment(updatedAt).format("DD/MM/YYYY, hh:mm")}
                </p>
                <p className={styles.description}>
                  <Link to={"/tin-tuc/" + slug}>{description}</Link>
                </p>
              </MDBCardBody>
            </MDBCard>
          );
        }
      )}
      <Pagination {...paginationConfig} />
    </div>
  );
};

const mapStateToProps = state => {
  const {
    news: { totalData, listNews }
  } = state;
  return { totalData, listNews };
};

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ListNews);
