@import "src/assets/scss/custom-variables.scss";
.list_group {
  flex: 0 1 33.33333%;
  display: flex;
  padding-left: 30px;
  .list_group_item {
    background-color: #fff;
    width: 100%;
    padding: 30px 15px;
    border-radius: 6px;
    text-align: center;
  }
}
.list_group_img {
  text-align: center;
  display: flex;
  flex-direction: column;
  img {
    margin-bottom: 10px;
    width: max-content !important;
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
}
.title {
  text-transform: uppercase;
  font-size: 1.625rem;
  font-weight: 900;
}
.desc {
  font-size: 1rem;
  text-align: center;
  margin-bottom: 0;
  color: #000 !important;
}
.icon {
  margin-top: 10px;
}

.link {
  font-size: 0.875rem;
  margin-top: 10px;
  cursor: pointer;
}
.phone {
  margin-top: 10px;
  font-size: 1rem;
  color: blue;
  font-weight: 900;
}
.in_working_hours {
  margin-top: 10px;
  font-size: 0.875rem;
  color: blue;
}
.out_working_hours {
  margin-top: 10px;
  font-size: 0.875rem;
  color: red;
}
