import { find, get } from "lodash";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import {
  checkConfirm,
  insertPatient,
  resetConfirmCheck,
  updateSelectedPatient,
  verifyInfoPatient
} from "~/store/patient/patientAction";
import { getInfoFollowHospital } from "~/utils/flowRouting";
const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const Desktop = Loadable({
  loader: () => import("~/components/desktop/pages/PatientVerifyInfomation"),
  loading: LoadableLoading
});

class DetectPatientVerifyInfomation extends Component {
  methods = {
    changeConfirm: e => {
      this.props.handleCheckConfirm();
    },

    handleVerifyInfoPatient: () => {
      this.props.verifyInfoPatient();
    }
  };

  componentDidMount() {
    this.props.resetConfirmCheck();
    const {
      selectedPatient,
      match: {
        params: { state }
      }
    } = this.props;
    if (!state) return;

    const { selectedHospital } = this.props;
    const { router } = getInfoFollowHospital(selectedHospital.id);
    if (!selectedPatient) {
      this.props.history.push(`${router}/${state}`);
    }
  }

  render() {
    return (
      <React.Fragment>
        <Desktop {...this.props} {...this.methods} />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { info },
    patient: { selectedPatient, confirmChecked, loading },
    features: { selectedFeature, selectedFeatureBooking },
    hospital: { selectedHospital }
  } = state;
  return {
    device: type,
    user: info,
    selectedPatient,
    confirmChecked,
    selectedFeature,
    loading,
    selectedHospital,
    selectedFeatureBooking
  };
};

const mapDispatchToProps = dispatch => ({
  handleCheckConfirm: () => {
    dispatch(checkConfirm());
  },
  updateSelectedPatient: patient => {
    dispatch(updateSelectedPatient(patient));
  },
  insertPatient: (data, callback) => {
    dispatch(insertPatient(data, callback));
  },
  resetConfirmCheck: () => {
    dispatch(resetConfirmCheck());
  },
  verifyInfoPatient: () => {
    dispatch(verifyInfoPatient());
  }
});

const PatientVerifyInfomationHelmet = withTitle({
  component: DetectPatientVerifyInfomation,
  title: `${hospitalName.value} | Xác nhận thông tin bệnh nhân`
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(PatientVerifyInfomationHelmet));
