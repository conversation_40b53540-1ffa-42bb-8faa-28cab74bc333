@import "src/assets/scss/custom-variables.scss";

.card_news {
  font-family: "Averta", serif;
  box-shadow: none;
  display: flex;
  flex-direction: revert;
  margin-bottom: 1rem;
  height: 120px;
  .view {
    max-width: 150px;
    max-height: 120px;
  }
  .card_body {
    padding: 0 0 0 15px !important;
    .title {
      text-transform: capitalize !important;
      font-size: 0.875rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3; /* number of lines to show */
      -webkit-box-orient: vertical;
      text-align: justify;
      text-justify: distribute;
      text-align-last: left;
    }
    .tag {
      font-size: 0.75rem;
      color: #777;
      overflow: hidden;
      margin-bottom: 5px;
    }
  }
}
