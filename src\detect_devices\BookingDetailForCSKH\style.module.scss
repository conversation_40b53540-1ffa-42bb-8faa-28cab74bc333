.wrapper_bill {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  background-color: whitesmoke;

  @media (max-width: 500px) {
    margin-top: 3rem;
  }

  .groupBtn {
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    max-width: 360px;

    button {
      border-radius: 15px;
      max-width: 360px;
      margin: 10px 0;
      width: 100%;
    }
  }
}
.BookingCoupon {
  position: relative;
  .tabsBooking {
    padding: 10px;
    margin: 0;
    border: 0;
    width: 100%;
    display: flex;
    justify-content: space-around;

    span {
      padding: 12px 22px 10px;
      font-size: 16px;
      color: #003553;
      font-weight: 500;
      border-bottom: 2px solid transparent;
      &:hover {
        cursor: pointer;
      }
      @media (max-width: 576px) {
        font-size: 16px;
      }
    }
  }
  .NormalMedpro,
  .MedproCare {
    span {
      color: #00b5f1 !important;
      border-bottom-color: #00b5f1 !important;
      border-radius: 14px 14px 0 0;
    }
  }
  .NoteMedproCare {
    position: absolute;
    top: 10px;
    right: 0;
    background-color: #fd6262;
    color: white;
    width: fit-content;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 10px;
    transform: rotate(20deg);
    margin-bottom: 0;
  }
}
