/* eslint-disable react/no-did-update-set-state */
import cx from "classnames";
import { MDBAnimation } from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import Slider from "react-slick";
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";
import BoxService from "~/components/desktop/organisms/BoxService";
import { urlBannerHomeDesktop } from "~/utils/manageResource";
import styles from "./style.module.scss";
class BannerHome extends Component {
  constructor(props) {
    super(props);
    this.state = {
      backgroundImage: ""
    };
  }

  render() {
    const settings = {
      arrows: true,
      infinite: false,
      slidesToShow: 1,
      slidesToScroll: 1,
      autoplay: true,
      autoplaySpeed: 8000,
      fade: true,
      speed: 900,
      cssEase: "cubic-bezier(0.7, 0, 0.3, 1)",
      touchThreshold: 100,
      lazyLoad: true,
      dots: false
    };

    return (
      <MDBAnimation className={styles.slideBanerHome}>
        <Slider {...settings}>
          <div className={styles.bannerHome_item}>
            <div
              className={cx(styles.imgInside)}
              style={{
                background: `url(${urlBannerHomeDesktop})`,
                backgroundRepeat: "no-repeat",
                backgroundPosition: "center",
                backgroundSize: "100% 100%"
              }}
            />
          </div>
        </Slider>
        <BoxService />
      </MDBAnimation>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    }
  } = state;
  return {
    device: type
  };
};

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(BannerHome);
