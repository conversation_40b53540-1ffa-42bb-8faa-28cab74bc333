apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: ingress-service
  namespace: medpro-web
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"    
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/session-cookie-name: "hello-cookie"
    nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"
    nginx.ingress.kubernetes.io/affinity-mode: persistent
    nginx.ingress.kubernetes.io/session-cookie-hash: sha1
    nginx.ingress.kubernetes.io/affinity: cookie
    nginx.ingress.kubernetes.io/session-cookie-path: "/"
spec:
  rules:
  - host: v2-testing.medpro.com.vn
    http:
      paths:
        - path: /?(.*)
          backend:
            serviceName: medpro-web-v2
            servicePort: 3000
  tls:
    - hosts:
      - v2-testing.medpro.com.vn
      secretName: medpro