.coupon {
  background-color: white;
  padding: 15px;
  padding-bottom: unset;
  width: 360px;
  max-width: 360px;
  color: #12263f;
  font-size: 14px;
  height: fit-content;
}

.printBillCare {
  max-width: 360px;
  margin: auto;
  padding: 3px 17px;
  background: #ffffff;
}
.titleBillDesc {
  font-size: 18px;
  text-align: center;
  font-weight: 700;
  line-height: 24.2px;
  margin-bottom: 0;
  padding: 0 12px;
}
.support {
  display: flex;
  align-items: center;
  gap: 10px;
  svg {
    width: unset;
  }
  p {
    margin-bottom: 0;
    text-align: left;
    color: #f99f00;
    a {
      font-weight: 700;
      font-size: 16px;
      text-decoration: underline;
      color: #f99f00;
    }
  }
}
.groupButton {
  padding: 0 16px;
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  .buttonTele {
    height: 50px;
    padding: 12px;
    gap: 8px;
    border-radius: 12px;
    border: 1px solid #11a2f3;
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    line-height: 19.2px;
    text-align: center;
    color: #11a2f3;
    max-width: 360px !important;
  }
  .buttonBooking {
    max-width: 360px !important;
    height: 50px;
    padding: 12px;
    gap: 8px;
    border-radius: 12px;
    width: 100%;
    background-color: #11a2f3;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    line-height: 19.2px;
    text-align: center;
  }
}
.numbericalOrderNext {
  text-align: center;
  border-radius: 8px;
  p {
    margin-bottom: 4px;
    font-size: 16px;
    font-weight: 400;
    line-height: 16.94px;
    text-align: center;
  }
  .time span {
    font-size: 16px;
    font-weight: 600;
    line-height: 19.36px;
    text-align: left;

    color: #00b5f1;
  }
}
.radioCare247 {
  display: flex;
  flex-direction: column;
  gap: 4px;
  p {
    margin-bottom: 0;
  }
  .careItemName {
    font-size: 16px;
    font-weight: 500;
    line-height: 19.36px;
    text-align: left;
  }
  .careItemPrice {
    font-size: 16px;
    font-weight: 400;
    line-height: 19.36px;
    text-align: left;
    .originalPrice {
      margin-left: 20px;
      color: #d7dbe0;
      text-decoration: line-through;
    }
  }
}
.modal {
  .headerModal {
    display: flex;
    justify-content: center;
  }
  :global {
    .modal-content {
      border-radius: 14px;
    }
    .modal-header {
      border-bottom: 0;
    }
    .close {
      padding-top: 10px;
      font-size: 32px;
    }
    .modal-header .modal-title {
      width: 100%;
      text-align: center;
      font-size: 22px;
      font-weight: 600;
      line-height: 21.6px;
    }
    .modal-body {
      display: flex;
      flex-direction: column;
      gap: 8px;
      > div {
        height: 70px !important;
        border-radius: 12px;
        padding: 12px;
        border: 1px solid #d7dbe0;
      }
    }
    .modal-footer {
      border-top: 0;
    }
  }
}
