import cx from "classnames";
import { find, get } from "lodash";
import {
  MDBAnimation,
  MDBCollapse,
  MDBContainer,
  MDBDropdown,
  MDBDropdownMenu,
  MDBDropdownToggle,
  MDBListGroup,
  MDBListGroupItem,
  MDBNavbar,
  MDBNavbarNav,
  MDBNavbarToggler,
  MDBNavItem,
  MDBNavLink
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Link, withRouter } from "react-router-dom";
import Chat from "~/assets/img/desktop/logo/chat.svg";
import Chat_minhanh from "~/assets/img/desktop/logo/minhanh/minhanh.svg";
import logo_nhidonghcm from "~/assets/img/desktop/logo/nhidonghcm/logo_header_nhidonghcm.gif";
import { partnerInfo } from "~/configs/partnerDetails";
import { VERSION_WEB } from "~/configs/version";
import { redirectToMedproId } from "~/store/login/actions";
import { checkReadNoti } from "~/store/notifications/notificationsActions";
import { logOut } from "~/store/user/userAction";
import { getDurationFromNowTo, getNextStepViewNoti } from "~/utils/func";
import { urlLogoHeader } from "~/utils/manageResource";
import partnerId from "~/utils/partner";
import styles from "./style.module.scss";

class Header extends Component {
  state = {
    collapseID: ""
  };

  notiBell = React.createRef();
  userButton = React.createRef();

  toggleCollapse = collapseID => () =>
    this.setState(prevState => ({
      collapseID: prevState.collapseID !== collapseID ? collapseID : ""
    }));

  renderSomeNotifications = () => {
    const {
      notification: { data }
    } = this.props;

    // Wait update API noti
    if (!data) {
      return <div />;
    }

    return data.slice(0, 3).map((noti, i) => {
      const titleNoti = noti.hospital ? noti.hospital.name : "";
      return (
        <MDBListGroupItem
          key={i}
          className={!noti.isRead ? styles.new : null}
          onClick={() => this.handleViewDetailNoti(noti)}
        >
          <div className={styles.list_news_head}>{titleNoti}</div>
          <p>{noti.title}</p>
          <p>
            <i className="fal fa-clock" />
            {getDurationFromNowTo(noti.createdAt)}
          </p>
        </MDBListGroupItem>
      );
    });
  };

  handleViewDetailNoti = noti => {
    const { history } = this.props;
    this.props.checkReadNoti(noti.id);
    const typePaymentNoti = get(noti, "topicId", null);

    const url = getNextStepViewNoti(noti);
    typePaymentNoti === "invoice.confirm"
      ? window.open(url, "_blank") || window.location.replace(url)
      : history.push(url);
  };

  getActiveClass = pathname => {
    return this.props.location.pathname === pathname
      ? styles.active + " " + styles["active_" + partnerId]
      : styles["itemMenu_" + partnerId];
  };

  render() {
    const { IsAuthenticated, logOut, infoUser, notification } = this.props;

    const fullName = get(infoUser, "fullName");
    const userName = get(infoUser, "userName");
    const displayName = fullName !== "" ? fullName : userName;

    const info = get(partnerInfo, "info", "");
    const name = find(info, { key: "name" });

    const menu = get(partnerInfo, "menu");
    const renderMenu = menu.map((item, index) => {
      return (
        <MDBNavItem className={cx(this.getActiveClass(item.url))} key={index}>
          <MDBNavLink
            className=""
            to={item.link}
            replace={this.props.location.pathname === item.link}
          >
            {item.name}
          </MDBNavLink>
        </MDBNavItem>
      );
    });

    const cls = cx({
      [styles.logoDefault]: true
    });

    const img_Support = () => {
      switch (partnerId) {
        case "minhanh":
          return Chat_minhanh;
        default:
          return Chat;
      }
    };

    return (
      <MDBContainer
        fluid
        className={cx(styles.MenuTop, styles["MenuTop" + partnerId])}
      >
        <MDBAnimation
          type="fadeIn"
          className={cx(
            styles.header,
            styles.header_classic,
            styles.header_shadow,
            styles["header_" + partnerId],
            "d-none d-lg-block"
          )}
        >
          <div className={styles.wrap}>
            <div className={styles.header_content}>
              <div className={styles.header_brand}>
                <Link to="/" replace={this.props.location.pathname === "/"}>
                  <img
                    src={
                      partnerId === "nhidonghcm"
                        ? logo_nhidonghcm
                        : urlLogoHeader
                    }
                    alt={name.value}
                    className={cls}
                  />
                </Link>
              </div>
              <nav className={styles.navigator}>
                <div className={styles.nav_info}>
                  <div className={styles.widget} />
                  <MDBListGroup className={styles.list_group_dangnhap}>
                    {IsAuthenticated ? (
                      <MDBListGroupItem>
                        <MDBDropdown className={styles.mdbropdown}>
                          <MDBDropdownToggle
                            ref={this.notiBell}
                            className={cx(styles.button, styles.bell)}
                          >
                            <i className="fal fa-bell" />
                            {notification.totalNew ? (
                              <span className={styles.count}>
                                {notification.totalNew}
                              </span>
                            ) : null}
                          </MDBDropdownToggle>
                          <MDBDropdownMenu
                            onClick={() => this.notiBell.current.onClick()}
                            right
                            className={cx(
                              styles.mdbdropdownMenu,
                              styles.mdbdropdownBell
                            )}
                          >
                            <div className={styles.head_alert}>
                              <span>Danh sách thông báo</span>
                            </div>
                            <div className={styles.mdbdropdownItem}>
                              <MDBListGroup className={styles.list_news}>
                                {this.renderSomeNotifications()}
                                <MDBListGroupItem>
                                  <Link
                                    to={{
                                      pathname: "/user",
                                      state: { activeItem: 3 }
                                    }}
                                    replace={
                                      this.props.location.pathname === "/user"
                                    }
                                    className={styles.view_all}
                                  >
                                    <span>Xem tất cả </span>
                                  </Link>
                                </MDBListGroupItem>
                              </MDBListGroup>
                            </div>
                          </MDBDropdownMenu>
                        </MDBDropdown>
                      </MDBListGroupItem>
                    ) : null}
                    {/*  {partnerId === "medpro" ? (
                      <MDBListGroupItem>
                        <Link
                          to={{
                            pathname: "/clinic"
                          }}
                          replace={this.props.location.pathname === "/"}
                          className={cx(styles.button, styles.clinic)}
                        >
                          Quản lý phòng mạch
                        </Link>
                      </MDBListGroupItem>
                    ) : null} */}
                    <MDBListGroupItem>
                      <Link
                        to={{
                          pathname: "/",
                          state: { scrollToDownload: true }
                        }}
                        replace={this.props.location.pathname === "/"}
                        className={cx(
                          styles.button,
                          styles.downloadApp,
                          styles["downloadApp_" + partnerId]
                        )}
                      >
                        Tải ứng dụng
                      </Link>
                    </MDBListGroupItem>
                    {!IsAuthenticated ? (
                      <MDBListGroupItem>
                        <Link
                          to="#"
                          className={cx(
                            styles.button,
                            styles.login,
                            styles["login_" + partnerId]
                          )}
                          onClick={() => {
                            this.props.redirectToMedproId();
                          }}
                        >
                          Đăng nhập
                        </Link>
                      </MDBListGroupItem>
                    ) : (
                      <MDBListGroupItem>
                        <MDBDropdown className={styles.mdbropdown}>
                          <MDBDropdownToggle
                            className={cx(styles.button, styles.after_login)}
                            ref={this.userButton}
                          >
                            {displayName}
                          </MDBDropdownToggle>
                          <MDBDropdownMenu
                            onClick={() => this.userButton.current.onClick()}
                            right
                            basic
                            className={cx(
                              styles.mdbdropdownMenu,
                              styles.mdbdropdownProfile
                            )}
                          >
                            <MDBListGroup>
                              <MDBListGroupItem className={styles.item_user}>
                                <div className={styles.item_user_icon}>
                                  <i className="fas fa-user-circle" />{" "}
                                </div>
                                <div className={styles.info}>
                                  <span>Xin chào!</span>
                                  <div className={styles.info_name}>
                                    <b>{displayName}</b>
                                  </div>
                                </div>
                              </MDBListGroupItem>
                              <MDBListGroupItem
                                className={styles.item_information}
                              >
                                <Link
                                  to={{
                                    pathname: "/user",
                                    state: { activeItem: 1 }
                                  }}
                                  replace={this.props.location.pathname === "/"}
                                >
                                  <i className="fal fa-address-book" /> Hồ sơ
                                  bệnh nhân{" "}
                                </Link>
                              </MDBListGroupItem>
                              <MDBListGroupItem
                                className={styles.item_information}
                              >
                                <Link
                                  to={{
                                    pathname: "/user",
                                    state: { activeItem: 2 }
                                  }}
                                  replace={this.props.location.pathname === "/"}
                                >
                                  <i className="fal fa-file-medical" /> Phiếu
                                  khám bệnh
                                </Link>
                              </MDBListGroupItem>
                              <MDBListGroupItem
                                className={styles.item_information}
                              >
                                <Link
                                  to={{
                                    pathname: "/user",
                                    state: { activeItem: 3 }
                                  }}
                                  replace={this.props.location.pathname === "/"}
                                >
                                  <i className="fal fa-bell" />
                                  Thông báo
                                </Link>
                              </MDBListGroupItem>

                              <MDBListGroupItem
                                className={styles.item_sign_out}
                              >
                                <Link
                                  onClick={() => {
                                    logOut();
                                    this.props.history.push("/");
                                  }}
                                  to="#"
                                >
                                  <i className="fal fa-power-off" /> Thoát
                                </Link>
                              </MDBListGroupItem>
                              <MDBListGroupItem
                                className={styles.item_information}
                              >
                                <div className={styles.version}>
                                  <i>Phiên bản {VERSION_WEB}</i>
                                </div>
                              </MDBListGroupItem>
                            </MDBListGroup>
                          </MDBDropdownMenu>
                        </MDBDropdown>
                      </MDBListGroupItem>
                    )}
                  </MDBListGroup>
                </div>
                <div className={styles.menubar}>
                  <MDBNavbar expand="lg" className={styles.mdbNavbar}>
                    <MDBNavbarToggler
                      className={styles.mdbNavbarToggler}
                      onClick={this.toggleCollapse("navbarCollapse3")}
                    >
                      <i className="fas fa-bars" />
                    </MDBNavbarToggler>
                    <MDBCollapse
                      className={styles.mdbCollapse}
                      id="navbarCollapse3"
                      isOpen={this.state.collapseID}
                      navbar
                    >
                      <MDBNavbarNav
                        className={cx(
                          styles.mdbNavbarNav,
                          styles["mdbNavbarNav_" + partnerId]
                        )}
                      >
                        {renderMenu}
                      </MDBNavbarNav>
                    </MDBCollapse>
                  </MDBNavbar>
                </div>
              </nav>
              <div className={styles.menu_extra}>
                <div
                  className={cx(
                    styles.header_info_text,
                    styles["header_info_text_" + partnerId]
                  )}
                >
                  <div className={cx(styles.icons_info, styles.widget)}>
                    <div className={styles.icons}>
                      <img src={img_Support()} alt="Support Medpro" />
                    </div>
                    <div className={styles.info_right}>
                      Bạn cần trợ giúp?
                      <br />
                      <a className={styles.dot} href="tel:1900-2115">
                        1900-2115
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </MDBAnimation>
      </MDBContainer>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { IsAuthenticated, info: infoUser },
    notification
  } = state;
  return {
    IsAuthenticated,
    notification,
    infoUser
  };
};

export default connect(mapStateToProps, {
  redirectToMedproId,
  checkReadNoti,
  logOut
})(withRouter(Header));
