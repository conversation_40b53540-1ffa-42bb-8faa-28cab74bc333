  .note {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    margin-bottom: 15px;
    flex-direction: column;

    .cancelMessage {
      font-style: italic;
      color: #df0000;
      margin-bottom: 1rem;
    }

    .timeNoteCancel {
      color: #c6cace;
    }

    .greenNote {
      background-color: #6dec7b;
    }

    .redNote {
      background-color: red;
    }

    .greyNote {
      background-color: #c6cace;
    }

    .btnShare {
      margin-left: 0.5rem;
      display: inline-block;
      padding: 7px 25px;
      border-radius: 15px;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border: 1px solid snow;
      box-shadow: 0 2.8px 2.2px rgba(0, 0, 0, 0.034),
        0 12.5px 10px rgba(0, 0, 0, 0.06), 0 100px 80px rgba(0, 0, 0, 0.12);

      a {
        color: blue;
      }
    }


    .totalPaymentMessage {
      color: orangered;
      margin-top: 20px;
      text-align: center;

      .MessageExtra {
        display: block;
        font-size: 12px;
      }
    }
  }
