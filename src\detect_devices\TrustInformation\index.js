import { find, get } from "lodash";
import { MDBSpinner } from "mdbreact";
import moment from "moment";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import { resetStatusScanQrCodeInsurance } from "~/store/patient/patientAction";
import {
  checkRequirePayment,
  getAllPaymentMethod,
  resetPaymentMethod
} from "~/store/payment/paymentAction";
import {
  resetLoading,
  changeDirection,
  getBookingTree,
  getExtraConfig,
  reserveBooking,
  reserveBookingPartner
} from "~/store/totalData/actions";
import { MOMO } from "~/utils/constants";
import { getPartnerFromUrl } from "~/utils/func";
import AppId from "~/utils/partner";
import cx from "classnames";
import styles from "./style.module.scss";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const TrustInformationPageDesktop = Loadable({
  loader: () => import("~/components/desktop/pages/TrustInformation"),
  loading: LoadableLoading
});

class DetectTrustInformation extends Component {
  componentWillMount() {
    const { getAllPaymentMethod } = this.props;

    if (AppId === MOMO) {
      getAllPaymentMethod();
    }
  }

  componentDidMount() {
    const {
      resetStatusScanQrCodeInsurance,
      getBookingTree,
      schedulesSelected,
      resetPaymentMethod,
      getExtraConfig,
      resetLoading
    } = this.props;
    resetLoading();
    resetPaymentMethod();

    getExtraConfig();

    if (schedulesSelected.length === 0) {
      const currentPagePartnerId = getPartnerFromUrl(
        this.props.history.location.pathname
      );
      getBookingTree("", currentPagePartnerId);
    }
    resetStatusScanQrCodeInsurance();
  }

  methods = {
    handleNextStep: () => {
      const { reserveBooking } = this.props;
      if (AppId === MOMO) {
        reserveBooking();
      } else {
        this.props.checkRequirePayment();
      }
    },

    handleGoBack: () => {
      const { isRepayment, changeDirection } = this.props;
      let route = "";
      if (isRepayment) {
        route = "/user";
      } else {
        route = "/chon-ho-so";
      }
      changeDirection();
      this.props.history.push({
        pathname: route,
        state: { activeItem: 2 }
      });
    }
  };

  render() {
    const { schedulesSelected, loading } = this.props;

    if (schedulesSelected.length < 0 || (AppId === MOMO && loading)) {
      return (
        <div className={cx(styles.hLoading, "loading text-center")}>
          <MDBSpinner big />
          <p>
            <span>Đang </span>
            <span>tiến </span>
            <span>hành </span>
            <span>thanh </span>
            <span>toán </span>
            <span>với </span>
            <span>Momo ...</span>
          </p>
        </div>
      );
    }

    return (
      <React.Fragment>
        <TrustInformationPageDesktop {...this.props} {...this.methods} />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { IsAuthenticated },
    doctorAndTime: { alertMaxSpecialist },
    dateAndSpecialist: { selectedSpecialist },
    totalData: {
      schedulesSelected,
      bookingTree,
      room,
      hospitalSelected,
      isRepayment,
      partnerId,
      subject: { isHasSubject },
      service: { isHasService },
      room: { isHasRoom },
      doctor: { isHasDoctor },
      selectSvAddOn,
      loading
    },
    hospital: { selectedHospital },
    payment
  } = state;
  const selectedDate = moment(state.dateAndSpecialist.selectedDate).format(
    "DD-MM-YYYY"
  );
  return {
    loading,
    payment,
    selectSvAddOn,
    IsAuthenticated,
    device: type,
    alertMaxSpecialist,
    selectedSpecialist,
    selectedDate,
    schedulesSelected,
    bookingTree,
    room,
    hospitalSelected,
    isRepayment,
    partnerId,
    selectedHospital,
    isHasSubject,
    isHasService,
    isHasRoom,
    isHasDoctor
  };
};

const TrustInformationHelmet = withTitle({
  component: DetectTrustInformation,
  title: `${hospitalName.value} | Xác nhận thông tin đặt khám`
});

export default connect(mapStateToProps, {
  getExtraConfig,
  changeDirection,
  resetStatusScanQrCodeInsurance,
  reserveBookingPartner,
  getBookingTree,
  checkRequirePayment,
  resetPaymentMethod,
  reserveBooking,
  resetLoading,
  getAllPaymentMethod: getAllPaymentMethod
})(TrustInformationHelmet);
