import cx from "classnames";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MDBSpinner } from "mdbreact";
import moment from "moment";
import React, { Fragment, useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import NewsSame from "../../molecules/NewsSame";
import PostRelated from "../../molecules/PostRelated";
import styles from "./style.module.scss";
import { SEO } from "~/components/common/molecules/SEO";

const isHtml = require("is-html");
const apiNews = "https://cms.medpro.com.vn";

export const NewsDetail = props => {
  const [post, setpost] = useState();
  const [loading, setloading] = useState(false);

  useEffect(() => {
    const getData = async () => {
      setloading(true);
      const url = apiNews + "/posts?slug=" + props.match.params.slug;
      const data = await fetch(url);
      const rs = await data.json();
      setpost(rs[0]);
      setloading(false);
    };
    getData();
  }, [props.match.params.slug]);

  if (loading) {
    return (
      <div className="loading">
        <MDBSpinner big />
      </div>
    );
  }

  console.log(`post`, post);

  return (
    <Fragment>
      <SEO
        title="Medpro - Tin Tức"
        meta={[
          {
            name: `description`,
            content: `Medpro, đặt khám trực tuyến, thanh toán viện phí, nhắc uống thuốc, khám chuyên khoa, bác sĩ, tư vấn sức khoẻ từ xa`
          },
          {
            property: `og:title`,
            content: post?.title
          },
          {
            property: `og:description`,
            content: post?.description
          },
          {
            property: `og:url`,
            content: "https://medpro.vn/tin-tuc" + post?.slug
          },
          {
            property: `og:image`,
            content: apiNews + post?.image[0].url
          },
          {
            property: `og:type`,
            content: `article`
          }
        ]}
      />
      <MDBContainer className="mt-5">
        <MDBRow className="">
          <MDBCol md="8" className="mb-4">
            <Content {...post} />
          </MDBCol>

          <MDBCol md="4">
            <NewsSame />
          </MDBCol>
        </MDBRow>

        <PostRelated />
      </MDBContainer>
    </Fragment>
  );
};

export const Content = post => {
  return (
    <MDBContainer className="p-0">
      <MDBRow>
        <MDBCol>
          <h2 className="text-uppercase font-weight-bolder h3-responsive">
            {post.title}
          </h2>
        </MDBCol>
      </MDBRow>

      <MDBRow>
        <MDBCol>
          <p>
            <em>
              {moment(post?.update_at).format("DD/MM/YYYY hh:mm")} bởi{" "}
              <strong>{post?.author}</strong>
            </em>
          </p>
        </MDBCol>
      </MDBRow>

      <MDBRow className={post?.description ? "" : " d-none"}>
        <MDBCol className="text-justify">
          <blockquote>{checkContent(post?.description)}</blockquote>
        </MDBCol>
      </MDBRow>

      <MDBRow className={cx(styles.content, post?.content ? "" : " d-none")}>
        <MDBCol>{checkContent(post?.content)}</MDBCol>
      </MDBRow>

      <MDBRow>
        <MDBCol>
          <p className="h5 text-right">
            <strong>{post?.author}</strong>
          </p>
        </MDBCol>
      </MDBRow>
    </MDBContainer>
  );
};

const checkContent = content => {
  const replaceContent = content?.replace(
    /\/uploads\//g,
    `${apiNews}/uploads/`
  );
  return (
    <Fragment>
      {isHtml(content) ? (
        <div dangerouslySetInnerHTML={{ __html: content }} />
      ) : (
        <ReactMarkdown source={replaceContent} />
      )}
    </Fragment>
  );
};
