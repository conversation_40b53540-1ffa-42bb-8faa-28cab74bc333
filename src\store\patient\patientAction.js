import * as TYPES from "~/store/patient/patientType";

export const setTypeCreatePatient = value => ({
  type: TYPES.SET_TYPE_CREATE_PATIENT,
  value
});

export const postInfoXNC = value => ({
  type: TYPES.POST_INFO_XNC,
  value
});

export const setPatientId = id => ({
  type: TYPES.SET_PATIENTID,
  id
});

export const changePatientNumber = value => ({
  type: TYPES.CHANGE_PATIENT_NUMBER,
  value
});

export const selectedPatient = value => {
  return {
    type: TYPES.SELECTED_PATIENT,
    value
  };
};

export const selectedPatientDetail = value => ({
  type: TYPES.SELECTED_PATIENT_DETAIL,
  value
});

export const toggleModalConfirm = () => ({ type: TYPES.TOGGLE_MODAL_CONFIRM });

export const checkConfirm = () => ({ type: TYPES.CHECK_CONFIRM });

export const resetConfirmCheck = () => ({ type: TYPES.RESET_CONFIRM_CHECK });

export const clearState = () => ({ type: TYPES.CLEAR_STATE });

export const resetData = () => ({ type: TYPES.RESET_DATA });

export const getPatientByMSBN = (data = null) => ({
  type: TYPES.GET_PATIENT_BY_MSBN_REQUEST,
  data
});

export const resetFormGetPatienByMSBN = () => ({
  type: TYPES.RESET_FORM_GET_PATIENT_BY_MSBN
});

export const getPatientByUserId = data => ({
  type: TYPES.GET_PATIENT_BY_USERID_REQUEST,
  data
});

export const putPatient = patient => ({
  type: TYPES.GET_PATIENT_BY_MSBN_SUCCESS,
  patient
});

export const insertPatient = (data, callback = () => {}) => ({
  type: TYPES.INSERT_PATIENT_REQUEST,
  data,
  callback
});

export const deletePatient = () => ({
  type: TYPES.DELETE_PATIENT_REQUEST
});

export const searchPatientByInfo = () => ({
  type: TYPES.SEARCH_PATIENT_BY_INFO_REQUEST
});

// export const getClsSearchHistory = data => ({
//   type: TYPES.GET_CLS_SEARCH_HISTORY_REQUEST,
//   data
// });

// choose patient

export const getPatientList = () => ({
  type: TYPES.UMC_CHOOSE_PATIENT_LIST_REQUEST
});

export const updateSelectedPatient = patient => {
  return {
    type: TYPES.UPDATE_SELECTED_PATIENT,
    patient
  };
};

export const showAlertInfoChoosePatient = alertMsg => {
  return {
    type: TYPES.SHOW_ALERT_INFO_CHOOSE_PATIENT,
    alertMsg
  };
};

export const hideAlertInfoChoosePatient = () => {
  return {
    type: TYPES.HIDE_ALERT_INFO_CHOOSE_PATIENT
  };
};

export const selectPatientFromList = id => {
  return { type: TYPES.SELECT_PATIENT_FROM_LIST, id };
};

export const selectPatientReExam = () => {
  return { type: TYPES.SELECT_PATIENT_REEXAM };
};

export const selectPatientFromData = patient => {
  return { type: TYPES.SELECT_PATIENT_FROM_DATA, payload: patient };
};

export const toggleModalChoosePatient = () => {
  return {
    type: TYPES.TOGGLE_MODAL_CHOOSE_PATIENT
  };
};

export const resetModalChoosePatient = () => {
  return {
    type: TYPES.RESET_MODAL_CHOOSE_PATIENT
  };
};

export const resetModalConfirmPatient = () => {
  return {
    type: TYPES.RESET_MODAL_CONFIRM
  };
};

export const resetRedirectToCheckPhone = () => {
  return {
    type: TYPES.RESET_REDIRECT_TO_CHECK_PHONE
  };
};

export const checkInsurance = (bvId, date) => {
  return { type: TYPES.CHECK_INSURANCE, bvId, date };
};

export const checkInsuranceSuccess = data => {
  return { type: TYPES.CHECK_INSURANCE_SUCCESS, data };
};

export const checkInsuranceFail = error => {
  return { type: TYPES.CHECK_INSURANCE_FAIL, error };
};

export const getMedproPatientList = () => {
  return { type: TYPES.GET_MEDPRO_PATIENT_LIST };
};

export const getMedproPatientSuggestionList = () => {
  return { type: TYPES.GET_MEDPRO_PATIENT_SUGGESTION_LIST };
};

export const verifyPhonePatient = phone => {
  return { type: TYPES.VERIFY_PHONE_PATIENT, payload: phone };
};
export const verifyPhonePatientSuccess = data => {
  return { type: TYPES.VERIFY_PHONE_PATIENT_SUCCESS, payload: data };
};
export const verifyPhonePatientFailure = error => {
  return { type: TYPES.VERIFY_PHONE_PATIENT_FAILURE, payload: error };
};

export const verifyInfoPatient = () => {
  return {
    type: TYPES.VERIFY_INFO_PATIENT
  };
};
export const saveFieldSearchPatientByInfo = fields => {
  return {
    type: TYPES.SAVE_FIELD_FORM_SEARH_PATIENT_BY_INFO,
    payload: fields
  };
};
export const saveFieldsCheckInsurance = fields => {
  return {
    type: TYPES.SAVE_FIELD_FORM_CHECK_INSURANCE,
    payload: fields
  };
};
export const updateFieldsCheckInsurance = data => {
  return {
    type: TYPES.UPDATE_FORM_CHECK_INSURANCE,
    payload: data
  };
};
export const resetFormCheckInsurance = () => {
  return {
    type: TYPES.RESET_FORM_CHECK_INSURANCE
  };
};
export const scanQrCodeInsuranceSuccess = () => {
  return {
    type: TYPES.SCAN_QR_CODE_INSURANCE_SUCCESS
  };
};
export const resetStatusScanQrCodeInsurance = () => {
  return {
    type: TYPES.RESET_STATUS_SCAN_QR_CODE_INSURANCE
  };
};

export const verifyPatientWithoutPhone = key => {
  return {
    type: TYPES.VERIRY_PATIENT_WITHOUT_PHONE,
    key
  };
};
export const checkInsuranceOfPatient = (callback, text) => {
  return {
    type: TYPES.CHECK_INSURANCE_OF_PATIENT,
    callback,
    text
  };
};
export const resetErrorCheckInsurance = () => {
  return {
    type: TYPES.RESET_ERROR_CHECK_INSURANCE
  };
};

export const hideLoading = () => {
  return {
    type: TYPES.HIDE_LOADING
  };
};
