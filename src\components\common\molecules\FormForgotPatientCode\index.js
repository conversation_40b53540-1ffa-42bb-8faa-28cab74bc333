/* eslint-disable react/jsx-handler-names */
import React, { Component, Fragment } from "react";
import styles from "./style.module.scss";
import cx from "classnames";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import Select from "~/components/common/atoms/Select";
import PKHBtn from "~/components/common/atoms/Button";
import { getBirthYears } from "~/utils/func";
import {
  searchPatientByInfo,
  saveFieldSearchPatientByInfo
} from "~/store/patient/patientAction";
import {
  requestAllCountries,
  // onChangeCity,
  resetCity
} from "~/store/resource/resourceAction";
import { createForm, createFormField } from "rc-form";
class ForgotPatientCode extends Component {
  state = {
    sexs: [
      {
        name: "Chọn giới tính",
        id: -1
      },
      {
        name: "<PERSON>",
        id: 1
      },
      {
        name: "Nữ",
        id: 0
      }
    ]
  };

  componentDidMount() {
    const { getCountries } = this.props;
    this.props.resetCity();
    getCountries();
  }

  validatorSurName = (rule, value, callback) => {
    if (value) {
      value = value.toLowerCase();
      value = value.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
      value = value.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
      value = value.replace(/ì|í|ị|ỉ|ĩ/g, "i");
      value = value.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
      value = value.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
      value = value.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
      value = value.replace(/đ/g, "d");
      const patternName = /^[a-zA-Z ]+$/;
      if (patternName.test(value)) {
        callback();
      } else {
        callback("Họ và tên lót chỉ bao gồm chữ cái!");
      }
    } else {
      callback();
    }
    callback();
  };

  validatorFirstName = (rule, value, callback) => {
    if (value) {
      value = value.toLowerCase();
      value = value.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
      value = value.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
      value = value.replace(/ì|í|ị|ỉ|ĩ/g, "i");
      value = value.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
      value = value.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
      value = value.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
      value = value.replace(/đ/g, "d");
      const patternName = /^[a-zA-Z ]+$/;
      if (patternName.test(value)) {
        callback();
      } else {
        callback("Tên chỉ bao gồm chữ cái!");
      }
    } else {
      callback();
    }
    callback();
  };

  validatorBirthYear = (rule, value, callback) => {
    if (Number(value) === 0) {
      callback("Vui lòng chọn năm sinh!");
    }
    callback();
  };

  validatorGender = (rule, value, callback) => {
    if (Number(value) === -1) {
      callback("Vui lòng chọn giới tính!");
    }
    callback();
  };

  validatorCity = (rule, value, callback) => {
    if (Number(value) === 0) {
      callback("Vui lòng chọn tỉnh thành!");
    } else {
      callback();
    }
  };

  onChangeYear = value => {
    this.props.form.setFieldsValue({
      birthYear: value
    });
  };

  onChangeGender = id => {
    this.props.form.setFieldsValue({
      gender: id
    });
  };

  onChangeCity = value => {
    this.props.form.setFieldsValue({
      cityId: value
    });
  };

  handleConfirm = () => {
    this.props.form.validateFields((error, value) => {
      if (!error) {
        this.props.handleSearchPatientByInfo();
        // this.props.history.push("/ket-qua-tim-benh-nhan");
      }
    });
  };

  render() {
    const {
      className,
      cities,
      loadingWards,
      form: { getFieldProps, getFieldError }
    } = this.props;

    const { sexs } = this.state;

    const fieldLastName = getFieldError("surName");
    const lastNameError = fieldLastName ? fieldLastName.join(",") : null;

    const fieldFirstName = getFieldError("firstName");
    const firstNameError = fieldFirstName ? fieldFirstName.join(",") : null;

    const fieldBirthyear = getFieldError("birthYear");
    const birthyearError = fieldBirthyear ? fieldBirthyear.join(", ") : null;

    const fieldGender = getFieldError("gender");
    const genderError = fieldGender ? fieldGender.join(", ") : null;

    const fieldCity = getFieldError("cityId");
    const cityError = fieldCity ? fieldCity.join(", ") : null;

    return (
      <Fragment>
        <div className={cx(styles.info_required, className)}>
          <span
            className={styles.alertSpan}
            style={{ marginBottom: "15px", display: "block" }}
          >
            (*) Thông tin bắt buộc nhập
          </span>
        </div>
        <form
          className={cx(styles.form_medpro, styles.form_forgot_patient_code, className)}
        >
          <div className={styles.form_group}>
            <label htmlFor="label_surName">
              Họ và tên lót (có dấu)<sup>*</sup>
            </label>
            <input
              {...getFieldProps("surName", {
                rules: [
                  {
                    required: true,
                    message: "Vui lòng nhập họ và tên lót"
                  },
                  { validator: this.validatorSurName }
                ]
              })}
              id="label_surName"
              type="text"
              className="form-control"
              placeholder="Vui lòng nhập họ và tên lót"
            />
            <span className={styles.alertSpan}>{lastNameError}</span>
          </div>

          <div className={styles.form_group}>
            <label htmlFor="label_firstName">
              Tên bệnh nhân (có dấu)<sup>*</sup>
            </label>
            <input
              {...getFieldProps("firstName", {
                rules: [
                  {
                    required: true,
                    message: "Vui lòng nhập tên"
                  },
                  { validator: this.validatorFirstName }
                ]
              })}
              id="label_firstName"
              type="text"
              className="form-control"
              placeholder="Vui lòng nhập tên"
            />
            <span className={styles.alertSpan}>{firstNameError}</span>
          </div>

          <div className={styles.form_group}>
            <label htmlFor="label_cmnd">
              Mã định danh/CCCD/Passport
            </label>
            <input
              {...getFieldProps("cmnd", {
                rules: []
              })}
              id="label_cmnd"
              type="text"
              className="form-control"
              placeholder="Nhập mã định danh/CCCD/Passport"
            />
          </div>

          <div className={styles.form_group}>
            <label htmlFor="label_year_forgot">
              Năm sinh<sup>*</sup>
            </label>
            <Select
              {...getFieldProps("birthYear", {
                trigger: ["onChange"],
                valuePropName: "value",
                rules: [{ validator: this.validatorBirthYear }]
              })}
              data={getBirthYears()}
              onChange={this.onChangeYear}
            />
            <span className={styles.alertSpan}>{birthyearError}</span>
          </div>

          <div className={styles.form_group}>
            <label htmlFor="gioitinh_sdt_forgot">
              Giới tính<sup>*</sup>
            </label>
            <Select
              {...getFieldProps("gender", {
                trigger: ["onChange"],
                valuePropName: "value",
                rules: [{ validator: this.validatorGender }]
              })}
              data={sexs}
              onChange={this.onChangeGender}
            />
            <span className={styles.alertSpan}>{genderError}</span>
          </div>

          <div className={cx(styles.form_group, styles.form_select)}>
            <label htmlFor="label_tp_forgot">
              Tỉnh / Thành<sup>*</sup>
            </label>
            <Select
              {...getFieldProps("cityId", {
                trigger: ["onChange"],
                valuePropName: "value",
                rules: [{ validator: this.validatorCity }]
              })}
              data={cities}
              onChange={this.onChangeCity}
            />
            <span className={styles.alertSpan}>{cityError}</span>
          </div>
          <div className={cx(styles.form_group, styles.action_form)}>
            <PKHBtn
              create="create"
              className="m-0"
              onClick={this.handleConfirm}
              disabled={loadingWards}
            >
              Xác nhận
            </PKHBtn>
          </div>
        </form>
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    resource: {
      dataCity: cities,
      selectedCity,
      dataDistrict: districts,
      selectedDistrict,
      dataWard: wards,
      selectedWard,
      loading: loadingWards
    },
    patient
  } = state;
  return {
    cities,
    districts,
    wards,
    selectedCity,
    selectedDistrict,
    selectedWard,
    loadingWards,
    patient
  };
};

const mapDispatchToProps = dispatch => ({
  getCountries: () => {
    dispatch(requestAllCountries());
  },
  handleSearchPatientByInfo: () => {
    dispatch(searchPatientByInfo());
  },
  resetCity: () => {
    dispatch(resetCity());
  },
  saveFieldSearchPatientByInfo: fields => {
    dispatch(saveFieldSearchPatientByInfo(fields));
  }
});

const FormForgotPatientCode = createForm({
  mapPropsToFields: props => {
    const {
      patient: { formSearchPatientByInfo }
    } = props;
    return {
      firstName: createFormField(formSearchPatientByInfo.firstName),
      surName: createFormField(formSearchPatientByInfo.surName),
      gender: createFormField(formSearchPatientByInfo.gender),
      birthYear: createFormField(formSearchPatientByInfo.birthYear),
      cityId: createFormField(formSearchPatientByInfo.cityId),
      cmnd: createFormField(formSearchPatientByInfo.cmnd),
    };
  },
  onFieldsChange(props, fields) {
    const newFields = { ...fields };
    if ("firstName" in newFields) {
      newFields.firstName.value = fields.firstName.value.toUpperCase();
    }
    if ("surName" in newFields) {
      newFields.surName.value = fields.surName.value.toUpperCase();
    }
    props.saveFieldSearchPatientByInfo(newFields);
  }
})(ForgotPatientCode);

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(FormForgotPatientCode));
