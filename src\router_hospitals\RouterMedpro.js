import React from "react";
import { Route, Switch } from "react-router-dom";
import { connect } from "react-redux";

class RouterMedpro extends React.Component {
  render() {
    const { Component, path, partnerId } = this.props;
    console.log(`${path}${partnerId}`);
    console.log(this.props);

    return (
      <Route
        // exact
        path={`${path}${partnerId}`}
        render={props => <Component {...props} />}
      />
    );
  }
}

export default connect(null, null)(RouterMedpro);
