/* eslint-disable max-len */
import { find, get } from "lodash";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { SEO } from "~/components/common/molecules/SEO";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import * as action from "~/store/resource/resourceAction";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const IntroducePageDesktop = Loadable({
  loader: () => import("~/components/desktop/pages/Introduce"),
  loading: LoadableLoading
});

class Introduce extends Component {
  constructor(props) {
    super(props);
    this.props.getIntroduction();
  }

  render() {
    return (
      <React.Fragment>
        <SEO
          title={`Giới Thiệu | ${hospitalName.value}  `}
          meta={[
            {
              name: `description`,
              content: `Med<PERSON><PERSON>, đặt kh<PERSON>m tr<PERSON><PERSON>, <PERSON>h to<PERSON> vi<PERSON>h<PERSON>, nh<PERSON><PERSON> u<PERSON>ng thu<PERSON>, kh<PERSON><PERSON> chuy<PERSON>hoa, b<PERSON><PERSON> sĩ, tư vấn sức khoẻ từ xa`
            },
            {
              property: `og:title`,
              content: "Giới Thiệu"
            },
            {
              property: `og:description`,
              content: `Trang giới thiệu`
            },
            {
              property: `og:url`,
              content: "https://medpro.vn/gioi-thieu"
            },
            {
              property: `og:image`,
              content: "https://cms.medpro.com.vn/uploads/d_e1b14f67d2.jpg"
            },
            {
              property: `og:type`,
              content: `website`
            }
          ]}
        />
        <IntroducePageDesktop {...this.props} />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    resource: { loading, data, error }
  } = state;
  return {
    device: type,
    loading,
    data,
    error
  };
};

const mapDispatchToProps = dispatch => {
  return {
    getIntroduction: () => dispatch(action.getIntroduction())
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(Introduce);
