import * as jwt from "jsonwebtoken";
import * as api from "~/api";
import { KEY_NAME } from "~/configs";
import { PATIENT } from "~/utils/urlApi";
import { createQueryString } from "~/utils/func";

// this url for testing, please do not remove
// const umcUrl = 'https://alpha-api.medpro.com.vn/api'
export const getByMSBN = (baseUrl, data) => {
  const url = createQueryString(`${baseUrl}${PATIENT.GETBYMSBN_URL}`, data);
  return api.postHttpRequest(url);
};

export const getByUserId = (baseUrl, data) => {
  data.sign = jwt.sign(data, KEY_NAME);
  return api.getHttpRequestParamsAsync(
    `${baseUrl}${PATIENT.GETBYUSERID_URL}`,
    data
  );
};

export const insertPatient = (baseUrl, data) =>
  api.postHttpRequestAsync(`${baseUrl}${PATIENT.INSERT_URL}`, data);

export const apiDeletePatient = (baseUrl, data) => {
  const url = createQueryString(`${baseUrl}${PATIENT.DELETE_URL}`, data);
  return api.deleteHttpRequest(url);
};

export const searchPatientByInfo = (baseUrl, data) => {
  const url = createQueryString(`${baseUrl}${PATIENT.SEARCHBYINFO_URL}`, data);
  return api.postHttpRequest(url);
};

// export const getClsSearchHistory = (baseUrl, data) => {
//   data.sign = jwt.sign(data, KEY_NAME);
//   return api.getHttpRequestParamsAsync(
//     `${baseUrl}/rebooking/getallpatient`,
//     data
//   );
// };

export const apiRequestChoosePatientList = (baseUrl, config) => {
  return api.getHttpRequest(
    `${baseUrl}${PATIENT.GET_LIST_PATIENT_BY_USER_ID}`,
    config
  );
};

export const checkInsurance = (baseUrl, data) => {
  return api.postHttpRequestAsync(baseUrl + PATIENT.CHECK_INSURANCE, data);
};

export const apiRequestMedproPatientList = (baseUrl, info) => {
  return api.getHttpRequestAsync(
    `${baseUrl}/patient?user_id=${info.user_id}&access_token=${info.access_token}&hospital_id=${info.hospital_id}`
  );
};

export const apiRequestMedproSuggestionPatientList = (baseUrl, info) => {
  return api.getHttpRequestAsync(
    `${baseUrl}/patient/suggestions?user_id=${info.user_id}&access_token=${info.access_token}&hospital_id=${info.hospital_id}`
  );
};

export const apiVerifyPhonePatient = (baseUrl, data) => {
  const url = createQueryString(
    `${baseUrl}${PATIENT.VERIFY_PHONE_PATIENT}`,
    data
  );
  return api.postHttpRequest(url);
};

export const apiVerifyInfoPatient = (baseUrl, data) => {
  const url = createQueryString(
    `${baseUrl}${PATIENT.VERIFY_INFO_PATIENT}`,
    data
  );
  return api.postHttpRequest(url);
};

export const apiCheckRequirePatient = (baseUrl, params) => {
  const url = createQueryString(`${baseUrl}${PATIENT.CHECK_REQUIRE_PATIENT}`);
  return api.getHttpRequestAsync(url, {
    headers: { partnerid: params.partnerId }
  });
};
