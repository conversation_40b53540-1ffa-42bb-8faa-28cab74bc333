import * as types from "~/store/booking/bookingType";

export const getMedicalBills = data => {
  return {
    type: types.GET_MEDICAL_BILLS,
    data
  };
};

export const getMedicalBillDetail = data => {
  return {
    type: types.GET_MEDICAL_BILL_DETAIL,
    data
  };
};

export const cancelMedicalBill = data => {
  return {
    type: types.CANCEL_MEDICAL_BILL,
    data
  };
};
export const clearCanceledBookingMessage = () => {
  return {
    type: types.CLEAR_CANCELED_BOOKING_MESSAGE
  };
};

export const resendSMS = data => {
  return {
    type: types.RESEND_SMS,
    data
  };
};

export const submitBooking = postData => {
  return {
    type: types.SUBMIT_BOOKING,
    postData
  };
};

export const loadingSubmitBooking = () => {
  return {
    type: types.LOADING_SUBMIT_BOOKING
  };
};

export const submitBookingSuccess = data => {
  return {
    type: types.SUBMIT_BOOKING_SUCCESS,
    data
  };
};

export const submitBookingFail = error => {
  return {
    type: types.SUBMIT_BOOKING_FAIL,
    error
  };
};

export const resetErrorOfSubmitBooking = () => {
  return {
    type: types.RESET_ERROR_SUBMIT_BOOKING
  };
};

export const resetIsRedirectToPaymentSupportAndPaymentOfflinePage = () => {
  return {
    type: types.RESET_IS_REDIRECT_TO_PAYMENT_SUPPORT_AND_PAYMENT_OFFLINE_PAGE
  };
};

export const checkDuplicateBooking = () => {
  return { type: types.CHECK_DUPLICATE_BOOKING };
};

export const resetDuplicateBooking = () => {
  return { type: types.RESET_DUPLICATE_BOOKING };
};
