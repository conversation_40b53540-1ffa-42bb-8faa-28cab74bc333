import React, { Component } from "react";
import { connect } from "react-redux";
import { Redirect } from "react-router-dom";
import Loadable from "react-loadable";
import { get, find } from "lodash";
import qs from "qs";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import {
  resetRedirectPatientForm,
  resetPatientForm
} from "~/store/patientForm/patientFormAction";
// import { currentEnv } from "~/configs";
// import { getInfoFollowHospital } from "~/utils/flowRouting";
import {
  resetData,
  getPatientByMSBN,
  changePatientNumber,
  resetFormGetPatienByMSBN,
  hideLoading
} from "~/store/patient/patientAction";

import {
  resetCity,
  resetDistrict,
  resetWard,
  requestAllCareers,
  requestAllNation,
  requestAllCountries,
  resetSelectedCountry,
  requestAllRelationShip,
  onChangeNation,
  onChangeCountry,
  onChangeCity,
  onChangeDistrict
} from "~/store/resource/resourceAction";
import Modal from "~/components/common/molecules/Modal";
import AppId from "~/utils/partner";
import { urlImgPatientCode } from "~/utils/manageResource";

import { partnerInfo } from "~/configs/partnerDetails";
const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

// const apiurl = currentEnv.RESTFULL_API_URL;
// const bienlai = apiurl + "/st/hospitals/bienlai_trungvuong.jpg";

const PatientCreateMobile = Loadable({
  loader: () => import(/* mobile */ "~/components/desktop/pages/PatientCreate"),
  loading: LoadableLoading
});
const PatientCreateDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/PatientCreate"),
  loading: LoadableLoading
});

class PatientCreate extends Component {
  constructor(props) {
    super(props);
    const activeItem = get(
      this.props,
      "history.location.state.activeItem",
      "1"
    );
    this.state = {
      activeItem: AppId === "115" ? "2" : activeItem,
      modalViewImage: false,
      errorMessage: ""
    };
  }

  method = {
    toggle: tab => {
      this.setState({
        activeItem: tab
      });
    },

    getPatientCallback: () => {
      const {
        history: {
          location: { search }
        }
      } = this.props;
      this.props.history.push("/xac-thuc-dien-thoai-benh-nhan" + search);
    },

    checkPatientNumber: () => {
      this.props.handleGetPatient();
    },

    toggleModalViewImage: () => {
      this.setState({
        modalViewImage: !this.state.modalViewImage
      });
    }
  };

  properties = {
    dataContent: {
      "115": {
        id: "115",
        title: "Hồ sơ bệnh nhân",
        tab1: false,
        tab2: true,
        titleTab1: "",
        titleTab2: "",
        titleInside: "Nhập mã số bệnh nhân"
      },
      others: {
        id: "1",
        title: "Tạo mới hồ sơ",
        tab1: true,
        tab2: true,
        titleTab1: "Chưa từng khám",
        titleTab2: "Đã từng khám",
        titleInside: "Nhập mã số bệnh nhân / Mã số BHYT"
      }
    }
  };

  static getDerivedStateFromProps = (nextProps, prevState) => {
    if (nextProps.errorMessage !== prevState.errorMessage) {
      return { errorMessage: nextProps.errorMessage };
    }
    return null;
  };

  componentWillUnmount() {
    this.props.resetRedirectPatientForm();
    this.props.handleResetData();
    this.props.resetPatientForm();
  }

  componentDidMount() {
    const {
      formData,
      isCreateWithSuggestInfo,
      resetCity,
      resetDistrict,
      resetWard,
      requestAllCountries,
      requestAllCareers,
      requestAllRelationShip,
      requestAllNation,
      resetFormGetPatienByMSBN,
      hideLoading,
      onChangeCountry,
      onChangeCity,
      onChangeDistrict
    } = this.props;
    resetCity();
    resetDistrict();
    resetWard();
    requestAllCountries();
    requestAllCareers();
    requestAllRelationShip();
    requestAllNation();
    hideLoading();
    if (!isCreateWithSuggestInfo) {
      resetFormGetPatienByMSBN();
    }
    onChangeCountry({
      id: formData.country_code.value
    });
    if (formData.city_id.value) {
      onChangeCity({
        id: formData.city_id.value
      });
    }
    if (formData.district_id.value) {
      onChangeDistrict({
        id: formData.district_id.value
      });
    }
    // this.props.onChangeNation(defaultNation);
  }

  render() {
    const {
      device,
      redirectToChooseCalendar,
      redirectToChooseProfile,
      history: {
        location: { search }
      },
      user
    } = this.props;

    if (!user) {
      return <Redirect to="/" />;
    }

    const queryString = qs.parse(search, {
      ignoreQueryPrefix: true
    });

    if (
      queryString.back &&
      (redirectToChooseCalendar || redirectToChooseProfile)
    ) {
      this.props.resetRedirectPatientForm();
      return <Redirect push to={queryString.back} />;
    }

    if (redirectToChooseCalendar) {
      return <Redirect push to="/chon-lich-kham" />;
    } else if (redirectToChooseProfile) {
      return <Redirect push to="/chon-ho-so" />;
    }

    return (
      <React.Fragment>
        {device === "mobile" ? (
          <PatientCreateMobile
            {...this.props}
            {...this.method}
            {...this.state}
            {...this.properties}
          />
        ) : (
          <PatientCreateDesktop
            {...this.props}
            {...this.method}
            {...this.state}
            {...this.properties}
          />
        )}
        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={this.state.modalViewImage}
          toggle={this.method.toggleModalViewImage}
          centered
          className="centered"
          image={urlImgPatientCode}
          title="Xem cách tìm mã số bệnh nhân"
        />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    patientForm: {
      redirectToChooseCalendar,
      redirectToChooseProfile,
      info: formData,
      isCreateWithSuggestInfo
    },
    patient: {
      selectedPatient,
      redirectToCheckPatientPhone,
      errorMessage,
      patientNumber
    },
    user: { info }
  } = state;
  return {
    device: type,
    redirectToChooseCalendar,
    redirectToChooseProfile,
    user: info,
    selectedPatient,
    redirectToCheckPatientPhone,
    errorMessage,
    patientNumber,
    formData,
    isCreateWithSuggestInfo
  };
};

const mapDispatchToProps = dispatch => ({
  resetRedirectPatientForm: () => {
    dispatch(resetRedirectPatientForm());
  },
  resetPatientForm: () => {
    dispatch(resetPatientForm());
  },
  requestAllCountries: () => {
    dispatch(requestAllCountries());
  },
  requestAllCareers: () => {
    dispatch(requestAllCareers());
  },
  requestAllRelationShip: () => {
    dispatch(requestAllRelationShip());
  },
  requestAllNation: () => {
    dispatch(requestAllNation());
  },
  handleChangePatientNumber: value => {
    dispatch(changePatientNumber(value));
  },
  handleGetPatient: () => {
    dispatch(getPatientByMSBN());
  },
  handleResetData: () => dispatch(resetData()),
  resetSelectedCountry: () => {
    dispatch(resetSelectedCountry());
  },
  resetCity: () => {
    dispatch(resetCity());
  },
  resetDistrict: () => {
    dispatch(resetDistrict());
  },
  resetWard: () => {
    dispatch(resetWard());
  },
  onChangeNation: id => {
    dispatch(onChangeNation(id));
  },
  onChangeCountry: selectedCountry => {
    dispatch(onChangeCountry(selectedCountry));
  },
  onChangeCity: selectedCity => {
    dispatch(onChangeCity(selectedCity));
  },
  onChangeDistrict: selectedDistrict => {
    dispatch(onChangeDistrict(selectedDistrict));
  },
  resetFormGetPatienByMSBN: () => {
    dispatch(resetFormGetPatienByMSBN());
  },
  hideLoading: () => {
    dispatch(hideLoading());
  }
});

const PatientCreateHelmet = withTitle({
  component: PatientCreate,
  title: `${hospitalName.value} | Tạo mới hồ sơ bệnh nhân`
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PatientCreateHelmet);
