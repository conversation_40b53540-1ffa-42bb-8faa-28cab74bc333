import {
  MDBCard,
  MDBCardBody,
  MDBCardGroup,
  MDBCardText,
  MDBCol,
  MDBContainer,
  MDBRow,
  MDBView
} from "mdbreact";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Image } from "semantic-ui-react";
import styles from "./style.module.scss";
import cx from "classnames";
import { apiNews } from "~/utils/constants";
import moment from "moment";

const PostRelated = () => {
  const [data, setdata] = useState([]);

  useEffect(() => {
    const getdata = async () => {
      const url = apiNews + `/posts?_sort=updated_at:DESC&_limit=3`;
      const x = await fetch(url);
      const dta = await x.json();
      setdata(dta);
    };
    getdata();
  }, []);

  return (
    <MDBContainer className="my-5 p-0">
      <MDBRow>
        <MDBCol>
          <h5 className={cx(styles.tag_news, "h5-responsive")}>
            <PERSON><PERSON><PERSON> viết mới nhất
          </h5>
        </MDBCol>
      </MDBRow>
      <MDBRow className="mt-5">
        <MDBCardGroup column className={styles.card_group}>
          {data?.map(({ id, title, image, slug, updated_at }) => {
            const img = image[0]
              ? apiNews + image[0]?.url
              : "/img/imgweb/670x402.jpg";
            return (
              <>
                <MDBCard
                  key={id}
                  className={cx(styles.card_news, "mx-3 shadow-none border-0")}
                >
                  <MDBView className={styles.view}>
                    <Link to={"/tin-tuc/" + slug}>
                      <Image className="img-fluid" src={img} alt={img} />
                    </Link>
                  </MDBView>
                  <MDBCardBody className={styles.card_body}>
                    <MDBCardText className={styles.title}>
                      <Link to={"/tin-tuc/" + slug}>{title}</Link>
                    </MDBCardText>
                    <div className={styles.tag}>
                      {moment(updated_at).format("DD/MM/YYYY, hh:mm")}
                    </div>
                  </MDBCardBody>
                </MDBCard>
              </>
            );
          })}
        </MDBCardGroup>
      </MDBRow>
    </MDBContainer>
  );
};

export default PostRelated;
