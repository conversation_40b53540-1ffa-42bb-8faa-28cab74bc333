@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/button.scss";
@import "src/assets/scss/pro-custom/card.scss";
@import "src/assets/scss/pro-custom/listgroup.scss";
@import "src/assets/scss/extend/placeholder.scss";

.card_minhanh {
  &.active {
    border-color: #db2233 !important;
  }
  &:hover,
  &:focus {
    .list_group {
      li {
        &:first-child {
          .fullname {
            color: red;
          }
        }
      }
    }
  }
  .list_group {
    li {
      &:first-child {
        .fullname {
          color: #db2233;
        }
      }
    }
  }
}

.card {
  overflow: visible;
  padding: 15px;
  transition: 0.5s;
  margin-bottom: 40px;
  box-shadow: none;
  &:hover,
  &:focus {
    box-shadow: 0 0.3rem 1.525rem -0.375rem rgba(0, 0, 0, 0.1);
    cursor: pointer;
    .list_group {
      li {
        .fullname {
          color: #0352cc;
        }
      }
    }
  }
  &.active {
    border-color: #0352cc;
  }
}
.list_group {
  li {
    .column1 {
      color: #627792;
    }
    color: $main_black;
    padding-bottom: 0;
    &:first-child {
      .fullname {
        color: #0352cc;
        font-weight: 500;
        transition: 0.3s linear;
      }
    }
    i {
      width: 20px;
      text-align: center;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.has_msbn {
  padding: 20px 15px 15px 15px;
}
.action_edit_remove {
  border-top: 2px solid #dfe3eb;
  padding-top: 30px;
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  > div {
    &:first-child {
      display: flex;
      > button {
        margin-right: 15px;
        @media screen and (max-width: 376px) {
          margin-right: 10px;
        }
      }
      @media screen and (max-width: 321px) {
        justify-content: space-between;
      }
    }
  }
  .action_right {
    i {
      margin-left: 7px;
      margin-right: 0;
    }
    button {
      padding-left: 35px;
      padding-right: 35px;
      @media screen and (max-width: 321px) {
        width: 100%;
        margin-top: 10px;
        display: inline-block;
      }
      @media screen and (max-width: 376px) {
        padding-left: 25px;
        padding-right: 25px;
      }
    }
  }
  @media screen and (max-width: 321px) {
    display: block;
  }
}

.msbn {
  position: absolute;
  top: -14px;
  left: 10px;
  border: 0;
  background: #54dd76 !important;
  color: #fff !important;
  padding: 2px 10px !important;
  border-radius: 3px !important;
  font-weight: 500;
}
