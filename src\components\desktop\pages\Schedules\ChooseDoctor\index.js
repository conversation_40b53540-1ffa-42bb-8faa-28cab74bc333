import React, { Component } from "react";
import {
  MD<PERSON>ow,
  MDBCol,
  MDBContainer,
  MDBCard,
  MDBCardHeader,
  MDBCardBody
} from "mdbreact";
import PatientInfomationBooking from "~/components/desktop/molecules/PatientInfomationBooking";
import TagName from "~/components/common/atoms/TagName";
import { withRouter } from "react-router-dom";
import PKHBtn from "~/components/common/atoms/Button";
import styles from "./style.module.scss";
import FilterList from "~/components/common/molecules/FilterDoctorList";
import ListDoctor from "~/components/common/molecules/ListDoctor";
import cx from "classnames";
import partnerId from "~/utils/partner";
class ChooseSubject extends Component {
  render() {
    const {
      handleGoBack,
      listDoctorFilter,
      handleSeeMoreDoctor,
      isLastPage,
      filterSubject
    } = this.props;

    return (
      <div
        className={cx(
          styles.wapper_page_desktop,
          styles["wapper_page_desktop_" + partnerId]
        )}
        onClick={() => {
          filterSubject({ target: { value: "" } });
        }}
      >
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="3">
              <PatientInfomationBooking />
            </MDBCol>
            <MDBCol md="12" lg="9">
              <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between",
                      "title_header_mobile"
                    ]}
                  >
                    <span>Vui lòng chọn bác sĩ</span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody className={styles.card_body}>
                  <div className={styles.choose_specialty}>
                    <FilterList
                      titleFieldSearch="Tìm nhanh bác sĩ"
                      {...this.props}
                    />
                    <ListDoctor
                      listDoctorFilter={listDoctorFilter}
                      isLastPage={isLastPage}
                      handleSeeMoreDoctor={handleSeeMoreDoctor}
                    />
                  </div>
                </MDBCardBody>
                <div className={styles.next_prev}>
                  <PKHBtn backdesktop="backdesktop" onClick={handleGoBack}>
                    Quay lại
                  </PKHBtn>
                </div>
              </MDBCard>
              <div ref={this.scrollToDiv} />
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default withRouter(ChooseSubject);
