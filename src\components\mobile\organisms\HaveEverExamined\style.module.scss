@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-mobile/form.scss";
@import "src/assets/scss/pro-custom/button.scss";

.tag {
  font-size: 18px;
  font-weight: bold;
}

.have_ever_examined {
  display: flex;
  gap: 24px;
  padding: 0 20px 24px;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 991px) {
    min-height: auto;
    flex-direction: column;
  }

  .left {
    width: 40%;
    border-radius: 8px;
    box-shadow: 0 2px 8px #00000040;
    background: #fff;
    padding: 24px;

    @media (max-width: 991px) {
      width: 100%;
      margin-top: 20px;
    }
  }

  .right {
    width: 60%;
    border-radius: 8px;
    box-shadow: 0 2px 8px #00000040;
    background: #fff;
    padding: 24px;

    @media (max-width: 991px) {
      width: 100%;
    }

    .title {
      font-size: 18px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .formWrapper {
      .form {
        max-width: unset;
      }
    }
  }
}
.form_have_ever_examined {
  .form_group {
    margin-bottom: 15px;
  }
}

.url_scan_qr_code {
  text-decoration: underline;
  color: blue;
}
