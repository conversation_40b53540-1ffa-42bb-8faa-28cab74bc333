@import "src/assets/scss/custom-variables.scss";

.loading {
  min-height: 70vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  align-content: center;
  opacity: 0.5;
}

.listCoupon {
  display: flex;
  gap: 1rem;
}

.sliderCoupon {
  width: 100vw;
  min-width: 360px;
  display: flex;
  justify-content: center;

  position: relative;
  > button {
    position: absolute;
    top: 0;
    &:first-child {
      left: 0;
    }
    &:last-child {
      right: 0;
    }
  }

  > div {
    // margin-top: 35px;
    > div {
      display: flex;

      @media (min-width: 560px) {
        justify-content: center;
      }
      > div {
        display: flex !important;

        height: auto;
        // align-items: center; //optional
        // justify-content: center; //optional
        padding: 15px;
        > div {
          cursor: pointer;
          width: 100%;
        }
      }
    }
  }
}
.notice {
  max-width: 360px;
  margin: auto;
  margin-top: -20px;
  margin-bottom: 12px;
  background-color: #ffebec;
  padding: 8px;
  border-radius: 8px;
  p {
    display: flex;
    justify-content: flex-start;
    gap: 4px;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.94px;

    color: #f5222d;
    margin-bottom: 0;
    svg {
      min-width: 18px;
    }
  }
  .btnMore {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 2px;
    text-align: end;
    font-weight: 400;
    font-style: italic;
    font-size: 12px;
    line-height: 14.52px;
    letter-spacing: 0%;
    text-align: right;
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
}
