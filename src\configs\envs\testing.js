import partner from "~/utils/partner";

const checkApiByParnerId = () => {
  const hostname = window.location.hostname;
  if (hostname.includes("beta")) {
    return "https://api-111.medpro.com.vn:5000";
  }
  if (hostname.includes("hotfix")) {
    return "https://api-hotfix.medpro.com.vn:5000";
  }
  switch (partner) {
    default:
      // return "https://api-111.medpro.com.vn:5000";
      // return "https://api-hotfix.medpro.com.vn:5000";
      return "https://medpro-api-v3-testing.medpro.com.vn";
  }
};

const checkApiLogin = () => {
  const hostname = window.location.hostname;

  if (hostname.includes("hotfix")) {
    return `https://id-v121.medpro.com.vn/check-phone`;
  }

  if (hostname.includes("beta")) {
    return `https://id-v121.medpro.com.vn/check-phone`;
  }

  switch (partner) {
    default:
      // return `https://id-v121.medpro.com.vn/check-phone`;
      return `https://id-testing.medpro.com.vn/check-phone`;
  }
};

export const RESTFULL_API_URL = checkApiByParnerId();
export const MEDPRO_LOGIN = checkApiLogin();

export const DOMAIN_SHARE_URL = `https://testing.medpro.com.vn`;

export const COOKIE_EXPIRES = 43200; // 12*60*60; // 12 tiếng

export const SOCKET_CHAT = "wss://alpha-api.medpro.com.vn/cable";

export const PAYMENT_HUB_URL =
  "https://payment-gateway-qrcode-testing.medpro.com.vn";

export const CSKH_API_URL = "https://*************:8080";

export const STATIC_CONTENT_API_URL = "https://bo-api-testing.medpro.com.vn";

export const BO_WEB = "https://bo-testing.medpro.com.vn";

export const MEDPRO_WEB = "https://testing.medpro.com.vn";
