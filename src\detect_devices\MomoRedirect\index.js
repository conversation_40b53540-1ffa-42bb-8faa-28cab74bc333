/* eslint-disable no-useless-constructor */
import _ from "lodash";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Redirect } from "react-router-dom";
import {
  requestAllFeatures,
  resetAllFeature
} from "~/store/features/featuresActions";
import { requestHospitalList } from "~/store/hospital/hospitalActions";
import { createPartnerUser } from "~/store/partnerUser/actions";
import { setPartnerId } from "~/store/totalData/actions";
import AppId from "~/utils/partner";
import { MOMO } from "~utils/constants";
const queryString = require("query-string");
class PartnerRedirect extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount() {
    this.props.resetAllFeature();
  }

  componentDidMount() {
    if (AppId === MOMO) {
      const paramsId = this.props.match.params.walletId;
      const parsed = queryString.parse(window.location.search);
      const partnerId = _.get(parsed, "partnerId", "medpro");

      this.props.setPartnerId(partnerId);
      this.props.createPartnerUser(paramsId, partnerId);
      this.props.requestHospitalList();
      this.props.requestAllFeatures();
    }
  }

  render() {
    if (AppId !== MOMO) return <Redirect to="/" />;
    return null;
  }
}

export default connect(null, {
  createPartnerUser,
  setPartnerId,
  resetAllFeature: resetAllFeature,
  requestHospitalList: requestHospitalList,
  requestAllFeatures: requestAllFeatures
})(PartnerRedirect);
