import React, { Component } from "react";
import { connect } from "react-redux";
import { with<PERSON>out<PERSON> } from "react-router";
import Breadcrumb from "~/components/desktop/atoms/Breadcrumb";
import HaveEverExamined from "~/components/mobile/organisms/HaveEverExamined";
import {
  changePatientNumber,
  getPatientByMSBN
} from "~/store/patient/patientAction";

class HaveEverExamPage extends Component {
  constructor(props) {
    super(props);

    this.state = { modalViewImage: false };
  }

  method = {
    toggle: tab => {
      this.setState({
        activeItem: tab
      });
    },

    getPatientCallback: () => {
      const {
        history: {
          location: { search }
        }
      } = this.props;
      this.props.history.push("/xac-thuc-dien-thoai-benh-nhan" + search);
    },

    checkPatientNumber: () => {
      const { user, patientNumber } = this.props;
      const data = {
        user_id: user.user_id,
        access_token: user.access_token,
        msbn: patientNumber,
        callback: this.method.getPatientCallback
      };
      this.props.handleGetPatient(data);
    },

    toggleModalViewImage: () => {
      this.setState({
        modalViewImage: !this.state.modalViewImage
      });
    }
  };

  render() {
    const { selectedHospital } = this.props;
    const listItemBreadcumb = [
      { title: "Trang chủ", url: "/" },
      {
        title: selectedHospital?.name,
        url: "/chon-benh-vien"
      },
      {
        title: "Đã từng khám",
        url: "#"
      }
    ];

    return (
      <>
        <Breadcrumb className="head" type="text" listItem={listItemBreadcumb} />
        <HaveEverExamined {...this.method} {...this.props} />
      </>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { info },
    patientForm: { redirectToPatientUpdate },
    patient: { patientList, loading, data },
    hospital: { selectedHospital }
  } = state;
  return {
    selectedHospital,
    user: info,
    patients: patientList,
    redirectToPatientUpdate,
    loading,
    data
  };
};

const mapDispatchToProps = {
  handleChangePatientNumber: changePatientNumber,
  handleGetPatient: getPatientByMSBN
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(HaveEverExamPage));
