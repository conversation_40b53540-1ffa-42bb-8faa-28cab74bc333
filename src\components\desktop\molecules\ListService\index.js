import React, { Component } from "react";
import { MDBListGroup, MDBListGroupItem } from "mdbreact";
import { Link } from "react-router-dom";
import LazyLoad from "react-lazyload";
import { connect } from "react-redux";
import { Facebook } from "react-content-loader";
import cx from "classnames";
import {
  selectFeatureById,
  selectFeature
} from "~/store/features/featuresActions";
import styles from "./style.module.scss";
import {
  selectHospital,
  changeBookingFlow,
  getBookingTree
} from "~/store/totalData/actions";
import { getHistoryPayment } from "~/store/fee/feeActions";
import AppId from "~/utils/partner";
import Modal from "~/components/common/molecules/Modal";
import { OK, CLOSE } from "~/utils/constants";
class ListServiceDesktop extends Component {
  constructor(props) {
    super(props);
    this.state = {
      toggleModalConfirm: false,
      typeFeature: "",
      message: "",
      isShowMessage: false
    };
  }

  handleOpenToggleModalConfirm = type => {
    this.setState({ toggleModalConfirm: true, typeFeature: type });
  };

  handleCloseToggleModalConfirm = () => {
    this.setState({ toggleModalConfirm: false, typeFeature: "" });
  };

  handleChooseFeature = type => {
    const { allListHospital, partnerId } = this.props;
    const infoHos = allListHospital.find(e => e.partnerId === partnerId);

    const { message } = infoHos;

    if (message) {
      this.setState({
        isShowMessage: true,
        message
      });
    } else {
      this.props.selectFeature(type);
    }
  };

  toggleModalChoosePatient = () => {
    this.setState({
      isShowMessage: !this.state.isShowMessage
    });
  };

  renderIsAuthenticated = item => {
    const imageErrorSrc = "/assets/icon/serviceDefault.svg";
    const urlImage = item.image || imageErrorSrc;

    if (AppId === "medpro") {
      return (
        <Link
          to="/chon-benh-vien"
          onClick={() => {
            this.props.selectFeatureById(item.id);
          }}
        >
          <LazyLoad height={65}>
            <img
              src={urlImage}
              onError={e => {
                e.target.src = imageErrorSrc;
              }}
              alt=""
            />
          </LazyLoad>
          <span>{item.name}</span>
        </Link>
      );
    } else {
      return (
        <Link
          to="#"
          onClick={() => {
            item.type === "payment.fee"
              ? this.handleOpenToggleModalConfirm(item.type)
              : this.handleChooseFeature(item.type);
          }}
        >
          <LazyLoad height={65}>
            <img
              src={urlImage}
              onError={e => {
                e.target.src = imageErrorSrc;
              }}
              alt=""
            />
          </LazyLoad>
          <span>{item.name}</span>
        </Link>
      );
    }
  };

  renderServicesList = () => {
    const { list = [] } = this.props;
    const serviceListNode = list.map((item, i) => {
      return item.status ? (
        <MDBListGroupItem key={i}>
          {this.renderIsAuthenticated(item)}
        </MDBListGroupItem>
      ) : null;
    });
    return serviceListNode;
  };

  renderModalChooseHospital = () => {
    return (
      <Modal
        iconTitle={<i className="fal fa-bell" />}
        modal={this.state.isShowMessage}
        toggle={() => this.toggleModalChoosePatient()}
        title="Thông báo"
        children={this.state.message}
        centered
        className="centered"
      />
    );
  };

  renderModalFee = () => {
    return (
      <Modal
        modal={this.state.toggleModalConfirm}
        toggle={() => this.handleCloseToggleModalConfirm()}
        children="Đây là tính năng có tính phí. Bạn có muốn tiếp tục?"
        centered
        className="centered"
        cancelText={CLOSE}
        okText={OK}
        onCancel={this.handleCloseToggleModalConfirm}
        onOk={() => this.handleChooseFeature(this.state.typeFeature)}
        footer
        footerConfirm
      />
    );
  };

  render() {
    const { loading } = this.props;
    return (
      <React.Fragment>
        <MDBListGroup
          className={cx(
            styles.list_group,
            styles.list_service_mobile,
            styles["list_group_" + AppId]
          )}
        >
          {loading ? <Facebook /> : this.renderServicesList()}
        </MDBListGroup>

        {this.renderModalFee()}
        {this.renderModalChooseHospital()}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { IsAuthenticated },
    features: {
      allFeatures: { loading, list }
    },
    hospital: {
      hospitalList: { data: allListHospital }
    },
    totalData: { partnerId }
  } = state;
  return {
    partnerId,
    list,
    loading,
    IsAuthenticated,
    allListHospital
  };
};
export default connect(mapStateToProps, {
  getHistoryPayment,
  selectFeatureById,
  selectHospital,
  changeBookingFlow,
  getBookingTree,
  selectFeature
})(ListServiceDesktop);
