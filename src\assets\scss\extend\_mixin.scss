@import "src/assets/scss/custom-variables.scss";
@mixin placeholder {
  &::-webkit-input-placeholder {
    @content;
  }
  &::-moz-placeholder {
    @content;
  }
  &::-moz-placeholder {
    @content;
  }
  &::-ms-input-placeholder {
    @content;
  }
}
/* Mixins */
@mixin btn-theme($btn-color) {
  border-color: darken($btn-color, 20%);
  background-color: $btn-color;
  &:hover {
    background-color: darken($btn-color, 6%);
  }
}
@mixin trans($val...) {
  -webkit-transition: $val;
  -moz-transition: $val;
  -o-transition: $val;
  transition: $val;
}
