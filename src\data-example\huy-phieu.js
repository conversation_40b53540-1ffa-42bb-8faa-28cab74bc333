export const data_huy_phieu_kham = [
  {
    extraInfo: {
      transactionIdV1: "TTDev-**********",
      methodIdV1: 9,
      booking: {
        id: 3076,
        platform: "pc",
        app: "medpro",
        booking_date: "2022-10-07",
        transaction_code_gd: "A2209193074",
        payment_id: null,
        schedule_id: 129782,
        patient_id: 1162,
        user_id: 549,
        booking_time_id: 550393,
        booking_number: 30,
        booking_phone: "**********",
        bhyt_accept: 0,
        email: "<EMAIL>",
        status: 0,
        status_process: 0,
        refund_status: 0,
        is_deleted: 0,
        is_editable: 0,
        is_changed: 0,
        date_expired: "2022-09-19T12:58:57.000+07:00",
        sms_count: 0,
        related: null,
        bv_booking_time: "09:00",
        description:
          "Vui lòng đến trực tiếp phòng khám trướ<PERSON> gi<PERSON> hẹn 15-30 phút để khám bệnh.",
        booking_note:
          "Ghi chú: <PERSON>ế<PERSON> khám bệnh chỉ có gi<PERSON> trị trong ngày khám từ <b>6h30 - 16h30.</b>",
        booking_time: {
          from: 9,
          to: 10
        },
        patient: {
          id: 1162,
          bvdhyd_msbn: null,
          medpro_id: "MP-220627058450",
          surname: "TEST NGÀY",
          name: "SINH",
          cmnd: "",
          sex: 1,
          dantoc_id: 1,
          profession_id: null,
          birthdate: "1989-07-02",
          birthyear: 1989,
          job: null,
          bhyt: null,
          mobile: "**********",
          email: "<EMAIL>",
          country_code: "VIE",
          city_id: 12,
          district_id: 94,
          ward_id: 4356,
          address: "123",
          note: null,
          is_medpro: 0,
          origin_id: null,
          mobile_censored: "032****167",
          mobile_secure: "f97180a6f60dde93d5c326f5da68d087",
          country: {
            id: 203,
            code: "VIE",
            name: "Việt Nam"
          },
          city: {
            id: 12,
            name: "Cao Bằng"
          },
          district: {
            id: 94,
            name: "H. Hoà An"
          },
          ward: {
            id: 4356,
            name: "Xã Hà Trì"
          },
          dantoc: {
            id: 1,
            name: "Kinh"
          }
        },
        user: {
          id: 549,
          username: "+84908291406",
          email: null,
          phone: null,
          fullname: "+84908291406",
          money: 0
        },
        hospital: {
          id: 2,
          name: "Bệnh viện Đại học Y Dược TPHCM",
          short_name: "BV Đại Học Y Dược",
          sms_name: "DHYD CS1",
          image: "logo-yduoc.png",
          status: 1,
          city_id: 62,
          address: "215 Hồng Bàng, Phường 11, Quận 5, TP Hồ Chí Minh",
          base_url: "https://alpha-api.medpro.com.vn/api",
          lat: null,
          long: null,
          hotline: null
        },
        subject: {
          id: 32,
          name: "HÔ HẤP",
          short_name: null,
          code: "PQ",
          is_cls: 0,
          is_internal: 0,
          price: 150000
        },
        room: {
          id: 129785,
          name: "Phòng 54",
          description: "Khu B",
          hospital_id: 2,
          bvdhyd_room_id: 81,
          is_old: 0
        },
        doctor: {
          id: 427,
          name: "Hoàng Đình Hữu Hạnh",
          name_only: "Hạnh",
          birthyear: 0,
          role: "THS.BS.",
          sex: 1,
          khoa: null,
          avatar: null,
          lichkham: null
        },
        ptime: {
          id: 129782,
          hour_from: 6,
          hour_to: 11,
          day_from: 1,
          day_to: 31,
          month_from: 1,
          month_to: 12,
          buoi: 1,
          weekday: "5",
          is_old: 0
        },
        subject_id: 32,
        room_id: 129785,
        doctor_id: 427,
        buoi: 1
      }
    },
    telemedInfoBooking: {
      files: [],
      dataInfo: []
    },
    visible: true,
    bookingNote: "ABCDE - ANCVNS",
    noPayment: false,
    sharePayment: false,
    serviceType: "",
    idReExam: "",
    syncBookingType: 2,
    syncBookingIdV1: 0,
    syncUserIdV1: 0,
    syncPatientIdV1: 0,
    promotion: false,
    promotionChilds: [],
    numberConfirmed: false,
    isChanged: false,
    _id: "6327f67e1fb16a001a66a829",
    serviceId: "umc_service-150000",
    subjectId: "umc_PQ",
    roomId: "eeae699f42c24d768f54c99b8c8e90af",
    doctorId: "umc_427",
    endTime: "2022-09-19T05:56:30.128Z",
    bookingSlotId:
      "221007_umc_service-150000_umc_PQ_umc_81_umc_427_1_09:00_umc",
    treeId: "DATE",
    id: "c5cf4e0e8f284e65958fe51536270855",
    bookingCode: "T220919CQNP4K",
    bookingCodeV1: "A2209193074",
    sequenceNumber: 30,
    date: "2022-10-07T02:00:00.000Z",
    transactionId: "TTDev-**********",
    appId: "medpro",
    partner: {
      banner: "",
      status: 1,
      subjects: [],
      services: [],
      doctors: [],
      rooms: [],
      message: "",
      sortOrder: 1,
      deliveryStatus: 3,
      hospitalType: 1,
      homeBannerAction: [],
      _id: "61d508b452350f0019e14548",
      id: "7c09a609229d4f408e38ea3672cdcc96",
      partnerId: "umc",
      city_id: "trungvuong_79",
      name: "Bệnh Viện Đại Học Y Dược TP HCM - Cơ sở chính",
      city: "5ede15c33274b52923760e26",
      features: [
        {
          message: "",
          disabled: false,
          bookingLimit: 3,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa5b",
          mobileIcon: "",
          type: "booking.date",
          name: "Đặt khám theo chuyên khoa",
          image:
            "https://bo-api.medpro.com.vn:5000/static/images/medpro/app/image/featuremain1.png?t=11111",
          priority: 1,
          status: true,
          mobileStatus: true,
          __v: 0,
          mobileRoute: "/dat-lich-kham",
          webRoute: "/dat-lich-kham",
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-08-08T02:21:28.350Z"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 3,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa5c",
          mobileIcon: "",
          type: "booking.doctor",
          name: "Đặt khám theo Bác Sĩ",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/fd27f55b1df944d88570878f32c80063_djat_kham_theo_bac_si.png",
          priority: 2,
          status: true,
          mobileStatus: true,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-07-06T09:29:53.233Z",
          mobileRoute: "dat-kham",
          webRoute: "dat-kham",
          customUrl: "",
          localeName: ""
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa5e",
          mobileIcon: "",
          type: "insurance.check",
          name: "Kiểm tra BHYT",
          image:
            "https://medpro-inside-testing.medpro.com.vn/static/images/choray/app/icon/main/mainfeature12.png",
          priority: 4,
          status: false,
          mobileStatus: false,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-07-13T04:06:40.737Z"
        },
        {
          message: "",
          disabled: true,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa60",
          mobileIcon: "",
          type: "payment.fee",
          name: "Thanh toán viện phí",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/23aed95794f145c6a827257af1dbc4ac_thanh_toan_vien_phi.png",
          priority: 6,
          status: true,
          mobileStatus: true,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-07-13T04:06:50.350Z",
          mobileRoute: "abc",
          webRoute: "abc"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa61",
          mobileIcon: "",
          type: "instruc",
          name: "Hướng dẫn đặt lịch",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/0522450bd71a44f28f21ed7979b7fe6d_huong_dan_djat_kham.png",
          priority: 7,
          status: false,
          mobileStatus: true,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-06-09T08:47:55.172Z",
          mobileRoute: "abc",
          webRoute: "abc",
          customUrl: "",
          displayIcon: "undefined",
          localeName: ""
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa62",
          mobileIcon: "",
          type: "callphone",
          name: "Đặt khám tổng đài\n 1900-2115",
          image:
            "https://bo-api.medpro.com.vn:5000/static/images/medpro/app/image/featuremain8.png?t=11111111",
          priority: 8,
          status: false,
          mobileStatus: true,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-03-30T10:11:21.737Z"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa65",
          name: "Lịch sử Thanh toán Viện phí",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/fdc894977756470d82bc3916e677fffe_lich_su_thanh_toan_vien_phi.png",
          priority: 11,
          status: true,
          type: "payment.fee.history",
          updatedAt: "2022-03-30T10:11:21.737Z",
          createdAt: "2022-03-30T10:11:21.737Z",
          mobileRoute: "abc",
          mobileStatus: true,
          webRoute: "abc"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa66",
          name: "Khai báo Y tế",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/8e3a71fa85b14ea98cc128881d26427f_khai_bao_y_te.png",
          priority: 9,
          status: true,
          type: "declare.yt",
          mobileStatus: true,
          updatedAt: "2022-03-30T10:11:21.737Z",
          createdAt: "2022-03-30T10:11:21.737Z",
          mobileRoute: "abc",
          webRoute: "abc"
        },
        {
          message:
            "Tính năng đang được cập nhật và phát triển. Vui lòng quay lại sau.",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa67",
          name: "Tư vấn khám bệnh từ xa",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/bffb329cc1f64c93a6361144a8a2f68a_tu_van_kham_benh_tu_xa.png",
          priority: 12,
          type: "telemed.online",
          status: false,
          mobileStatus: true,
          updatedAt: "2022-04-04T08:45:26.773Z",
          mobileRoute: "abc",
          webRoute: "abc",
          createdAt: "2022-03-30T10:11:21.737Z",
          mobileIcon:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/mobile/176632a3116941699e71833048fa5a31_tu_van_truc_tuyen.png"
        },
        {
          message: "Tính năng đang phát triển",
          disabled: true,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa68",
          name: "Tra cứu kết quả",
          type: "exam.profile",
          priority: 15,
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/8aa561ff877740378c3b8742d2b91c01_tra_cuu_ket_qua.png",
          webRoute: "1",
          mobileRoute: "1",
          status: true,
          mobileStatus: false,
          mobileIcon:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/mobile/bee035f150a14d2bb38f1b559232e61b_tra_cuu_ket_qua.png",
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-05-09T05:04:33.377Z",
          __v: 0,
          customUrl: "",
          localeName: ""
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa6a",
          name: "Tra cứu BHYT",
          image:
            "https://medpro-api-v2-testing.medpro.com.vn/st/feature/dv9.svg",
          priority: 8,
          status: true,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-06-03T04:27:43.470Z",
          __v: 0,
          type: "tracking.bhyt",
          mobileStatus: false,
          customUrl: "",
          displayIcon: "undefined",
          localeName: "",
          mobileRoute: "1",
          webRoute: "1"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 3,
          position: "IN",
          children: [],
          translate: [],
          _id: "611cbb24f473ed001911ac69",
          name: "Đăng ký thực hiện Cận lâm sàng",
          type: "booking.cls",
          priority: 0,
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/a41433de517146139d4ac2d7e6eb42bd_djat_lich_can_lam_sang.png",
          status: true,
          mobileStatus: true,
          createdAt: "2021-08-18T07:47:48.638Z",
          updatedAt: "2022-06-22T10:07:21.736Z",
          __v: 0,
          mobileIcon:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/mobile/562f954033f04e3ca782a88febf9aee7_djat_lich_can_lam_sang.png",
          mobileRoute: "1",
          webRoute: "1",
          localeName: "undefined",
          customUrl: "",
          displayIcon:
            "https://bo-api.medpro.com.vn:5000/static/images/medpro/app/image/featuremain3.png?t=111111111"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "616e3649156bac0019ab6786",
          name: "Tư vấn trực tuyến",
          type: "booking.telemed",
          priority: 12,
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/b04e9ee4e5634c3d9ac5707321c42250_tu_van_truc_tuyen.png",
          webRoute: "abc",
          mobileRoute: "abc",
          status: true,
          mobileStatus: true,
          mobileIcon:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/mobile/86a29796cd9a4233837284713a2d30ea_tu_van_truc_tuyen.png",
          createdAt: "2021-10-19T03:06:49.212Z",
          updatedAt: "2022-04-06T07:23:58.132Z",
          __v: 0,
          localeName: "undefined"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "5f375e2d60d7f73b38007a03",
          name: "Hóa đơn Điện tử",
          image:
            "https://medpro-api-v2-testing.medpro.com.vn/st/feature/hoa_don_dien_tu.svg",
          priority: 10,
          status: true,
          type: "e.invoice",
          createdAt: "2022-07-12T08:52:07.588Z",
          updatedAt: "2022-07-12T08:52:07.588Z"
        }
      ],
      oldVersionFeature: [],
      createdAt: "2022-01-05T02:55:48.902Z",
      updatedAt: "2022-08-08T02:21:28.351Z",
      __v: 23,
      email: "<EMAIL>",
      website: "http://medpro.vn",
      image:
        "https://bo-api-testing.medpro.com.vn/static/images/umc/web/logo.png?t=Wed%20Jan%2005%202022%2010:31:47%20GMT+0700%20(Indochina%20Time)",
      address: "215 Hồng Bàng, Phường 11, Quận 5, TP.HCM",
      sms_name: "BV DH Y DUOC",
      googleMap: "w",
      hotline: "êf",
      hotlineMedpro: "ề",
      workingDate: "ừ",
      workingTime: "w",
      contact: "Cơ sở chính",
      circleLogo:
        "https://bo-api-testing.medpro.com.vn/static/images/umc/web/logo.png?t=Wed%20Jan%2005%202022%2010:31:47%20GMT+0700%20(Indochina%20Time)"
    },
    partnerId: "umc",
    userId: "6163b88f7bb58900191fc07e",
    bookingId: "6f7bc70d-1eef-43be-8957-704c13556446",
    status: -2,
    patientId: "473176ca70d34d9bb4eeaa862898bf9b",
    patient: "62820ffa778373001be3c2f7",
    patientVersionId: "035b4acdc56840f587426b6e2f270445",
    patientVersion: "62875cb5512b83001a951ccb",
    orderId: "6bfab03abd664368a783ebdc284ca04c",
    checkInRoom: {
      name: null,
      id: null,
      type: null,
      roomType: null,
      description: "",
      room: null,
      bookingNote: "Không tìm thấy phòng khám",
      nextCombine: false,
      combineNodes: [],
      maxDay: null,
      image_url: null
    },
    room: "5ed06e5fbf9d04acb2c7dce2",
    bookingOrder: "6327f67d1fb16a001a66a827",
    bookingOrderId: "6bfab03abd664368a783ebdc284ca04c",
    service: "60404c8ebf9d04d8c708174d",
    subject: "6291b1cdbf9d04ecd1f55722",
    doctor: "5ee0a1f4bf9d045ed5c87455",
    platform: "pc",
    smsCode: "A2209193074",
    bookingsRelation: [
      {
        info: {
          service: {
            isRequiredCheckInsurance: false,
            addonServiceIds: [],
            _id: "60404c8ebf9d04d8c708174d",
            id: "umc_service-150000",
            createTime: "2022-09-11T22:44:32.012Z",
            code: "service-150000",
            partnerId: "umc",
            name: "Khám dịch vụ",
            sourceId: "MEDPRO_SYNC",
            price: 150000,
            pay: null,
            roomIds: [
              "umc_59",
              "umc_32",
              "umc_49",
              "umc_203",
              "umc_81",
              "umc_120",
              "umc_187",
              "umc_20",
              "umc_22",
              "umc_16",
              "umc_74",
              "umc_423",
              "umc_53",
              "umc_1",
              "umc_118",
              "umc_130",
              "umc_58",
              "umc_181",
              "umc_408",
              "umc_61",
              "umc_76",
              "umc_19",
              "umc_27",
              "umc_201",
              "umc_416",
              "umc_209",
              "umc_28",
              "umc_23",
              "umc_119",
              "umc_213",
              "umc_31",
              "umc_204",
              "umc_192",
              "umc_55",
              "umc_52",
              "umc_56",
              "umc_83",
              "umc_21",
              "umc_63",
              "umc_18",
              "umc_15",
              "umc_44",
              "umc_24",
              "umc_50",
              "umc_82",
              "umc_406",
              "umc_191",
              "umc_36",
              "umc_70",
              "umc_90",
              "umc_409",
              "umc_71",
              "umc_109",
              "umc_183",
              "umc_35",
              "umc_2",
              "umc_66",
              "umc_95",
              "umc_325",
              "umc_64",
              "umc_17",
              "umc_37",
              "umc_30",
              "umc_212",
              "umc_111",
              "umc_62",
              "umc_25",
              "umc_414",
              "umc_211",
              "umc_188",
              "umc_48",
              "umc_189",
              "umc_177",
              "umc_424",
              "umc_425",
              "umc_29",
              "umc_54",
              "umc_381",
              "umc_100",
              "umc_421"
            ],
            searchUnicode: "kham dich vu",
            score: 0,
            sourceAPI: "https://portal.medpro.com.vn",
            advanced: 0,
            alias: "",
            description: "",
            displayDetail: "",
            groupName: "",
            roomBHXHId: null,
            roomDVId: null,
            sequence: null,
            serviceType: "BOTH",
            syncBookingSetting: "DEFAULT",
            type: "ADMIT",
            deleted: false,
            daysOff: "",
            maxDay: null,
            popupContent: "",
            popupType: 0,
            priceDescription: "",
            roomCode: "",
            serviceGroup: null,
            subjectCode: "",
            popupTitle: "",
            shortDescription: "",
            updateTime: "2022-09-17T04:10:40.522Z"
          },
          subject: {
            _id: "6291b1cdbf9d04ecd1f55722",
            id: "umc_PQ",
            createTime: "2022-09-11T22:44:32.075Z",
            code: "PQ",
            name: "HÔ HẤP",
            partnerId: "umc",
            status: 1,
            updateTime: "2022-09-14T04:21:36.445Z",
            deleted: false,
            alias: "",
            description: "Dành cho bệnh nhân từ 3 tuổi trở lên",
            image_url:
              "https://s3-hcm-r1.longvan.net/medpro-production/umc/subjects/1655697618384-ho_hap.png",
            roomId: null,
            searchUnicode: "ho hap",
            sequence: null,
            sourceAPI: "https://portal.medpro.com.vn",
            sourceId: "MEDPRO_SYNC",
            storageUrl:
              "https://portal.medpro.com.vn/image/file/umc/subjects/1655697618384-ho_hap.png",
            syncBookingSetting: "DEFAULT"
          },
          doctor: {
            _id: "5ee0a1f4bf9d045ed5c87455",
            id: "umc_427",
            createTime: "2022-09-11T22:44:32.130Z",
            partnerId: "umc",
            name: "Hoàng Đình Hữu Hạnh",
            gender: true,
            role: "ThS BS.",
            sourceId: "MEDPRO_SYNC",
            birthday: null,
            code: "427",
            phone: null,
            searchUnicode: "hoang dinh huu hanh null",
            score: 0,
            deleted: false,
            sourceAPI: "https://portal.medpro.com.vn",
            mapToCode: null,
            updateTime: "2022-09-14T04:21:39.769Z"
          },
          bookingCode: "A2209193074"
        },
        _id: "6327f67e1fb16a001a66a82a",
        idBooking: "c5cf4e0e8f284e65958fe51536270855"
      },
      {
        info: {
          service: {
            isRequiredCheckInsurance: false,
            addonServiceIds: [],
            _id: "60404c8ebf9d04d8c708174d",
            id: "umc_service-150000",
            createTime: "2022-09-11T22:44:32.012Z",
            code: "service-150000",
            partnerId: "umc",
            name: "Khám dịch vụ",
            sourceId: "MEDPRO_SYNC",
            price: 150000,
            pay: null,
            roomIds: [
              "umc_59",
              "umc_32",
              "umc_49",
              "umc_203",
              "umc_81",
              "umc_120",
              "umc_187",
              "umc_20",
              "umc_22",
              "umc_16",
              "umc_74",
              "umc_423",
              "umc_53",
              "umc_1",
              "umc_118",
              "umc_130",
              "umc_58",
              "umc_181",
              "umc_408",
              "umc_61",
              "umc_76",
              "umc_19",
              "umc_27",
              "umc_201",
              "umc_416",
              "umc_209",
              "umc_28",
              "umc_23",
              "umc_119",
              "umc_213",
              "umc_31",
              "umc_204",
              "umc_192",
              "umc_55",
              "umc_52",
              "umc_56",
              "umc_83",
              "umc_21",
              "umc_63",
              "umc_18",
              "umc_15",
              "umc_44",
              "umc_24",
              "umc_50",
              "umc_82",
              "umc_406",
              "umc_191",
              "umc_36",
              "umc_70",
              "umc_90",
              "umc_409",
              "umc_71",
              "umc_109",
              "umc_183",
              "umc_35",
              "umc_2",
              "umc_66",
              "umc_95",
              "umc_325",
              "umc_64",
              "umc_17",
              "umc_37",
              "umc_30",
              "umc_212",
              "umc_111",
              "umc_62",
              "umc_25",
              "umc_414",
              "umc_211",
              "umc_188",
              "umc_48",
              "umc_189",
              "umc_177",
              "umc_424",
              "umc_425",
              "umc_29",
              "umc_54",
              "umc_381",
              "umc_100",
              "umc_421"
            ],
            searchUnicode: "kham dich vu",
            score: 0,
            sourceAPI: "https://portal.medpro.com.vn",
            advanced: 0,
            alias: "",
            description: "",
            displayDetail: "",
            groupName: "",
            roomBHXHId: null,
            roomDVId: null,
            sequence: null,
            serviceType: "BOTH",
            syncBookingSetting: "DEFAULT",
            type: "ADMIT",
            deleted: false,
            daysOff: "",
            maxDay: null,
            popupContent: "",
            popupType: 0,
            priceDescription: "",
            roomCode: "",
            serviceGroup: null,
            subjectCode: "",
            popupTitle: "",
            shortDescription: "",
            updateTime: "2022-09-17T04:10:40.522Z"
          },
          subject: {
            _id: "6291b1cdbf9d04ecd1f5572c",
            id: "umc_PB",
            createTime: "2022-09-11T22:44:32.055Z",
            code: "PB",
            name: "DA LIỄU",
            partnerId: "umc",
            status: 1,
            updateTime: "2022-09-14T04:21:36.481Z",
            mapToCode: "KH006",
            deleted: false,
            alias: "",
            description: "Dành cho bệnh nhân từ 3 tuổi trở lên",
            image_url:
              "https://s3-hcm-r1.longvan.net/medpro-production/umc/subjects/1655697331057-da_lieu.png",
            roomId: null,
            searchUnicode: "da lieu",
            sequence: null,
            sourceAPI: "https://portal.medpro.com.vn",
            sourceId: "MEDPRO_SYNC",
            storageUrl:
              "https://portal.medpro.com.vn/image/file/umc/subjects/1655697331057-da_lieu.png",
            syncBookingSetting: "DEFAULT"
          },
          doctor: {
            _id: "5ee0a1f4bf9d045ed5c873b5",
            id: "umc_980",
            createTime: "2022-09-11T22:44:32.381Z",
            partnerId: "umc",
            name: "Lê Minh Phúc",
            gender: false,
            role: "BSCKI.",
            sourceId: "MEDPRO_SYNC",
            birthday: null,
            code: "980",
            phone: null,
            searchUnicode: "le minh phuc null",
            score: 0,
            deleted: false,
            sourceAPI: "https://portal.medpro.com.vn",
            mapToCode: null,
            mappedId: null,
            updateTime: "2022-09-14T04:21:39.212Z"
          },
          bookingCode: "A2209193075"
        },
        _id: "6327f67e1fb16a001a66a82b",
        idBooking: "79d0c72d27e3401fac9d18344cbdea9a"
      }
    ],
    filterCheckData: [],
    addonServices: [],
    __v: 0,
    createdAt: "2022-09-19T04:56:30.190Z",
    updatedAt: "2022-09-19T10:01:25.742Z",
    paymentStatus: 1,
    canceledDate: "2022-09-19T10:01:25.740Z",
    cancelBookingGuideHtml: "Hủy phiếu khám bệnh thành công!",
    description: "Đã hủy",
    insuranceChoiceText: ""
  },
  {
    extraInfo: {
      transactionIdV1: "TTDev-**********",
      methodIdV1: 9,
      booking: {
        id: 3077,
        platform: "pc",
        app: "medpro",
        booking_date: "2022-10-07",
        transaction_code_gd: "A2209193075",
        payment_id: null,
        schedule_id: 129510,
        patient_id: 1162,
        user_id: 549,
        booking_time_id: 549268,
        booking_number: 27,
        booking_phone: "**********",
        bhyt_accept: 0,
        email: "<EMAIL>",
        status: 0,
        status_process: 0,
        refund_status: 0,
        is_deleted: 0,
        is_editable: 0,
        is_changed: 0,
        date_expired: "2022-09-19T12:58:57.000+07:00",
        sms_count: 0,
        related: null,
        bv_booking_time: "15:35",
        description:
          "Vui lòng đến trực tiếp phòng khám trước giờ hẹn 15-30 phút để khám bệnh.",
        booking_note:
          "Ghi chú: Phiếu khám bệnh chỉ có giá trị trong ngày khám từ <b>6h30 - 16h30.</b>",
        booking_time: {
          from: 15.5,
          to: 16.5
        },
        patient: {
          id: 1162,
          bvdhyd_msbn: null,
          medpro_id: "MP-220627058450",
          surname: "TEST NGÀY",
          name: "SINH",
          cmnd: "",
          sex: 1,
          dantoc_id: 1,
          profession_id: null,
          birthdate: "1989-07-02",
          birthyear: 1989,
          job: null,
          bhyt: null,
          mobile: "**********",
          email: "<EMAIL>",
          country_code: "VIE",
          city_id: 12,
          district_id: 94,
          ward_id: 4356,
          address: "123",
          note: null,
          is_medpro: 0,
          origin_id: null,
          mobile_censored: "032****167",
          mobile_secure: "f97180a6f60dde93d5c326f5da68d087",
          country: {
            id: 203,
            code: "VIE",
            name: "Việt Nam"
          },
          city: {
            id: 12,
            name: "Cao Bằng"
          },
          district: {
            id: 94,
            name: "H. Hoà An"
          },
          ward: {
            id: 4356,
            name: "Xã Hà Trì"
          },
          dantoc: {
            id: 1,
            name: "Kinh"
          }
        },
        user: {
          id: 549,
          username: "+84908291406",
          email: null,
          phone: null,
          fullname: "+84908291406",
          money: 0
        },
        hospital: {
          id: 2,
          name: "Bệnh viện Đại học Y Dược TPHCM",
          short_name: "BV Đại Học Y Dược",
          sms_name: "DHYD CS1",
          image: "logo-yduoc.png",
          status: 1,
          city_id: 62,
          address: "215 Hồng Bàng, Phường 11, Quận 5, TP Hồ Chí Minh",
          base_url: "https://alpha-api.medpro.com.vn/api",
          lat: null,
          long: null,
          hotline: null
        },
        subject: {
          id: 18,
          name: "DA LIỄU",
          short_name: null,
          code: "PB",
          is_cls: 0,
          is_internal: 0,
          price: 150000
        },
        room: {
          id: 129513,
          name: "Phòng 31",
          description: "Khám Nội Lầu 1 Khu A",
          hospital_id: 2,
          bvdhyd_room_id: 15,
          is_old: 0
        },
        doctor: {
          id: 980,
          name: "Lê Minh Phúc",
          name_only: "Phúc",
          birthyear: 0,
          role: "BS.CKI.",
          sex: 1,
          khoa: null,
          avatar: null,
          lichkham: null
        },
        ptime: {
          id: 129510,
          hour_from: 13,
          hour_to: 16,
          day_from: 1,
          day_to: 31,
          month_from: 1,
          month_to: 12,
          buoi: 2,
          weekday: "5",
          is_old: 0
        },
        subject_id: 18,
        room_id: 129513,
        doctor_id: 980,
        buoi: 2
      }
    },
    telemedInfoBooking: {
      files: [],
      dataInfo: []
    },
    visible: true,
    bookingNote: "ABCDE - ANCVNS",
    noPayment: false,
    sharePayment: false,
    serviceType: "",
    idReExam: "",
    syncBookingType: 2,
    syncBookingIdV1: 0,
    syncUserIdV1: 0,
    syncPatientIdV1: 0,
    promotion: false,
    promotionChilds: [],
    numberConfirmed: false,
    isChanged: false,
    _id: "6327f67e1fb16a001a66a82c",
    serviceId: "umc_service-150000",
    subjectId: "umc_PB",
    roomId: "aee83399b54744478742b65bd642b0da",
    doctorId: "umc_980",
    endTime: "2022-09-19T05:56:30.173Z",
    bookingSlotId:
      "221007_umc_service-150000_umc_PB_umc_15_umc_980_2_15:30_umc",
    treeId: "DATE",
    id: "79d0c72d27e3401fac9d18344cbdea9a",
    bookingCode: "T220919OKHZZC",
    bookingCodeV1: "A2209193075",
    sequenceNumber: 27,
    date: "2022-10-07T08:35:00.000Z",
    transactionId: "TTDev-**********",
    appId: "medpro",
    partner: {
      banner: "",
      status: 1,
      subjects: [],
      services: [],
      doctors: [],
      rooms: [],
      message: "",
      sortOrder: 1,
      deliveryStatus: 3,
      hospitalType: 1,
      homeBannerAction: [],
      _id: "61d508b452350f0019e14548",
      id: "7c09a609229d4f408e38ea3672cdcc96",
      partnerId: "umc",
      city_id: "trungvuong_79",
      name: "Bệnh Viện Đại Học Y Dược TP HCM - Cơ sở chính",
      city: "5ede15c33274b52923760e26",
      features: [
        {
          message: "",
          disabled: false,
          bookingLimit: 3,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa5b",
          mobileIcon: "",
          type: "booking.date",
          name: "Đặt khám theo chuyên khoa",
          image:
            "https://bo-api.medpro.com.vn:5000/static/images/medpro/app/image/featuremain1.png?t=11111",
          priority: 1,
          status: true,
          mobileStatus: true,
          __v: 0,
          mobileRoute: "/dat-lich-kham",
          webRoute: "/dat-lich-kham",
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-08-08T02:21:28.350Z"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 3,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa5c",
          mobileIcon: "",
          type: "booking.doctor",
          name: "Đặt khám theo Bác Sĩ",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/fd27f55b1df944d88570878f32c80063_djat_kham_theo_bac_si.png",
          priority: 2,
          status: true,
          mobileStatus: true,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-07-06T09:29:53.233Z",
          mobileRoute: "dat-kham",
          webRoute: "dat-kham",
          customUrl: "",
          localeName: ""
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa5e",
          mobileIcon: "",
          type: "insurance.check",
          name: "Kiểm tra BHYT",
          image:
            "https://medpro-inside-testing.medpro.com.vn/static/images/choray/app/icon/main/mainfeature12.png",
          priority: 4,
          status: false,
          mobileStatus: false,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-07-13T04:06:40.737Z"
        },
        {
          message: "",
          disabled: true,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa60",
          mobileIcon: "",
          type: "payment.fee",
          name: "Thanh toán viện phí",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/23aed95794f145c6a827257af1dbc4ac_thanh_toan_vien_phi.png",
          priority: 6,
          status: true,
          mobileStatus: true,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-07-13T04:06:50.350Z",
          mobileRoute: "abc",
          webRoute: "abc"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa61",
          mobileIcon: "",
          type: "instruc",
          name: "Hướng dẫn đặt lịch",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/0522450bd71a44f28f21ed7979b7fe6d_huong_dan_djat_kham.png",
          priority: 7,
          status: false,
          mobileStatus: true,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-06-09T08:47:55.172Z",
          mobileRoute: "abc",
          webRoute: "abc",
          customUrl: "",
          displayIcon: "undefined",
          localeName: ""
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa62",
          mobileIcon: "",
          type: "callphone",
          name: "Đặt khám tổng đài\n 1900-2115",
          image:
            "https://bo-api.medpro.com.vn:5000/static/images/medpro/app/image/featuremain8.png?t=11111111",
          priority: 8,
          status: false,
          mobileStatus: true,
          __v: 0,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-03-30T10:11:21.737Z"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa65",
          name: "Lịch sử Thanh toán Viện phí",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/fdc894977756470d82bc3916e677fffe_lich_su_thanh_toan_vien_phi.png",
          priority: 11,
          status: true,
          type: "payment.fee.history",
          updatedAt: "2022-03-30T10:11:21.737Z",
          createdAt: "2022-03-30T10:11:21.737Z",
          mobileRoute: "abc",
          mobileStatus: true,
          webRoute: "abc"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa66",
          name: "Khai báo Y tế",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/8e3a71fa85b14ea98cc128881d26427f_khai_bao_y_te.png",
          priority: 9,
          status: true,
          type: "declare.yt",
          mobileStatus: true,
          updatedAt: "2022-03-30T10:11:21.737Z",
          createdAt: "2022-03-30T10:11:21.737Z",
          mobileRoute: "abc",
          webRoute: "abc"
        },
        {
          message:
            "Tính năng đang được cập nhật và phát triển. Vui lòng quay lại sau.",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa67",
          name: "Tư vấn khám bệnh từ xa",
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/bffb329cc1f64c93a6361144a8a2f68a_tu_van_kham_benh_tu_xa.png",
          priority: 12,
          type: "telemed.online",
          status: false,
          mobileStatus: true,
          updatedAt: "2022-04-04T08:45:26.773Z",
          mobileRoute: "abc",
          webRoute: "abc",
          createdAt: "2022-03-30T10:11:21.737Z",
          mobileIcon:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/mobile/176632a3116941699e71833048fa5a31_tu_van_truc_tuyen.png"
        },
        {
          message: "Tính năng đang phát triển",
          disabled: true,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa68",
          name: "Tra cứu kết quả",
          type: "exam.profile",
          priority: 15,
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/8aa561ff877740378c3b8742d2b91c01_tra_cuu_ket_qua.png",
          webRoute: "1",
          mobileRoute: "1",
          status: true,
          mobileStatus: false,
          mobileIcon:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/mobile/bee035f150a14d2bb38f1b559232e61b_tra_cuu_ket_qua.png",
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-05-09T05:04:33.377Z",
          __v: 0,
          customUrl: "",
          localeName: ""
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "62442cc99283ef1868b5fa6a",
          name: "Tra cứu BHYT",
          image:
            "https://medpro-api-v2-testing.medpro.com.vn/st/feature/dv9.svg",
          priority: 8,
          status: true,
          createdAt: "2022-03-30T10:11:21.737Z",
          updatedAt: "2022-06-03T04:27:43.470Z",
          __v: 0,
          type: "tracking.bhyt",
          mobileStatus: false,
          customUrl: "",
          displayIcon: "undefined",
          localeName: "",
          mobileRoute: "1",
          webRoute: "1"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 3,
          position: "IN",
          children: [],
          translate: [],
          _id: "611cbb24f473ed001911ac69",
          name: "Đăng ký thực hiện Cận lâm sàng",
          type: "booking.cls",
          priority: 0,
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/a41433de517146139d4ac2d7e6eb42bd_djat_lich_can_lam_sang.png",
          status: true,
          mobileStatus: true,
          createdAt: "2021-08-18T07:47:48.638Z",
          updatedAt: "2022-06-22T10:07:21.736Z",
          __v: 0,
          mobileIcon:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/mobile/562f954033f04e3ca782a88febf9aee7_djat_lich_can_lam_sang.png",
          mobileRoute: "1",
          webRoute: "1",
          localeName: "undefined",
          customUrl: "",
          displayIcon:
            "https://bo-api.medpro.com.vn:5000/static/images/medpro/app/image/featuremain3.png?t=111111111"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "616e3649156bac0019ab6786",
          name: "Tư vấn trực tuyến",
          type: "booking.telemed",
          priority: 12,
          image:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/b04e9ee4e5634c3d9ac5707321c42250_tu_van_truc_tuyen.png",
          webRoute: "abc",
          mobileRoute: "abc",
          status: true,
          mobileStatus: true,
          mobileIcon:
            "https://bo-api-testing.medpro.com.vn/feature/image-source/mobile/86a29796cd9a4233837284713a2d30ea_tu_van_truc_tuyen.png",
          createdAt: "2021-10-19T03:06:49.212Z",
          updatedAt: "2022-04-06T07:23:58.132Z",
          __v: 0,
          localeName: "undefined"
        },
        {
          message: "",
          disabled: false,
          bookingLimit: 1,
          position: "IN",
          children: [],
          translate: [],
          _id: "5f375e2d60d7f73b38007a03",
          name: "Hóa đơn Điện tử",
          image:
            "https://medpro-api-v2-testing.medpro.com.vn/st/feature/hoa_don_dien_tu.svg",
          priority: 10,
          status: true,
          type: "e.invoice",
          createdAt: "2022-07-12T08:52:07.588Z",
          updatedAt: "2022-07-12T08:52:07.588Z"
        }
      ],
      oldVersionFeature: [],
      createdAt: "2022-01-05T02:55:48.902Z",
      updatedAt: "2022-08-08T02:21:28.351Z",
      __v: 23,
      email: "<EMAIL>",
      website: "http://medpro.vn",
      image:
        "https://bo-api-testing.medpro.com.vn/static/images/umc/web/logo.png?t=Wed%20Jan%2005%202022%2010:31:47%20GMT+0700%20(Indochina%20Time)",
      address: "215 Hồng Bàng, Phường 11, Quận 5, TP.HCM",
      sms_name: "BV DH Y DUOC",
      googleMap: "w",
      hotline: "êf",
      hotlineMedpro: "ề",
      workingDate: "ừ",
      workingTime: "w",
      contact: "Cơ sở chính",
      circleLogo:
        "https://bo-api-testing.medpro.com.vn/static/images/umc/web/logo.png?t=Wed%20Jan%2005%202022%2010:31:47%20GMT+0700%20(Indochina%20Time)"
    },
    partnerId: "umc",
    userId: "6163b88f7bb58900191fc07e",
    bookingId: "c4792025-e070-46cc-afa0-9b76ff00556b",
    status: -2,
    patientId: "473176ca70d34d9bb4eeaa862898bf9b",
    patient: "62820ffa778373001be3c2f7",
    patientVersionId: "035b4acdc56840f587426b6e2f270445",
    patientVersion: "62875cb5512b83001a951ccb",
    orderId: "6bfab03abd664368a783ebdc284ca04c",
    checkInRoom: {
      name: null,
      id: null,
      type: null,
      roomType: null,
      description: "",
      room: null,
      bookingNote: "Không tìm thấy phòng khám",
      nextCombine: false,
      combineNodes: [],
      maxDay: null,
      image_url: null
    },
    room: "5ed06e5fbf9d04acb2c7dcbd",
    bookingOrder: "6327f67d1fb16a001a66a827",
    bookingOrderId: "6bfab03abd664368a783ebdc284ca04c",
    service: "60404c8ebf9d04d8c708174d",
    subject: "6291b1cdbf9d04ecd1f5572c",
    doctor: "5ee0a1f4bf9d045ed5c873b5",
    platform: "pc",
    smsCode: "A2209193075",
    bookingsRelation: [
      {
        info: {
          service: {
            isRequiredCheckInsurance: false,
            addonServiceIds: [],
            _id: "60404c8ebf9d04d8c708174d",
            id: "umc_service-150000",
            createTime: "2022-09-11T22:44:32.012Z",
            code: "service-150000",
            partnerId: "umc",
            name: "Khám dịch vụ",
            sourceId: "MEDPRO_SYNC",
            price: 150000,
            pay: null,
            roomIds: [
              "umc_59",
              "umc_32",
              "umc_49",
              "umc_203",
              "umc_81",
              "umc_120",
              "umc_187",
              "umc_20",
              "umc_22",
              "umc_16",
              "umc_74",
              "umc_423",
              "umc_53",
              "umc_1",
              "umc_118",
              "umc_130",
              "umc_58",
              "umc_181",
              "umc_408",
              "umc_61",
              "umc_76",
              "umc_19",
              "umc_27",
              "umc_201",
              "umc_416",
              "umc_209",
              "umc_28",
              "umc_23",
              "umc_119",
              "umc_213",
              "umc_31",
              "umc_204",
              "umc_192",
              "umc_55",
              "umc_52",
              "umc_56",
              "umc_83",
              "umc_21",
              "umc_63",
              "umc_18",
              "umc_15",
              "umc_44",
              "umc_24",
              "umc_50",
              "umc_82",
              "umc_406",
              "umc_191",
              "umc_36",
              "umc_70",
              "umc_90",
              "umc_409",
              "umc_71",
              "umc_109",
              "umc_183",
              "umc_35",
              "umc_2",
              "umc_66",
              "umc_95",
              "umc_325",
              "umc_64",
              "umc_17",
              "umc_37",
              "umc_30",
              "umc_212",
              "umc_111",
              "umc_62",
              "umc_25",
              "umc_414",
              "umc_211",
              "umc_188",
              "umc_48",
              "umc_189",
              "umc_177",
              "umc_424",
              "umc_425",
              "umc_29",
              "umc_54",
              "umc_381",
              "umc_100",
              "umc_421"
            ],
            searchUnicode: "kham dich vu",
            score: 0,
            sourceAPI: "https://portal.medpro.com.vn",
            advanced: 0,
            alias: "",
            description: "",
            displayDetail: "",
            groupName: "",
            roomBHXHId: null,
            roomDVId: null,
            sequence: null,
            serviceType: "BOTH",
            syncBookingSetting: "DEFAULT",
            type: "ADMIT",
            deleted: false,
            daysOff: "",
            maxDay: null,
            popupContent: "",
            popupType: 0,
            priceDescription: "",
            roomCode: "",
            serviceGroup: null,
            subjectCode: "",
            popupTitle: "",
            shortDescription: "",
            updateTime: "2022-09-17T04:10:40.522Z"
          },
          subject: {
            _id: "6291b1cdbf9d04ecd1f55722",
            id: "umc_PQ",
            createTime: "2022-09-11T22:44:32.075Z",
            code: "PQ",
            name: "HÔ HẤP",
            partnerId: "umc",
            status: 1,
            updateTime: "2022-09-14T04:21:36.445Z",
            deleted: false,
            alias: "",
            description: "Dành cho bệnh nhân từ 3 tuổi trở lên",
            image_url:
              "https://s3-hcm-r1.longvan.net/medpro-production/umc/subjects/1655697618384-ho_hap.png",
            roomId: null,
            searchUnicode: "ho hap",
            sequence: null,
            sourceAPI: "https://portal.medpro.com.vn",
            sourceId: "MEDPRO_SYNC",
            storageUrl:
              "https://portal.medpro.com.vn/image/file/umc/subjects/1655697618384-ho_hap.png",
            syncBookingSetting: "DEFAULT"
          },
          doctor: {
            _id: "5ee0a1f4bf9d045ed5c87455",
            id: "umc_427",
            createTime: "2022-09-11T22:44:32.130Z",
            partnerId: "umc",
            name: "Hoàng Đình Hữu Hạnh",
            gender: true,
            role: "ThS BS.",
            sourceId: "MEDPRO_SYNC",
            birthday: null,
            code: "427",
            phone: null,
            searchUnicode: "hoang dinh huu hanh null",
            score: 0,
            deleted: false,
            sourceAPI: "https://portal.medpro.com.vn",
            mapToCode: null,
            updateTime: "2022-09-14T04:21:39.769Z"
          },
          bookingCode: "A2209193074"
        },
        _id: "6327f67e1fb16a001a66a82d",
        idBooking: "c5cf4e0e8f284e65958fe51536270855"
      },
      {
        info: {
          service: {
            isRequiredCheckInsurance: false,
            addonServiceIds: [],
            _id: "60404c8ebf9d04d8c708174d",
            id: "umc_service-150000",
            createTime: "2022-09-11T22:44:32.012Z",
            code: "service-150000",
            partnerId: "umc",
            name: "Khám dịch vụ",
            sourceId: "MEDPRO_SYNC",
            price: 150000,
            pay: null,
            roomIds: [
              "umc_59",
              "umc_32",
              "umc_49",
              "umc_203",
              "umc_81",
              "umc_120",
              "umc_187",
              "umc_20",
              "umc_22",
              "umc_16",
              "umc_74",
              "umc_423",
              "umc_53",
              "umc_1",
              "umc_118",
              "umc_130",
              "umc_58",
              "umc_181",
              "umc_408",
              "umc_61",
              "umc_76",
              "umc_19",
              "umc_27",
              "umc_201",
              "umc_416",
              "umc_209",
              "umc_28",
              "umc_23",
              "umc_119",
              "umc_213",
              "umc_31",
              "umc_204",
              "umc_192",
              "umc_55",
              "umc_52",
              "umc_56",
              "umc_83",
              "umc_21",
              "umc_63",
              "umc_18",
              "umc_15",
              "umc_44",
              "umc_24",
              "umc_50",
              "umc_82",
              "umc_406",
              "umc_191",
              "umc_36",
              "umc_70",
              "umc_90",
              "umc_409",
              "umc_71",
              "umc_109",
              "umc_183",
              "umc_35",
              "umc_2",
              "umc_66",
              "umc_95",
              "umc_325",
              "umc_64",
              "umc_17",
              "umc_37",
              "umc_30",
              "umc_212",
              "umc_111",
              "umc_62",
              "umc_25",
              "umc_414",
              "umc_211",
              "umc_188",
              "umc_48",
              "umc_189",
              "umc_177",
              "umc_424",
              "umc_425",
              "umc_29",
              "umc_54",
              "umc_381",
              "umc_100",
              "umc_421"
            ],
            searchUnicode: "kham dich vu",
            score: 0,
            sourceAPI: "https://portal.medpro.com.vn",
            advanced: 0,
            alias: "",
            description: "",
            displayDetail: "",
            groupName: "",
            roomBHXHId: null,
            roomDVId: null,
            sequence: null,
            serviceType: "BOTH",
            syncBookingSetting: "DEFAULT",
            type: "ADMIT",
            deleted: false,
            daysOff: "",
            maxDay: null,
            popupContent: "",
            popupType: 0,
            priceDescription: "",
            roomCode: "",
            serviceGroup: null,
            subjectCode: "",
            popupTitle: "",
            shortDescription: "",
            updateTime: "2022-09-17T04:10:40.522Z"
          },
          subject: {
            _id: "6291b1cdbf9d04ecd1f5572c",
            id: "umc_PB",
            createTime: "2022-09-11T22:44:32.055Z",
            code: "PB",
            name: "DA LIỄU",
            partnerId: "umc",
            status: 1,
            updateTime: "2022-09-14T04:21:36.481Z",
            mapToCode: "KH006",
            deleted: false,
            alias: "",
            description: "Dành cho bệnh nhân từ 3 tuổi trở lên",
            image_url:
              "https://s3-hcm-r1.longvan.net/medpro-production/umc/subjects/1655697331057-da_lieu.png",
            roomId: null,
            searchUnicode: "da lieu",
            sequence: null,
            sourceAPI: "https://portal.medpro.com.vn",
            sourceId: "MEDPRO_SYNC",
            storageUrl:
              "https://portal.medpro.com.vn/image/file/umc/subjects/1655697331057-da_lieu.png",
            syncBookingSetting: "DEFAULT"
          },
          doctor: {
            _id: "5ee0a1f4bf9d045ed5c873b5",
            id: "umc_980",
            createTime: "2022-09-11T22:44:32.381Z",
            partnerId: "umc",
            name: "Lê Minh Phúc",
            gender: false,
            role: "BSCKI.",
            sourceId: "MEDPRO_SYNC",
            birthday: null,
            code: "980",
            phone: null,
            searchUnicode: "le minh phuc null",
            score: 0,
            deleted: false,
            sourceAPI: "https://portal.medpro.com.vn",
            mapToCode: null,
            mappedId: null,
            updateTime: "2022-09-14T04:21:39.212Z"
          },
          bookingCode: "A2209193075"
        },
        _id: "6327f67e1fb16a001a66a82e",
        idBooking: "79d0c72d27e3401fac9d18344cbdea9a"
      }
    ],
    filterCheckData: [],
    addonServices: [],
    __v: 0,
    createdAt: "2022-09-19T04:56:30.192Z",
    updatedAt: "2022-09-19T10:01:25.742Z",
    paymentStatus: 1,
    canceledDate: "2022-09-19T10:01:25.740Z",
    cancelBookingGuideHtml: "Hủy phiếu khám bệnh thành công!",
    description: "Đã hủy",
    insuranceChoiceText: ""
  }
];
