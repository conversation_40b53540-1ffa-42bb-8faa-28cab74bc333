import cx from "classnames";
import { get, isEmpty, isNull } from "lodash";
import {
  MDBAlert,
  MDBBtn,
  MDBInputGroup,
  MDBModal,
  MDBModalBody,
  MDBModalHeader
} from "mdbreact";
import moment from "moment";
import React, { Component } from "react";
import Barcode from "react-barcode";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import { openToast } from "~/components/common/molecules/ToastNotification";
import { getInfoPatient } from "~/utils/flowRouting";
import { getLogoInBill } from "~/utils/manageResource";
import partnerId from "~/utils/partner";
import styles from "./style.module.scss";
const QRCode = require("qrcode.react");

class MedicalBillPrint extends Component {
  constructor(props) {
    super(props);

    this.state = {
      shareToPay: false
    };
  }

  componentDidMount() {
    const { paymentInformation, reviewBooking } = this.props;
    const status = get(paymentInformation, "bookingInfo.status");
    if (!reviewBooking) {
      if (Number(status) === 6) {
        this.setState({
          shareToPay: !this.state.shareToPay
        });
      }
    }
  }

  valueShareToPay() {
    const { paymentInformation } = this.props;

    const id = get(paymentInformation, "bookingInfo.id");
    const rs = window.location.origin + "/thanh-toan-ho/" + id;
    return rs;
  }

  copyTexthandler = url => {
    openToast("Đường dẫn đã được sao chép.");
    console.log("navigator.clipboard");
    var textArea = document.createElement("input");
    textArea.value = url;
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand("copy");
    } catch (err) { }

    document.body.removeChild(textArea);

    this.toggleShareToPay();
  };

  toggleShareToPay = () => {
    this.setState({
      shareToPay: !this.state.shareToPay
    });
  };

  renderModalShareToPay = () => {
    const { shareToPay } = this.state;

    return (
      <MDBModal
        backdrop={false}
        isOpen={shareToPay}
        toggle={this.toggleShareToPay}
        style={{ witdh: "70%" }}
      >
        <MDBModalHeader
          className={cx(
            "text-center text-uppercase  text-white py-2",
            partnerId === "minhanh" ? "bg-danger" : "bg-primary"
          )}
          toggle={this.toggleShareToPay}
          tag="h6"
        >
          <p className="m-0"> Thanh Toán hộ</p>
        </MDBModalHeader>
        <MDBModalBody style={{ fontSize: "0.875rem" }}>
          <p>
            <em>
              Quý bệnh nhân có thể nhờ người thân hoặc bạn bè để thanh toán
              phiếu khám bệnh. Phiếu khám bệnh chỉ <b>hợp lệ</b> sau khi
              <b> thanh toán thành công</b>.
            </em>
          </p>
          <p className=" text-center">
            <b>Sao chép và chia sẻ liên kết bên dưới để nhờ thanh toán hộ</b>
          </p>

          <MDBInputGroup
            value={this.valueShareToPay()}
            size="sm"
            containerClassName="mb-3"
            disabled
            append={
              <MDBBtn
                outline
                className="m-0 px-3 py-2 z-depth-0"
                onClick={() => {
                  this.copyTexthandler(this.valueShareToPay());
                }}
              >
                Sao chép
              </MDBBtn>
            }
          />
        </MDBModalBody>
      </MDBModal>
    );
  };

  renderMedicalBill = () => {
    const { paymentInformation } = this.props;
    let bookingDate, bookingTime, bookingTimeBig;

    const partnerId = get(paymentInformation, "bookingInfo.partnerId");
    const logoInBill = getLogoInBill(partnerId);

    const date = get(paymentInformation, "bookingInfo.date", "");

    if (date) {
      bookingDate = moment(date).format("DD/MM/YYYY");
      bookingTime = moment(date).format("HH:mm");
      bookingTimeBig = moment(date).format("HH:mm");
    } else {
      bookingDate = get(paymentInformation, "bookingInfo.waitingConfirmDate");
      bookingTime = get(paymentInformation, "bookingInfo.waitingConfirmDate");
      bookingTimeBig = "";
    }

    const totalPaymentMessage = get(
      paymentInformation,
      "bookingInfo.totalPaymentMessage",
      ""
    );

    const serviceName = get(paymentInformation, "bookingInfo.service.name", "");

    const addonServices = get(
      paymentInformation,
      "bookingInfo.addonServices",
      ""
    );
    const subjectName = get(paymentInformation, "bookingInfo.subject.name", "");
    const roomName = get(paymentInformation, "bookingInfo.room.name", "");
    const doctorName = get(paymentInformation, "bookingInfo.doctor.name", "");

    const sectionName = get(paymentInformation, "bookingInfo.section.name", "");

    const patient = get(paymentInformation, "patientInfo", {});

    const { fullName, sex, birthdate } = getInfoPatient(patient);

    const msbn = get(paymentInformation, "patientInfo.patientCode", "");
    const displayCodeBooking = get(
      paymentInformation,
      "bookingInfo.displayCodeBooking"
    );

    const insuranceCode = get(paymentInformation, "bookingInfo.insuranceCode");
    const insuranceChoiceText = get(
      paymentInformation,
      "bookingInfo.insuranceChoiceText",
      ""
    );

    const price = get(paymentInformation, "bookingInfo.service.priceText", "");

    const description = get(paymentInformation, "checkInRoom.description", "");
    const sectionAddress = get(
      paymentInformation,
      "checkInRoom.sectionAddress",
      ""
    );
    const statusBooking = get(paymentInformation, "bookingInfo.status", 1);

    const bookingNote = get(
      paymentInformation,
      "bookingInfo.bookingNote",
      null
    );

    const bookingNumber = get(
      paymentInformation,
      "bookingInfo.sequenceNumber",
      ""
    );

    const titleBookingTime =
      partnerId === "trungvuong" ? "Giờ tiếp nhận dự kiến" : "Giờ khám dự kiến";

    const titleBooking = get(paymentInformation, "bookingInfo.description", "");

    const canRepayment = get(
      paymentInformation,
      "bookingInfo.canRepayment",
      true
    );
    const awaitMessage = get(
      paymentInformation,
      "bookingInfo.awaitMessage",
      ""
    );

    const classNumber = styles.number;
    const classGray = styles.gray;

    const classCX = cx({
      [classNumber]: true,
      [classGray]: statusBooking === -2
    });

    const classGreen = styles.greenNote;
    const classRed = styles.redNote;
    const classGrey = styles.greyNote;

    const classTimeNote = cx({
      [classGreen]: true,
      [classRed]: statusBooking === 0 || statusBooking === 6,
      [classGrey]: statusBooking === -2
    });

    const checkHiddenShareToPay = () => {
      if (statusBooking !== 6) return "d-none";
    };

    const checkHidden = e => {
      if (!e || e.value === "" || isNull(e.value) || isEmpty(e.value)) {
        return "d-none";
      } else {
        return "";
      }
    };

    const typeCode = e => {
      switch (e?.type) {
        case "barcode":
          return (
            <React.Fragment>
              <p>{e.title}</p>
              <Barcode
                value={e.value}
                format="CODE128"
                height={50}
                width={1}
                fontSize={14}
              />
            </React.Fragment>
          );
        case "qrcode":
          return (
            <React.Fragment>
              <p>{e.title}</p>
              <QRCode fgColor="#000000" size={90} value={e.value} />
            </React.Fragment>
          );
        default:
          return null;
      }
    };

    return (
      <React.Fragment>
        <div className={styles.title_hospital}>
          <img src={logoInBill} alt="" />
        </div>
        <div className={cx(styles.sub_title, "mb-3")}>PHIẾU KHÁM BỆNH</div>

        <div className={cx(styles.bar_code, checkHidden(displayCodeBooking))}>
          {typeCode(displayCodeBooking)}
        </div>
        {/*
        <div className={styles.form_code}>(Mã phiếu: {bookingCode})</div> */}

        {sectionName && <div className={styles.top_note}>{sectionName}</div>}
        {roomName && <div className={styles.top_note}>Phòng: {roomName}</div>}
        {sectionAddress && (
          <div className={styles.top_note}>{sectionAddress}</div>
        )}
        {subjectName && (
          <div className={styles.top_note}>Chuyên khoa: {subjectName}</div>
        )}

        {statusBooking !== 0 ? (
          <React.Fragment>
            {awaitMessage !== "" ? (
              <div className={styles.noti_unpayment}>{awaitMessage}</div>
            ) : bookingNumber ? (
              <React.Fragment>
                <div className={classCX}>{bookingNumber}</div>
                <div className={styles.time_note}>(Số thứ tự khám)</div>
              </React.Fragment>
            ) : (
              <div className={classCX}>{bookingTimeBig}</div>
            )}
          </React.Fragment>
        ) : (
          ""
        )}

        <div className={cx(styles.time_note)}>
          <span className={classTimeNote}>{titleBooking}</span>
          <span className={cx(checkHiddenShareToPay(), styles.btnShare)}>
            <Link to="#" onClick={() => this.toggleShareToPay()}>
              Thanh toán hộ
            </Link>
          </span>
        </div>

        {statusBooking === 0 && canRepayment && (
          <div className={styles.noti_unpayment}>
            Vui lòng THANH TOÁN để hoàn tất đăng ký khám bệnh và nhận phiếu khám
            bệnh điện tử.
          </div>
        )}

        {/* text: số tiền bệnh viện thanh toán giúp */}
        {(statusBooking === 2 || statusBooking === 1 || statusBooking === -2) &&
          totalPaymentMessage && (
            <div className={styles.totalPaymentMessage}>
              <span>{totalPaymentMessage}</span>
            </div>
          )}

        <div className={styles.list_detail}>
          <ul>
            {/* <li>
              <span className={styles.column_left}>Ngày khám:</span>
              <strong className={styles.column_right}>{bookingDate}</strong>
            </li> */}
            {awaitMessage === "" && (
              <li>
                <span className={styles.column_left}>Thời gian khám:</span>
                <strong>
                  {bookingTime} - {bookingDate}
                </strong>
              </li>
            )}

            {serviceName && (
              <li>
                <span className={styles.column_left}>Dịch vụ:</span>
                <strong>{serviceName}</strong>
              </li>
            )}
            {doctorName && (
              <li>
                <span className={styles.column_left}>Bác sĩ:</span>
                <strong>{doctorName}</strong>
              </li>
            )}

            <li>
              <span className={styles.column_left}>Họ tên:</span>
              <strong>{fullName}</strong>
            </li>
            <li>
              <span className={styles.column_left}>Giới tính:</span>
              {sex}
            </li>
            <li>
              <span className={styles.column_left}>Năm sinh:</span>
              {birthdate}
            </li>
            <li>
              <span className={styles.column_left}>Hình thức khám:</span>
              {insuranceCode ? " Có BHYT" : " Không có BHYT"}
            </li>

            {insuranceChoiceText !== "" && (
              <li>
                <span className={styles.column_left}>Loại bảo hiểm:</span>
                {insuranceChoiceText}
              </li>
            )}

            <li>
              <span className={styles.column_left}>Tiền khám:</span>
              {price}
            </li>

            {addonServices &&
              addonServices.map((v, i) => {
                return (
                  <li key={i}>
                    <span className={styles.column_left}>{v.name}:</span>
                    {v.priceText}
                  </li>
                );
              })}
          </ul>
        </div>
        <div className={styles.note}>{description}</div>
        <hr />
        <div className={styles.profile_number}>
          Số hồ sơ (Mã số bệnh nhân): <br />
          <strong>{msbn}</strong>
        </div>

        <div className={styles.bar_code}>
          {msbn !== "" && (
            <Barcode
              value={msbn}
              format="CODE128"
              height={100}
              width={2}
              fontSize={25}
            />
          )}
        </div>
        {bookingNote && (
          <div
            dangerouslySetInnerHTML={{
              __html: bookingNote
            }}
          />
        )}
      </React.Fragment>
    );
  };

  renderInformationTTCK = () => {
    const { paymentInformation } = this.props;

    const bankName = get(paymentInformation, "payment.bankInfo.name");
    const accountHolder = get(
      paymentInformation,
      "payment.bankInfo.accountHolder"
    );
    const accountNumber = get(
      paymentInformation,
      "payment.bankInfo.accountNumber"
    );
    const bankBranch = get(paymentInformation, "payment.bankInfo.bankBranch");

    return (
      <React.Fragment>
        <p>Thông tin chuyển khoản:</p>
        Ngân hàng: <strong>{bankName}</strong> <br />
        Tài khoản: <strong>{accountHolder}</strong> <br />
        Số tài khoản: <strong>{accountNumber}</strong> <br />
        Chi nhánh: <strong>{bankBranch}</strong> <br />
      </React.Fragment>
    );
  };

  renderInformationHTTT = () => {
    return (
      <React.Fragment>
        <p>Hỗ trợ thanh toán:</p>
        Liên hệ hỗ trợ <strong>Zalo **********</strong> hoặc tổng đài{" "}
        <strong>********</strong> để được hướng dẫn thanh toán.
      </React.Fragment>
    );
  };

  render() {
    const { paymentInformation } = this.props;

    const paymentMethod = get(paymentInformation, "payment.gatewayId");

    // if (loading) {
    //   return (
    //     <React.Fragment>
    //       <Facebook />
    //     </React.Fragment>
    //   );
    // }

    return (
      <React.Fragment>
        {paymentMethod === "wiretransfer" ? (
          <MDBAlert color="primary">{this.renderInformationTTCK()}</MDBAlert>
        ) : (
          ""
        )}

        <div className={styles.print}>{this.renderMedicalBill()}</div>
        {this.renderModalShareToPay()}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    totalData: { paymentInformation, loading, reviewBooking }
  } = state;
  return {
    paymentInformation,
    loading,
    reviewBooking
  };
};

const mergeProps = (stateProps, dispatchProps, ownProps) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps
});

export default connect(mapStateToProps, null, mergeProps)(MedicalBillPrint);
