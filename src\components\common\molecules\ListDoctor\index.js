import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import {
  MDBListGroup,
  MDBSpinner,
  MDBCard,
  MDBCardBody,
  MDBCardText,
  MDBIcon
} from "mdbreact";
import cx from "classnames";
import NotFoundResult from "~/components/common/atoms/NotFoundResult";
import styles from "./style.module.scss";
import {
  selectDoctor,
  selectService,
  selectSubject,
  selectRoom
} from "~/store/totalData/actions";
import { get } from "lodash";
import { getFormatMoney } from "~/utils/getFormatMoney";

export class ListDoctor extends Component {
  handleChooseDoctor = doctor => {
    const { selectDoctor } = this.props;
    selectDoctor(doctor);
  };

  handleSkipCombine = (nextStep, item) => {
    switch (nextStep.type) {
      case "service":
        this.props.selectService(item);
        break;
      case "room":
        this.props.selectRoom(item);
        break;
      case "subject":
        this.props.selectSubject(item);
        break;
      default:
        break;
    }
  };

  renderDoctorList = () => {
    const { idDoctor, listDoctorFilter, nextStep, partnerId } = this.props;

    const listNextItem = get(nextStep, "list", []);

    return listDoctorFilter.map((doctor, i) => {
      const doctorName = get(doctor, "detail.name", "");
      const doctorId = get(doctor, "detail.id", "");
      const classActive = doctorId === idDoctor ? styles.active : "";
      const bookingNote = get(doctor, "detail.bookingNote", "");
      const subjectName = get(doctor, "detail.subject", "");
      const treatments = get(doctor, "detail.treatments", "");
      const doctorRole = get(doctor, "detail.role", "");
      const price = get(doctor, "detail.price", "");
      const genderId = get(doctor, "detail.gender", "");
      const gender = genderId === 1 ? "Nam" : "Nữ";
      const bookingDays = get(doctor, "detail.days", "");

      const isShowNextCombine =
        idDoctor === doctor.id && nextStep && nextStep.list.length > 1;

      return (
        <MDBCard className={cx(styles.doctor_info, classActive)} key={i}>
          <MDBCardBody
            className={styles.card_body}
            onClick={() => {
              this.handleChooseDoctor(doctor);
            }}
          >
            <MDBCardText className={styles.highlight}>
              <MDBIcon icon="user-md" className={styles.icons} />
              {doctorRole} {doctorName}
            </MDBCardText>
            <MDBCardText>
              <MDBIcon icon="transgender" className={styles.icons} />
              Giới tính: {gender}
            </MDBCardText>
            <MDBCardText>
              <MDBIcon icon="stethoscope" className={styles.icons} />
              Chuyên khoa:{" "}
              {doctor?.detail?.subjects?.map(itemSubject => {
                return (
                  <span
                    key={itemSubject?.subjectId}
                    className={styles.subjects}
                  >
                    {itemSubject?.subject}
                  </span>
                );
              }) || <span>{subjectName}</span>}{" "}
            </MDBCardText>
            {treatments && (
              <MDBCardText>
                <MDBIcon icon="stethoscope" className={styles.icons} />
                Chuyên trị: <span>{treatments}</span>
              </MDBCardText>
            )}
            <MDBCardText>
              <MDBIcon far icon="calendar-alt" className={styles.icons} />
              Lịch khám: {bookingDays.indexOf("CN") !== 0 ? "Thứ" : ""}{" "}
              {bookingDays}
            </MDBCardText>
            {!["binhthanhhcm"].includes(partnerId) && (
              <MDBCardText>
                <MDBIcon icon="dollar-sign" className={styles.icons} />
                Giá khám: {price}
              </MDBCardText>
            )}
            {bookingNote && (
              <MDBCardText className={styles.description}>
                <strong>Ghi chú</strong>: {bookingNote}
              </MDBCardText>
            )}
          </MDBCardBody>
          {isShowNextCombine && (
            <div>
              <p
                style={{ textAlign: "center", fontWeight: 600 }}
                className={styles.sub_item_title}
              >
                Chọn dịch vụ
              </p>
              {listNextItem.map(item => {
                return (
                  <div
                    key={item.detail.id}
                    className={styles.sub_item_content}
                    onClick={() => this.handleSkipCombine(nextStep, item)}
                  >
                    <div className={styles.left_content}>
                      <div>{item.detail.name}</div>
                      {item.detail.description && (
                        <span>{item.detail.description}</span>
                      )}
                    </div>
                    <div>{getFormatMoney(item.detail.price)}</div>
                  </div>
                );
              })}
            </div>
          )}
        </MDBCard>
      );
    });
  };

  render() {
    const {
      loading,
      listDoctorFilter,
      isLastPage,
      handleSeeMoreDoctor
    } = this.props;
    return (
      <Fragment>
        {loading ? (
          <div className={styles.loading}>
            <MDBSpinner big />
          </div>
        ) : (
          <Fragment>
            <div className={styles.wapper_list_group}>
              {listDoctorFilter.length > 0 ? (
                <>
                  <MDBListGroup className={styles.list_group}>
                    {this.renderDoctorList()}
                    {listDoctorFilter.length > 0 && (
                      <p
                        className={styles.show_more}
                        style={{
                          cursor: isLastPage ? "not-allowed" : "pointer"
                        }}
                        onClick={isLastPage ? () => {} : handleSeeMoreDoctor}
                      >
                        {isLastPage ? "Đã hết danh sách" : "Xem thêm..."}
                      </p>
                    )}
                  </MDBListGroup>
                </>
              ) : (
                <NotFoundResult styleTitle="h5" />
              )}
            </div>
          </Fragment>
        )}
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    dateAndSpecialist: { searchPattern },
    totalData: {
      doctor: { id: idDoctor, subType },
      doctorSearchTerm,
      partnerId
    }
  } = state;
  const nextStep = state.totalData[subType];
  return {
    partnerId,
    searchPattern,
    idDoctor,
    doctorSearchTerm,
    subType,
    nextStep
  };
};

export default connect(mapStateToProps, {
  selectDoctor,
  selectService,
  selectSubject,
  selectRoom
})(ListDoctor);
