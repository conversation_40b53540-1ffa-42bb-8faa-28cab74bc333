import React, { Component } from "react";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import SearchReExamination from "~/components/desktop/pages/SearchReExamination";

class DetectSearchReExamination extends Component {
    render() {
        return (
            <React.Fragment>
                <SearchReExamination />
            </React.Fragment>
        );
    }
}

const mapStateToProps = state => {
    const {
        global: {
            device: { type }
        }
    } = state;
    return {
        device: type
    };
};

const SearchHelmet = withTitle({
    component: DetectSearchReExamination,
    title: "Medpro | Tìm kiếm thông tin tái khám"
});

export default connect(mapStateToProps)(SearchHelmet);
