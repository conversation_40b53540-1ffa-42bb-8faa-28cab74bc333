@import "src/assets/scss/pro-mobile/medicalBillPrint.scss";

.headerModal {
  :global {
    .modal-title {
      font-size: 18px !important;
      color: #4285f4;
      font-weight: 600;
    }
  }
}

.bodyModal {
  min-height: 400px;
  position: relative;

  .containerLoading {
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.6);
  z-index: 10;
  transition: opacity 0.3s;
}

.loadingBlur {
  filter: blur(2px) grayscale(0.2);
  opacity: 0.7;
  pointer-events: none;
  transition: filter 0.3s, opacity 0.3s;
}

:global(.modal-dialog) {
  max-width: 600px;
}

.btnAct {
  font-size: 14px;
  padding: 8px 16px;
}

// Booking Info Section
.bookingInfoWrapper {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 20px;
}

.sectionTitle {
  color: #4285f4;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.bookingInfoGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.bookingInfoItem {
  display: flex;
  flex-direction: column;
}

.bookingLabel {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.bookingValue {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

// Divider
.divider {
  height: 1px;
  background: linear-gradient(to right, #e0e0e0, #f5f5f5, #e0e0e0);
  margin: 20px 0;
}

// Cancel Info Section
.cancelSection {
  .sectionTitle {
    color: #ff6b35;
  }
}

.cancelInfoWrapper {
  background: #fff5f5;
  border: 1px solid #ffebee;
  border-radius: 8px;
  padding: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.cancelInfoItem {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.cancelLabel {
  font-size: 14px;
  color: #666;
  font-weight: 600;
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.cancelValue {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  padding-left: 24px;
}

.cancelReason {
  font-size: 14px;
  color: #333;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  margin-left: 24px;
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 40px;
}

.statusInfo {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  margin-top: 10px;
}

.statusCanceled {
  font-weight: 600;
  color: #856404;
}

.noCancelInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  color: #6c757d;
  font-size: 16px;
  font-weight: 500;
}

// Banking Info Section
.bankingInfoWrapper {
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 20px;
}

.bankingInfoGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.bankingInfoItem {
  display: flex;
  flex-direction: column;
}

.bankingLabel {
  font-size: 13px;
  color: #2e7d32;
  font-weight: 600;
}

.bankingValue {
  font-size: 14px;
  color: #1b5e20;
  font-weight: 600;
  background: #f1f8e9;
  padding: 6px 10px;
  border-radius: 4px;
  border: 1px solid #dcedc8;
}

// Responsive
@media (max-width: 768px) {
  .bookingInfoGrid,
  .bankingInfoGrid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .cancelInfoWrapper,
  .bankingInfoWrapper {
    padding: 15px;
  }

  .bookingInfoWrapper {
    padding: 15px;
  }

  :global(.modal-dialog) {
    max-width: 95%;
    margin: 10px auto;
  }
}
