import React from "react";
import { withRouter } from "react-router-dom";
import BannerHome from "~/components/desktop/molecules/BannerHomeClinic";
import AdvanceHospital from "~/components/desktop/organisms/AdvanceHospital";
import FeatureHospital from "~/components/desktop/organisms/FeatureHospital";
import AdvanceMedProClinic from "~/components/desktop/organisms/AdvanceMedproClinic";
import FeatureMedProClinic from "~/components/desktop/organisms/FeatureMedproClinic";
import StatisMedproClinic from "~/components/desktop/organisms/StatisMedproClinic";
import ChooseClinicDesktop from "~/components/desktop/organisms/ListMedproClinic";
import AppId from "~/utils/partner";
import styles from "./style.module.scss";

class ClinicPage extends React.Component {
  componentDidUpdate() {
    if (
      this.props.history.location.state &&
      this.props.history.location.state.scrollToDownload
    ) {
      let downloadSection = window.document.getElementById(
        "download_application"
      );
      if (downloadSection) {
        let height = 0;
        do {
          height += downloadSection.offsetTop;
          downloadSection = downloadSection.offsetParent;
        } while (downloadSection);
        window.scrollTo({
          top: height - 52,
          behavior: "smooth"
        });
        this.props.history.replace();
      }
    }
  }

  render() {
    const featureNode =
      AppId !== "medpro" ? (
        <React.Fragment>
          <AdvanceHospital {...this.props} />
          <FeatureHospital />
        </React.Fragment>
      ) : (
        <React.Fragment>
          <StatisMedproClinic />
          <FeatureMedProClinic />
          <AdvanceMedProClinic />
        </React.Fragment>
      );

    return (
      <div className={styles.bg_page}>
        <BannerHome />
        {featureNode}
        <ChooseClinicDesktop {...this.props} {...this.methods} />
        <div id="firebaseui-auth-container" style={{ display: "none" }} />
      </div>
    );
  }
}

export default withRouter(ClinicPage);
