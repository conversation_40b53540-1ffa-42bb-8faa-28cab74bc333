import * as types from "./customerServiceTypes";

export const getHistoryBooking = payload => {
  return {
    type: types.GET_HISTORY_BOOKING,
    payload
  };
};

export const setDataHistoryBooking = value => {
  return {
    type: types.GET_HISTORY_BOOKING_SUCCESS,
    value
  };
};

export const setCSToken = value => {
  return {
    type: types.SET_CS_TOKEN,
    value
  };
};

export const selectPatientCSKhById = value => {
  return {
    type: types.SELECT_PATIENT_CSKH_BY_ID,
    value
  };
};

export const getTransactionInfoCSKH = transactionId => {
  return {
    type: types.GET_TRANSACTIONID_INFO_CSKH,
    transactionId
  };
};

export const resetTransactionInfoCSKH = () => {
  return {
    type: types.RESET_TRANSACTIONID_INFO_CSKH
  };
};

export const requestPaymentInfoCSKH = ({
  id,
  smsCode,
  transactionId,
  bookingCode,
  partnerId,
  typeAction = "",
  phone
}) => {
  return {
    type: types.GET_BOOKING_INFO_CSKH,
    payload: {
      id,
      smsCode,
      transactionId,
      bookingCode,
      partnerId,
      typeAction,
      phone
    }
  };
};

export const updateStatusBill = booking => {
  return {
    type: types.UPDATE_CANCEL_BILL,
    booking
  };
};

export const getPatientsByPhoneCSKH = phone => {
  return {
    type: types.GET_PATIENTS_BY_PHONE_CSKH,
    phone
  };
};

export const changeHospitalCSKH = hospital => {
  return {
    type: types.CHANGE_HOSPITAL_CSKH,
    payload: hospital
  };
};

export const selectPatientInfoCSKH = patientInfo => {
  return {
    type: types.SELECT_PATIENT_CSKH,
    payload: patientInfo
  };
};

export const getBookingbyPatientCSKH = patientInfo => {
  return {
    type: types.REQUEST_BOOKINGS_BY_PATIENT_CSKH,
    patientInfo
  };
};

// care247-independent
export const getCare247IndependentCSKH = (bookingId, actionType = "cancel") => {
  return {
    type: types.REQUEST_CARE247_INDEPENDENT_CSKH,
    bookingId: bookingId,
    actionType: actionType
  };
};

// Get History Booking
export const getHistoryBookingCSKH = patientInfo => {
  return {
    type: types.GET_HISTORY_BOOKING_CSKH,
    patientInfo
  };
};

export const clearSelectedPatientCSKH = () => {
  return {
    type: types.CLEAR_SELECTED_PATIENT
  };
};

export const setCskhSecretKey = secretKey => {
  return {
    type: types.SET_CSKH_SECRET_KEY,
    payload: secretKey
  };
};

export const deleteSelectedPatientByCSKH = () => {
  return {
    type: types.DELETE_PATIENT_BY_CSKH
  };
};
export const sendBookingViaMail = (id, email) => {
  return {
    type: types.SEND_BOOKING_VIA_EMAIL,
    payload: {
      id,
      email
    }
  };
};
export const sendBookingViaSms = (bookingCode, mobile) => {
  return {
    type: types.SEND_BOOKING_VIA_SMS,
    payload: {
      bookingCode,
      mobile
    }
  };
};

export const insertPatientIntoUserCskh = patientInfo => {
  return {
    type: types.INSERT_PATIEN_INTO_USER_CSKH,
    patientInfo
  };
};

export const resetAllCS = () => {
  return {
    type: types.RESET_ALL_CS
  };
};

export const resetBookingByPatientCS = () => {
  return {
    type: types.RESET_BOOKING_BY_PATIENT_CS
  };
};

export const resetBookingInfoCS = () => {
  return {
    type: types.RESET_BOOKING_INFO_CSKH
  };
};
