import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import Loadable from "react-loadable";
import { get, find } from "lodash";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import Modal from "~/components/common/molecules/Modal";
import {
  putPatient,
  toggleModalConfirm,
  resetModalConfirmPatient,
  verifyPhonePatient,
  selectedPatientDetail,
  verifyPatientWithoutPhone
} from "~/store/patient/patientAction";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import { partnerInfo } from "~/configs/partnerDetails";
const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const PatientResultSearchPageMobile = Loadable({
  loader: () =>
    import(/* mobile */ "~/components/desktop/pages/PatientResultSearch"),
  loading: LoadableLoading
});
const PatientResultSearchPageDesktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/PatientResultSearch"),
  loading: LoadableLoading
});

class PatientResultSearch extends Component {
  state = {
    phone: "",
    isExactPhone: "",
    errorCheckPhone: ""
  };

  methods = {
    onChangePhone: phone => {
      // const patternPhone = /^[0-9]+$/;
      // eslint-disable-next-line max-len
      const patternPhone = /^((\+?84|0)?(86|96|97|98|32|33|34|35|36|37|38|39|89|90|93|70|79|77|76|78|88|91|94|83|84|85|81|82|92|52|56|58|99|59))[0-9]{7}$/;
      if (phone !== "") {
        this.setState({
          errorCheckPhone: "",
          isExactPhone: true
        });
        if (!patternPhone.test(phone)) {
          this.setState({
            errorCheckPhone: "Vui lòng nhập đúng định dạng",
            isExactPhone: false
          });
        } else {
          this.setState({
            isExactPhone: true,
            errorCheckPhone: ""
          });
        }
      } else {
        this.setState({
          errorCheckPhone: "Vui lòng nhập số điện thoại",
          isExactPhone: false
        });
      }
      this.setState({ phone });
    },
    onSelectPatient: selectedPatient => {
      const { verifyPatientWithoutPhone, history } = this.props;
      const isVerifiedByPhone = get(selectedPatient, "isVerifiedByPhone", true);
      const secretKey = get(selectedPatient, "secretKey");
      if (isVerifiedByPhone) {
        this.props.handleToggleModalConfirm();
      } else {
        verifyPatientWithoutPhone(secretKey);
        history.push("/xac-nhan-thong-tin-benh-nhan");
      }
      this.props.handleSelectedPatientDetail(selectedPatient);
    }
  };

  styles = {
    red: {
      color: "text-danger"
    }
  };

  handleToggleModalConfirmPhone = () => {
    this.setState({ errorCheckPhone: "" });
    this.props.handleToggleModalConfirm();
  };

  handleSubmitPhone = () => {
    const { isExactPhone, phone } = this.state;
    if (phone !== "") {
      this.setState({
        errorCheckPhone: "",
        isExactPhone: true
      });
      if (isExactPhone) {
        this.props.handleVerifyPhonePatient(this.state.phone);
      }
    } else {
      this.setState({
        errorCheckPhone: "Vui lòng nhập số điện thoại",
        isExactPhone: false
      });
    }
  };

  renderBodyModalConfirmPhone = () => {
    const { selectedPatient, phone } = this.props;
    const { isExactPhone, errorCheckPhone } = this.state;
    let message = `Bệnh nhân vui lòng liên hệ Bộ phận hỗ trợ (Hotline: 1900-2115) để cập nhật số điện thoại mới.`;
    let isNoPhone = true;
    if (selectedPatient && selectedPatient.mobile !== "") {
      message = `Vui lòng nhập lại đầy đủ số điện thoại để xác nhận hồ sơ bệnh nhân`;
      isNoPhone = false;
    }
    return (
      <Fragment>
        <p className={isNoPhone ? this.styles.red.color : ""}>{message}</p>
        {!isNoPhone && (
          <input
            type="text"
            placeholder="Nhập số điện thoại"
            className="form-control"
            value={phone}
            onChange={e => this.methods.onChangePhone(e.target.value)}
            onKeyPress={event => {
              if (event.key === "Enter") {
                this.handleSubmitPhone();
              }
            }}
          />
        )}
        {!isExactPhone && (
          <p style={{ fontSize: 11, paddingTop: 5, color: "red" }}>
            {errorCheckPhone}
          </p>
        )}
      </Fragment>
    );
  };

  componentDidMount = () => {
    this.props.resetModalConfirmPatient();
  };

  render() {
    const { device } = this.props;
    const { selectedPatient } = this.props;
    return (
      <React.Fragment>
        {device === "mobile" ? (
          <PatientResultSearchPageMobile
            {...this.state}
            {...this.props}
            {...this.methods}
          />
        ) : (
          <PatientResultSearchPageDesktop
            {...this.state}
            {...this.props}
            {...this.methods}
          />
        )}
        {selectedPatient && selectedPatient.mobile === "" ? (
          <Modal
            iconTitle={<i className="fal fa-bell" />}
            backdropClassName="backdrop_centered"
            modal={this.props.showModalConfirm}
            // eslint-disable-next-line react/jsx-handler-names
            toggle={this.handleToggleModalConfirmPhone}
            title="Xác nhận số điện thoại"
            children={this.renderBodyModalConfirmPhone()}
            centered
            className="centered"
            onCancel={this.handleToggleModalConfirmPhone}
            onOk={() => this.handleSubmitPhone()}
          />
        ) : (
          <Modal
            iconTitle={<i className="fal fa-bell" />}
            backdropClassName="backdrop_centered"
            modal={this.props.showModalConfirm}
            // eslint-disable-next-line react/jsx-handler-names
            toggle={this.handleToggleModalConfirmPhone}
            title="Xác nhận số điện thoại"
            children={this.renderBodyModalConfirmPhone()}
            centered
            className="centered"
            footer
            footerConfirm
            cancelText="Đóng"
            okText="Xác nhận"
            onCancel={this.handleToggleModalConfirmPhone}
            onOk={() => this.handleSubmitPhone()}
          />
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    patient: {
      searchPatientByInfoList,
      showLoading,
      showModalConfirm,
      selectedPatient
    },
    hospital: { selectedHospital }
  } = state;
  return {
    device: type,
    searchPatientByInfoList,
    isShowLoading: showLoading,
    showModalConfirm,
    selectedHospital,
    selectedPatient
  };
};

const mapDispatchToProps = dispatch => ({
  handlePutPatient: data => {
    dispatch(putPatient(data));
  },
  handleToggleModalConfirm: () => {
    dispatch(toggleModalConfirm());
  },
  resetModalConfirmPatient: () => {
    dispatch(resetModalConfirmPatient());
  },
  handleVerifyPhonePatient: phone => {
    dispatch(verifyPhonePatient(phone));
  },
  handleSelectedPatientDetail: patient => {
    dispatch(selectedPatientDetail(patient));
  },
  verifyPatientWithoutPhone: key => {
    dispatch(verifyPatientWithoutPhone(key));
  }
});

const PatientResultSearchHelmet = withTitle({
  component: PatientResultSearch,
  title: `${hospitalName.value} | Kết quả tìm bệnh nhân`
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PatientResultSearchHelmet);
