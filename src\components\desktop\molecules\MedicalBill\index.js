import { MDBAnimation } from "mdbreact";
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import Alert from "~/components/common/atoms/Alert";
import NoContentAlert from "~/components/desktop/atoms/NoContentAlert";
import HealthInsuranceCardList from "~/components/mobile/molecules/HealthInsuranceCardList";
import { resendSMS } from "~/store/booking/bookingAction";
import {
  hideAlertSMSOK,
  toggleResendSMSModal
} from "~/store/payment/paymentAction";
import { getAllMedicalBills } from "~/store/totalData/actions";

class MedicalBill extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpenDeleteModal: false,
      selectedPatient: false
    };
  }

  resendSMS = phoneNumber => {
    const {
      info: user,
      selectedHealthInsuranceCard: { id }
    } = this.props;
    const data = {
      user_id: user.user_id,
      access_token: user.access_token,
      booking_id: id,
      mobile: phoneNumber
    };
    this.props.handleResendSMS(data);
  };

  hideAlert = () => {
    this.props.hideAlertSMSOK();
  };

  closeResendSMSModal = () => {
    this.props.OnToggleResendSMSModal();
  };

  componentDidMount() {
    this.props.getAllMedicalBills();
  }

  renderListBooking = () => {
    const { bookingList } = this.props;
    if (bookingList.length === 0) {
      return <NoContentAlert message="Bạn chưa có thông tin phiếu khám." />;
    } else {
      return <HealthInsuranceCardList bookingList={bookingList} />;
    }
  };

  render() {
    const { showResendSMSOK, resendSMSMessage } = this.props;
    return (
      <Fragment>
        <MDBAnimation type="fadeIn">
          {this.renderListBooking()}
          <Alert
            isModal={showResendSMSOK}
            message={resendSMSMessage}
            toggleAlert={this.hideAlert}
          />
        </MDBAnimation>
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { info },
    patientForm: { redirectToPatientUpdate },
    healthInsuranceCard: {
      showResendSMSModal,
      showResendSMSOK,
      selectedHealthInsuranceCard,
      resendSMSMessage
    },
    totalData: { bookingList }
  } = state;
  return {
    user: info,
    redirectToPatientUpdate,
    showResendSMSModal,
    showResendSMSOK,
    selectedHealthInsuranceCard,
    resendSMSMessage,
    bookingList
  };
};

const mapDispatchToProps = dispatch => ({
  OnToggleResendSMSModal: () => {
    dispatch(toggleResendSMSModal());
  },
  handleResendSMS: data => {
    dispatch(resendSMS(data));
  },
  hideAlertSMSOK: () => {
    dispatch(hideAlertSMSOK());
  },
  getAllMedicalBills: () => {
    dispatch(getAllMedicalBills());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(MedicalBill));
