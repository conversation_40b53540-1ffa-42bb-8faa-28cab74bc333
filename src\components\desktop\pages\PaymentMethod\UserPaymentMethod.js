import React, { useState } from "react";
import { get, size } from "lodash";
import {
  MDBCardBody,
  MDBCol,
  MDBListGroup,
  MDBListGroupItem,
  MDBRow
} from "mdbreact";
import styles from "~/components/desktop/pages/PaymentMethod/style.module.scss";
import ListPaymentMethod from "~/components/desktop/molecules/ListPaymentMethod";
import { getFormatMoney } from "~/utils/getFormatMoney";
import AppId from "~/utils/partner";

const UserPaymentMethod = props => {
  const [type, setType] = useState({});
  const [method, setMethod] = useState({});
  const [methodId, setMethodId] = useState({});

  const { schedulesSelected, listUserPaymentMethod } = props;
  const handleSelectedPaymentType = (type, methodId) => {
    console.log("type: ", type);
    setType(type);
    setMethodId(methodId);
  };

  const handleSelectedMethod = _method => {
    if (_method.methodId !== methodId) {
      setType({});
    }

    setMethod(_method);
    if (_method.paymentTypes.length === 1) {
      setType(_method.paymentTypes[0]);
      setMethodId(_method.methodId);
    }
  };

  const displayDetail = get(schedulesSelected[0], "service.displayDetail", "");
  console.log("type: ", type);
  console.log("listUserPaymentMethod[0]: ", listUserPaymentMethod[0]);
  const subTotal = get(
    size(type) > 0 ? type : listUserPaymentMethod[0],
    "subTotal",
    0
  );
  const totalFee = get(type, "totalFee", 0);
  const grandTotal = get(
    size(type) > 0 ? type : listUserPaymentMethod[0],
    "grandTotal",
    0
  );

  return (
    <MDBCardBody className={styles.card_body}>
      <MDBRow>
        <MDBCol md="6">
          <div className={styles.group_payment}>
            <ListPaymentMethod
              allPaymentMethodGroups={listUserPaymentMethod}
              handleSelectedMethod={handleSelectedPaymentType}
              selectedMethod={type}
              selectPaymentMethodGroup={handleSelectedMethod}
              selectedPaymentMethodGroupId={method?.methodId}
            />
          </div>
        </MDBCol>
        <MDBCol md="6">
          <div className={styles.list_group_payment}>
            <div className={styles.sub_title}>
              <i className="fal fa-info-square" />
              Thông tin thanh toán
            </div>
            <div className={styles.total_payment}>
              <MDBListGroup className={styles.list_group}>
                <MDBListGroupItem>
                  Tổng tiền khám:
                  <strong>
                    {displayDetail && displayDetail !== ""
                      ? displayDetail
                      : getFormatMoney(subTotal) + " VNĐ"}
                  </strong>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  {AppId !== "vanhanh"
                    ? "Phí tiện ích:"
                    : "Phí đăng kí qua ứng dụng:"}
                  <strong>
                    {totalFee === 0
                      ? "0 VNĐ"
                      : getFormatMoney(totalFee) + " VNĐ"}{" "}
                  </strong>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  TỔNG CỘNG:
                  <strong>{getFormatMoney(grandTotal)} VNĐ</strong>
                </MDBListGroupItem>
              </MDBListGroup>
            </div>
          </div>
        </MDBCol>
      </MDBRow>
    </MDBCardBody>
  );
};

export default UserPaymentMethod;
