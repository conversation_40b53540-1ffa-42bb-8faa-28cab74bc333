.Box_Wrapper{
    height: fit-content;
    max-height: 300px;
    overflow: scroll;
    overflow-x: hidden;
    padding: 1%;
    border: 1px solid blue
}

.loadingText {
    width: 100%;
    text-align: center;
    margin: auto;
    font-weight: bold;
    font-size: 1.2vw;
    color: blue;
}

.loader,
.loader:after {
  border-radius: 50%;
  width: 10em;
  height: 10em;
}
.loader {
  margin: 60px auto;
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 1.1em solid rgba(97,173,255, 0.2);
  border-right: 1.1em solid rgba(97,173,255, 0.2);
  border-bottom: 1.1em solid rgba(97,173,255, 0.2);
  border-left: 1.1em solid #61adff;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load8 1.1s infinite linear;
  animation: load8 1.1s infinite linear;
}
@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
