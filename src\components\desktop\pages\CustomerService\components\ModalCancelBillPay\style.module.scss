@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/button.scss";

.loading {
  min-height: 70vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  align-content: center;
  opacity: 0.5;
}

.banner {
  min-width: 360px;
  max-width: 360px;
  background-image: url("/assets/img/bannerBillPrint.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;

  .title {
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    color: white;
    margin-bottom: 5px;
  }
  .subTitle {
    font-size: 13px;
    text-align: center;
    color: white;
    margin-bottom: 5px;
  }
  .group {
    margin-top: 10px;
    display: flex;
    justify-content: space-around;
    padding: 0 10px;
    .QR {
      padding: 10px;
      border-radius: 10px;
      background-color: white;
    }
    .linkDown {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      align-content: center;
      p {
        font-style: italic;
        color: white;
        font-size: 17px;
        font-weight: bolder;
        margin-bottom: 5px;
      }
    }
  }
}
.print {
  background-color: white;
  padding: 15px;
  max-width: 360px;
  color: #12263f;
  font-size: 14px;
  margin: 10px;
  @media screen and (max-width: 576px) {
    margin: 0px;
    max-width: unset;
    width: 100%;
  }
  ul {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
    li {
      b,
      strong {
        font-weight: 500;
        text-align: right;
      }
      .nbold {
        font-weight: normal;
      }
      p {
        margin-bottom: 0;
      }
    }
  }

  .totalPaymentMessage {
    color: orangered;
    margin-top: 20px;
    text-align: center;
    .Message {
      display: block;
    }
    .MessageExtra {
      display: block;
      font-size: 12px;
    }
  }

  .red {
    color: orangered;
  }

  .green {
    color: #6dec7b;
  }
  .public_medpro {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 18px;
    p {
      color: #24313d;
      font-size: 16px;
      font-weight: 600;
      line-height: 16.94px;
      text-transform: uppercase;
      margin-bottom: 4px;
    }
    img {
      width: 88px;
      height: 27px;
      object-fit: cover;
    }
  }
  .title_hospital {
    margin-bottom: 10px;
    text-align: center;
    img {
      width: 70%;
      height: auto;
      margin: 15px 0;
    }
    .sub_title {
      font-size: 18px;
      text-align: center;
      font-weight: bold;
    }
    .nameParner {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    .addressParner {
      font-size: 13px;
    }
  }

  .bar_code {
    margin-bottom: 20px;
    text-align: center;

    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    p {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .isNewUserTxt {
      font-style: italic;
      color: #df0000;
      font-size: 12px;
      margin-bottom: 1rem;
    }

    svg {
      max-width: 100%;
      margin: 0 0 !important;
    }
  }

  .form_code {
    text-align: center;
    margin-bottom: 15px;
  }
  .info_examination {
    text-align: center;
    margin-bottom: 20px;
    ul {
      li {
        font-weight: 500;
      }
    }
  }

  .txtNum {
    font-size: 13px !important;
    font-weight: 500 !important;
  }
  .number,
  .gray {
    font-family: Roboto;
    font-size: 60px;
    color: #00b5f1;
    font-weight: bold;
    text-align: center;
  }
  .gray {
    color: #c6cace;
  }

  .top_note {
    text-align: center;
    font-weight: 500;
    margin-bottom: 5px;
    font-size: 0.9rem;
  }
  .attention {
    color: #df0000;
    font-style: italic;
    font-size: 14px;
    font-weight: bold;
  }
  .time_note {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    margin-bottom: 15px;
    flex-direction: column;

    .cancelMessage {
      font-style: italic;
      color: #df0000;
      margin-bottom: 1rem;
    }

    .titleBooking {
      padding: 7px 25px;
      border-radius: 15px;
      color: white;
      font-size: 14px;
      margin-bottom: 1rem;
    }

    .timeNoteCancel {
      color: #c6cace;
    }
    .greenNote {
      background-color: #6dec7b;
    }
    .redNote {
      background-color: red;
    }
    .greyNote {
      background-color: #c6cace;
    }
    .btnShare {
      margin-left: 0.5rem;
      display: inline-block;
      padding: 7px 25px;
      border-radius: 15px;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border: 1px solid snow;
      box-shadow: 0 2.8px 2.2px rgba(0, 0, 0, 0.034),
        0 12.5px 10px rgba(0, 0, 0, 0.06), 0 100px 80px rgba(0, 0, 0, 0.12);
      a {
        color: blue;
      }
    }
  }
  .status_number {
    text-align: center;
    span {
      display: inline-block;
      padding: 0.25em 0.4em;
      font-size: 75%;
      font-weight: 700;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25rem;
      background-color: #6e84a3;
      color: #ffffff;
    }
  }
  .list_detail {
    margin-top: 20px;
    margin-bottom: 10px;
    ul {
      li {
        margin-bottom: 10px;
        display: flex;
        align-items: flex-start;
        align-content: center;
        justify-content: space-between;

        &:last-child {
          margin-bottom: 0;
        }
        span {
          display: block;
          min-width: 160px;
        }
      }
    }
  }
  .note {
    margin-bottom: 10px;
    font-style: italic;
  }
  .profile_number,
  .note_time {
    margin-bottom: 10px;
  }

  .organization {
    margin-bottom: 15px;
    .titleOrg {
      width: 100%;
      display: flex;
      align-items: center;
      align-content: center;
      justify-content: center;

      .txt {
        font-size: 12px;
        margin-right: 15px;
        margin-bottom: 0;
      }
      img {
        width: 60px;
      }
    }
    .txtOrg {
      font-size: 11px;
      text-align: center;
    }
  }
  .thanhToanLai {
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: center;
    justify-content: center;

    .identified {
      margin-bottom: 15px;
      text-align: center;
      img {
        margin-bottom: 5px;
      }
      .status {
        font-weight: 600;
        font-size: 11px;
        margin-bottom: 0px;
      }
    }
    .message {
      background: #fff8eb;
      border: 1px solid #ffcc80;
      box-sizing: border-box;
      border-radius: 8px;
      padding: 10px;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      padding: 8px;
      margin-bottom: 15px;
      img {
        width: 20px;
        height: 20px;
      }
      .txt {
        padding-left: 5px;
      }
    }
    .timesDiv {
      margin-bottom: 15px;
      .txt {
        font-size: 14px;
        text-align: center;
        color: orangered;
        margin-bottom: 5px;
        font-weight: bold;
      }
      .time {
        margin-bottom: 5px;
        font-size: 26px;
        text-align: center;
        color: orangered;
        font-weight: bold;
      }
    }
    .groupBtn {
      display: flex;
      justify-content: space-around;
      width: 100%;
      margin-bottom: 15px;
    }
    .support {
      background: #f5f7fa;
      border: 0.5px solid #ffcc80;
      box-sizing: border-box;
      border-radius: 8px;
      padding: 10px;

      display: flex;
      flex-direction: column;
      align-items: center;
      align-content: center;
      justify-content: center;
    }
  }
  .awaitMessage {
    color: #ffb340;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    flex-direction: column;
    margin-bottom: 15px;
    p {
      padding: 0 35px;
    }
    .spin {
      display: block;
    }
  }

  //  đường line chuột gặm
  .line {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    margin: 0 -35px;
    padding: 15px 0;
    .circle {
      width: 35px;
      height: 35px;
      background-color: whitesmoke;
      border-radius: 50%;
    }
    .dashed {
      width: 80%;
      border-top: 3px dashed whitesmoke;
    }
  }
  .top {
    padding: 0;
    margin-top: -28px;
    .dashed {
      border: none;
    }
  }
  .bottom {
    padding: 0;
    margin-bottom: -28px;
    .dashed {
      border: none;
    }
  }
  //  đường line chuột gặm
}

.footer_button_navigation {
  display: flex;
  justify-content: space-around;
  .bottom_navigation_custom {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    .button {
      font-size: 14px;
      padding: 10px 20px;
      &.cancel {
        &:hover,
        &:focus {
          color: #0352cc !important;
          box-shadow: 0 3px 6px rgba(59, 64, 69, 0.2);
          border: 15px;
        }
      }
    }
  }
}
.cancelModal {
  max-width: 618px !important;
  display: flex;
  align-items: center;
  margin: 1.75rem auto;
  border-radius: 8px;
  overflow: hidden;
  min-height: calc(100% - 3.5rem);
}

.modalBody {
  padding: 20px 40px;
  background-color: #fff;
  @media screen and (max-width: 768px) {
    padding: 20px 16px;
  }
  .closeButton {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 10;

    &:hover {
      background-color: #f5f5f5;
      color: #333;
    }

    &:active {
      transform: scale(0.95);
    }

    i {
      font-size: 16px;
    }
  }
  h3 {
    margin: 0;
    text-align: center;
    margin-bottom: 5px;
    font-size: 18px;
    font-weight: 600;
    color: #24313d;
  }
  .stepIndicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    gap: 16px;

    .stepLine {
      height: 4px;
      width: 40px;
      background: #e0e0e0;
      border-radius: 2px;
      transition: all 0.3s ease;

      &.lineActive {
        background: #00b5e2;
      }
    }

    .stepCircle {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 16px;
      border: 2px solid #e0e0e0;
      background: #e0e0e0;
      color: #a0a0a0;
      transition: all 0.3s ease;

      &.stepActive {
        background: #00b5e2;
        border-color: #00b5e2;
        color: white;
      }

      &.clickable {
        cursor: pointer;

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 2px 8px rgba(0, 181, 226, 0.3);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  .modalDescription {
    text-align: center;
    color: #666;
    font-style: italic;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .reasonList {
    margin-bottom: 20px;
    width: 100%;

    .card_body_cance {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .reasonCheckbox {
        margin-right: 10px;
        width: 18px;
        height: 18px;
        accent-color: #00b0f0;
      }

      label {
        margin-bottom: 0;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }

  .refundAmount {
    text-align: center;
    margin-bottom: 24px;
    padding: 20px;
    border-radius: 8px;

    .amountTitle {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 17px;
      font-weight: 500;
      line-height: 22px;
      color: #12263f;
      margin-bottom: 4px !important;
      .amountValue {
        font-size: 28px;
        font-weight: 700;
        color: #24313d;
        margin-bottom: 0;
      }
    }

    .tooltipWrapper {
      position: relative;
      display: inline-block;
      transform: translate(1px, -15px);
      .infoIcon {
        font-size: 16px;
        color: #666;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          // color: #00b5e2;
        }
      }

      .tooltip {
        max-width: 300px;
        min-width: 300px;
        visibility: hidden;
        opacity: 0;
        position: absolute;
        bottom: 125%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #333;
        color: white;
        text-align: center;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 12px;
        font-weight: normal;
        z-index: 1000;
        transition: opacity 0.3s, visibility 0.3s;

        // Mũi tên tooltip
        &::after {
          content: "";
          position: absolute;
          top: 100%;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: #333 transparent transparent transparent;
        }
      }

      &:hover .tooltip {
        visibility: visible;
        opacity: 1;
      }
    }

    .amountNote {
      font-weight: 400;
      font-size: 16px;
      color: #767676;
      margin: 0;
    }
  }

  .appointmentInfo {
    margin-bottom: 24px;
    background: #f5f7fa;
    border-radius: 12px;
    padding: 16px;

    .sectionTitle {
      font-size: 17px;
      font-weight: 590;
      color: #12263f;
      line-height: 22px;
    }

    .infoGrid {
      .infoItem {
        display: flex;

        .infoLabel,
        .infoValue {
          font-size: 16px;
          color: #757575;
          font-weight: 400;
        }

        .infoValue {
        }
        .infoLabel {
          min-width: 130px;
        }
      }
    }
  }

  .refundForm {
    margin-bottom: 24px;
    background: #f5f7fa;
    border-radius: 12px;
    padding: 16px;

    .sectionTitle {
      font-size: 17px;
      font-weight: 590;
      color: #12263f;
      line-height: 22px;
    }
    .formRow {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    .formGroup {
      margin-bottom: 16px;

      .formLabel {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
        span {
          color: red;
        }
      }

      .formInput {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        background: white;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: #00b5e2;
          box-shadow: 0 0 0 2px rgba(0, 181, 226, 0.1);
        }

        &::placeholder {
          color: #999;
        }
      }
      .formSelect {
      }

      .singleValue {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .refundNote {
    margin-bottom: 24px;

    .noteTitle {
      font-size: 16px;
      font-weight: 700;
      color: #000000;
      line-height: 21px;
      margin-bottom: 8px;
    }

    .noteText {
      font-size: 16px;
      font-weight: 400;
      color: #000000;
      line-height: 21px;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .hotlineLink {
      color: #00b5f1;
      font-weight: 600;
    }
  }

  .modalActions {
    display: flex;
    gap: 12px;
    justify-content: center;

    .cancelButton,
    .confirmButton {
      padding: 12px 24px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      min-width: 120px;
    }
    .confirmButton:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none; /* nếu muốn không nhận sự kiện */
    }

    .cancelButton {
      background: #f8f9fa;
      color: #666;
      border: 1px solid #ddd;

      &:hover {
        background: #e9ecef;
      }
    }

    .confirmButton {
      background: #00b5e2;
      color: white;

      &:hover {
        background: #0099cc;
      }
    }
  }

  .reasonTextarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    min-height: 100px;
    margin-bottom: 20px;
    font-size: 14px;
    resize: none;

    &::placeholder {
      color: #aaa;
    }
  }

  .modalActions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;

    button {
      padding: 10px 0;
      border-radius: 4px;
      font-weight: 500;
      width: 48%;
      border: none;
      cursor: pointer;
      font-size: 14px;
    }

    .cancelButton {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;
    }

    .confirmButton {
      background-color: #00b0f0;
      color: white;
    }
  }

  .hotlineInfo {
    text-align: center;
    font-size: 14px;
    color: #666;

    .hotlineNumber {
      color: #00b0f0;
      font-weight: 600;
    }
  }
}
