@import "src/assets/scss/custom-variables";
.Container{
  background: linear-gradient(90deg, #7687D5, #1BADC4);
  max-width: 100%;
  padding: 55px 12px;

  display: flex;
  justify-content: center;
  .rowBanner{
    max-width: 1440px;
    .colLogin{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      padding: 12px;
      .Intro{
        max-width: 80%;
        p{
          display: flex;
          flex-direction: column;
          text-align: center;
          padding-bottom: 40px;
          .title{
            font-size: 32px;
            font-weight: 700;
            color: white;
            padding-bottom: 16px;
          }
          .subtitle{
            font-size: 16px;
            font-weight: 400;
            color: white;
          }
        }
        .ButtonLogin{
          display: flex;
          justify-content: space-around;
          .register{
            border-radius: 50px;
            font-size: 14px;
            font-weight: 700;
          }
          .login{
            border-radius: 50px;
            font-size: 14px;
            font-weight: 700;
            color: #7B8794;
            padding: 0 40px;
          }
        }
      }
    }
    .colAds{
      display: flex;
      align-items: center;
      padding: 12px;
      .img{
        max-width: 628px;
        max-height: 388px;
      }
    }
  }
}

@media (max-width: 425px){
  .title{
    font-size: 22px !important;
  }
  .subtitle{
    font-size: 12px !important;
  }
}
@media (max-width: 537px){
  .ButtonLogin{
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }
}
@media (max-width : 768px){
  .login{
    padding: 10px 40px !important;
  }
  .colAds{
    display: none !important;
  }
}
@media (min-width: 768px) AND (max-width: 1440px){
  .colAds{
    justify-content: center !important;
  }
}
