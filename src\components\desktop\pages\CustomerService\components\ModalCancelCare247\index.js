import {
  MDBModalBody,
  MDBModalHeader,
  MDBModal,
  MDBModalFooter,
  MDBBtn
} from "mdbreact";
import React, { useState } from "react";
import styles from "./style.module.scss";
import { isEmpty } from "lodash";

const ModalCancelCare247 = ({ isOpen, toggle, infoExam, onCancelCare247 }) => {
  const [reasonCancel, setReasonCancel] = useState("");
  const [isError, setIsError] = useState(false);
  return (
    <MDBModal isOpen={isOpen} centered>
      <MDBModalHeader className={styles.headerModal}>
        Hủy dịch vụ Care247
      </MDBModalHeader>
      <MDBModalBody>
        <label className={styles.content}>
          Bạn có muốn hủy dịch vụ đặt thêm của phiếu khám{" "}
          <span>{infoExam?.bookingCode}</span> không?
        </label>
        <p className={styles.titleForm}>
          <PERSON><PERSON> do hủy <sup>*</sup> :
        </p>
        <textarea
          rows="3"
          placeholder="Vui lòng nhập lý do hủy dịch vụ care247......"
          className={styles.reasonCancel}
          onChange={e => setReasonCancel(e.target.value)}
        />
        {isError && (
          <span style={{ color: "red" }} className={styles.errorMessage}>
            Vui lòng nhập lý do hủy dịch vụ!!!
          </span>
        )}
      </MDBModalBody>
      <MDBModalFooter>
        <MDBBtn size="sm" color="" className={styles.btnAct} onClick={toggle}>
          Đóng
        </MDBBtn>
        <MDBBtn
          size="sm"
          color="primary"
          className={styles.btnAct}
          onClick={() => {
            if (!isEmpty(reasonCancel.trim())) {
              onCancelCare247({
                id: infoExam.care247._id,
                message: reasonCancel
              });
              setIsError(false);
            } else {
              setIsError(true);
            }
          }}
        >
          Đồng ý
        </MDBBtn>
      </MDBModalFooter>
    </MDBModal>
  );
};

export default ModalCancelCare247;
