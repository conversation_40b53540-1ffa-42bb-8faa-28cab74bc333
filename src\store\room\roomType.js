export const REQUEST_UMC_ROOM_LIST = 'REQUEST_UMC_ROOM_LIST';
export const REQUEST_UMC_ROOM_LIST_SUCCESS = 'REQUEST_UMC_ROOM_LIST_SUCCESS';
export const REQUEST_UMC_ROOM_LIST_FAIL = 'REQUEST_UMC_ROOM_LIST_FAIL';

export const SELECTED_ROOM_AND_DOCTOR = 'SELECTED_ROOM_AND_DOCTOR';
export const SELECTED_ROOM_AND_DOCTOR_WITHOUT_LOADING =
  'SELECTED_ROOM_AND_DOCTOR_WITHOUT_LOADING';
export const HIDE_MODAL_PICK_TIME = 'HIDE_MODAL_PICK_TIME';

export const SELECTED_ROOM_AND_DOCTOR_SUCCESS =
  'SELECTED_ROOM_AND_DOCTOR_SUCCESS';
export const SELECTED_ROOM_AND_DOCTOR_FAIL = 'SELECTED_ROOM_AND_DOCTOR_FAIL';

export const SELECTED_TIME = 'SELECTED_TIME';

export const SELECTED_BHYT = 'SELECTED_BHYT';

export const SUBMIT_DOCTOR_AND_TIME = 'SUBMIT_DOCTOR_AND_TIME';

export const RESET_REDIRECT_TO_CONFIRM_INFO = 'RESET_REDIRECT_TO_CONFIRM_INFO';

export const REMOVE_DOCTOR_AND_TIME = 'REMOVE_DOCTOR_AND_TIME';

export const SHOW_MESSAGE_ERROR_CHOOSE_TIME_AND_BHYT =
  'SHOW_MESSAGE_ERROR_CHOOSE_TIME_AND_BHYT';

export const RESET_ALL_PROPS_DOCTOR_AND_TIME =
  'RESET_ALL_PROPS_DOCTOR_AND_TIME';

export const GET_DOCTOR_TIME_SLOT_REQUEST = 'GET_DOCTOR_TIME_SLOT_REQUEST';
export const GET_DOCTOR_TIME_SLOT_SUCCESS = 'GET_DOCTOR_TIME_SLOT_SUCCESS';
export const GET_DOCTOR_TIME_SLOT_FAILURE = 'GET_DOCTOR_TIME_SLOT_FAILURE';

export const REQUEST_TIME_SLOT_IN_DOCTOR_FLOW =
  'REQUEST_TIME_SLOT_IN_DOCTOR_FLOW';
export const RESET_SELECTED_TIME_AND_BHYT = 'RESET_SELECTED_TIME_AND_BHYT';
export const RESET_SELECTED_DOCTOR = 'RESET_SELECTED_DOCTOR';
