import React, { Component } from "react";
import styles from "./style.module.scss";

class BoxHeader extends Component {
  btnCreateNewHandler = () => {
    this.props.setSecretKey();
  };

  render() {
    return (
      <div className={styles.box_header}>
        <div className={styles.box_header_title}>
          <p className={styles.title}>{this.props.title}</p>
        </div>
        <button
          onClick={() => this.btnCreateNewHandler()}
          style={{
            backgroundImage: "linear-gradient(90deg, #15e627 0%, #07be3e 100%)"
          }}
        >
          {/* <MDBIcon icon="plus-circle" /> */}
          <span className={styles.button_text}> tạo mới</span>
        </button>
        {/* <button
          onClick={() => this.props.toggleSearchBox()}
          style={{
            backgroundImage: "linear-gradient(90deg, #11ece1 0%, #0cd5a9 100%)"
          }}
        >
          <MDBIcon icon="plus-circle" />
          <span className={styles.button_text}> t<PERSON><PERSON> kiếm</span>
        </button> */}
      </div>
    );
  }
}

export default BoxHeader;
