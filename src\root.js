import axios from "axios";
import "bootstrap-css-only/css/bootstrap.min.css";
import "core-js";
import { find, get } from "lodash";
import React, { Fragment } from "react";
import { Provider } from "react-redux";
import { Redirect, Route, Router, Switch } from "react-router-dom";
import { PersistGate } from "redux-persist/lib/integration/react";
import "~/api/axios.config";
import "~/assets/scss/mdb.scss";
import Button from "~/components/common/atoms/Button";
import ScrollToTop from "~/components/common/atoms/ScrollToTop";
import GetContent from "~/components/common/molecules/GetContent";
import { ToastNotification } from "~/components/common/molecules/ToastNotification";
import Breadcrumb from "~/components/desktop/atoms/Breadcrumb";
import StickyNotification from "~/components/desktop/molecules/StickyNotification";
import Footer from "~/components/desktop/organisms/Footer";
import Header from "~/components/desktop/organisms/Header";
import styles from "~/components/desktop/templates/AuthTemplate/style.module.scss";
import NavBar from "~/components/mobile/atoms/NavBar";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import history from "~/history";
import AuthenticationMedpro from "~/router_hospitals/AuthenticationMedpro";
import { listAppId, ROUTE_WITHOUT_HEADER } from "~/utils/constants";
import listRouters from "~/utils/listRouter";
import AppId from "~/utils/partner";
import { scrollToTop } from "~/utils/tool";
import { persistor, store } from "./store";
import "./store/checkVersion";

import { client } from "./utils/medproSDK";

const favicon = document.getElementById("favicon");
favicon.href = `./favicon/${AppId}.png`;

// dynamic title web
const info = get(partnerInfo, "info");
const nameInfo = find(info, { key: "name" });
document.getElementById(
  "title-page"
).textContent = `${nameInfo.value} | Phần mềm đăng ký khám bệnh Online`;

class Root extends React.Component {
  constructor(props) {
    super(props);
    axios.defaults.headers.appid = AppId;
    axios.defaults.headers.partnerid = AppId;
  }

  state = {
    isOpen: false
  };

  headerScrollRef = React.createRef();
  socialSupportRef = React.createRef();

  onhandleToggle = () => {
    this.setState({
      isOpen: !this.state.isOpen
    });
  };

  handleTop = () => {
    scrollToTop(700);
  };

  async componentDidMount() {
    const currentState = store.getState();
    const typeDv = currentState.global.device.type;
    client.setPlatform(typeDv === "desktop" ? "pc" : "web");

    window.addEventListener("scroll", this.handleScroll);
  }

  componentWillUnmount() {
    window.removeEventListener("scroll", this.handleScroll);
  }

  handleScroll = () => {
    if (!!this.headerScrollRef.current && !!this.socialSupportRef.current) {
      if (window.pageYOffset > 150 && window.innerWidth > 992) {
        this.headerScrollRef.current.style.display = "block";
        this.socialSupportRef.current.style.display = "block";
      } else {
        this.headerScrollRef.current.style.display = "none";
        this.socialSupportRef.current.style.display = "none";
      }
    }
  };

  render() {
    window.history.scrollRestoration = "manual";

    return (
      <Provider store={store}>
        <PersistGate loading={<LoadableLoading />} persistor={persistor}>
          <Router history={history}>
            <Route
              render={({ location }) => {
                const pathname = location.pathname;
                const mainPathname = pathname.split("/")[1];
                // Các route kh chứa header
                const routeWithoutHeader = !ROUTE_WITHOUT_HEADER.includes(
                  `/${mainPathname}`
                );
                // console.log(`/${mainPathname[1]}`);
                // console.log(routeWithoutHeader);
                return (
                  <React.Fragment>
                    <div className={styles.wrapper_template}>
                      {routeWithoutHeader &&
                      !pathname.includes("-app") &&
                      !pathname.includes("/in-phieu-kham-cskh") &&
                      !pathname.includes("/cham-soc-khach-hang") ? (
                        <Fragment>
                          <Header handleToggle={this.onhandleToggle} />
                          <NavBar
                            hideLeft
                            rightContent={[
                              <Button
                                column="column"
                                menu="menu"
                                icon={<i className="fal fa-bars" />}
                                key={1}
                              >
                                Menu
                              </Button>
                            ]}
                          />
                        </Fragment>
                      ) : (
                        <StickyNotification />
                      )}

                      <div className={styles.wrapper_content}>
                        <Switch location={location}>
                          {listRouters.map(item => {
                            if (item.childDynamic) {
                              return item.childDynamic.map(itemChild => {
                                if (itemChild.authorized) {
                                  return (
                                    <Route
                                      key={itemChild.id}
                                      path={
                                        listAppId.includes(AppId)
                                          ? `${item.path}${itemChild.path}`
                                          : itemChild.path
                                      }
                                      exact
                                      render={props => (
                                        <AuthenticationMedpro
                                          ChildComponent={itemChild.component}
                                          {...props}
                                          {...itemChild}
                                        />
                                      )}
                                    />
                                  );
                                } else {
                                  return (
                                    <Route
                                      key={itemChild.id}
                                      path={
                                        listAppId.includes(AppId)
                                          ? `${item.path}${itemChild.path}`
                                          : itemChild.path
                                      }
                                      exact
                                      render={props => (
                                        <>
                                          {itemChild.breadcrumb && (
                                            <Breadcrumb className="head" />
                                          )}
                                          <itemChild.component {...props} />
                                        </>
                                      )}
                                    />
                                  );
                                }
                              });
                            } else {
                              if (item.authorized) {
                                return (
                                  <Route
                                    key={item.id}
                                    path={item.path}
                                    exact
                                    render={props => (
                                      <AuthenticationMedpro
                                        ChildComponent={item.component}
                                        {...props}
                                        {...item}
                                      />
                                    )}
                                  />
                                );
                              } else {
                                return (
                                  <Route
                                    key={item.id}
                                    path={item.path}
                                    exact
                                    render={props => (
                                      <>
                                        {item.breadcrumb && (
                                          <Breadcrumb className="head" />
                                        )}
                                        <item.component {...props} />
                                      </>
                                    )}
                                  />
                                );
                              }
                            }
                          })}

                          <Route
                            render={() => <Redirect to={{ pathname: "/" }} />}
                          />
                        </Switch>
                        <ScrollToTop />
                      </div>
                    </div>

                    {!pathname.includes("medicalDetailByUser") &&
                    !pathname.includes("-app") &&
                    !pathname.includes("/in-phieu-kham-cskh") &&
                    !pathname.includes("/cham-soc-khach-hang") &&
                    pathname.indexOf("in-phieu-kham-benh") === -1 ? (
                      <Fragment>
                        <Footer />
                      </Fragment>
                    ) : null}
                  </React.Fragment>
                );
              }}
            />
          </Router>
          <ToastNotification />
          <GetContent />
        </PersistGate>
      </Provider>
    );
  }
}

export default Root;
