import * as firebase from "firebase/app";
import "firebase/auth";
import "firebaseui/dist/firebaseui.css";
import React from "react";
import { withRouter } from "react-router-dom";
import BannerHome from "~/components/desktop/molecules/BannerHome";
import AdvanceHospital from "~/components/desktop/organisms/AdvanceHospital";
import AdvanceMedPro from "~/components/desktop/organisms/AdvanceMedPro";
import FeatureHospital from "~/components/desktop/organisms/FeatureHospital";
import FeatureMedPro from "~/components/desktop/organisms/FeatureMedPro";
import SupportMedPro from "~/components/desktop/organisms/SupportMedPro";
import AppId from "~/utils/partner";
import DeployedHospital from "../../molecules/DeployedHospital";
import { NewsAndEvents } from "../../molecules/NewsAndEvents";
import styles from "./style.module.scss";
const firebaseConfig = {
  apiKey: "AIzaSyD8eFYzbdyThCdpJsoyk6OUqugXP7an6pM",
  authDomain: "medpro-dkkb.firebaseapp.com",
  databaseURL: "https://medpro-dkkb.firebaseio.com",
  storageBucket: "medpro-dkkb.appspot.com",
  clientId: "112956123847370780160"
};
// Initialize Firebase
if (firebase.apps.length === 0) {
  firebase.initializeApp(firebaseConfig);
}
firebase.auth().languageCode = "vi";
class HomePage extends React.Component {
  componentDidUpdate() {
    if (
      this.props.history.location.state &&
      this.props.history.location.state.scrollToDownload
    ) {
      let downloadSection = window.document.getElementById(
        "download_application"
      );
      if (downloadSection) {
        let height = 0;
        do {
          height += downloadSection.offsetTop;
          downloadSection = downloadSection.offsetParent;
        } while (downloadSection);
        window.scrollTo({
          top: height - 52,
          behavior: "smooth"
        });
        this.props.history.replace();
      }
    }
  }

  render() {
    const featureNode =
      AppId !== "medpro" ? (
        <React.Fragment>
          <AdvanceHospital {...this.props} />
          <FeatureHospital />
        </React.Fragment>
      ) : (
        <React.Fragment>
          <AdvanceMedPro />
          <FeatureMedPro />
        </React.Fragment>
      );

    const listApiHaveNews = ["medpro"];

    return (
      <div className={styles.bg_page}>
        <BannerHome />
        {/* chỉ có medpro.com mói có danh sách bệnh viện được triển khai */}
        {AppId === "medpro" ? <DeployedHospital /> : null}

        {featureNode}

        {/* danh sách bệnh viện có tin tức  */}
        {listApiHaveNews.includes(AppId) ? <NewsAndEvents /> : null}
        <SupportMedPro />
        <div id="firebaseui-auth-container" style={{ display: "none" }} />
      </div>
    );
  }
}

export default withRouter(HomePage);
