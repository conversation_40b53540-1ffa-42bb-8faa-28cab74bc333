import * as api from '~/api';
// import * as submitPaymentTypes from '~/actions/umc/submitPayment/types';
import { BOOKING } from '~/utils/urlApi';

export const apiSubmitPayment = (baseUrl, data) => {
  // const postData = new FormData();
  // postData.append('hospital_id', hospital_id);
  // postData.append('api_version', 2);

  return api.postHttpRequestAsync(
    // `${baseUrl}${submitPaymentTypes.UMC_URL_SUBMIT_PAYMENT}`,
    `${baseUrl}${BOOKING.INSERT_MULTI_URL}`,
    data,
  );
};
