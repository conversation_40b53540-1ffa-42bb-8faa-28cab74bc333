import { find, get } from "lodash";
import { MDBSpinner } from "mdbreact";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import { resetPatientForm } from "~/store/patientForm/patientFormAction";
import styles from "./style.module.scss";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const Desktop = Loadable({
  loader: () => import("~/components/desktop/pages/PatientUpdate"),
  loading: LoadableLoading
});

class PatientUpdate extends Component {
  componentWillUnmount() {
    this.props.resetPatientForm();
  }

  render() {
    const { loading } = this.props;
    if (loading) {
      return (
        <div className={styles.loading}>
          <MDBSpinner big />
        </div>
      );
    }
    return (
      <React.Fragment>
        <Desktop {...this.props} />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    patientForm: { loading }
  } = state;
  return {
    device: type,
    loading
  };
};

const PatientUpdateHelmet = withTitle({
  component: PatientUpdate,
  title: `${hospitalName.value} | Cập nhật hồ sơ bệnh nhân`
});

export default connect(mapStateToProps, {
  resetPatientForm
})(PatientUpdateHelmet);
