@import "src/assets/scss/custom-variables";
@import "src/assets/scss/pro-custom/listgroup";
.list_group {
  flex-wrap: wrap;
  flex-direction: unset;
  li {
    background: transparent;
    flex: 0 1 50%;
    justify-content: center;
    margin: 0 0 8% 0 !important;
    @media #{$medium-and-down} {
      flex: 0 1 33.33333%;
    }
    a {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: $main_black;
      &:hover,
      &:focus {
        color: #0352cc;
      }
      span {
        max-width: 106px;
        display: block;
        text-align: center;
        font-weight: 500;
        line-height: 18px;
      }
      img {
        max-width: 100%;
        width: 55px;
        height: 55px;
        margin: 0 auto;
      }
    }
  }
  @media #{$medium-and-down} {
    &.list_service_mobile {
      li {
        flex: 0 1 100%;
        a {
          font-weight: normal !important;
          display: flex;
          text-align: center;
          background-color: #1da1f2;
          color: #fff !important;
          text-transform: uppercase;
          max-width: 300px;
          margin: 0 auto;
          font-size: 0.81rem;
          width: 300px;
          height: 42px;
          justify-content: center;
          padding: 12px 15px;
          white-space: nowrap;
          span {
            max-width: none;
            font-weight: normal !important;
          }
        }
      }
      img {
        display: none;
      }
    }
    li {
      margin-bottom: 3% !important;
    }
  }
}

//  -------------------Minh anh

.list_group_minhanh {
  li {
    a {
      &:hover,
      &:focus {
        color: #db2233;
      }
    }
  }

  @media #{$medium-and-down} {
    &.list_service_mobile {
      li {
        a {
          background-color: #db2233;
        }
      }
    }
  }
}
