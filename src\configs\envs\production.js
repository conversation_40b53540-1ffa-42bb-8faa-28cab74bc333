import partner from "~/utils/partner";

const checkApiByParnerId = () => {
  const hostname = window.location.hostname;
  if (hostname.includes("cs-beta")) {
    return "https://api-v2.medpro.com.vn";
  }
  if (hostname.includes("cs.beta")) {
    return "https://api-hotfix.medpro.com.vn";
  }
  if (hostname.includes("hotfix")) {
    return "https://api-hotfix.medpro.com.vn";
  }

  switch (partner) {
    default:
      return "https://api-v2.medpro.com.vn";
  }
};

const checkDomainConvertUser = () => {
  const hostname = window.location.hostname;
  switch (true) {
    case hostname.includes("localhost"):
      return hostname;
    case hostname.includes("beta"):
      return "https://beta.medpro.com.vn";
    case hostname.includes("testing"):
      return "https://testing.medpro.com.vn";
    default:
      return "https://medpro.vn";
  }
};

// Api backend
export const RESTFULL_API_URL = checkApiByParnerId();

export const MEDPRO_LOGIN = `https://id-v121.medpro.com.vn/check-phone`;

export const COOKIE_EXPIRES = 43200; // 12*60*60; // 12 tiếng

export const SOCKET_CHAT = "wss://alpha-api.medpro.com.vn/cable";

export const PAYMENT_HUB_URL = "https://payment-gateway-qrcode.medpro.com.vn";

export const CSKH_API_URL = "https://portal.medpro.com.vn";

export const STATIC_CONTENT_API_URL = `https://resource.medpro.com.vn`;

export const BO_WEB = "https://backoffice.medpro.com.vn";

export const MEDPRO_WEB = checkDomainConvertUser();
