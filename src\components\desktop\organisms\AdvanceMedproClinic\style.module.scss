.Container{
  background-image: url('../../../../assets/img/desktop/HompageClinic/BG_CLINIC.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  min-width: 100%;
  padding: 56px 0;

  display: flex;
  flex-direction: column;
  align-items: center;
  .Label{
    text-align: center;
    padding-bottom: 32px;
    text-transform: uppercase;
    p{
      font-size: 28px;
      font-weight: 700;
      color: #1E56C5;
    }
  }
  .rowAdvance{
    max-width: 1440px;
    .colAds{
      display: flex;
      justify-content: flex-end;
      .img{
        max-width: 410px;
        max-height: 252px;
      }
    }
    .colAdvance{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .listAdvance{
        list-style: none;
        li{
          .Advance{
            display: flex;
            .img{
              max-width: 32px ;
              min-width: 32px ;
              max-height: 28px ;
              min-height: 28px;
            }
            .content{
              font-size: 18px;
              font-weight: 600;
              padding-left: 16px;
            }
          }
        }
      }
    }
    .Btn{
      width: 100%;
      display: flex;
      justify-content: center;
      .btnRegister{
        border-radius: 50px;
      }
    }
  }
}

@media (max-width: 768px){
  .Container{
    padding: 32px 12px !important;
  }
  .Label{
    p{
      font-size: 22px !important;
    }
  }
  .colAds{
    justify-content: center !important;
    .img{
      max-width: 70% !important;
    }
  }
  .colAdvance{
    width: 100% !important;
    .listAdvance{
      padding: 24px !important;
      .Advance{
        .img{
          max-width: 16px !important;
          max-height: 14px !important;
          padding-top: 5px !important;
          margin: 0 !important;
        }
      }
    }
  }
}
@media (min-width: 768px) AND (max-width : 1024px) {
  .Container{
    padding: 32px 12px !important;
    .colAds{
      align-items: center !important;
      .img{
        max-width: 90% !important;
      }
    }
    .colAdvance{
      .listAdvance{
        .Advance{
          .img{
            max-width: 24px !important;
            min-width: 24px !important;
            max-height: 20px !important;
            min-height: 20px !important;
            padding-top: 5px !important;
            margin: 0 !important;
          }
        }
      }
    }
  }

}
