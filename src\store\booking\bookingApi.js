import * as api from "~/api";
import { BOOKING } from "~/utils/urlApi";
import axios from "axios";

export const getMedicalBills = (baseUrl, data) => {
  const modifiedData = data.data;
  return api.getHttpRequestParamsAsync(
    `${baseUrl}${BOOKING.GETBYUSERID_URL}`,
    modifiedData
  );
};

export const getMedicalBillDetail = (baseUrl, data) => {
  const modifiedData = data.data;
  return api.getHttpRequestParamsAsync(
    `${baseUrl}${BOOKING.DETAIL_URL}`,
    modifiedData
  );
};

export const cancelMedicalBill = (baseUrl, data) => {
  const modifiedData = data.data;
  return api.postHttpRequestAsync(
    `${baseUrl}${BOOKING.CANCEL_URL}`,
    modifiedData
  );
};

export const resendSMS = (baseUrl, data) => {
  const modifiedData = data.data;
  return api.postHttpRequestAsync(
    `${baseUrl}${BOOKING.RESEND_CODE_URL}`,
    modifiedData
  );
};

export const submitBooking = (baseUrl, postData) => {
  return api.postHttpRequestAsync(
    `${baseUrl}${BOOKING.INSERT_MULTI_URL}`,
    postData
  );
};

export const fetchMedproCare = async partnerId => {
  const { data } = await axios.get(`${BOOKING.GET_MEDPRO_CARE}/${partnerId}`, {
    headers: {
      appid: "medpro",
      partnerid: partnerId
    }
  });
  return data;
};

export const fetchMedproCareByRules = async (postData, headers) => {
  console.log("partnerId", postData?.partnerId);
  const { data } = await axios.post(`${BOOKING.GET_MEDPRO_CARE}`, postData, {
    headers: {
      appid: "medpro",
      partnerid: postData?.partnerId,
      cskhtoken: headers.cskhToken
    }
  });
  return data;
};

export const fetchMedproCareRoom = postData => {
  return api.postHttpRequestAsync(`${BOOKING.GET_MEDPRO_CARE_ROOM}`, postData);
};

export const fetchHistoryBookingCSKH = postData => {
  return api.postHttpRequestAsync(
    `${BOOKING.GET_HISTORY_BOOKING_CSKH}`,
    postData
  );
};

export const fetchHistoryTelemdRoom = postData => {
  return api.postHttpRequestAsync(
    `${BOOKING.GET_HISTORY_BOOKING_CSKH}`,
    postData
  );
};
