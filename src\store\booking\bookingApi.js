import * as api from "~/api";
import { BOOKING } from "~/utils/urlApi";

export const getMedicalBills = (baseUrl, data) => {
  const modifiedData = data.data;
  return api.getHttpRequestParamsAsync(
    `${baseUrl}${BOOKING.GETBYUSERID_URL}`,
    modifiedData
  );
};

export const getMedicalBillDetail = (baseUrl, data) => {
  const modifiedData = data.data;
  return api.getHttpRequestParamsAsync(
    `${baseUrl}${BOOKING.DETAIL_URL}`,
    modifiedData
  );
};

export const cancelMedicalBill = (baseUrl, data) => {
  const modifiedData = data.data;
  return api.postHttpRequestAsync(
    `${baseUrl}${BOOKING.CANCEL_URL}`,
    modifiedData
  );
};

export const resendSMS = (baseUrl, data) => {
  const modifiedData = data.data;
  return api.postHttpRequestAsync(
    `${baseUrl}${BOOKING.RESEND_CODE_URL}`,
    modifiedData
  );
};

export const submitBooking = (baseUrl, postData) => {
  return api.postHttpRequestAsync(
    `${baseUrl}${BOOKING.INSERT_MULTI_URL}`,
    postData
  );
};
