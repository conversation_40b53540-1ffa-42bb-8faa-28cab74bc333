import cx from "classnames";
import { first, get } from "lodash";
import React, { Component } from "react";
import { connect } from "react-redux";
import { getInfoPatient } from "~/utils/flowRouting";
import styles from "./style.module.scss";
import { BOOKING_STATUS } from "~/utils/constants";
import IconPhone from "./../../../../../src/assets/img/desktop/icon/phone.png";
const QRCode = require("qrcode.react");

class MedicalBillMedproCarePrinter extends Component {
  constructor(props) {
    super(props);
    this.state = {
      shareToPay: true
    };
  }

  onTitle_STT = ({ partnerId }) => {
    switch (partnerId) {
      case "choray":
        return "Số thứ tự phòng khám";

      default:
        return "Số thứ tự tiếp nhận";
    }
  };

  renderMedicalBill = () => {
    // const { type } = this.props;
    const bill = first(this.props.paymentInformation);
    const patient = get(bill, "bookingInfo.patient", {});
    console.log("getInfoPatient(patient) :>> ", getInfoPatient(patient), bill);
    const { fullName, mobile } = getInfoPatient(patient);
    // const partnerId = get(bill, "bookingInfo.partnerId");
    const medproCare = get(bill, "bookingInfo.medproCare");
    const userInfo = get(bill, "bookingInfo.userInfo");
    const patientCode = get(bill, "bookingInfo.patient.patientCode", "");
    const bookingNumber = get(bill, "bookingInfo.sequenceNumber", "");
    const bookingStatus = get(bill, "bookingInfo.status", "");
    // const date = get(bill, "bookingInfo.date", "");
    // const waitingConfirmDate = get(bill, "bookingInfo.waitingConfirmDate", "");
    const Instructor = get(bill, "bookingInfo.medproCare.instructor", "");
    const name = get(medproCare?.addonServices?.[0], "name", "");
    const subname = get(medproCare?.addonServices?.[0], "subname", "");
    const description = get(medproCare?.addonServices?.[0], "description", "");
    const provider = get(medproCare, "provider", {});
    const partner = get(bill, "bookingInfo.partner", {});
    const timeCare247 = get(medproCare, "time", "");
    // const titleBookingTime =
    //   partnerId === "trungvuong" ? "Giờ tiếp nhận dự kiến" : "Giờ khám dự kiến";

    // const bookingTimeBig = date
    //   ? moment(date).format("HH:mm")
    //   : waitingConfirmDate;

    const unPaid = [BOOKING_STATUS.CHUA_THANH_TOAN].includes(bookingStatus);
    const CustomLine = ({ top, bottom, normal = true }) => {
      return (
        <div
          className={cx(
            styles.line,
            top && styles.top,
            bottom && styles.bottom,
            normal && styles.normal
          )}
        >
          <div className={styles.circleLeft} />
          <div className={styles.dashed} />
          <div className={styles.circleRight} />
        </div>
      );
    };
    return (
      <div className={styles.print}>
        <CustomLine top />

        {/* Thông tin bệnh viện: logo, tên, địa chỉ */}
        <div className={styles.title_hospital}>
          <p className={styles.sub_title}>Dịch vụ Care247</p>
        </div>
        {/* Sô thứ tự, thời gian dự kiến hoặc đợi */}
        {![BOOKING_STATUS.CHUA_THANH_TOAN, BOOKING_STATUS.DA_HUY].includes(
          bookingStatus
        ) ? (
          <>
            <div className={styles.info}>
              {bookingNumber && (
                <div className={styles.numbericalOrderNext}>
                  <p>Giờ đồng hành:{timeCare247}</p>
                  <p>Địa chỉ: {partner?.name}</p>
                  <p>{partner?.address}</p>
                </div>
              )}
            </div>

            <CustomLine />
          </>
        ) : null}

        {/* phần thông tin mô tả Phiếu Care247 */}
        <div className={styles.MedicalCouponCare}>
          <div className={styles.groupTitle}>
            <h4 className={styles.title}>{name} Care247</h4>
            <p className={styles.subTitle}>{subname}</p>
            <p className={styles.aboutCare247}>
              (Được cung cấp bởi <a href={provider.url}>{provider.name}</a>)
            </p>
          </div>
          <div
            dangerouslySetInnerHTML={{
              __html: description
            }}
          />
        </div>

        <CustomLine />

        {/* phần thông tin bệnh nhân */}
        {!unPaid && (
          <>
            <div className={styles.list_detail}>
              <ul>
                {userInfo?.fullname && (
                  <li>
                    <span className={styles.column_left}>Người đặt:</span>
                    <b>
                      {userInfo?.fullname} - {userInfo?.username}
                    </b>
                  </li>
                )}

                <li>
                  <span className={styles.column_left}>Bệnh nhân:</span>
                  <b>
                    {fullName} - {mobile}
                  </b>
                </li>

                {patientCode && (
                  <li>
                    <span className={styles.column_left}>Mã bệnh nhân:</span>
                    <b>{patientCode}</b>
                  </li>
                )}
                {Instructor && (
                  <li>
                    <span className={styles.column_left}>Người hướng dẫn:</span>
                    <b>{Instructor?.info}</b>
                  </li>
                )}
              </ul>
            </div>
            <CustomLine />
          </>
        )}

        {!unPaid && (
          <div className={styles.organization}>
            <img src={IconPhone} width={48} height={48} alt="" />{" "}
            <div>
              <p>Gọi 1900 2115</p>
              <span>để được hỗ trợ trực tiếp</span>
            </div>
          </div>
        )}
        <CustomLine bottom />
      </div>
    );
  };

  renderBanner = () => {
    return (
      <div className={styles.banner}>
        <p className={styles.title}>Quét QR để tải app !</p>

        <div className={styles.group}>
          <div className={styles.QR}>
            <QRCode
              fgColor="#000000"
              size={90}
              value="https://medpro.vn/getapp"
            />
          </div>
          <div className={styles.linkDown}>
            <p>
              Quý khách vui lòng cài đặt ứng dụng để xem chi tiết hướng dẫn và
              quản lý hồ sơ khám bệnh.
            </p>
          </div>
        </div>
      </div>
    );
  };

  render() {
    return (
      <div className={styles.printercskh}>
        {this.renderBanner()}
        {this.renderMedicalBill()}
      </div>
    );
  }
}
const mapStateToProps = state => {
  const {
    global,
    user: { IsAuthenticated },
    totalData: { paymentInformation, loading, reviewBooking }
  } = state;
  return {
    global,
    IsAuthenticated,
    paymentInformation,
    loading,
    reviewBooking
  };
};
const mergeProps = (stateProps, dispatchProps, ownProps) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps
});
export default connect(
  mapStateToProps,
  {},
  mergeProps
)(MedicalBillMedproCarePrinter);
