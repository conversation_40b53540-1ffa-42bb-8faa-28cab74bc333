import { MDBContainer } from "mdbreact";
import React, { Component } from "react";
import PatientCard from "../../../components/PatientCard";
import styles from "./style.module.scss";

class PatientBoxBody extends Component {
  renderPatientList = patientList => {
    if (!patientList) {
      return (
        <MDBContainer className={styles.LoadingInBox}>
          <span>
            <em><PERSON>ui lòng nhập số điện thoại để hiển thị hồ sơ bệnh nhân! </em>
          </span>
        </MDBContainer>
      );
    } else if (patientList.length < 1) {
      return (
        <MDBContainer className={styles.LoadingInBox}>
          <span>
            <em>Ch<PERSON><PERSON> có hồ sơ bệnh nhân! </em>
          </span>
        </MDBContainer>
      );
    }
    return patientList?.map((patient, i) => {
      const { onEdit, onDelete } = this.props;
      const handleEdit = () => onEdit(patient);
      const handleDelete = () => {
        onDelete(patient);
        this.props.selectPatient<PERSON><PERSON><PERSON>(patient);
      };
      let focus = false;
      if (this.props.selectedPatient) {
        focus = patient.id === this.props.selectedPatient.id;
      }
      if (patient.id) {
        return (
          <PatientCard
            key={patient.id + "_" + i}
            patientInfo={patient}
            {...this.props}
            onEdit={handleEdit}
            onDelete={handleDelete}
            focus={focus}
          />
        );
      }
      return null;
    });
  };

  render() {
    return this.renderPatientList(this.props.list, this.props.loading);
  }
}

export default PatientBoxBody;
