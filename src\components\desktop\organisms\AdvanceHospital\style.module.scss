@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/listgroup.scss";
.advance_medpro {
  position: relative;
  z-index: 1;
}

.about_item {
  font-size: 1rem;
  color: #ffffff;
  display: flex;
}
.about_item_1 {
  width: 35%;
  h3 {
    font-size: 1.875rem;
    font-weight: 500;
  }
  span {
    letter-spacing: 3px;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 10px;
    color: $bg_noti_news;
  }
}
.about_item_2 {
  width: 65%;
  padding-left: 30px;
}
.box_advance_medpro {
  .list_group {
    flex-direction: unset !important;
    flex-wrap: wrap;
    li {
      position: relative;
      flex-direction: column;
      flex: 0 1 33.33333%;
      padding-right: 30px;
      .img {
        padding-bottom: 60%;
        display: block;
        overflow: hidden;
        height: 1px;
        position: relative;
        width: 100%;
        background: #fff;
        border-radius: 5px;
        img {
          width: 100%;
          position: absolute;
          top: 0;
          left: 0;
        }
      }
      .content {
        margin: -45px 8% 0 8%;
        background: #fff;
        padding: 15px;
        position: relative;
        transition: all 0.3s ease-in-out;
        box-shadow: 0 5px 35px rgba(0, 0, 0, 0.1);
        h4 {
          text-align: center;
          font-size: 1.1rem;
          font-weight: 500;
          color: #12263f;
          a {
            color: inherit;
            &:hover {
              color: #0352cc;
            }
          }
        }
        &:hover {
          // transform: translateY(-15px);
          box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.09);
          transform: translateY(-0.5rem);
          cursor: pointer;
          a {
            color: #0352cc;
          }
        }
      }
    }
  }
  @media (max-width: 768px) {
    .list_group {
      li {
        flex: 0 1 50%;
      }
    }
  }
}
.advance_medpro_2 {
  padding: 80px 0 60px;
  background: #fff;
  @media #{$medium-and-down} {
    padding: 50px 0 0px;
  }
}
.col {
  @media (max-width: 767px) {
    width: 100%;
    flex: 0 1 100%;
    margin-bottom: 15px;
  }
  @media #{$medium-and-down} {
    &:before {
      border: 1px solid #bfd5de;
      content: "";
      height: 100%;
      position: absolute;
      top: -10px;
      bottom: 8px;
      right: 15px;
      left: 15px;
      z-index: 2;
      border-radius: 0.25rem;
    }
  }
}
.mdb_card {
  display: flex;
  flex-direction: row;
  border: 1px solid #bfd5de;
  padding: 20px 15px;
  box-shadow: none;
  transition: all 0.3s ease;
  min-height: 200px;
  cursor: pointer;
  justify-content: stretch;

  @media #{$medium-and-down} {
    height: auto;
    border: none;
  }
  .mdb_card_img {
    width: 50px;
    margin-right: 15px;
  }
  .mdb_card_content {
    z-index: 4;
    width: 100%;
    font-size: 14px;
    h2 {
      color: #0070c3;
      font-weight: 700;
      font-size: 14px;
      margin-bottom: 7px;
      text-transform: uppercase;
    }
  }
  button {
    background: #0070c3 !important;
    border: 1px solid #0070c3 !important;
    color: #fff !important;
    font-weight: 700;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    position: relative;
    padding: 8px 40px;
    margin-top: 1.5rem;
    &:hover {
      transform: translateY(-2px);
    }
  }
  @media #{$medium-and-up} {
    &:hover,
    &:focus {
      --webkit-transform: translateY(-2px);
      --webkit-backface-visibility: hidden;
      --webkit-transform: translateZ(0) scale(1, 1);
      transform: translateY(-2px);
      border: 1px solid #0070c3;
    }
  }
}
.view_all {
  text-align: right;
  a {
    color: #0352cc;
  }
}

// ----------------------Minh anh
.box_advance_medpro_minhanh {
  .mdb_card {
    @media #{$medium-and-up} {
      &:hover,
      &:focus {
        border: 1px solid #db2233 !important;
      }
    }
    h2,
    a {
      color: #db2233 !important;
    }
    button {
      border: 1px solid #db2233 !important;
      color: white;
      background: #db2233 !important;
    }
  }
}
