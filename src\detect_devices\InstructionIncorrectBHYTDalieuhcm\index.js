import { find, get } from "lodash";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MDBRow } from "mdbreact";
import React from "react";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import PatientInfomationLess from "~/components/desktop/molecules/PatientInfomationLess";
import BHYTTraiTuyenDalieuhcm from "~/components/mobile/molecules/BHYTTraiTuyenDalieuhcm";
import { partnerInfo } from "~/configs/partnerDetails";
import { setInsuranceChoice } from "~/store/totalData/actions";
import styles from "./style.module.scss";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

class InstructionIncorrectBHYTDalieuhcm extends React.Component {
  render() {
    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="3">
              <PatientInfomationLess />
            </MDBCol>
            <MDBCol md="12" lg="9">
              <BHYTTraiTuyenDalieuhcm type="desktop" {...this.props} />
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    totalData: {
      service: { insuranceChoice },
      partnerId
    }
  } = state;
  return {
    insuranceChoice,
    partnerId
  };
};

const InstructionIncorrectBHYTHelmet = withTitle({
  component: InstructionIncorrectBHYTDalieuhcm,
  title: `Hướng dẫn BHYT | ${hospitalName.value}`
});

export default connect(mapStateToProps, { setInsuranceChoice })(
  InstructionIncorrectBHYTHelmet
);
