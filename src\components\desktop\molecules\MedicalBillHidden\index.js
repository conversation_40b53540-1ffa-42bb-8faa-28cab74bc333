/* eslint-disable react/jsx-curly-brace-presence */
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import HealthInsuranceCardList from "~/components/mobile/molecules/HealthInsuranceCardList";
// import cx from "classnames";
// import styles from "./style.module.scss";
import { resendSMS } from "~/store/booking/bookingAction";
import {
  toggleResendSMSModal,
  hideAlertSMSOK
} from "~/store/payment/paymentAction";
import { setPatientInfo } from "~/store/patientForm/patientFormAction";
import Alert from "~/components/common/atoms/Alert";
import {
  selectedPatientDetail,
  getPatientByUserId,
  deletePatient
} from "~/store/patient/patientAction";
import { getAllHiddenMedicalBills } from "~/store/totalData/actions";
import { MDBAnimation } from "mdbreact";
import NoContentAlert from "~/components/desktop/atoms/NoContentAlert";

class MedicalBillHidden extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpenDeleteModal: false,
      selectedPatient: false
    };
  }

  resendSMS = phoneNumber => {
    const {
      info: user,
      selectedHealthInsuranceCard: { id }
    } = this.props;
    const data = {
      user_id: user.user_id,
      access_token: user.access_token,
      booking_id: id,
      mobile: phoneNumber
    };
    this.props.handleResendSMS(data);
  };

  hideAlert = () => {
    this.props.hideAlertSMSOK();
  };

  closeResendSMSModal = () => {
    this.props.OnToggleResendSMSModal();
  };

  componentDidMount() {
    this.props.getAllHiddenMedicalBills();
  }

  renderListBooking = () => {
    const { bookingListHidden } = this.props;
    if (bookingListHidden.length === 0) {
      return <NoContentAlert message={"Bạn chưa có thông tin phiếu khám."} />;
    } else {
      return (
        <HealthInsuranceCardList bookingList={this.props.bookingListHidden} />
      );
    }
  };

  render() {
    const { showResendSMSOK, resendSMSMessage } = this.props;
    return (
      <Fragment>
        <MDBAnimation type="fadeIn">
          {this.renderListBooking()}
          <Alert
            isModal={showResendSMSOK}
            message={resendSMSMessage}
            toggleAlert={this.hideAlert}
          />
        </MDBAnimation>
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { info },
    patientForm: { redirectToPatientUpdate },
    healthInsuranceCard: {
      showResendSMSModal,
      showResendSMSOK,
      selectedHealthInsuranceCard,
      resendSMSMessage
    },
    totalData: { bookingListHidden }
  } = state;
  return {
    user: info,
    redirectToPatientUpdate,
    showResendSMSModal,
    showResendSMSOK,
    selectedHealthInsuranceCard,
    resendSMSMessage,
    bookingListHidden
  };
};

const mapDispatchToProps = dispatch => ({
  handleGetPatients: data => {
    dispatch(getPatientByUserId(data));
  },
  handleDeletePatient: payload => {
    dispatch(deletePatient(payload));
  },
  handleSetPatientInfo: info => {
    dispatch(setPatientInfo(info));
  },
  handleSelectedPatient: patient => {
    dispatch(selectedPatientDetail(patient));
  },
  OnToggleResendSMSModal: () => {
    dispatch(toggleResendSMSModal());
  },
  handleResendSMS: data => {
    dispatch(resendSMS(data));
  },
  hideAlertSMSOK: () => {
    dispatch(hideAlertSMSOK());
  },
  getAllHiddenMedicalBills: () => {
    dispatch(getAllHiddenMedicalBills());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(MedicalBillHidden));
