import cx from "classnames";
import { get } from "lodash";
import {
  MDBBtn,
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBContainer,
  MDBListGroup,
  MDBListGroupItem,
  MDBModal,
  MDBModalBody,
  MDBModalHeader,
  MDBNav,
  MDBNavItem,
  MDBRow,
  MDBSpinner,
  MDBTabContent,
  MDBTabPane
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import scancode from "~/assets/img/svg/scan.svg";
import PKHBtn from "~/components/common/atoms/Button";
import TagName from "~/components/common/atoms/TagName";
import ListPaymentMethod from "~/components/desktop/molecules/ListPaymentMethod";
import PatientInfomationLess from "~/components/desktop/molecules/PatientInfomationLess";
import { currentEnv } from "~/configs";
import {
  getStatusTransaction,
  stopGetStatusTransaction
} from "~/store/payment/paymentAction";
import { getFormatMoney } from "~/utils/getFormatMoney";
import AppId from "~/utils/partner";
import styles from "./style.module.scss";

const QRCode = require("qrcode.react");

class PaymentHelpsMethod extends Component {
  constructor(props) {
    super(props);
    this.state = {
      count: 0,
      selectedPaymentMethodGroupId: 0,
      modalPaymentGuide: false,
      activeItem: "1",
      items: {
        default: "1"
      }
    };
    this.scrollToDiv = React.createRef();
  }

  toggle = tab => e => {
    if (this.state.activeItem !== tab) {
      this.setState({
        activeItem: tab
      });
    }
  };

  selectPaymentMethodGroup = group => {
    this.props.resetPaymentMethod();
    if (
      group.methodId !== this.state.selectedPaymentMethodGroupId &&
      group.paymentTypes.length === 1
    ) {
      this.props.handleSelectedMethod(group.paymentTypes[0], group.methodId);
    }
    this.setState({
      selectedPaymentMethodGroupId:
        group.methodId !== this.state.selectedPaymentMethodGroupId
          ? group.methodId
          : 0
    });
  };

  toggleShowPaymentConfirm = () => {
    const { selectedMethod } = this.props;
    if (selectedMethod.code) {
      this.props.toggleShowPaymentConfirm();
    } else {
      this.props.toggleAlert();
    }
  };

  renderListSpecialist = () => {
    const { schedulesSelected } = this.props;

    return schedulesSelected.map((item, index) => {
      const serviceName = get(item, "service.name", "");
      const price = get(item, "service.price", 0);
      const advanced = get(item, "service.advanced", 0);
      return (
        <MDBListGroup key={`aaa${index}`} className={styles.list_group}>
          <MDBListGroupItem>
            <div className={styles.column1}>
              <i className="fal fa-stethoscope" />
              Dịch vụ
            </div>
            <div className={styles.column2}>{serviceName}</div>
          </MDBListGroupItem>
          <MDBListGroupItem>
            <div className={styles.column1}>Tiền khám</div>
            <div className={styles.column2}>
              <strong>{getFormatMoney(price - advanced)} VNĐ</strong>
            </div>
          </MDBListGroupItem>
          <MDBListGroupItem
            className={advanced !== 0 || advanced ? "" : "d-none"}
          >
            <div className={styles.column1}>Tiền tạm ứng</div>
            <div className={styles.column2}>
              <strong>{getFormatMoney(advanced)} VNĐ</strong>
            </div>
          </MDBListGroupItem>
        </MDBListGroup>
      );
    });
  };

  componentWillUnmount = () => {
    if (this.timeOut) {
      clearTimeout(this.timeOut);
      this.timeOut = 0;
    }
    console.log("stopGetStatusTransaction :>> ");
    const { stopGetStatusTransaction } = this.props;
    stopGetStatusTransaction();
  };

  componentDidMount() {
    const { checkStatusTransaction, paymentInformation } = this.props;

    const extendData = get(paymentInformation, "bookingInfo.extendData", "");
    if (extendData) {
      const status = "active";
      checkStatusTransaction(status);
    }
  }

  componentDidUpdate() {
    const { selectedMethod } = this.props;

    if (Object.keys(selectedMethod).length > 1) {
      if (this.scrollToDiv.current) {
        const {
          top,
          bottom
        } = this.scrollToDiv.current.getBoundingClientRect();
        if (top < 0 || bottom > window.innerHeight) {
          this.scrollToDiv.current.scrollIntoView({
            behavior: "smooth",
            block: "end"
          });
        }
      }
    }
  }

  togglePaymentGuide = () => {
    this.setState({
      modalPaymentGuide: !this.state.modalPaymentGuide
    });
  };

  togglePills = (type, tab) => e => {
    e.preventDefault();
    if (this.state.items[type] !== tab) {
      const items = { ...this.state.items };
      items[type] = tab;
      this.setState({
        items
      });
    }
  };

  handlerRouterThanhToanMobileBanking = () => {
    const { paymentInformation } = this.props;

    const extendData = get(paymentInformation, "bookingInfo.extendData", "");
    const transactionId = get(
      paymentInformation,
      "bookingInfo.transactionId",
      ""
    );
    const partnerId = get(paymentInformation, "bookingInfo.partnerId", "");

    const router =
      `${currentEnv.PAYMENT_HUB_URL}/?extendData=${extendData}` +
      `&transactionId=${transactionId}&redirectUrl=${window.location.hostname}&partnerId=${partnerId}`;

    window.location.href = router;
  };

  checkHiddenQRcode = params => {
    const { paymentInformation } = this.props;

    const extendData = get(paymentInformation, "bookingInfo.extendData", "");
    if (extendData) return "";
    else return "d-none";
  };

  render() {
    const {
      data,
      selectedMethod,
      handleSelectedMethod,
      loadingReserveBooking,
      price,
      handleGoBack,
      isRepayment,
      paymentInformation
    } = this.props;

    let convertDataPayment;
    if (isRepayment) {
      convertDataPayment = data?.filter(item => {
        return item.methodId !== "SHARE_PAYMENT";
      });
    } else {
      convertDataPayment = data;
    }

    const extendData = get(paymentInformation, "bookingInfo.extendData", "");

    const subTotal = get(price, "subTotal", 0);
    const totalFee = get(price, "totalFee", 0);
    const grandTotal = get(price, "grandTotal", 0);

    const objCheckNextStep = Object.create(null);
    if (Object.keys(selectedMethod).length > 1) {
      objCheckNextStep.nextStep = true;
    }

    if (loadingReserveBooking) {
      return (
        <div className="loading">
          <MDBSpinner big crazy tag="div" />
        </div>
      );
    }

    return (
      <div
        className={cx(
          styles.wapper_page_desktop,
          styles["wapper_page_desktop_" + AppId]
        )}
      >
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="3">
              <PatientInfomationLess />
            </MDBCol>
            <MDBCol md="12" lg="9">
              <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between",
                      "title_header_mobile"
                    ]}
                  >
                    <span>Chọn phương thức thanh toán</span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody className={styles.card_body}>
                  <MDBRow
                    className={cx(
                      "justify-content-end",
                      this.checkHiddenQRcode()
                    )}
                  >
                    <MDBCol
                      md="12"
                      className={cx(
                        styles.infoQRCode,
                        " d-flex flex-column justify-content-center align-items-center mb-3"
                      )}
                    >
                      <span
                        className={cx(
                          styles.titleQRCode,
                          " font-weight-bold mb-2"
                        )}
                      >
                        Thanh toán hóa đơn
                      </span>

                      <span
                        className={cx(
                          styles.sumbTotal,
                          " font-weight-bold mb-2 text-success"
                        )}
                      >
                        <strong>{getFormatMoney(subTotal)} VNĐ</strong>
                      </span>
                      <span className={cx(styles.QRCode, "mb-2")}>
                        <QRCode
                          fgColor="#000000"
                          size={150}
                          value={extendData}
                        />
                      </span>
                      <div className={cx(styles.scancode, "mb-1")}>
                        <span className="mr-2">
                          <img
                            alt="thumbnail"
                            src={scancode}
                            width={25}
                            className="img-fluid"
                          />
                        </span>
                        Sử dụng các ứng dụng ngân hàng <br /> để quét mã và tiến
                        hành thanh toán.
                      </div>

                      <MDBBtn
                        className={styles.btnQRCode}
                        onClick={() => {
                          this.handlerRouterThanhToanMobileBanking();
                        }}
                      >
                        {/* Tải về */}
                        Thanh toán bằng Mobile banking
                      </MDBBtn>

                      <div className={styles.paymentGuide}>
                        <Link to="#" onClick={() => this.togglePaymentGuide()}>
                          Hướng dẫn thanh toán?
                        </Link>
                      </div>
                    </MDBCol>
                  </MDBRow>

                  <MDBRow>
                    <MDBCol md="6">
                      <div className={styles.group_payment}>
                        <ListPaymentMethod
                          allPaymentMethodGroups={convertDataPayment}
                          handleSelectedMethod={handleSelectedMethod}
                          selectedMethod={selectedMethod}
                          selectPaymentMethodGroup={
                            this.selectPaymentMethodGroup
                          }
                          selectedPaymentMethodGroupId={
                            this.state.selectedPaymentMethodGroupId
                          }
                        />
                      </div>
                    </MDBCol>
                    <MDBCol md="6">
                      <div className={styles.list_group_payment}>
                        <div className={styles.sub_title}>
                          <i className="fal fa-info-square" />
                          Thông tin thanh toán
                        </div>
                        {this.renderListSpecialist()}
                        <div className={styles.total_payment}>
                          <MDBListGroup className={styles.list_group}>
                            <MDBListGroupItem>
                              Tổng tiền khám:
                              <strong>{getFormatMoney(subTotal)} VNĐ</strong>
                            </MDBListGroupItem>
                            <MDBListGroupItem>
                              {AppId !== "vanhanh"
                                ? "Phí tiện ích:"
                                : "Phí đăng kí qua ứng dụng:"}
                              <strong>
                                {totalFee === 0
                                  ? "0 VNĐ"
                                  : getFormatMoney(totalFee) + " VNĐ"}{" "}
                              </strong>
                            </MDBListGroupItem>
                            <MDBListGroupItem>
                              TỔNG CỘNG:
                              <strong>{getFormatMoney(grandTotal)} VNĐ</strong>
                            </MDBListGroupItem>
                          </MDBListGroup>
                        </div>
                      </div>
                    </MDBCol>
                  </MDBRow>
                  <div className={styles.next_prev}>
                    <PKHBtn
                      hidden
                      backdesktop="backdesktop"
                      onClick={handleGoBack}
                    >
                      Quay lại
                    </PKHBtn>

                    <PKHBtn
                      color={AppId === "minhanh" ? "red" : ""}
                      create="create"
                      buttonArrow="buttonArrow"
                      {...objCheckNextStep}
                      onClick={() => this.toggleShowPaymentConfirm()}
                    >
                      Thanh toán
                    </PKHBtn>
                  </div>
                </MDBCardBody>
              </MDBCard>
              <div ref={this.scrollToDiv} />
            </MDBCol>
          </MDBRow>

          <MDBModal
            isOpen={this.state.modalPaymentGuide}
            toggle={() => this.togglePaymentGuide()}
            style={{ borderRadius: "1.125rem" }}
          >
            <MDBModalHeader toggle={() => this.togglePaymentGuide()}>
              Hướng dẫn thanh toán
            </MDBModalHeader>
            <MDBModalBody>
              <MDBNav className={cx(styles.pillsGuide, "nav-pills")}>
                <MDBNavItem
                  onClick={this.togglePills("default", "1")}
                  className={
                    this.state.items.default === "1" ? styles.active : ""
                  }
                >
                  <Link to="#">Qua tải mã VNPAYQR</Link>
                </MDBNavItem>
                <MDBNavItem
                  onClick={this.togglePills("default", "2")}
                  className={
                    this.state.items.default === "2" ? styles.active : ""
                  }
                >
                  <Link to="#">Qua VNPAYQR</Link>
                </MDBNavItem>
              </MDBNav>
              <MDBTabContent
                className="px-0"
                activeItem={this.state.items.default}
              >
                <MDBTabPane tabId="1" className={styles.byTabOne}>
                  <ul className={styles.stepByTabOne}>
                    <li>
                      <span>
                        <strong>Bước 1:</strong> Tải mã thanh toán
                      </span>
                    </li>
                    <li>
                      <span>
                        <strong>Bước 2:</strong> Đăng nhập ứng dụng hỗ trợ thanh
                        toán, chọn chức năng QRPay
                      </span>
                    </li>
                    <li>
                      <span>
                        <strong>Bước 3:</strong> Tại màn hình quét QR, chọn Thư
                        viện ảnh
                      </span>
                    </li>
                    <li>
                      <span>
                        <strong>Bước 4:</strong> Chọn ảnh QR đã tải trong thư
                        viện ảnh
                      </span>
                    </li>
                    <li>
                      <span>
                        <strong>Bước 5:</strong> Kiểm tra thông tin hoá đơn và
                        chọn phương thức thanh toán
                      </span>
                    </li>
                    <li>
                      <span>
                        <strong>Bước 6:</strong> Xác nhận thanh toán và hoàn tất
                        giao dịch
                      </span>
                    </li>
                  </ul>
                  <span className={styles.noteByTabOne}>
                    <em>
                      Lưu ý: Ngoài tải mã thanh toán Khách hàng có thể sử dụng
                      thiết bị khác (nếu có) để quét mã hiển thị trên màn hình
                      mà không phải tải mã.
                    </em>
                  </span>
                </MDBTabPane>
                <MDBTabPane tabId="2" className={styles.byTabTwo}>
                  <ul className={styles.stepByTabTwo}>
                    <li>
                      <span>
                        <strong>Bước 1:</strong> Nhấn vào logo của ứng dụng hỗ
                        trợ thanh toán QR trong danh sách
                      </span>
                    </li>
                    <li>
                      <span>
                        <strong>Bước 2:</strong> Đồng ý mở ứng dụng hỗ trợ thanh
                        toán QR theo thông báo trên màn hình và Đăng nhập
                      </span>
                    </li>
                    <li>
                      <span>
                        <strong>Bước 3:</strong> Xác nhận thanh toán và hoàn tất
                        giao dịch
                      </span>
                    </li>
                  </ul>
                  <span className={styles.noteByTabTwo}>
                    <em>
                      Lưu ý: Ngoài tải mã thanh toán Khách hàng có thể sử dụng
                      thiết bị khác (nếu có) để quét mã hiển thị trên màn hình
                      mà không phải tải mã.
                    </em>
                  </span>
                </MDBTabPane>
              </MDBTabContent>
            </MDBModalBody>
          </MDBModal>
        </MDBContainer>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { IsAuthenticated },
    payment: { selectedMethod, data, showModal, price },
    doctorAndTime: { sumaryInfo },
    umcSubmitPayment: {
      isRedirectToPaymentSupportPage,
      errorMsg,
      errorBooking
    },
    features: { selectedFeature, selectedFeatureBooking },
    totalData: {
      schedulesSelected,
      bookingTree,
      loadingReserveBooking,
      loading,
      isRepayment,
      partnerId,
      subject: { isHasSubject },
      service: { isHasService },
      room: { isHasRoom },
      doctor: { isHasDoctor },
      paymentInformation
    },
    hospital: { selectedHospital }
  } = state;
  return {
    IsAuthenticated,
    device: type,
    selectedMethod,
    sumaryInfo,
    isRedirectToPaymentSupportPage,
    errorMsg,
    errorBooking,
    showModal,
    data,
    selectedFeature,
    selectedFeatureBooking,
    schedulesSelected,
    bookingTree,
    loadingReserveBooking,
    price,
    loading,
    isRepayment,
    selectedHospital,
    partnerId,
    isHasSubject,
    isHasService,
    isHasRoom,
    isHasDoctor,
    paymentInformation
  };
};

const mapDispatchToProps = dispatch => ({
  checkStatusTransaction: status => {
    dispatch(getStatusTransaction(status));
  },
  stopGetStatusTransaction: () => {
    dispatch(stopGetStatusTransaction());
  }
});

export default connect(mapStateToProps, mapDispatchToProps)(PaymentHelpsMethod);
