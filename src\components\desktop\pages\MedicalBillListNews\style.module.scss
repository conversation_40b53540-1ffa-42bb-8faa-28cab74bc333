@import "src/assets/scss/pro-desktop/medicalBillList.scss";
@import "src/assets/scss/pro-custom/button.scss";
@import "src/components/desktop/pages/User/style.module.scss";
@import "src/assets/scss/pro-common/loading.scss";

.sub_card_header {
  margin: 10px 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-content: center;
  .reload {
    button {
      background: transparent;
      border: none;
      color: white;
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: center;
      span {
        font-size: 0.875rem;
        font-weight: normal;
        margin-right: 10px;
      }
    }
    .active {
      i {
        animation: rotation 2s infinite linear;
      }
    }
  }
}
.back_btn {
  margin: 15px 0;
}

.wapper_page_desktop_minhanh {
  .panels {
    .panels_header {
      background-color: #db2233 !important;
    }
  }
}

.wapper_page_desktop {
  .panels {
    // margin: 0;
    margin-bottom: 60px;
    .card_body {
      border: none;
      background-color: whitesmoke;

      display: flex;
      align-items: center;
      align-content: center;
      flex-direction: column;
      justify-content: flex-start;

      .tabsBooking {
        padding: 10px;
        margin: 0;
        border: 0;
        width: 100%;
        display: flex;
        justify-content: space-around;

        span {
          padding: 12px 22px 10px;
          font-size: 16px;
          color: #003553;
          font-weight: 500;
          border-bottom: 2px solid transparent;
          &:hover {
            cursor: pointer;
          }
          @media (max-width: 576px) {
            font-size: 16px;
          }
        }
      }
      .NormalMedpro,
      .MedproCare {
        span {
          color: #00b5f1 !important;
          border-bottom-color: #00b5f1 !important;
          border-radius: 14px 14px 0 0;
        }
      }
    }
  }
}

@keyframes rotation {
  100% {
    transform: rotate(360deg);
  }
}
