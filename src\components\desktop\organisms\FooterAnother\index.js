import cx from "classnames";
import { find, get } from "lodash";
import {
  MDBAnimation,
  MDBCol,
  MDBContainer,
  MDBListGroup,
  MDBListGroupItem,
  MDBRow
} from "mdbreact";
import React from "react";
import LazyLoad from "react-lazyload";
import { Link } from "react-router-dom";
import appStore from "~/assets/img/common/logo/appstore.svg";
import googlePlay from "~/assets/img/common/logo/googleplay.svg";
import bocongthuong1 from "~/assets/img/mobile/logo/bocongthuong1.svg";
import bocongthuong2 from "~/assets/img/mobile/logo/bocongthuong2.svg";
import { partnerInfo } from "~/configs/partnerDetails";
import {
  LINK_ANDROID_APP,
  LINK_IOS_APP
  // urlLogoFooter
} from "~/utils/manageResource";
import partnerId from "~/utils/partner";
import styles from "./style.module.scss";
import logo_nhidonghcm from "~/assets/img/desktop/logo/nhidonghcm/logo_footer_nhidonghcm.gif";
import logoMedpro from "~/assets/img/desktop/logo/nhidonghcm/logo_footer_medpro.png";

const FooterAnother = props => {
  /* --------- INFO------------ */
  const info = get(partnerInfo, "info");
  // const nameInfo = find(info, { key: "name" });
  const addressInfo = find(info, { key: "address" });
  const websiteInfo = find(info, { key: "website" });
  const emailInfo = find(info, { key: "email" });
  const phoneInfo = find(info, { key: "phone" });

  /* --------- MENU------------ */
  const menuData = get(partnerInfo, "menuFooter");
  let menuNode = [];
  if (menuData && menuData.length > 0) {
    menuNode = menuData.map((menu, index) => {
      return (
        <MDBListGroupItem key={`${index}-menu-footer`}>
          <Link to={menu.link}>{menu.name}</Link>
        </MDBListGroupItem>
      );
    });
  }
  return (
    <MDBAnimation
      reveal="true"
      type="fadeIn"
      className={cx(styles.footer, styles["footer_" + partnerId])}
    >
      <div className={styles.footer_inner}>
        <MDBContainer>
          <MDBRow className={styles.rowFooter}>
            <MDBCol lg="4" className={styles.colInfo}>
              <div className={styles.footer_item}>
                <MDBListGroup className={styles.item_info}>
                  <div
                    className={styles.footer_item}
                    onClick={() => {
                      window.scrollTo(0, 0);
                    }}
                  >
                    <Link to="/">
                      <LazyLoad height={43}>
                        <img
                          src={logo_nhidonghcm}
                          className={cx("img-fluid", styles.logo_footer)}
                          alt=""
                        />
                      </LazyLoad>
                    </Link>
                  </div>
                  <MDBListGroupItem>
                    <span href="#">
                      {`${addressInfo.displayPrefix} ${addressInfo.value}`}
                    </span>
                  </MDBListGroupItem>
                  <MDBListGroupItem>
                    <a
                      href={websiteInfo.value}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {`${websiteInfo.displayPrefix} ${websiteInfo.value}`}
                    </a>
                  </MDBListGroupItem>
                  <MDBListGroupItem
                    className={emailInfo.hidden ? "d-none" : ""}
                  >
                    <span href="#">{`${emailInfo.displayPrefix} ${emailInfo.value}`}</span>
                  </MDBListGroupItem>
                  <MDBListGroupItem>
                    <span href="#">
                      {`${phoneInfo.displayPrefix} ${phoneInfo.value}`}
                    </span>
                  </MDBListGroupItem>
                </MDBListGroup>
              </div>
            </MDBCol>
            <MDBCol
              lg="2"
              className={cx("d-none d-lg-block", styles.colContact)}
            >
              <div className={cx(styles.footer_item, styles.location)}>
                <MDBListGroup className={styles.item_info}>
                  {menuNode}
                </MDBListGroup>
              </div>
            </MDBCol>
            <MDBCol lg="3" className={styles.colDownload_Stamp}>
              <div className={styles.footer_item}>
                <MDBListGroup className={cx(styles.item_info, styles.icons)}>
                  <MDBListGroupItem>
                    <a
                      href="http://online.gov.vn/Home/WebDetails/44668"
                      target="_blank"
                      title="Bộ công thương"
                      rel="noreferrer"
                    >
                      <LazyLoad height={50}>
                        <img src={bocongthuong1} alt="" />
                      </LazyLoad>
                    </a>
                  </MDBListGroupItem>
                  <MDBListGroupItem>
                    <a
                      href="http://online.gov.vn/Home/WebDetails/44209"
                      target="_blank"
                      title="Bộ công thương"
                      rel="noreferrer"
                    >
                      <LazyLoad height={50}>
                        <img src={bocongthuong2} alt="" />
                      </LazyLoad>
                    </a>
                  </MDBListGroupItem>
                  <MDBListGroupItem>
                    <a
                      href={LINK_ANDROID_APP}
                      alt="apple-app-icon"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <LazyLoad height={50}>
                        <img src={googlePlay} alt="" />
                      </LazyLoad>
                    </a>
                  </MDBListGroupItem>
                  <MDBListGroupItem>
                    <a
                      href={LINK_IOS_APP}
                      alt="apple-app-icon"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <LazyLoad height={50}>
                        <img src={appStore} alt="" />
                      </LazyLoad>
                    </a>
                  </MDBListGroupItem>
                </MDBListGroup>
              </div>
            </MDBCol>
            <MDBCol lg="3" className={styles.colMedpro}>
              <div className={styles.footer_item}>
                <MDBListGroup className={styles.item_info}>
                  <div
                    className={styles.footer_item}
                    onClick={() => {
                      window.scrollTo(0, 0);
                    }}
                  >
                    <a
                      href="https://medpro.com.vn/"
                      alt="Medpro"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <LazyLoad height={43}>
                        <img
                          src={logoMedpro}
                          className={cx("img-fluid", styles.logo_footer)}
                          alt=""
                        />
                      </LazyLoad>
                    </a>
                  </div>
                  <MDBListGroupItem key={`${1}-menu-footer`}>
                    <Link to="#">ĐƯỢC PHÁT TRIỂN BỞI MEDPRO</Link>
                  </MDBListGroupItem>
                  <MDBListGroupItem key={`${2}-menu-footer`}>
                    <a
                      href="https://medpro.vn"
                      target="_blank"
                      rel="noreferrer"
                    >
                      Website: medpro.vn
                    </a>
                  </MDBListGroupItem>
                  <MDBListGroupItem key={`${3}-menu-footer`}>
                    <Link to="#">Email: <EMAIL></Link>
                  </MDBListGroupItem>
                  <MDBListGroupItem key={`${4}-menu-footer`}>
                    <a href="tel:1900 2115">Điện thoại: 1900 2115</a>
                  </MDBListGroupItem>
                </MDBListGroup>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
      <div className={styles.footer_copy_right}>
        <MDBContainer>
          <MDBRow>
            <MDBCol size="12">
              <div className={styles.footer_item}>
                © 2020 - Bản quyền thuộc Công Ty Cổ Phần Ứng Dụng PKH
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    </MDBAnimation>
  );
};

export default FooterAnother;
