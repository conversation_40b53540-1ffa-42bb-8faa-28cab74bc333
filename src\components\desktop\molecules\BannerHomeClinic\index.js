import React, { Component } from "react";
import { MDBContainer, MDBRow, MDBCol, MDBBtn, MDBIcon } from "mdbreact";
import styles from "./style.module.scss";
import laptop from "~/assets/img/desktop/HompageClinic/laptop_banner.svg";

class BannerHomeClinic extends Component {
  render() {
    return (
      <MDBContainer className={styles.Container} sty>
        <MDBRow className={styles.rowBanner}>
          <MDBCol xl={5} className={styles.colLogin}>
            <div className={styles.Intro}>
              <p>
                <span className={styles.title}>
                  <PERSON><PERSON> thống quản lý <br /> Phòng khám/phòng mạch
                </span>
                <span className={styles.subtitle}>
                  Hiện đại hóa Phòng khám của bạn thành Hệ thống tối ưu với Ứng
                  dụng MedPro Clinic.
                </span>
              </p>
              <div className={styles.ButtonLogin}>
                <MDBBtn className={styles.register} gradient="peach">
                  <PERSON><PERSON><PERSON> ký <MDBIcon icon="arrow-right" className="ml-3" />
                </MDBBtn>
                <MDBBtn className={styles.login} gradient="cloudy-knoxville">
                  Đăng Nhập
                </MDBBtn>
              </div>
            </div>
          </MDBCol>
          <MDBCol xl={7} className={styles.colAds}>
            <figure className={styles.img}>
              <img src={laptop} alt="" />
            </figure>
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    );
  }
}
export default BannerHomeClinic;
