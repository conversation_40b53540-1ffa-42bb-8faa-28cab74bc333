import cx from "classnames";
import { get } from "lodash";
import {
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBContainer,
  MDBListGroup,
  MDBListGroupItem,
  MDBRow,
  MDBSpinner
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import PKHBtn from "~/components/common/atoms/Button";
import TagName from "~/components/common/atoms/TagName";
import ListPaymentMethod from "~/components/desktop/molecules/ListPaymentMethod";
import PatientInfomationLess from "~/components/desktop/molecules/PatientInfomationLess";
import {
  getStatusTransaction,
  stopGetStatusTransaction
} from "~/store/payment/paymentAction";
import { getFormatMoney } from "~/utils/getFormatMoney";
import AppId from "~/utils/partner";
import styles from "./style.module.scss";

class PaymentMethod extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedPaymentMethodGroupId: 0
    };
    this.scrollToDiv = React.createRef();
  }

  selectPaymentMethodGroup = group => {
    this.props.resetPaymentMethod();
    if (
      group.methodId !== this.state.selectedPaymentMethodGroupId &&
      group.paymentTypes.length === 1
    ) {
      this.props.handleSelectedMethod(group.paymentTypes[0], group.methodId);
    }
    this.setState({
      selectedPaymentMethodGroupId:
        group.methodId !== this.state.selectedPaymentMethodGroupId
          ? group.methodId
          : 0
    });
  };

  toggleShowPaymentConfirm = () => {
    const {
      selectedMethod,
      extraConfig,
      handleDoPayment,
      toggleShowPaymentConfirm,
      toggleAlert
    } = this.props;
    const isConfirmDialog = get(extraConfig, "isConfirmDialog", "");

    if (isConfirmDialog) {
      if (selectedMethod.code) {
        toggleShowPaymentConfirm();
      } else {
        toggleAlert();
      }
    } else {
      if (selectedMethod.code) {
        handleDoPayment();
      } else {
        toggleAlert();
      }
    }
  };

  toggleShowPaymentHelpsConfirm = () => {
    this.props.toggleShowPaymentHelpsConfirm();
  };

  checkHiddenQRcode = () => {
    const { paymentInformation } = this.props;

    const extendData = get(paymentInformation, "bookingInfo.extendData", "");
    if (extendData) return "";
    else return "d-none";
  };

  renderListSpecialist = data => {
    const { schedulesSelected, selectSvAddOn } = this.props;

    return schedulesSelected.map((item, index) => {
      const serviceName = get(item, "service.name", "");
      const price = get(item, "service.price", 0);
      const advanced = get(item, "service.advanced", 0);
      return (
        <MDBListGroup key={`aaa${index}`} className={styles.list_group}>
          <MDBListGroupItem>
            <div className={styles.column1}>
              <i className="fal fa-stethoscope" />
              Dịch vụ
            </div>
            <div className={styles.column2}>{serviceName}</div>
          </MDBListGroupItem>
          <MDBListGroupItem>
            <div className={styles.column1}>Tiền khám</div>
            <div className={styles.column2}>
              <strong>{getFormatMoney(price - advanced)} VNĐ</strong>
            </div>
          </MDBListGroupItem>

          {selectSvAddOn.map((v, i) => {
            return (
              <MDBListGroupItem key={i}>
                <div className={styles.column1}>{v.name}</div>
                <div className={styles.column2}>
                  <strong>{getFormatMoney(v.price)} VNĐ</strong>
                </div>
              </MDBListGroupItem>
            );
          })}

          <MDBListGroupItem
            className={advanced !== 0 || advanced ? "" : "d-none"}
          >
            <div className={styles.column1}>Tiền tạm ứng</div>
            <div className={styles.column2}>
              <strong>{getFormatMoney(advanced)} VNĐ</strong>
            </div>
          </MDBListGroupItem>
        </MDBListGroup>
      );
    });
  };

  componentDidUpdate(prevProps) {
    const { selectedMethod } = this.props;
    if (Object.keys(selectedMethod).length > 1) {
      if (this.scrollToDiv.current) {
        const {
          top,
          bottom
        } = this.scrollToDiv.current.getBoundingClientRect();
        if (top < 0 || bottom > window.innerHeight) {
          this.scrollToDiv.current.scrollIntoView({
            behavior: "smooth",
            block: "end"
          });
        }
      }
    }
    if (prevProps.data !== this.props.data) {
      if (!this.props.isRepayment)
        this.selectPaymentMethodGroup(this.props.data[0]);
    }
  }

  componentDidMount() {
    const { checkStatusTransaction, paymentInformation } = this.props;

    const extendData = get(paymentInformation, "bookingInfo.extendData", "");
    if (extendData) {
      const status = "active";
      checkStatusTransaction(status);
    }
  }

  render() {
    const {
      data,
      selectedMethod,
      handleSelectedMethod,
      loadingReserveBooking,
      price,
      isRepayment,
      handleGoBack,
      handleDoPaymentHelps,
      extraConfig
    } = this.props;

    const shareToPay = get(extraConfig, "shareToPay", "");

    const subTotal = get(price, "subTotal", 0);
    const totalFee = get(price, "totalFee", 0);
    const grandTotal = get(price, "grandTotal", 0);

    const objCheckNextStep = Object.create(null);
    if (Object.keys(selectedMethod).length > 1) {
      objCheckNextStep.nextStep = true;
    }

    if (loadingReserveBooking) {
      return (
        <div className="loading">
          <MDBSpinner big crazy tag="div" />
        </div>
      );
    }

    let convertDataPayment;
    if (isRepayment) {
      convertDataPayment = data?.filter(item => {
        return item.methodId !== "SHARE_PAYMENT";
      });
    } else {
      convertDataPayment = data;
    }

    return (
      <div
        className={cx(
          styles.wapper_page_desktop,
          styles["wapper_page_desktop_" + AppId]
        )}
      >
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="3">
              <PatientInfomationLess />
            </MDBCol>
            <MDBCol md="12" lg="9">
              <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between",
                      "title_header_mobile"
                    ]}
                  >
                    <span>Chọn phương thức thanh toán</span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody className={styles.card_body}>
                  <MDBRow>
                    <MDBCol md="6">
                      <div className={styles.group_payment}>
                        <ListPaymentMethod
                          allPaymentMethodGroups={convertDataPayment}
                          handleSelectedMethod={handleSelectedMethod}
                          selectedMethod={selectedMethod}
                          selectPaymentMethodGroup={
                            this.selectPaymentMethodGroup
                          }
                          selectedPaymentMethodGroupId={
                            this.state.selectedPaymentMethodGroupId
                          }
                        />
                      </div>
                    </MDBCol>
                    <MDBCol md="6">
                      <div className={styles.list_group_payment}>
                        <div className={styles.sub_title}>
                          <i className="fal fa-info-square" />
                          Thông tin thanh toán
                        </div>
                        {this.renderListSpecialist()}
                        <div className={styles.total_payment}>
                          <MDBListGroup className={styles.list_group}>
                            <MDBListGroupItem>
                              Tổng tiền khám:
                              <strong>{getFormatMoney(subTotal)} VNĐ</strong>
                            </MDBListGroupItem>
                            <MDBListGroupItem>
                              {AppId !== "vanhanh"
                                ? "Phí tiện ích:"
                                : "Phí đăng kí qua ứng dụng:"}
                              <strong>
                                {totalFee === 0
                                  ? "0 VNĐ"
                                  : getFormatMoney(totalFee) + " VNĐ"}{" "}
                              </strong>
                            </MDBListGroupItem>
                            <MDBListGroupItem>
                              TỔNG CỘNG:
                              <strong>{getFormatMoney(grandTotal)} VNĐ</strong>
                            </MDBListGroupItem>
                          </MDBListGroup>
                        </div>
                      </div>
                    </MDBCol>
                  </MDBRow>
                  <div className={styles.next_prev}>
                    <PKHBtn backdesktop="backdesktop" onClick={handleGoBack}>
                      Quay lại
                    </PKHBtn>
                    <ul className="list-inline">
                      <li
                        className={cx(
                          shareToPay ? "" : "d-none",
                          "list-inline-item"
                        )}
                      >
                        <PKHBtn
                          create="create"
                          onClick={() => handleDoPaymentHelps()}
                        >
                          Thanh toán hộ
                        </PKHBtn>
                      </li>
                      <li className="list-inline-item">
                        <PKHBtn
                          color={AppId === "minhanh" ? "red" : ""}
                          create="create"
                          buttonArrow
                          {...objCheckNextStep}
                          onClick={() => this.toggleShowPaymentConfirm()}
                        >
                          {isRepayment ? "Thanh toán lại" : "Thanh toán"}
                        </PKHBtn>
                      </li>
                    </ul>
                  </div>
                </MDBCardBody>
              </MDBCard>
              <div ref={this.scrollToDiv} />
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { IsAuthenticated },
    payment: { selectedMethod, data, showModal, price },
    doctorAndTime: { sumaryInfo },
    umcSubmitPayment: {
      isRedirectToPaymentSupportPage,
      errorMsg,
      errorBooking
    },
    features: { selectedFeature, selectedFeatureBooking },
    totalData: {
      schedulesSelected,
      bookingTree,
      loadingReserveBooking,
      loading,
      isRepayment,
      partnerId,
      subject: { isHasSubject },
      service: { isHasService },
      room: { isHasRoom },
      doctor: { isHasDoctor },
      paymentInformation,
      selectSvAddOn
    },
    hospital: { selectedHospital }
  } = state;
  return {
    selectSvAddOn,
    IsAuthenticated,
    device: type,
    selectedMethod,
    sumaryInfo,
    isRedirectToPaymentSupportPage,
    errorMsg,
    errorBooking,
    showModal,
    data,
    selectedFeature,
    selectedFeatureBooking,
    schedulesSelected,
    bookingTree,
    loadingReserveBooking,
    price,
    loading,
    isRepayment,
    selectedHospital,
    partnerId,
    isHasSubject,
    isHasService,
    isHasRoom,
    isHasDoctor,
    paymentInformation
  };
};

const mapDispatchToProps = dispatch => ({
  checkStatusTransaction: status => {
    dispatch(getStatusTransaction(status));
  },
  stopGetStatusTransaction: () => {
    dispatch(stopGetStatusTransaction());
  }
});

export default connect(mapStateToProps, mapDispatchToProps)(PaymentMethod);
