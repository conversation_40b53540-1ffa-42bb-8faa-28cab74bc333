/* eslint-disable prettier/prettier */
import cx from "classnames";
import { first, get, isEmpty, size } from "lodash";
import {
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBContainer,
  MDBListGroup,
  MDBListGroupItem,
  MDBModal,
  MDBModalFooter,
  MDBModalHeader,
  MDBRow,
  MDBSpinner
} from "mdbreact";
import queryString from "query-string";
import React, { Component } from "react";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import PKHBtn from "~/components/common/atoms/Button";
import TagName from "~/components/common/atoms/TagName";
import ListPaymentMethod from "~/components/desktop/molecules/ListPaymentMethod";
import PatientInfomationLess from "~/components/desktop/molecules/PatientInfomationLess";
import {
  getStatusTransaction,
  stopGetStatusTransaction
} from "~/store/payment/paymentAction";
import { getExtraConfig, getPaymentInfo } from "~/store/totalData/actions";
import { getFormatMoney } from "~/utils/getFormatMoney";
import AppId from "~/utils/partner";
import styles from "./style.module.scss";
import moment from "moment";
import { client } from "~/utils/medproSDK";
import { openToast } from "~/components/common/molecules/ToastNotification";
import UserPaymentMethod from "~/components/desktop/pages/PaymentMethod/UserPaymentMethod";
import Modal from "~/components/common/molecules/Modal";
import iconInfo from "~/assets/img/svg/info.svg";

class PaymentMethod extends Component {
  constructor(props) {
    super(props);

    const { noBack } = queryString.parse(this.props.location.search);

    this.state = {
      noBack,
      selectedPaymentMethodGroupId: 0,
      showListUserPaymentMethod: false,
      listUserPaymentMethod: [],
      subNote: false,
      feeNote: false,
      careNote: false,
      referralCode: ""
    };
    this.scrollToDiv = React.createRef();
  }

  selectPaymentMethodGroup = group => {
    console.log("group :>> ", group);
    this.props.resetPaymentMethod();
    if (
      group.methodId !== this.state.selectedPaymentMethodGroupId &&
      group.paymentTypes.length === 1
    ) {
      this.props.handleSelectedMethod(group.paymentTypes[0], group.methodId);
    }
    this.setState({
      selectedPaymentMethodGroupId:
        group.methodId !== this.state.selectedPaymentMethodGroupId
          ? group.methodId
          : 0
    });
  };

  toggleShowPaymentConfirm = () => {
    const {
      selectedMethod,
      extraConfig,
      handleDoPayment,
      toggleShowPaymentConfirm,
      toggleAlert
    } = this.props;
    const isConfirmDialog = get(extraConfig, "isConfirmDialog", "");

    if (isConfirmDialog) {
      if (selectedMethod.code) {
        if (this.props.setReferralCode) {
          this.props.setReferralCode(this.state.referralCode);
        }
        toggleShowPaymentConfirm();
      } else {
        toggleAlert();
      }
    } else {
      if (selectedMethod.code) {
        handleDoPayment(this.state.referralCode);
      } else {
        toggleAlert();
      }
    }
  };

  toggleShowPaymentHelpsConfirm = () => {
    this.props.toggleShowPaymentHelpsConfirm();
  };

  checkHiddenQRcode = () => {
    const { paymentInformation } = this.props;

    const extendData = get(paymentInformation, "bookingInfo.extendData", "");
    if (extendData) return "";
    else return "d-none";
  };

  renderListSpecialist = data => {
    const { schedulesSelected, addonServices, isRepayment } = this.props;
    return schedulesSelected.map((item, index) => {
      const subjectName = get(item, "subject.name", "");
      const serviceName = get(item, "service.name", "");
      const price = get(item, "service.price", 0);
      const advanced = get(item, "service.advanced", 0);
      const displayDetail = get(item, "service.displayDetail", 0);
      const getADDON = isRepayment ? item?.addonServices : addonServices;
      return (
        <MDBListGroup key={`aaa${index}`} className={styles.list_group}>
          <MDBListGroupItem>
            <div className={styles.column1}>
              <i className="fal fa-stethoscope" />
              <b>Chuyên khoa</b>
            </div>
            <div className={styles.column2}>
              <b>{subjectName}</b>
            </div>
          </MDBListGroupItem>
          <MDBListGroupItem>
            <div className={styles.column1}>
              <i className="fal fa-stethoscope" />
              Dịch vụ
            </div>
            <div className={styles.column2}>{serviceName}</div>
          </MDBListGroupItem>
          {(displayDetail || getFormatMoney(price - advanced) > 0) && (
            <MDBListGroupItem>
              <div className={styles.column1}>Tiền khám</div>
              <div className={styles.column2}>
                <strong>
                  {displayDetail && displayDetail !== ""
                    ? displayDetail
                    : getFormatMoney(price - advanced) + " VNĐ"}
                </strong>
              </div>
            </MDBListGroupItem>
          )}
          {Number(advanced) !== 0 && (
            <MDBListGroupItem>
              <div className={styles.column1}>Tiền tạm ứng</div>
              <div className={styles.column2}>
                <strong>{getFormatMoney(advanced)} VNĐ</strong>
              </div>
            </MDBListGroupItem>
          )}
          {getADDON?.map((v, i) => {
            return (
              <MDBListGroupItem key={i}>
                <div className={styles.column1}>{v.name}</div>
                <div
                  className={
                    v.price === 0 ? styles.priceDescription : styles.column2
                  }
                >
                  <strong>
                    {v.price === 0
                      ? v?.priceText
                      : getFormatMoney(v.price) + " VNĐ"}
                  </strong>
                </div>
              </MDBListGroupItem>
            );
          })}
        </MDBListGroup>
      );
    });
  };

  componentDidUpdate(prevProps) {
    const { selectedMethod } = this.props;
    if (Object.keys(selectedMethod).length > 1) {
      if (this.scrollToDiv.current) {
        const {
          top,
          bottom
        } = this.scrollToDiv.current.getBoundingClientRect();
        if (top < 0 || bottom > window.innerHeight) {
          this.scrollToDiv.current.scrollIntoView({
            behavior: "smooth",
            block: "end"
          });
        }
      }
    }
    if (prevProps.data !== this.props.data) {
      if (!this.props.isRepayment)
        this.selectPaymentMethodGroup(this.props.data[0]);
    }
  }

  componentDidMount() {
    const { checkStatusTransaction, paymentInformation, extraConfig, partnerId } = this.props;
    console.log("paymentInformation :>> ", paymentInformation);
    const extendData = get(paymentInformation, "bookingInfo.extendData", "");
    console.log("extendData", extendData);
    if (extendData) {
      const status = "active";
      checkStatusTransaction(status);
    }

    const secretBooking = window.localStorage.getItem("secretBooking");
    if (secretBooking) {
      this.props.getPaymentInfo(secretBooking, "secretBooking");
    }
    console.log('!extraConfig :>> ', !extraConfig);
    if (isEmpty(extraConfig)) {
      this.props.getExtraConfig(partnerId)
    }
  }

  handleToggleShowUserPaymentMethod() {
    this.setState(state => ({
      showListUserPaymentMethod: !state.showListUserPaymentMethod
    }));
  }

  handleReferralCodeChange = e => {
    this.setState({
      referralCode: e.target.value
    });
  };

  async viewUserPaymentMethod() {
    const {
      schedulesSelected,
      patient,
      treeId,
      addonServices,
      partnerId,
      platform
    } = this.props;
    const arrBooking = schedulesSelected.map(schedule => {
      const price = get(schedule, "service.price", 0);
      const bookingDateTransfer =
        treeId === "TELEMEDNOW"
          ? moment().toISOString()
          : moment(schedule?.date).toISOString();
      return {
        bookingId: schedule?.id || "",
        patientId: patient?.selectedPatientId || "",
        price: price,
        groupId: 1,
        treeId: treeId || "",
        serviceId: schedule?.service?.id || "",
        subjectId: schedule?.subject?.id || "",
        roomId: schedule?.room?.id || "",
        doctorId: schedule?.doctor?.id || "",
        bookingDate: bookingDateTransfer,
        addonServices: addonServices?.map(i => i.id) || []
      };
    });
    try {
      const { data } = await client.getAllPaymentMethod(
        {
          ...arrBooking[0]
        },
        {
          partnerid: partnerId || AppId,
          appid: AppId,
          platform,
          cskhtoken: ""
        }
      );
      this.state.listUserPaymentMethod = data;
      this.handleToggleShowUserPaymentMethod();
    } catch (err) {
      console.log("error viewUserPaymentMethod: ", err);
      openToast("Lỗi phương thức thanh toán", "error");
    }
  }

  render() {
    const {
      data,
      selectedMethod,
      handleSelectedMethod,
      loading,
      price,
      isRepayment,
      handleGoBack,
      schedulesSelected,
      partnerId,
      treeId,
      addonMedproCare,
      extraConfig,
      medproCare
    } = this.props;
    const medproCareNote = get(
      first(schedulesSelected),
      "service.medproCareNote",
      ""
    );
    const { subNote, feeNote, careNote } = this.state;

    const displayDetail = get(
      schedulesSelected[0],
      "service.displayDetail",
      ""
    );
    const subTotal = get(price, "subTotal", 0);
    const totalFee = get(price, "totalFee", 0);
    const grandTotal = get(price, "grandTotal", 0);
    const medproCareFee = get(price, "medproCareFee", 0);
    const medproCareService = isRepayment
      ? {
        price: medproCareFee
      }
      : get(
        first(schedulesSelected),
        "service.addonMedproCare[0]",
        addonMedproCare
      );

    const objCheckNextStep = Object.create(null);
    if (size(selectedMethod) > 1) {
      objCheckNextStep.nextStep = true;
    }

    if (loading) {
      return (
        <div className="loading">
          <MDBSpinner big crazy tag="div" />
        </div>
      );
    }

    let convertDataPayment;
    if (isRepayment) {
      convertDataPayment = data?.filter(item => {
        return item.methodId !== "SHARE_PAYMENT";
      });
    } else {
      convertDataPayment = data;
    }
    return (
      <div>
        <div
          className={cx(
            styles.wapper_page_desktop,
            styles["wapper_page_desktop_" + AppId]
          )}
        >
          <MDBContainer>
            <MDBRow className="row_mobile">
              <MDBCol md="12" lg="3">
                <PatientInfomationLess />
              </MDBCol>
              <MDBCol md="12" lg="9">
                <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
                  <MDBCardHeader className={styles.panels_header}>
                    <TagName
                      element="h2"
                      className={[
                        "title_component",
                        "title_choose_date",
                        "space_between",
                        "title_header_mobile"
                      ]}
                    >
                      <span>Chọn phương thức thanh toán</span>
                    </TagName>
                  </MDBCardHeader>
                  <MDBCardBody className={styles.card_body}>
                    <MDBRow>
                      <MDBCol md="6">
                        <div className={styles.group_payment}>
                          <ListPaymentMethod
                            allPaymentMethodGroups={convertDataPayment}
                            handleSelectedMethod={handleSelectedMethod}
                            selectedMethod={selectedMethod}
                            selectPaymentMethodGroup={
                              this.selectPaymentMethodGroup
                            }
                            selectedPaymentMethodGroupId={
                              this.state.selectedPaymentMethodGroupId
                            }
                          />
                          {!isRepayment && (
                            <PKHBtn
                              onClick={() => this.viewUserPaymentMethod()}
                              create="create"
                            >
                              Xem danh sách PTTT hiển thị tại CSYT
                            </PKHBtn>
                          )}
                        </div>
                      </MDBCol>
                      <MDBCol md="6">
                        <div className={styles.list_group_payment}>
                          <div className={styles.sub_title}>
                            <i className="fal fa-info-square" />
                            Thông tin thanh toán
                          </div>
                          {this.renderListSpecialist()}
                          <div className={styles.total_payment}>
                            <MDBListGroup className={styles.list_group}>
                              {size(schedulesSelected) > 1 && (
                                <MDBListGroupItem className={styles.group_fee}>
                                  <span className={styles.title}>Tổng tiền khám&nbsp;
                                    {selectedMethod?.subTotalNote && (
                                      <img
                                        src={iconInfo}
                                        alt=""
                                        onClick={() => {
                                          this.setState({
                                            subNote: true
                                          });
                                        }}
                                      />
                                    )}
                                    :
                                  </span>
                                  <strong>
                                    {displayDetail && displayDetail !== ""
                                      ? displayDetail
                                      : getFormatMoney(subTotal) + " VNĐ"}
                                  </strong>
                                </MDBListGroupItem>
                              )}
                              {!medproCare?.partner2bill && medproCareService?.price > 0 && (
                                <MDBListGroupItem
                                  style={{
                                    textAlign: "right",
                                    display: "flex",
                                    alignItems: "center"
                                  }}
                                  className={styles.group_fee}
                                >
                                  <span className={styles.title}>Dịch vụ đặt thêm&nbsp;
                                    {medproCareNote && (
                                      <img
                                        src={iconInfo}
                                        alt=""
                                        onClick={() => {
                                          this.setState({
                                            careNote: true
                                          });
                                        }}
                                      />
                                    )}
                                    :
                                  </span>
                                  <strong>
                                    {getFormatMoney(medproCareService?.price)}
                                    {" VNĐ"}
                                  </strong>
                                </MDBListGroupItem>
                              )}
                              <MDBListGroupItem
                                style={{
                                  textAlign: "right",
                                  display: "flex",
                                  alignItems: "center"
                                }}
                                className={styles.customFee}
                              >
                                <div className={styles.group_fee}>
                                  <span className={styles.title}>
                                    {treeId !== "CSKH"
                                      ? partnerId === "bvsingapore"
                                        ? "Phí tư vấn trực tuyến với bệnh viện SGH"
                                        : "Phí tiện ích + Phí TGTT"
                                      : ""}
                                    &nbsp;
                                    {selectedMethod?.totalFeeNote && (
                                      <img
                                        src={iconInfo}
                                        alt=""
                                        onClick={() => {
                                          this.setState({
                                            feeNote: true
                                          });
                                        }}
                                      />
                                    )}
                                    :
                                  </span>
                                  <strong>
                                    {totalFee === 0
                                      ? "0 VNĐ"
                                      : getFormatMoney(totalFee) + " VNĐ"}{" "}
                                  </strong>
                                </div>
                                {extraConfig.discountUMCGroup?.includes(
                                  partnerId
                                ) && selectedMethod?.totalFeeNote && (
                                    <sup className={styles.note_discount_fee_umc}>
                                      Giảm 2.000đ, ưu đãi từ Medpro & UMC
                                    </sup>
                                  )}
                              </MDBListGroupItem>
                              <MDBListGroupItem className={styles.group_fee}>
                                <span className={styles.title}>TỔNG CỘNG:</span>
                                <strong>
                                  {getFormatMoney(grandTotal)} VNĐ
                                </strong>
                              </MDBListGroupItem>
                            </MDBListGroup>
                            <div className={styles.referral_code_section}>
                              <div className={styles.referral_code_title}>Mã giới thiệu</div>
                              <div className={styles.form_group}>
                                <input
                                  type="text"
                                  className="form-control"
                                  placeholder="Nhập mã giới thiệu (nếu có)"
                                  onChange={this.handleReferralCodeChange}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </MDBCol>
                    </MDBRow>
                    <div className={styles.next_prev}>
                      {this.state.noBack ? (
                        <div />
                      ) : (
                        <PKHBtn
                          backdesktop="backdesktop"
                          onClick={handleGoBack}
                        >
                          Quay lại
                        </PKHBtn>
                      )}{" "}
                      <ul className="list-inline">
                        <li className="list-inline-item">
                          <PKHBtn
                            create="create"
                            buttonArrow
                            {...objCheckNextStep}
                            onClick={() => this.toggleShowPaymentConfirm()}
                          >
                            Thanh toán
                          </PKHBtn>
                        </li>
                      </ul>
                    </div>
                  </MDBCardBody>
                </MDBCard>
                <div ref={this.scrollToDiv} />
              </MDBCol>
            </MDBRow>
            <Modal
              title="Thông báo"
              modal={subNote}
              children={selectedMethod.subTotalNote}
              centered
              toggle={() => {
                this.setState({
                  subNote: false
                });
              }}
              onOk={() => {
                this.setState({
                  subNote: false
                });
              }}
              footer
              footerConfirm
              okText="Đồng ý"
              cancelText={false}
              className="centered"
            />
            <Modal
              title="Thông báo"
              modal={feeNote}
              children={selectedMethod.totalFeeNote}
              centered
              toggle={() => {
                this.setState({
                  feeNote: false
                });
              }}
              onOk={() => {
                this.setState({
                  feeNote: false
                });
              }}
              footer
              footerConfirm
              okText="Đồng ý"
              cancelText={false}
              className="centered"
            />
            {medproCareNote && (
              <Modal
                title="Thông báo"
                modal={careNote}
                children={medproCareNote}
                centered
                toggle={() => {
                  this.setState({
                    careNote: false
                  });
                }}
                onOk={() => {
                  this.setState({
                    careNote: false
                  });
                }}
                footer
                footerConfirm
                okText="Đồng ý"
                cancelText={false}
                className="centered"
              />
            )}
          </MDBContainer>
        </div>

        <MDBModal
          iconTitle={<i className="fal fa-bell" />}
          isOpen={this.state.showListUserPaymentMethod}
          title="Danh sách PTTT hiển thị tại CSYT"
          footer
          footerConfirm
          className={styles.modal}
          contentClassName={styles.content}
          cancelText="Quay lại"
          toggle={() => {
            this.setState(state => ({
              showListUserPaymentMethod: !state.showListUserPaymentMethod
            }));
          }}
        >
          <MDBModalHeader
            toggle={() => {
              this.setState(state => ({
                showListUserPaymentMethod: !state.showListUserPaymentMethod
              }));
            }}
          >
            Danh sách PTTT hiển thị tại CSYT
          </MDBModalHeader>
          <UserPaymentMethod
            schedulesSelected={this.props.schedulesSelected}
            listUserPaymentMethod={this.state.listUserPaymentMethod}
          />
          <MDBModalFooter>
            <PKHBtn
              key="btn-cancel"
              onClick={() => {
                this.setState(state => ({
                  showListUserPaymentMethod: !state.showListUserPaymentMethod
                }));
              }}
            >
              Đóng
            </PKHBtn>
          </MDBModalFooter>
        </MDBModal>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { IsAuthenticated, info },
    payment: { selectedMethod, data, showModal, price },
    doctorAndTime: { sumaryInfo },
    umcSubmitPayment: {
      isRedirectToPaymentSupportPage,
      errorMsg,
      errorBooking
    },
    features: { selectedFeature, selectedFeatureBooking },
    totalData: {
      treeId,
      schedulesSelected,
      bookingTree,
      loading,
      isRepayment,
      partnerId,
      subject: { isHasSubject },
      service: { isHasService },
      room: { isHasRoom },
      doctor: { isHasDoctor },
      paymentInformation,
      addonServices,
      extraConfig
    },
    hospital: { selectedHospital }
  } = state;
  return {
    token: info?.token || "",
    addonServices,
    IsAuthenticated,
    device: type,
    selectedMethod,
    sumaryInfo,
    isRedirectToPaymentSupportPage,
    errorMsg,
    errorBooking,
    showModal,
    data,
    selectedFeature,
    selectedFeatureBooking,
    schedulesSelected,
    bookingTree,
    price,
    loading,
    isRepayment,
    selectedHospital,
    partnerId,
    isHasSubject,
    isHasService,
    isHasRoom,
    isHasDoctor,
    paymentInformation,
    treeId,
    extraConfig
  };
};

const mapDispatchToProps = {
  checkStatusTransaction: getStatusTransaction,
  stopGetStatusTransaction: stopGetStatusTransaction,
  getPaymentInfo: getPaymentInfo,
  getExtraConfig: getExtraConfig
};

export default withRouter(
  connect(mapStateToProps, mapDispatchToProps)(PaymentMethod)
);
