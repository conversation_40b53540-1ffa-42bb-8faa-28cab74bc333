import { first, get, isArray, size } from "lodash";
import {
  MDBAnimation,
  MDBBtn,
  MDBNav,
  MDBNavItem,
  MDBSpinner,
  MDBTabContent,
  MDBTabPane
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import Modal from "~/components/common/molecules/Modal";
import MedicalCoupon from "~/components/desktop/molecules/MedicalCoupon";
import history from "~/history";
import { cancelMedicalBill } from "~/store/booking/bookingAction";
import { requestPaymentInfoCSKH } from "~/store/customerService/customerServiceActions";
import { selectedPatientDetail } from "~/store/patient/patientAction";
import {
  addScheduleRepayment,
  checkRepayment,
  getBookingInfo,
  reserveBookingCare247,
  selectHospital,
  setPaymentInfoCSKH
} from "~/store/totalData/actions";
import v from "validator";
import {
  CONTENT_CANCEL_MEDICAL_BILL,
  NO,
  TITLE_CANCEL_MEDICAL_BILL,
  YES
} from "~/utils/constants";
import { getRouteFollowPartnerId, isEmptyObject } from "~/utils/func";
import styles from "./style.module.scss";
import { client } from "~/utils/medproSDK";
import MedicalCouponCare from "~/components/desktop/molecules/MedicalCouponCare";
import cx from "classnames";
import { getFormatMoney } from "~/utils/getFormatMoney";
class BookingDetailForUser extends Component {
  constructor(props) {
    super(props);
    const { smsCode, transactions } = this.props.match.params;
    this.state = {
      smsCode,
      transactions,
      isOpenCancelModal: false,
      isConfirmNumber: false,
      errorPhone: "",
      loadingConfirm: false,
      activeItemBooking: "NormalMedpro",
      care247Available: {},
      loading: true,
      isPayment2bill: false
    };
    this.myNumberRef = React.createRef();
  }

  fetchMedproCareInfo = async transactionId => {
    try {
      const { data } = await client.getMedproCareInformationCskh({
        transactionId: transactionId
      });
      this.setState({
        care247Available: data
      });
      if (data.popupPayPending.status) {
        this.togglePayment2Bill();
      }
    } catch (error) {
      console.log("error :>> ", error);
    } finally {
      this.setState({
        loading: false
      });
    }
  };

  handleRePaymentByUser = () => {
    const { paymentInformation, checkRepayment } = this.props;

    const {
      bookingInfo: { partnerId, patient }
    } = first(paymentInformation);

    this.props.selectHospital(partnerId);
    this.props.selectedPatientDetail(patient);

    this.props.addScheduleRepayment({ paymentInfo: paymentInformation });

    checkRepayment(true);

    const route = getRouteFollowPartnerId(
      "/hinh-thuc-thanh-toan?noBack=true&isUser=true",
      partnerId
    );
    history.push(route);
  };

  checkPhoneViewBooking = async () => {
    const { smsCode, transactions } = this.props.match.params;
    try {
      let query = {};
      if (smsCode) {
        query = { smsCode };
      } else if (transactions) {
        query = { transactionId: transactions };
      }
      const response = await client.checkPhoneViewBooking(query);
      const { enable } = response.data;
      this.setState({ isConfirmNumber: enable });
      if (!enable) {
        if (smsCode) {
          this.handlePaymentInfoWithSmsCode({
            smsCode,
            phone: ""
          });
        } else if (transactions) {
          this.handlePaymentInfoWithTransactionCode({
            transactionCode: transactions,
            phone: ""
          });
        }
      }
    } catch (error) {
      this.setState({ isConfirmNumber: false });
      if (smsCode) {
        this.handlePaymentInfoWithSmsCode({
          smsCode,
          phone: ""
        });
      } else if (transactions) {
        this.handlePaymentInfoWithTransactionCode({
          transactionCode: transactions,
          phone: ""
        });
      }
    }
  };

  componentDidMount() {
    const { smsCode, transactions } = this.props.match.params;

    if (smsCode || transactions) {
      this.checkPhoneViewBooking();
    }
  }

  closeCancelMedicalBillModal = () => {
    this.setState({
      isOpenCancelModal: false
    });
  };

  method = {
    toggleCancelModal: ({ bill }) => {
      const { isOpenCancelModal } = this.state;
      this.setState({ isOpenCancelModal: !isOpenCancelModal, bill });
    }
  };

  handleCancelMedicalleBill = () => {
    const { isOpenCancelModal, bill, transactionId } = this.state;
    this.setState({ isOpenCancelModal: !isOpenCancelModal });
    this.props.cancelMedicalBill({ bill, transactionId });
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  handlePaymentInfoWithSmsCode = async ({ smsCode, phone }) => {
    const { partnerId } = this.props;
    try {
      this.setState({
        loadingConfirm: true
      });
      const response = await client.getBookingInfoCSKH(
        {
          smsCode: smsCode,
          phone: phone
        },
        {
          partnerid: partnerId
        }
      );
      const editData = isArray(response.data) ? response.data : [response.data];
      if (isArray(response.data)) {
        this.fetchMedproCareInfo(
          first(response.data)?.bookingInfo?.transactionId
        );
      } else {
        this.fetchMedproCareInfo(response.data?.bookingInfo?.transactionId);
      }
      this.props.setPaymentInfoCSKH(editData);
      this.setState({
        isConfirmNumber: false
      });
    } catch (error) {
      const message = get(error, "response.data.message");
      this.setState({
        errorPhone: message
      });
    } finally {
      this.setState({
        loadingConfirm: false
      });
    }
  };

  handlePaymentInfoWithTransactionCode = async ({ transactionCode, phone }) => {
    const { partnerId } = this.props;
    try {
      this.setState({
        loadingConfirm: true
      });
      const response = await client.cskhGetBookingsTransaction(
        {
          transactionId: transactionCode,
          phone: phone
        },
        {
          partnerid: partnerId
        }
      );
      const editData = isArray(response.data) ? response.data : [response.data];
      if (isArray(response.data)) {
        this.fetchMedproCareInfo(
          first(response.data)?.bookingInfo?.transactionId
        );
      } else {
        this.fetchMedproCareInfo(response.data?.bookingInfo?.transactionId);
      }

      this.props.setPaymentInfoCSKH(editData);
      this.setState({
        isConfirmNumber: false
      });
    } catch (error) {
      const message = get(error, "response.data.message");
      this.setState({
        errorPhone: message
      });
    } finally {
      this.setState({
        loadingConfirm: false
      });
    }
  };

  validatePhone = (value, callback) => {
    if (!value) {
      callback("Vui lòng nhập số điện thoại !");
    } else {
      if (v.isMobilePhone(value, "vi-VN")) {
        callback();
      } else {
        callback("Vui lòng nhập đúng số điện thoại !");
      }
    }
  };

  toggleTabs = value => {
    this.setState({ activeItemBooking: value });
  };

  togglePayment2Bill = () => {
    this.setState(preState => ({
      ...preState,
      isPayment2bill: !preState.isPayment2bill
    }));
  };

  onClosePayment2Bill = () => {
    const { activeItemBooking } = this.state;
    this.togglePayment2Bill();
    if (activeItemBooking === "NormalMedpro") {
      this.toggleTabs("MedproCare");
    }
  };

  onPaymentCare247 = () => {
    const { paymentInformation } = this.props;
    const { care247Available } = this.state;
    const bookingInfo = first(paymentInformation).bookingInfo;
    const urlBookingDetail =
      size(paymentInformation) > 1
        ? `transactions/${bookingInfo.transactionId}`
        : `booking/${bookingInfo.bookingCode}`;
    const payload = {
      transactionId: bookingInfo.transactionId,
      redirectUrl: `${window.location.origin}/${urlBookingDetail}`,
      medproCareServiceIds: [
        care247Available?.popupPayPending?.addonServices[0]?.id
      ]
    };
    this.props.reserveBookingCare247(payload);
  };

  render() {
    const { paymentInformation } = this.props;
    const {
      isOpenCancelModal,
      isConfirmNumber,
      errorPhone,
      smsCode,
      transactions,
      loadingConfirm,
      activeItemBooking,
      care247Available,
      isPayment2bill
    } = this.state;
    if (isConfirmNumber) {
      return (
        <Modal
          title=""
          centered
          modal={isConfirmNumber}
          children={
            <div className={styles.confirmPhone}>
              <p>Vui lòng nhập số điện thoại đã đăng ký phiếu khám</p>
              <input
                type="text"
                className="form-control"
                placeholder="Ex: 090****123"
                ref={this.myNumberRef}
              />

              <div className={styles.alertSpan}>
                {errorPhone && (
                  <>
                    <i
                      className="fas fa-exclamation-triangle"
                      style={{ color: "#ff0000", fontSize: "10px" }}
                    />
                    <span>{errorPhone}</span>
                  </>
                )}
              </div>

              <div className={styles.confirm}>
                <button
                  className={styles.btnConfirm}
                  color="primary"
                  disabled={loadingConfirm}
                  onClick={event => {
                    event.preventDefault();

                    const valuePhone = this.myNumberRef.current?.value;
                    this.validatePhone(valuePhone, async value => {
                      if (!value) {
                        if (smsCode) {
                          this.handlePaymentInfoWithSmsCode({
                            smsCode,
                            phone: valuePhone
                          });
                        } else if (transactions) {
                          this.handlePaymentInfoWithTransactionCode({
                            transactionCode: transactions,
                            phone: valuePhone
                          });
                        }
                      } else {
                        this.setState({
                          errorPhone: value
                        });
                      }
                    });
                  }}
                >
                  {loadingConfirm ? "Đang kiểm tra..." : "Xác nhận"}
                </button>
              </div>
              <sup className={styles.note}>
                <span>Lưu ý:</span> Khi bạn nhập sai số điện thoại quá 3 lần
                phiếu khám của bạn sẽ tạm khóa
              </sup>
            </div>
          }
        />
      );
    }

    if (!paymentInformation) return null;

    const bookingInfo = first(paymentInformation).bookingInfo;
    const status = get(bookingInfo, "status", 1);
    const handleBill = paymentInformation?.map(item => {
      return {
        transactionId: item.bookingInfo?.bookingCode,
        subject: item.bookingInfo?.subject?.name || ""
      };
    });
    const contentCancel = () => {
      const bill =
        handleBill
          ?.map(({ transactionId, subject }) => {
            return "&emsp;" + transactionId + " khoa " + subject + "<br/>";
          })
          ?.join("") || "";
      return (
        <>
          Khi bạn hủy phiếu thì các phiếu <br />${bill} sẽ bị hủy theo <br />{" "}
          Tiền khám sẽ được hoàn theo quy định của bên thanh toán.
          <br />
          Mọi thắc mắc xin vui lòng liên hệ{" "}
          <a href="tel: 1900 2115">1900 2115</a> để được hỗ trợ.
          <br /> Bạn có chắc muốn hủy?
        </>
      );
    };
    if (this.state.loading) {
      return (
        <MDBAnimation type="fadeIn" className={styles.wapper_loading}>
          <div className="loading absolute">
            <MDBSpinner big />
          </div>
        </MDBAnimation>
      );
    }
    return (
      <React.Fragment>
        <div className={styles.wrapper_bill}>
          {/* {tabContent === "booking" &&} */}
          {[0, 6].includes(status) && (
            <div className={styles.wrapper_text}>
              <h5>
                Phiếu khám này chỉ sử dụng được sau khi thanh toán thành công.
                <br /> Vui lòng thanh toán.
              </h5>
            </div>
          )}

          <div className={styles.groupBtn}>
            {[0, 6].includes(status) && (
              <MDBBtn
                color="primary"
                className={styles.btnRepayment}
                onClick={() => this.handleRePaymentByUser()}
              >
                Thanh toán
              </MDBBtn>
            )}
          </div>
          {!isEmptyObject(care247Available) && (
            <div className={cx(styles.container)}>
              <MDBNav className={cx(styles.tabsBooking)}>
                <MDBNavItem
                  className={cx(
                    activeItemBooking === "NormalMedpro"
                      ? styles.NormalMedpro
                      : ""
                  )}
                  active={activeItemBooking === "NormalMedpro"}
                  onClick={() => this.toggleTabs("NormalMedpro")}
                >
                  <span>Phiếu khám</span>
                </MDBNavItem>
                <MDBNavItem
                  className={cx(
                    styles.isDots,
                    activeItemBooking === "MedproCare" ? styles.MedproCare : ""
                  )}
                  active={activeItemBooking === "MedproCare"}
                  onClick={() => this.toggleTabs("MedproCare")}
                >
                  <span>Dịch vụ Care247</span>
                </MDBNavItem>
              </MDBNav>
            </div>
          )}
          <MDBTabContent activeItem={activeItemBooking}>
            <MDBTabPane tabId="NormalMedpro">
              <MedicalCoupon type="UserForCSKH" {...this.method} />
            </MDBTabPane>
            <MDBTabPane tabId="MedproCare">
              <MedicalCouponCare
                {...this.props}
                care247Available={care247Available}
                togglePayment2Bill={this.togglePayment2Bill}
              />
            </MDBTabPane>
          </MDBTabContent>
        </div>

        <Modal
          className="centered"
          modal={isOpenCancelModal}
          title={TITLE_CANCEL_MEDICAL_BILL}
          toggle={this.closeCancelMedicalBillModal}
          centered
          footer
          footerConfirm
          children={
            paymentInformation && paymentInformation.length > 1
              ? contentCancel()
              : CONTENT_CANCEL_MEDICAL_BILL
          }
          cancelText={NO}
          okText={YES}
          onCancel={() => this.closeCancelMedicalBillModal()}
          onOk={this.handleCancelMedicalleBill}
        />

        {isPayment2bill && (
          <Modal
            className="centered"
            modal={isPayment2bill}
            title="Thông báo"
            toggle={this.togglePayment2Bill}
            centered
            footer
            footerConfirm
            children={
              <div className={styles.body}>
                <p className={styles.description}>
                  {care247Available?.popupPayPending?.content}
                </p>
                <div className={styles.infoPayment}>
                  <p>
                    <span>
                      {
                        care247Available?.popupPayPending?.addonServices[0]
                          ?.name
                      }
                    </span>
                    <span>
                      {getFormatMoney(
                        care247Available?.popupPayPending?.addonServices[0]
                          ?.price
                      )}
                    </span>
                  </p>
                  <p>
                    <span>Phí TGTT:</span>
                    <span>
                      {care247Available?.popupPayPending?.transferFee}
                      {
                        care247Available?.popupPayPending?.addonServices[0]
                          ?.currency
                      }
                    </span>
                  </p>
                  <p className={styles.totalFee}>
                    <span>Tổng cộng:</span>
                    <span>
                      {getFormatMoney(
                        care247Available?.popupPayPending?.amount
                      )}
                      {
                        care247Available?.popupPayPending?.addonServices[0]
                          ?.currency
                      }
                    </span>
                  </p>
                </div>
                {care247Available?.popupPayPending?.note && (
                  <p className={styles.note}>
                    <i className="fal fa-info-circle" />
                    {care247Available?.popupPayPending?.note}
                  </p>
                )}
              </div>
            }
            cancelText="Bỏ qua"
            okText="Thanh toán ngay"
            onCancel={this.onClosePayment2Bill}
            onOk={this.onPaymentCare247}
          />
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    totalData: { paymentInformation, partnerId }
  } = state;
  return {
    paymentInformation,
    partnerId
  };
};

const mergeProps = (stateProps, dispatchProps, ownProps) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps
});

export default connect(
  mapStateToProps,
  {
    requestPaymentInfoCSKH,
    checkRepayment,
    setPaymentInfoCSKH,
    selectedPatientDetail,
    selectHospital,
    getBookingInfo,
    addScheduleRepayment: addScheduleRepayment,
    cancelMedicalBill: cancelMedicalBill,
    reserveBookingCare247
  },
  mergeProps
)(BookingDetailForUser);
