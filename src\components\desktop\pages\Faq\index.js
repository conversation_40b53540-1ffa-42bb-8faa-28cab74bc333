/* eslint-disable max-len */
import React, { Component } from "react";
import {
  MDBContainer,
  MDBRow,
  MDBCol,
  MDBView,
  MDBCard,
  MDBCardTitle,
  MDBListGroup,
  MDBListGroupItem
} from "mdbreact";
import cx from "classnames";
import { Link } from "react-router-dom";
import styles from "./style.module.scss";
import InputSearch from "~/components/common/atoms/InputSearch";
import TagName from "~/components/common/atoms/TagName";
import support from "~/assets/img/desktop/common/avatar.png";
class Faq extends Component {
  constructor(props) {
    super(props);
    this.state = { isSuggestedFAQShowed: false, searchValue: "" };
  }

  toggleSuggestedFAQList = () => {
    this.setState({ isSuggestedFAQShowed: !this.state.isSuggestedFAQShowed });
  };

  timer;
  onChangeInputSearch = event => {
    const searchValue = event.target.value;
    const { updateShowedFAQBySearch } = this.props;
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.timer = setTimeout(() => {
      this.setState({ searchValue });
      updateShowedFAQBySearch(searchValue);
    }, 300);
  };

  renderMostSuggestedFAQList = () => {
    return (
      <MDBCard className={cx(styles.card, styles.suggested)}>
        <h4>Câu hỏi đề xuất</h4>
        <MDBListGroup className={styles.list_group}>
          <MDBListGroupItem>
            Đối tượng bệnh nhân nào có thể sử dụng phần mềm để đăng ký khám
            bệnh?
          </MDBListGroupItem>
          <MDBListGroupItem>
            Tôi đăng ký trước nhưng mai tôi bận không đến khám được tôi muốn hủy
            phiếu làm sao?
          </MDBListGroupItem>
          <MDBListGroupItem>
            Tôi điện thoại tổng đài 19007178 hoài không ai nghe máy?
          </MDBListGroupItem>
          <MDBListGroupItem>
            Điều kiện để được hoàn tiền là gì?
          </MDBListGroupItem>
          <MDBListGroupItem>
            Nếu BHYT của tôi bị từ chối, tôi phải làm sao?
          </MDBListGroupItem>
        </MDBListGroup>
      </MDBCard>
    );
  };

  renderSearchedFAQList = FAQList => {
    return (
      <MDBCard className={styles.card}>
        <h4>Kết quả</h4>
        <MDBListGroup className={styles.list_group}>
          {!FAQList.loading ? (
            FAQList.showedFAQ.data.length ? (
              FAQList.showedFAQ.data
                .map(group => group.faq)
                .reduce((a, b) => a.concat(b))
                .map((faq, i) => (
                  <MDBListGroupItem key={i}>{faq.question}</MDBListGroupItem>
                ))
            ) : (
              <MDBListGroupItem>Không tìm thấy câu hỏi nào!</MDBListGroupItem>
            )
          ) : null}
        </MDBListGroup>
      </MDBCard>
    );
  };

  render() {
    const { faq } = this.props;
    return (
      <div className={styles.wapper_page_desktop}>
        <MDBView className={styles.img_parallax} fixed>
          <MDBContainer>
            <MDBRow>
              <MDBCol>
                <div className={styles.wapper_page_head}>
                  <TagName
                    element="h1"
                    className={[
                      "title_component",
                      "title_line",
                      "title_contact"
                    ]}
                  >
                    <span>Trợ giúp MEDPRO</span>
                  </TagName>
                  <div className={styles.desc}>
                    <p>
                      Các câu hỏi đáp nhanh giúp bệnh nhân dễ dàng hơn khi trải
                      nghiệm MEDPRO
                    </p>
                  </div>
                  <InputSearch
                    title="Tìm nhanh câu hỏi"
                    icon={<i className="fal fa-search" />}
                    className="search_faq_desk"
                    onFocus={() => this.toggleSuggestedFAQList()}
                    onBlur={() => this.toggleSuggestedFAQList()}
                    onChange={() => this.onChangeInputSearch()}
                  />
                  <div className={styles.suggested_faq_list}>
                    {this.state.isSuggestedFAQShowed
                      ? !this.state.searchValue
                        ? this.renderMostSuggestedFAQList()
                        : this.renderSearchedFAQList(faq)
                      : null}
                  </div>
                </div>
              </MDBCol>
            </MDBRow>
          </MDBContainer>
        </MDBView>
        <MDBContainer>
          <MDBRow>
            <MDBCol size="12">
              <div className={styles.wapper_page_inner}>
                <MDBCard className={cx(styles.card, styles.card_item)}>
                  <div className={styles.icons}>
                    <Link to="/thac-mac/chi-tiet">
                      <svg width="48px" height="48px">
                        <g
                          id="Page-1"
                          stroke="none"
                          strokeWidth="1"
                          fill="none"
                          fillRule="evenodd"
                        >
                          <g
                            id="technical-support"
                            fill="#8B95A1"
                            fillRule="nonzero"
                          >
                            <path
                              d="M8.44226072,37.1202393 C7.77026363,37.1202393 7.12426762,37.2879638 6.55114744,37.6029052 C3.53210447,33.7327881 1.875,28.9251709 1.875,24 C1.875,11.8000489 11.8000489,1.875 24,1.875 C25.4831542,1.875 26.9655761,2.02258303 28.40625,2.31372075 C28.9130859,2.41625981 29.4082031,2.08813481 29.5107422,1.58056641 C29.6132812,1.072998 29.2851562,0.578613281 28.7775879,0.475708031 C27.2149658,0.160034156 25.6076661,0 24,0 C17.5894776,0 11.5623779,2.49645994 7.02941897,7.02941897 C2.49645994,11.5623779 0,17.5894776 0,24 C0,29.392456 1.83105469,34.6549073 5.16467287,38.8736573 C4.73547366,39.5148926 4.50476072,40.2685547 4.50476072,41.0577393 C4.50476072,42.1094971 4.91455078,43.0982666 5.65832522,43.8416748 C6.40173338,44.5854492 7.39050291,44.9952393 8.44226072,44.9952393 C9.49401853,44.9952393 10.4827881,44.5854492 11.2265625,43.8416748 C11.9703369,43.0982666 12.3797607,42.1091308 12.3797607,41.057373 C12.3797607,40.0059815 11.9703369,39.0168457 11.2265625,38.2734375 C10.4827881,37.5296631 9.49401853,37.1202393 8.44226072,37.1202393 Z M9.90051272,42.5159912 C9.51123047,42.9056396 8.99304197,43.1202393 8.44226072,43.1202393 C7.89147947,43.1202393 7.37365725,42.9056396 6.98400881,42.5159912 C6.59436037,42.1263427 6.37976072,41.6085205 6.37976072,41.0577393 C6.37976072,40.5065917 6.59436037,39.9887695 6.98400881,39.5991211 C7.37329106,39.2094727 7.89147947,38.9952393 8.44226072,38.9952393 C8.99304197,38.9952393 9.51086428,39.2098388 9.90051272,39.5991211 C10.2901612,39.9887695 10.5047607,40.5065917 10.5047607,41.0577393 C10.5047607,41.6085205 10.2901612,42.1263427 9.90051272,42.5159912 L9.90051272,42.5159912 Z"
                              id="Shape"
                            />
                            <path
                              d="M42.8345948,9.12524419 C43.8537598,7.5963135 43.6900635,5.50598147 42.3420411,4.15795894 C41.5982666,3.41455078 40.6094971,3.00476072 39.5577393,3.00476072 C38.5059815,3.00476072 37.5172119,3.41455078 36.7734375,4.15795894 C36.0296631,4.90173337 35.6202393,5.89050291 35.6202393,6.94226072 C35.6202393,7.99401853 36.0296631,8.98278806 36.7734375,9.7265625 C37.5172119,10.4703369 38.5059815,10.8797607 39.5577393,10.8797607 C40.2297364,10.8797607 40.8757324,10.7120362 41.4488526,10.3970947 C44.4678955,14.2675781 46.125,19.0748291 46.125,24 C46.125,36.1999511 36.1999511,46.125 24,46.125 C22.5314942,46.125 21.0629883,45.9803467 19.6358643,45.6947021 C19.1282959,45.5932617 18.6342773,45.9224854 18.5324708,46.4300537 C18.4310303,46.9379883 18.7602539,47.4316406 19.2678223,47.5334473 C20.8157959,47.8428955 22.408081,48 24,48 C30.4105224,48 36.4376221,45.5035401 40.970581,40.970581 C45.5035401,36.4376221 48,30.4105224 48,24 C48,18.6071777 46.1685791,13.3443604 42.8345948,9.12524419 Z M38.0994873,8.40087891 C37.7098388,8.01123047 37.4952393,7.49340825 37.4952393,6.94226072 C37.4952393,6.39147947 37.7098388,5.87365725 38.0994873,5.48400881 C38.4887695,5.09436037 39.006958,4.87976072 39.5577393,4.87976072 C40.1085205,4.87976072 40.6267089,5.09436037 41.0163574,5.48400881 C41.8201904,6.28820803 41.8201904,7.59667969 41.0163574,8.40087891 C40.6267089,8.79016116 40.1085205,9.00476072 39.5577393,9.00476072 C39.0065917,9.00476072 38.4887695,8.79016116 38.0994873,8.40087891 Z"
                              id="Shape"
                            />
                            <path
                              d="M20.1313477,40.125 C20.1313477,40.6428223 20.5513916,41.0625 21.0688477,41.0625 L26.9307862,41.0625 C27.4486084,41.0625 27.8682862,40.6428223 27.8682862,40.125 L27.8682862,38.3074951 C29.1009521,37.975708 30.279419,37.4871826 31.3868408,36.8492432 L32.670044,38.1324463 C32.8458252,38.3082276 33.0842286,38.4071045 33.333252,38.4071045 C33.581543,38.4071045 33.8203125,38.3082276 33.9960938,38.1320801 L38.1394043,33.9858398 C38.505249,33.6196289 38.505249,33.026001 38.1394043,32.6601562 L36.8576661,31.378418 C37.4956055,30.2709961 37.9841308,29.0925293 38.315918,27.8598633 L40.1264648,27.8598633 C40.6442871,27.8598633 41.0639648,27.4401855 41.0639648,26.9223633 L41.0639648,21.0604248 C41.0639648,20.5426026 40.6442871,20.1229248 40.1264648,20.1229248 L38.3155518,20.1229248 C37.9837646,18.890625 37.4956055,17.7121583 36.8576661,16.6047364 L38.1324463,15.329956 C38.3082276,15.1541748 38.4071045,14.9154052 38.4071045,14.666748 C38.4067383,14.4180908 38.3078614,14.1793213 38.1320801,14.0035401 L33.9854737,9.86022947 C33.6196289,9.49438481 33.026001,9.49438481 32.6597901,9.86059575 L31.3868408,11.1339112 C30.279419,10.4959717 29.1009521,10.0074463 27.8682862,9.67565916 L27.8682862,7.875 C27.8682862,7.35717769 27.4486084,6.9375 26.9307862,6.9375 L21.0688477,6.9375 C20.5513916,6.9375 20.1313477,7.35717769 20.1313477,7.875 L20.1313477,9.67602544 C18.8990479,10.0074463 17.720581,10.4959717 16.6131592,11.1339112 L15.3398438,9.86059575 C14.973999,9.494751 14.3803711,9.49438481 14.0141602,9.86022947 L9.86791988,14.0039062 C9.69177244,14.1796875 9.59289553,14.4180908 9.59289553,14.666748 C9.59289553,14.9154052 9.69177244,15.1541748 9.86755369,15.329956 L11.1423339,16.6047364 C10.5043945,17.7121583 10.0158692,18.8902588 9.68408203,20.1229248 L7.87683103,20.1229248 C7.35900881,20.1229248 6.93933103,20.5426026 6.93933103,21.0600586 L6.93603314,26.9219971 C6.93566897,27.1706543 7.03454588,27.4094239 7.21032713,27.5852051 C7.38610838,27.7609864 7.62451172,27.8598633 7.87353516,27.8598633 L9.68408203,27.8598633 C10.0158692,29.0925293 10.5043945,30.2709961 11.1423339,31.378418 L9.86059575,32.6601562 C9.494751,33.026001 9.49438481,33.6196289 9.86059575,33.9858398 L14.0039062,38.1320801 C14.1796875,38.3078614 14.4180908,38.4071045 14.666748,38.4071045 L14.6671143,38.4071045 C14.9157714,38.4071045 15.1541748,38.3082276 15.329956,38.1324463 L16.612793,36.8492432 C17.720581,37.4871826 18.8990479,37.975708 20.1313477,38.3074951 L20.1313477,40.125 Z M16.9804688,34.8775635 C16.6094971,34.6376953 16.1213379,34.6896973 15.8085937,35.0020752 L14.6671143,36.1435547 L11.8491211,33.3233643 L12.989502,32.1826172 C13.3018799,31.8702393 13.3538818,31.3820801 13.1140136,31.0111084 C12.2640381,29.6956787 11.6663818,28.2539062 11.3382568,26.7253417 C11.2452393,26.2935791 10.8636474,25.9848633 10.4216308,25.9848633 L8.81140134,25.9848633 L8.81359866,21.9979248 L10.4216308,21.9979248 C10.8636474,21.9979248 11.2452393,21.6895752 11.3382568,21.2574463 C11.6663818,19.729248 12.2640381,18.2874756 13.1140136,16.9720459 C13.3538818,16.6010742 13.3018799,16.1129151 12.989502,15.8005371 L11.8564453,14.6671143 L14.6766357,11.8491211 L15.8085937,12.9814453 C16.1213379,13.2938232 16.6091308,13.3454589 16.9804688,13.105957 C18.2955323,12.2559815 19.7373047,11.6583252 21.2658692,11.3302002 C21.6976318,11.2371826 22.0063477,10.8552246 22.0063477,10.4135742 L22.0063477,8.8125 L25.9932862,8.8125 L25.9932862,10.413208 C25.9932862,10.8552246 26.3016357,11.2371826 26.7337646,11.3298339 C28.2619629,11.6583252 29.7041016,12.2556152 31.0191651,13.1055908 C31.3901367,13.3454589 31.8782959,13.2938232 32.1906739,12.9810791 L33.322998,11.8491211 L36.1431885,14.6671143 L35.0101318,15.8001709 C34.6973877,16.1129151 34.645752,16.6010742 34.8856201,16.9720459 C35.7355958,18.2874756 36.3328857,19.729248 36.661377,21.2574463 C36.7540283,21.6895752 37.1359864,21.9979248 37.5780029,21.9979248 L39.1889648,21.9979248 L39.1889648,25.9848633 L37.5780029,25.9848633 C37.1359864,25.9848633 36.7543945,26.2935791 36.661377,26.7253417 C36.333252,28.2535401 35.7355958,29.6956787 34.8856201,31.0107422 C34.645752,31.3820801 34.6977539,31.8702393 35.0101318,32.1826172 L36.1505127,33.322998 L33.3325195,36.1431885 L32.1910401,35.0020752 C31.8782959,34.689331 31.3901367,34.6376953 31.0191651,34.8775635 C29.7037354,35.7275391 28.2619629,36.3248291 26.7337646,36.6533203 C26.3016357,36.7463379 25.9932862,37.1279297 25.9932862,37.5699463 L25.9932862,39.1875 L22.0063477,39.1875 L22.0063477,37.5699463 C22.0063477,37.1282959 21.6976318,36.7463379 21.2658692,36.6533203 C19.7376709,36.3251953 18.2958984,35.7275391 16.9804688,34.8775635 L16.9804688,34.8775635 Z"
                              id="Shape"
                            />
                            <path
                              d="M31.7464599,24 C31.7464599,19.7285156 28.2714844,16.2535401 24,16.2535401 C19.7285156,16.2535401 16.2535401,19.7285156 16.2535401,24 C16.2535401,28.2714844 19.7285156,31.7464599 24,31.7464599 C28.2714844,31.7464599 31.7464599,28.2714844 31.7464599,24 Z M18.1285401,24 C18.1285401,20.7623291 20.7623291,18.1285401 24,18.1285401 C27.2376709,18.1285401 29.8714599,20.7626953 29.8714599,24 C29.8714599,27.2373047 27.2376709,29.8714599 24,29.8714599 C20.7623291,29.8714599 18.1285401,27.2376709 18.1285401,24 Z"
                              id="Shape"
                            />
                            <path
                              d="M32.6323242,3.54565425 C32.8791504,3.54565425 33.1208496,3.4453125 33.2951661,3.27099609 C33.4694824,3.09558103 33.5698242,2.85388181 33.5698242,2.60705569 C33.5698242,2.36169431 33.4698487,2.11962891 33.2951661,1.94421384 C33.1208496,1.76989744 32.8791504,1.66955569 32.6323242,1.66955569 C32.3847656,1.66955569 32.1441651,1.76989744 31.9698487,1.94421384 C31.7951661,2.11962891 31.6948242,2.36059575 31.6948242,2.60705569 C31.6948242,2.85461428 31.7951661,3.09558103 31.9698487,3.27099609 C32.1441651,3.4453125 32.3847656,3.54565425 32.6323242,3.54565425 Z"
                              id="Path"
                            />
                            <path
                              d="M15.4086914,44.4711914 C15.1622315,44.4711914 14.9201661,44.5715333 14.7458496,44.7458496 C14.5715332,44.9201661 14.4711914,45.1622315 14.4711914,45.4086914 C14.4711914,45.65625 14.5715332,45.8972167 14.7458496,46.0715333 C14.9201661,46.2458496 15.1622315,46.3461914 15.4086914,46.3461914 C15.6551513,46.3461914 15.8972168,46.2458496 16.0715333,46.0715333 C16.2458496,45.8972167 16.3461914,45.6551513 16.3461914,45.4086914 C16.3461914,45.1622315 16.2458496,44.9201661 16.0715333,44.7458496 C15.8972168,44.5715333 15.6551513,44.4711914 15.4086914,44.4711914 Z"
                              id="Path"
                            />
                          </g>
                        </g>
                      </svg>
                    </Link>
                  </div>
                  <div className={styles.content}>
                    <MDBCardTitle tag="h2" className={styles.card_title}>
                      <Link to="/thac-mac/chi-tiet">Vấn đề chung </Link>
                    </MDBCardTitle>
                    <p>
                      Những câu hỏi thường xuyên của bệnh nhân sẽ tổng hợp tại
                      đây
                    </p>
                    {/* Avatar */}
                    <div className={styles.author}>
                      <div className={styles.avata}>
                        <img src={support} alt="" />
                      </div>
                      <div className={styles.author_item}>
                        <div className={styles.name}>
                          Đóng góp bởi <strong>Công Võ</strong>
                        </div>
                        <div className={styles.count_question}>
                          Thư mục có <strong>8</strong> câu hỏi
                        </div>
                      </div>
                    </div>
                  </div>
                </MDBCard>
                <MDBCard className={cx(styles.card, styles.card_item)}>
                  <div className={styles.icons}>
                    <Link to="/thac-mac/chi-tiet">
                      <svg width={48} height={44}>
                        <g
                          id="Page-1"
                          stroke="none"
                          strokeWidth="1"
                          fill="none"
                          fillRule="evenodd"
                        >
                          <g id="account" fill="#818A97" fillRule="nonzero">
                            <path
                              d="M38.9699063,0.0375 L2.********,0.0375 C1.32525,0.0375 0,1.36275 0,2.******** L0,33.0047813 C0,34.6336875 1.32525,35.9588437 2.********,35.9588437 L26.5425938,35.9588437 C26.9309062,35.9588437 27.2457188,35.6440312 27.2457188,35.2557187 C27.2457188,34.8674063 26.9309062,34.5525937 26.5425938,34.5525937 L23.9160938,34.5525937 L23.9160938,30.0034687 C23.9160938,28.8070312 23.2025625,27.7366875 22.0981875,27.2766562 L18.40425,25.7374687 C18.106125,25.61325 17.9134688,25.3243125 17.9134688,25.0013438 L17.9134688,23.9035312 C18.8600625,22.9408125 19.414125,21.6299062 19.414125,20.24925 L19.414125,17.2479375 C19.414125,14.7915938 17.0790938,12.793125 14.2090313,12.793125 C11.3389688,12.793125 9.0039375,14.7915 9.0039375,17.2479375 L9.0039375,20.24925 C9.0039375,21.6297188 9.558,22.9407188 10.5045938,23.9035312 L10.5045938,25.0013438 C10.5045938,25.3243125 10.3119375,25.61325 10.0138125,25.7375625 L6.31996875,27.2766563 C5.21559375,27.7367813 4.50196875,28.807125 4.50196875,30.0035625 L4.50196875,34.5526875 L2.********,34.5526875 C2.10065625,34.5526875 1.40625,33.8582813 1.40625,33.004875 L1.40625,8.94703125 L40.5177188,8.94703125 L40.5177188,20.6243438 C40.5177188,21.0126562 40.8325313,21.3274688 41.2208438,21.3274688 C41.6091563,21.3274688 41.9239688,21.0126562 41.9239688,20.6243438 L41.9239688,2.******** C41.9239688,1.36275 40.5988125,0.0375 38.9699063,0.0375 Z M5.90821875,30.0034688 C5.90821875,29.3765625 6.28209375,28.81575 6.8608125,28.574625 L10.55475,27.0355313 C11.3785313,26.6923125 11.9108438,25.8938438 11.9108438,25.0013438 L11.9108438,24.849 C12.1689375,24.8655 12.4264688,24.7393125 12.5649375,24.5003438 C12.7594687,24.16425 12.6448125,23.734125 12.3088125,23.5395 C11.1377813,22.8615 10.4102813,21.60075 10.4102813,20.2493438 L10.4102813,17.2480313 C10.4102813,15.5670938 12.1144688,14.1994688 14.209125,14.1994688 C16.3037813,14.1994688 18.0079688,15.567 18.0079688,17.2480313 L18.0079688,20.2493438 C18.0079688,21.6013125 17.28,22.8623438 16.1082188,23.5403438 C15.772125,23.7347813 15.6572813,24.1649063 15.8517188,24.5010938 C15.9820313,24.726375 16.2181875,24.8522813 16.461,24.8522813 C16.4764688,24.8522813 16.4919375,24.8503125 16.5074063,24.8492813 L16.5074063,25.0015313 C16.5074063,25.8940313 17.0397188,26.6925 17.8635938,27.0357188 L21.5575313,28.5749063 C22.1361563,28.8159375 22.5100313,29.3768438 22.5100313,30.0036563 L22.5100313,34.5527813 L5.90821875,34.5527813 L5.90821875,30.0034688 Z M40.5177188,7.54078125 L1.40625,7.54078125 L1.40625,2.******** C1.40625,2.13815625 2.10065625,1.44375 2.********,1.44375 L38.9699063,1.44375 C39.8234062,1.44375 40.5177188,2.13815625 40.5177188,2.******** L40.5177188,7.54078125 Z"
                              id="Shape"
                            />
                            <path
                              d="M3.7119375,3.7891875 L3.7044375,3.7891875 C3.316125,3.7891875 3.0050625,4.104 3.0050625,4.4923125 C3.0050625,4.880625 3.323625,5.1954375 3.7119375,5.1954375 C4.10025,5.1954375 4.4150625,4.880625 4.4150625,4.4923125 C4.4150625,4.104 4.10025,3.7891875 3.7119375,3.7891875 Z"
                              id="Path"
                            />
                            <path
                              d="M8.20640625,3.7891875 L8.19890625,3.7891875 C7.81059375,3.7891875 7.49953125,4.104 7.49953125,4.4923125 C7.49953125,4.880625 7.81809375,5.1954375 8.20640625,5.1954375 C8.59471875,5.1954375 8.90953125,4.880625 8.90953125,4.4923125 C8.90953125,4.104 8.59471875,3.7891875 8.20640625,3.7891875 Z"
                              id="Path"
                            />
                            <path
                              d="M5.955375,3.7891875 L5.947875,3.7891875 C5.5595625,3.7891875 5.2485,4.104 5.2485,4.4923125 C5.2485,4.880625 5.5670625,5.1954375 5.955375,5.1954375 C6.3436875,5.1954375 6.6585,4.880625 6.6585,4.4923125 C6.6585,4.104 6.3436875,3.7891875 5.955375,3.7891875 Z"
                              id="Path"
                            />
                            <path
                              d="M38.2195312,3.7891875 L11.2077187,3.7891875 C10.8194062,3.7891875 10.5045937,4.104 10.5045937,4.4923125 C10.5045937,4.880625 10.8194062,5.1954375 11.2077187,5.1954375 L38.2195312,5.1954375 C38.6078437,5.1954375 38.9226562,4.880625 38.9226562,4.4923125 C38.9226562,4.104 38.6078438,3.7891875 38.2195312,3.7891875 Z"
                              id="Path"
                            />
                            <path
                              d="M47.527875,25.359375 L37.3984688,21.8360625 C37.2488438,21.7839375 37.0860938,21.7839375 36.9364688,21.8360625 L29.6521875,24.3696562 C29.2854375,24.49725 29.0915625,24.8980313 29.2190625,25.2647812 C29.3466563,25.6315312 29.7474375,25.8253125 30.1140938,25.6979062 L37.167375,23.2446562 L46.56,26.5116563 C46.4781563,27.4095 46.2027188,29.4210937 45.2894063,31.8299063 C44.1760313,34.7664375 41.8673438,38.866875 37.167375,41.9256563 C32.4876563,38.8798125 30.1805625,34.8015 29.0649375,31.8812813 C27.8157188,28.611375 27.7414688,26.0334375 27.7408125,26.00775 C27.7320938,25.6199062 27.4155,25.3133438 27.0229688,25.3204687 C26.63475,25.3287188 26.3266875,25.6500937 26.3346855,26.0383125 C26.3372813,26.1511875 26.4085313,28.842 27.7303125,32.328375 C28.9494375,35.5439062 31.5118125,40.06725 36.7956563,43.356 C36.909375,43.4267812 37.0382813,43.4622187 37.1671875,43.4622187 C37.2960938,43.4622187 37.425,43.426875 37.5387188,43.356 C42.8225625,40.06725 45.3849375,35.5439062 46.6040625,32.328375 C47.9259375,28.842 47.9971875,26.1511875 47.9997049,26.0383125 C48.0062813,25.733625 47.8156875,25.4594063 47.527875,25.359375 Z"
                              id="Path"
                            />
                            <path
                              d="M41.7384375,29.3248125 C41.4638437,29.0502188 41.018625,29.0502188 40.744125,29.3248125 L36.39675,33.6721875 L33.8110312,31.0864687 C33.5364375,30.811875 33.0912187,30.811875 32.8167187,31.0864687 C32.542125,31.3610625 32.542125,31.8062812 32.8167187,32.0807813 L35.8995937,35.1636563 C36.0368437,35.301 36.2168437,35.369625 36.39675,35.369625 C36.5766562,35.369625 36.7566562,35.301 36.8939062,35.1636563 L41.7384375,30.319125 C42.0130312,30.0445313 42.0130312,29.5993125 41.7384375,29.3248125 Z"
                              id="Path"
                            />
                            <path
                              d="M35.9685937,12.04275 L23.96325,12.04275 C23.1617813,12.04275 22.5098438,12.6947812 22.5098438,13.4961562 L22.5098438,15.7471875 C22.5098438,16.5486563 23.161875,17.2005937 23.96325,17.2005937 L35.9685,17.2005937 C36.7699687,17.2006875 37.422,16.5486563 37.422,15.7471875 L37.422,13.4961563 C37.422,12.6946875 36.7699687,12.04275 35.9685937,12.04275 Z M36.0156562,15.7471875 C36.0156562,15.77325 35.9944687,15.7943441 35.9685,15.7943441 L23.96325,15.7943441 C23.9372813,15.7944375 23.9160938,15.77325 23.9160938,15.7471875 L23.9160938,13.4961562 C23.9160938,13.4700937 23.9372813,13.449 23.96325,13.449 L35.9685,13.449 C35.9945625,13.449 36.0156562,13.4701875 36.0156562,13.4961562 L36.0156562,15.7471875 Z"
                              id="Shape"
                            />
                            <path
                              d="M32.9671875,18.7956563 L23.96325,18.7956563 C23.5749375,18.7956563 23.260125,19.1104688 23.260125,19.4987813 C23.260125,19.8870937 23.5749375,20.2019063 23.96325,20.2019063 L32.9671875,20.2019063 C33.3555,20.2019063 33.6703125,19.8870937 33.6703125,19.4987813 C33.6703125,19.1104688 33.3555,18.7956563 32.9671875,18.7956563 Z"
                              id="Path"
                            />
                            <path
                              d="M29.2155938,21.7970625 L23.96325,21.7970625 C23.5749375,21.7970625 23.260125,22.111875 23.260125,22.5001875 C23.260125,22.8885 23.5749375,23.2033125 23.96325,23.2033125 L29.2155938,23.2033125 C29.6039063,23.2033125 29.9187188,22.8885 29.9187188,22.5001875 C29.9187188,22.111875 29.6039063,21.7970625 29.2155938,21.7970625 Z"
                              id="Path"
                            />
                          </g>
                        </g>
                      </svg>
                    </Link>
                  </div>
                  <div className={styles.content}>
                    <MDBCardTitle tag="h2" className={styles.card_title}>
                      <Link to="/thac-mac/chi-tiet">Thông tin tài khoản</Link>
                    </MDBCardTitle>
                    <p>
                      Các câu hỏi/câu trả lời liên quan đến tài khoản của bệnh
                      nhân
                    </p>
                    <div className={styles.author}>
                      <div className={styles.avata}>
                        <img src={support} alt="" />
                      </div>
                      <div className={styles.author_item}>
                        <div className={styles.name}>
                          Đóng góp bởi <strong>Công Võ</strong>
                        </div>
                        <div className={styles.count_question}>
                          Thư mục có <strong>8</strong> câu hỏi
                        </div>
                      </div>
                    </div>
                  </div>
                </MDBCard>
                <MDBCard className={cx(styles.card, styles.card_item)}>
                  <div className={styles.icons}>
                    <Link to="/">
                      <svg width={48} height={46}>
                        <g
                          id="Page-1"
                          stroke="none"
                          strokeWidth="1"
                          fill="none"
                          fillRule="evenodd"
                        >
                          <g id="stethoscope" fill="#818A97" fillRule="nonzero">
                            <path
                              d="M47.8429272,25.6047568 C47.4889647,23.1524657 45.5624782,21.2259792 43.1101871,20.8720166 L43.1101871,19.4206403 C43.1101871,15.416183 40.0661289,12.1557755 36.3243243,12.1557755 C32.6902952,12.1557755 29.6478337,15.3171892 29.5384615,19.2034927 L29.5847651,19.2034927 L29.5384615,19.2114761 L29.5384615,34.9442994 C29.5384615,39.9738212 25.7782952,44.0581123 21.1559252,44.0581123 C16.5335551,44.0581123 12.7733888,39.9682328 12.7733888,34.9442994 L12.7733888,33.5815385 C13.9309771,33.5815385 15.1683992,32.7424865 15.1683992,30.3881913 L15.1683992,28.3803742 C20.2921247,27.0703035 23.950104,22.6706694 23.950104,17.6826611 L23.950104,16.0181289 L22.3534304,16.0181289 L22.3199002,5.62777547 C22.3083243,3.70448233 20.9281996,2.06230353 19.0355426,1.71991684 C18.6272931,0.469222453 17.282395,-0.213754678 16.0316008,0.194594595 C14.7809064,0.602844075 14.0979293,1.9477422 14.5062786,3.19853638 C14.9145281,4.44923077 16.2594262,5.1322079 17.5102204,4.72385863 C18.1799252,4.50521414 18.7185031,4.00136383 18.9812557,3.34772557 C20.0040249,3.63871933 20.7125489,4.56918087 20.7208316,5.63256549 L20.7567568,16.0181289 L19.1600832,16.0181289 L19.1600832,17.7058129 C19.0964158,20.4827277 17.2346944,22.8959002 14.5648565,23.6622037 C13.1363326,24.1154595 11.6025281,24.1154595 10.1740042,23.6622037 C7.50725988,22.8933056 5.64972973,20.4805322 5.58835759,17.7058129 L5.58835759,16.0181289 L3.19334719,16.0181289 L3.19334719,5.63975052 C3.19793763,4.56069854 3.92362578,3.61786279 4.96565489,3.33734719 C5.45154262,4.57427027 6.84813306,5.18300208 8.08505613,4.69711435 C9.32197921,4.21122661 9.93071102,2.81453638 9.44482328,1.5777131 C8.95893555,0.340889813 7.56224532,-0.267941788 6.32542204,0.217945946 C5.6552183,0.481297297 5.13809563,1.03085239 4.916158,1.71592516 C3.00284407,2.04284407 1.60196258,3.69869439 1.5966736,5.63975052 L1.5966736,16.0181289 L0,16.0181289 L0,17.6786694 C0,22.6722661 3.66037422,27.0734969 8.78170478,28.3803742 L8.78170478,30.3881913 C8.78170478,32.7424865 10.0191268,33.5815385 11.1767152,33.5815385 L11.1767152,34.9442994 C11.1767152,40.8519917 15.6529896,45.6547859 21.1559252,45.6547859 C26.6588607,45.6547859 31.1351351,40.8464033 31.1351351,34.9442994 L31.1351351,19.2497963 C31.2149688,16.2201081 33.5445156,13.7548441 36.3243243,13.7548441 C39.1855634,13.7548441 41.5135135,16.2967484 41.5135135,19.4230353 L41.5135135,20.8720166 C38.4587775,21.3128981 36.3398919,24.1466944 36.7807734,27.2014304 C37.2216549,30.2561663 40.0554511,32.375052 43.1101871,31.9341705 C46.1649231,31.493289 48.2838087,28.6593929 47.8429272,25.6047568 Z M16.7650728,3.24474012 C16.3241913,3.24474012 15.966736,2.88728482 15.966736,2.44640333 C15.966736,2.00552183 16.3241913,1.64806653 16.7650728,1.64806653 C17.2059543,1.64806653 17.5634096,2.00552183 17.5634096,2.44640333 C17.5634096,2.88728482 17.2059543,3.24474012 16.7650728,3.24474012 Z M7.18503119,1.64806653 C7.62591268,1.64806653 7.98336798,2.00552183 7.98336798,2.44640333 C7.98336798,2.88728482 7.62591268,3.24474012 7.18503119,3.24474012 C6.74414969,3.24474012 6.38669439,2.88728482 6.38669439,2.44640333 C6.38669439,2.00552183 6.74414969,1.64806653 7.18503119,1.64806653 Z M1.5966736,17.6786694 L1.5966736,17.6148025 L3.99168399,17.6148025 L3.99168399,17.7058129 C4.05155925,21.175684 6.3600499,24.2038753 9.69021206,25.1806403 C11.4352765,25.7331892 13.3083742,25.7331892 15.0534387,25.1806403 C18.3851975,24.2051726 20.6957838,21.1768815 20.7567568,17.7058129 L20.7567568,17.6148025 L22.3534304,17.6148025 L22.3534304,17.6786694 C22.3534304,22.119817 18.9301622,26.0276757 14.210395,26.9729064 C13.102104,27.1948441 11.966869,27.2505281 10.8422121,27.1381622 C10.4719834,27.102237 10.1039501,27.046553 9.73970894,26.9713098 C5.01994179,26.0276757 1.5966736,22.119817 1.5966736,17.6786694 Z M11.1767152,31.9848649 C10.4582121,31.9848649 10.3783784,30.8671933 10.3783784,30.3881913 L10.3783784,28.6941206 C10.4422453,28.702104 10.5077089,28.7052973 10.5723742,28.711684 C10.6522079,28.7204657 10.7320416,28.7268524 10.8118753,28.7340374 C11.1894886,28.7675676 11.5702952,28.789921 11.9542952,28.7907193 L11.9942121,28.7907193 C12.3774137,28.7907193 12.757422,28.7675676 13.1326403,28.7340374 C13.212474,28.7268524 13.2970977,28.7204657 13.3793264,28.711684 C13.442395,28.704499 13.5078586,28.702104 13.5709272,28.6941206 L13.5709272,30.3881913 C13.5709272,30.8671933 13.4910936,31.9848649 12.7725904,31.9848649 L11.1767152,31.9848649 Z M42.3118503,30.3881913 C40.107343,30.3881913 38.3201663,28.6010146 38.3201663,26.3965073 C38.3201663,24.192 40.107343,22.4048233 42.3118503,22.4048233 C44.5163576,22.4048233 46.3035343,24.192 46.3035343,26.3965073 C46.3009397,28.6000166 44.5152599,30.3855967 42.3118503,30.3881913 Z"
                              id="Shape"
                            />
                            <path
                              d="M42.3118503,24.0014969 C40.989106,24.0014969 39.9168399,25.073763 39.9168399,26.3965073 C39.9168399,27.7192516 40.989106,28.7915177 42.3118503,28.7915177 C43.6345946,28.7915177 44.7068607,27.7192516 44.7068607,26.3965073 C44.7068607,25.073763 43.6345946,24.0014969 42.3118503,24.0014969 Z M42.3118503,27.1948441 C41.8709688,27.1948441 41.5135135,26.8373888 41.5135135,26.3965073 C41.5135135,25.9556258 41.8709688,25.5981705 42.3118503,25.5981705 C42.7527318,25.5981705 43.1101871,25.9556258 43.1101871,26.3965073 C43.1101871,26.8373888 42.7527318,27.1948441 42.3118503,27.1948441 Z"
                              id="Shape"
                            />
                          </g>
                        </g>
                      </svg>
                    </Link>
                  </div>
                  <div className={styles.content}>
                    <MDBCardTitle tag="h2" className={styles.card_title}>
                      <Link to="/thac-mac/chi-tiet"> Quy trình đặt khám </Link>
                    </MDBCardTitle>
                    <p>
                      Các vấn đề liên quan đến quy trình đặt khám tại các bệnh
                      viện
                    </p>
                    <div className={styles.author}>
                      <div className={styles.avata}>
                        <img src={support} alt="" />
                      </div>
                      <div className={styles.author_item}>
                        <div className={styles.name}>
                          Đóng góp bởi <strong>Công Võ</strong>
                        </div>
                        <div className={styles.count_question}>
                          Thư mục có <strong>8</strong> câu hỏi
                        </div>
                      </div>
                    </div>
                  </div>
                </MDBCard>
                <MDBCard className={cx(styles.card, styles.card_item)}>
                  <div className={styles.icons}>
                    <Link to="/thac-mac/chi-tiet">
                      <svg width={48} height={48}>
                        <g
                          id="Page-1"
                          stroke="none"
                          strokeWidth="1"
                          fill="none"
                          fillRule="evenodd"
                        >
                          <g id="debit-card" fill="#818A97" fillRule="nonzero">
                            <path
                              d="M39.978678,8.4706823 L39.978678,3.9978678 C39.9758795,1.79104478 38.1876333,0.00279850746 35.9808102,0 L3.9978678,0 C1.79104478,0.00279850746 0.00279850746,1.79104478 0,3.9978678 L0,21.5884861 C0.00619670789,23.6303971 1.55397124,25.3380864 3.58528785,25.5451759 L6.59088486,33.3472148 C7.38486141,35.4073161 9.69842753,36.4335688 11.7587287,35.6405917 L30.5277186,28.4104478 C29.2308102,31.7980411 29.5998135,35.5982143 31.5235874,38.6731743 L31.9829424,39.4077825 L31.9829424,47.1748401 C31.9829424,47.6166045 32.3407516,47.9744136 32.782516,47.9744136 L47.1748401,47.9744136 C47.6166045,47.9744136 47.9744136,47.6166045 47.9744136,47.1748401 L47.9744136,20.7819163 C47.9668177,18.016791 46.8660048,15.3674041 44.9110475,13.411847 L39.978678,8.4706823 Z M39.978678,13.252532 L42.4053838,19.5519723 C42.8809302,20.7885128 42.2646588,22.1759728 41.0283182,22.651919 L39.641258,23.1876333 C39.8627399,22.6835022 39.9774787,22.1385928 39.978678,21.5884861 L39.978678,13.252532 Z M1.59914712,21.5884861 L1.59914712,3.9978678 C1.59914712,2.67337423 2.67337423,1.59914712 3.9978678,1.59914712 L35.9808102,1.59914712 C37.3053038,1.59914712 38.3795309,2.67337423 38.3795309,3.9978678 L38.3795309,9.07995736 L38.3725346,9.07995736 L38.3795309,9.09814767 L38.3795309,21.5884861 C38.3733342,21.7280117 38.3551439,21.866338 38.3251599,22.0026652 L31.0010661,14.6777719 C29.3547441,13.0694297 26.7301439,13.0570362 25.0690299,14.6485874 C23.407516,16.241138 23.3077692,18.8637393 24.8443497,20.5768257 L27.9666844,23.9872068 L3.9978678,23.9872068 C2.67337423,23.9872068 1.59914712,22.9129797 1.59914712,21.5884861 Z M11.842484,25.5863539 L6.15671642,27.7753865 L5.31556503,25.5863539 L11.842484,25.5863539 Z M11.1836354,34.1479878 C9.94749467,34.6245336 8.55923509,34.0082623 8.08288913,32.7721215 L6.73400853,29.2643923 L16.2785181,25.5863539 L16.2785181,25.5815565 L29.4323028,25.5815565 L30.4715485,26.7145522 L11.1836354,34.1479878 Z M46.3752665,46.3752665 L33.5820896,46.3752665 L33.5820896,39.978678 L46.3752665,39.978678 L46.3752665,46.3752665 Z M46.3752665,38.3795309 L33.2262793,38.3795309 L32.8794643,37.8254264 C30.9299041,34.709888 30.8523454,30.7747868 32.6769723,27.5852878 C32.8496802,27.2834488 32.8073028,26.9044509 32.5722281,26.6481876 L26.0199227,19.5 C25.0428438,18.4305703 25.0978145,16.7772521 26.1438566,15.7755864 C27.1902985,14.7737207 28.8444163,14.790112 29.8702692,15.811967 L42.6116738,28.5503731 L43.7422708,27.4197761 L40.7838486,24.4613539 L41.6042111,24.1415245 C43.6627132,23.3473481 44.6885661,21.0357809 43.8965885,18.9760794 L41.1902319,11.9486274 L43.7804504,14.5442431 C45.4355677,16.1989606 46.3688699,18.4413646 46.3752665,20.7819163 L46.3752665,38.3795309 Z"
                              id="Shape"
                            />
                            <path
                              d="M9.11513859,11.1940299 C10.2633262,11.1940299 11.1940299,10.2633262 11.1940299,9.11513859 L11.1940299,5.2771855 C11.1940299,4.12899787 10.2633262,3.19829424 9.11513859,3.19829424 L5.2771855,3.19829424 C4.12899787,3.19829424 3.19829424,4.12899787 3.19829424,5.2771855 L3.19829424,9.11513859 C3.19829424,10.2633262 4.12899787,11.1940299 5.2771855,11.1940299 L9.11513859,11.1940299 Z M4.79744136,9.11513859 L4.79744136,7.99573561 L6.39658849,7.99573561 L6.39658849,6.39658849 L4.79744136,6.39658849 L4.79744136,5.2771855 C4.79744136,5.01232677 5.01232677,4.79744136 5.2771855,4.79744136 L9.11513859,4.79744136 C9.37999732,4.79744136 9.59488273,5.01232677 9.59488273,5.2771855 L9.59488273,6.39658849 L7.99573561,6.39658849 L7.99573561,7.99573561 L9.59488273,7.99573561 L9.59488273,9.11513859 C9.59488273,9.37999732 9.37999732,9.59488273 9.11513859,9.59488273 L5.2771855,9.59488273 C5.01232677,9.59488273 4.79744136,9.37999732 4.79744136,9.11513859 Z"
                              id="Shape"
                            />
                            <polygon
                              id="Path"
                              points="3.9978678 13.5927505 7.19616205 13.5927505 7.19616205 15.1918977 3.9978678 15.1918977"
                            />
                            <polygon
                              id="Path"
                              points="3.9978678 18.3901919 7.19616205 18.3901919 7.19616205 19.989339 3.9978678 19.989339"
                            />
                            <polygon
                              id="Path"
                              points="18.3901919 18.3901919 21.5884861 18.3901919 21.5884861 19.989339 18.3901919 19.989339"
                            />
                            <polygon
                              id="Path"
                              points="8.79530917 13.5927505 11.9936034 13.5927505 11.9936034 15.1918977 8.79530917 15.1918977"
                            />
                            <polygon
                              id="Path"
                              points="13.5927505 13.5927505 16.7910448 13.5927505 16.7910448 15.1918977 13.5927505 15.1918977"
                            />
                            <polygon
                              id="Path"
                              points="18.3901919 13.5927505 21.5884861 13.5927505 21.5884861 15.1918977 18.3901919 15.1918977"
                            />
                            <polygon
                              id="Path"
                              points="33.5820896 3.9978678 35.1812367 3.9978678 35.1812367 6.39658849 33.5820896 6.39658849"
                            />
                            <polygon
                              id="Path"
                              points="30.3837953 3.9978678 31.9829424 3.9978678 31.9829424 6.39658849 30.3837953 6.39658849"
                            />
                            <polygon
                              id="Path"
                              points="27.1855011 3.9978678 28.7846482 3.9978678 28.7846482 6.39658849 27.1855011 6.39658849"
                            />
                            <polygon
                              id="Path"
                              points="23.9872068 3.9978678 25.5863539 3.9978678 25.5863539 6.39658849 23.9872068 6.39658849"
                            />
                            <polygon
                              id="Path"
                              points="35.1812367 41.5778252 36.7803838 41.5778252 36.7803838 43.1769723 35.1812367 43.1769723"
                            />
                          </g>
                        </g>
                      </svg>
                    </Link>
                  </div>
                  <div className={styles.content}>
                    <MDBCardTitle tag="h2" className={styles.card_title}>
                      <Link to="/thac-mac/chi-tiet">Thanh toán </Link>
                    </MDBCardTitle>
                    <p>
                      Các vấn đề liên quan đến thanh toán khi sử dụng dịch vụ
                    </p>
                    <div className={styles.author}>
                      <div className={styles.avata}>
                        <img src={support} alt="" />
                      </div>
                      <div className={styles.author_item}>
                        <div className={styles.name}>
                          Đóng góp bởi <strong>Công Võ</strong>
                        </div>
                        <div className={styles.count_question}>
                          Thư mục có <strong>8</strong> câu hỏi
                        </div>
                      </div>
                    </div>
                  </div>
                </MDBCard>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default Faq;
