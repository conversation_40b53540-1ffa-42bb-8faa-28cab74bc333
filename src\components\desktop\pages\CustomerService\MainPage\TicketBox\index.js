import React, { Component } from "react";
// components
import <PERSON>Header from "./BoxHeader";
import styles from "./style.module.scss";
import BoxBody from "./TicketBoxBody";
import cx from "classnames";

class TicketBox extends Component {
  state = {
    showSearchBox: false
  };

  toggleSearchBox = () => {
    this.setState({ showSearchBox: !this.state.showSearchBox });
  };

  render() {
    return (
      <div className={cx(styles.ticket_box, "d-none")}>
        <BoxHeader
          title="<PERSON>ác yêu cầu hỗ trợ"
          toggleSearchBox={this.toggleSearchBox}
        />
        <BoxBody
          ticketList={this.props.ticketList}
          loading={this.props.loading}
        />
      </div>
    );
  }
}

export default TicketBox;
