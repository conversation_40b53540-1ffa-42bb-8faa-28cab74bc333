import * as types from "~/store/patientForm/patientFormType";

export const saveField = fields => {
  return {
    type: types.PATIENT_FORM_SAVE_FIELD,
    payload: fields
  };
};

// export const saveBirthDateField = fields => {
//   return {
//     type: PATIENT_FORM_SAVE_BIRTH_DATE_FIELD,
//     payload: fields,
//   }
// }

export const saveBirthdayField = fields => ({
  type: types.PATIENT_FORM_SAVE_BIRTH_DAY_FIELD,
  payload: fields
});

export const saveBirthmonthField = fields => ({
  type: types.PATIENT_FORM_SAVE_BIRTH_MONTH_FIELD,
  payload: fields
});

export const saveBirthyearField = fields => ({
  type: types.PATIENT_FORM_SAVE_BIRTH_YEAR_FIELD,
  payload: fields
});

export const saveCountryField = fields => {
  return {
    type: types.PATIENT_FORM_SAVE_COUNTRY_FIELD,
    payload: fields
  };
};

export const saveCityField = fields => {
  return {
    type: types.PATIENT_FORM_SAVE_CITY_FIELD,
    payload: fields
  };
};

export const saveDistrictField = fields => {
  return {
    type: types.PATIENT_FORM_SAVE_DISTRICT_FIELD,
    payload: fields
  };
};

export const onCreatePatientInfo = (force = false) => {
  return {
    type: types.CREATE_PATIENT_FORM_REQUEST,
    force
  };
};

export const onUpdatePatientInfo = () => {
  return {
    type: types.UPDATE_PATIENT_FORM_REQUEST
  };
};

export const getPatientDetail = (id, patientToken, callbackFn = "") => ({
  type: types.DETAIL_PATIENT_FORM_REQUEST,
  id,
  patientToken,
  callbackFn
});

export const setPatientInfo = info => {
  // console.log(info);
  return {
    type: types.SET_PATIENT_INFO,
    info
  };
};

export const resetRedirectPatientForm = () => {
  return {
    type: types.RESET_REDIRECT_PATIENT_FORM
  };
};

export const resetPatientForm = () => {
  return {
    type: types.RESET_PATIENT_FORM
  };
};

export const createPatientOverMaxCloseModal = () => {
  return { type: types.CREATE_PATIENT_OVER_MAX_CLOSE_MODAL };
};

export const resetDefaultCountry = id => {
  return {
    type: types.RESET_DEFAULT_COUNTRY,
    id
  };
};

export const resetDefaultNation = id => {
  return {
    type: types.RESET_DEFAULT_NATION,
    id
  };
};

//
export const changeUrlRedirectAfterCreatePatient = url => {
  return {
    type: types.URL_REDIRECT_AFTER_CREATE_PATIENT,
    payload: url
  };
};

export const getInfoPatientCMND = data => {
  return { type: types.GET_INFO_PATIENT_CMND, payload: data };
};

export const getInfoPatientCMNDConfirmSuccess = data => {
  return { type: types.GET_INFO_PATIENT_CMND_CONFIRM_SUCCESS, payload: data };
};

export const getInfoPatientCMNDConfirmCancel = () => {
  return { type: types.GET_INFO_PATIENT_CMND_CONFIRM_CANCEL };
};

export const getInfoPatientCMNDSuccess = data => {
  return { type: types.GET_INFO_PATIENT_CMND_SUCCESS, payload: data };
};

export const getInfoPatientCMNDFailure = error => {
  return { type: types.GET_INFO_PATIENT_CMND_FAILURE, payload: error };
};
