import cx from "classnames";
import { get, size } from "lodash";
import moment from "moment";
import React from "react";
import styles from "./styles.module.scss";
import Line from "../Line";
import { getFormatMoney } from "~/utils/getFormatMoney";

const BookingInfo = ({ bookingInfo }) => {
  const partnerId = get(bookingInfo, "partnerId");
  const bookingCode = get(bookingInfo, "bookingCode", "");
  const service = get(bookingInfo, "service");
  const serviceInfo = get(bookingInfo, "serviceInfo");
  const doctor = get(bookingInfo, "doctor");
  const subject = get(bookingInfo, "subject");
  const insuranceCode = get(bookingInfo, "insuranceCode", "");
  const room = get(bookingInfo, "room");
  const section = get(bookingInfo, "section");
  const awaitMessage = get(bookingInfo, "awaitMessage", "");
  const addonServices = get(bookingInfo, "addonServices", []);
  const date = get(bookingInfo, "date");
  const waitingConfirmDate = get(bookingInfo, "waitingConfirmDate", "");
  const status = get(bookingInfo, "status", 1);
  const isRenderPhiTamUng =
    Number(service?.advanced) > 0 ? "Phí tạm ứng:" : "Phí khám:";
  // const titleBookingTime =
  //   partnerId === "trungvuong" ? "Giờ tiếp nhận dự kiến" : "Giờ khám dự kiến";

  const dates = date ? moment(date).format("DD/MM/YYYY") : waitingConfirmDate;
  const time = date ? moment(date).format("HH:mm") : waitingConfirmDate;

  const bookingDate = bookingInfo?.dateStr || dates;
  const bookingTime = bookingInfo?.timeStr || time;

  return (
    <div className={styles.BookingInfo}>
      <ul className={styles.listInfo}>
        {bookingCode && (
          <li>
            <span className={styles.column_left}>Mã phiếu: </span>
            {bookingCode}
          </li>
        )}

        {service?.name && !["bvmathcm", "dalieuhcm"].includes(partnerId) && (
          <li>
            <span className={styles.column_left}>Dịch vụ:</span>
            <b>{service?.name}</b>
          </li>
        )}

        <li>
          <span className={styles.column_left}>Hình thức khám:</span>
          {["bvmathcm"].includes(partnerId) ? (
            <b>{service?.name || ""}</b>
          ) : (
            <b>{insuranceCode ? " Có BHYT" : " Không có BHYT"}</b>
          )}
        </li>

        {(section?.name || room?.sectionName) && (
          <li>
            <span className={styles.column_left}>Khu vực:</span>
            <b className={styles.value}>{section?.name || room?.sectionName}</b>
          </li>
        )}

        {room?.name && (
          <li>
            <span className={styles.column_left}>Phòng khám:</span>
            <b className={styles.value}> {room?.name}</b>
          </li>
        )}

        {subject?.name && (
          <li>
            <span className={styles.column_left}>Chuyên khoa:</span>
            <b className={styles.value}> {subject?.name}</b>
          </li>
        )}

        {doctor?.name && (
          <li>
            <span className={styles.column_left}>Bác sĩ:</span>
            <b>{doctor?.name}</b>
          </li>
        )}

        {[-2, 6].includes(status) ? null : (
          <>
            {/* <li>
              <span className={cx(styles.title)}>Ngày khám:</span>
              <b className={cx(styles.value, status === 1 && styles.green)}>
                {bookingDate}
              </b>
            </li> */}

            {awaitMessage === "" && (
              <li>
                <span className={cx(styles.column_left)}>Thời gian khám:</span>
                <b
                  className={cx(styles.value, status === 1 ? styles.green : "")}
                >
                  {bookingTime} - {bookingDate}
                </b>
              </li>
            )}
          </>
        )}

        <li>
          <span className={styles.column_left}>{isRenderPhiTamUng}</span>
          <b>{service?.priceText || serviceInfo?.priceText}</b>
        </li>

        {partnerId === "bvsingapore" && (
          <>
            {service?.advanced && (
              <li>
                <span className={styles.column_left}>
                  Ghi chú giá dịch vụ:{" "}
                </span>
                <b>{service?.priceDescription}</b>
              </li>
            )}
            {size(addonServices) > 0 && (
              <>
                <Line />
                <div style={{ marginTop: 20 }}>
                  <p style={{ fontWeight: 700, marginBottom: 10 }}>
                    Các dịch vụ khác:{" "}
                  </p>
                  {addonServices.map((v, i) => {
                    return (
                      <li key={i}>
                        <span className={styles.column_left}>{v?.name}:</span>
                        <span className={styles.column_right}>
                          {v.price === 0
                            ? v?.priceText
                            : getFormatMoney(v.price) + " VNĐ"}
                        </span>
                      </li>
                    );
                  })}
                </div>
              </>
            )}
          </>
        )}
      </ul>
    </div>
  );
};

export default BookingInfo;
