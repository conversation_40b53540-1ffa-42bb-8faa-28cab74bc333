import { get } from "lodash";
import React from "react";
import { getInfoPatient } from "~/utils/flowRouting";
import styles from "./styles.module.scss";

const PatientInfo = ({ bookingInfo }) => {
  const patient = get(bookingInfo, "patient");
  const { fullName, birthdate } = getInfoPatient(patient);
  return (
    <div className={styles.patientInfo}>
      <ul className={styles.listInfo}>
        {fullName && (
          <li>
            <span className={styles.column_left}>Bệnh nhân:</span>
            <b>{fullName}</b>
          </li>
        )}
        {birthdate && (
          <li>
            <span className={styles.column_left}>Ngày sinh:</span>
            <b>{birthdate}</b>
          </li>
        )}

        {patient?.patientCode && (
          <li>
            <span className={styles.column_left}>Mã bệnh nhân:</span>
            <b>{patient?.patientCode}</b>
          </li>
        )}
      </ul>
    </div>
  );
};

export default PatientInfo;
