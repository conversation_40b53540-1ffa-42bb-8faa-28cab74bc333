import { get } from "lodash";
import moment from "moment";
import {
  all,
  call,
  put,
  select,
  takeEvery,
  takeLatest
} from "redux-saga/effects";
import { openToast } from "~/components/common/molecules/ToastNotification";
import history from "~/history";
import * as patientActions from "~/store/patient/patientAction";
import {
  apiRequestMedproPatientList,
  apiRequestMedproSuggestionPatientList,
  getByUserId,
  insertPatient
} from "~/store/patient/patientApi";
import * as TYPES from "~/store/patient/patientType";
import * as patientFormTypes from "~/store/patientForm/patientFormType";
import * as typesCustomerService from "~/store/customerService/customerServiceTypes";
import * as totalDataActions from "~/store/totalData/actions";
import { USER_SESSSION_EXPIRED } from "~/store/user/userType";
import { huyi } from "~/utils/clog";
import { CHECK_WITH_INSURANCE_CODE_EXIST } from "~/utils/constants";
import { routerByPartnerId } from "~/utils/func";
import { getBaseUrl } from "~/utils/getBaseUrl";
import { client } from "~/utils/medproSDK";
import AppId from "~/utils/partner";
import { handleUnAuthorize } from "~/utils/unAuthorize";
import { selectPatientInfoCSKH } from "../customerService/customerServiceActions";
import { offLoading, onLoading } from "../totalData/actions";
import { logOutWithoutNoti } from "../user/userAction";

// chọn bệnh nhân tái khám để auto fill thông tin vào form bhyt
function* requestSelectPatientReExam() {
  try {
    const { patientExam } = yield select(state => state.followUpExam);
    // console.log(patientExam);
    yield put({
      type: TYPES.SELECT_PATIENT_REEXAM_SUCCESS,
      payload: patientExam
    });
  } catch (error) {
    console.log(error);
  }
}

function* getPatientByMSBNWorker({ data }) {
  try {
    const { cskhToken } = yield select(state => state.customerService);
    const user = yield select(state => state.user);

    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";

    yield put({ type: TYPES.SHOW_LOADING });
    const partnerid = yield select(state => state.totalData.partnerId);
    const patientNumber = yield select(state => state.patient.patientNumber);
    const postData = data !== null ? { ...data } : { msbn: patientNumber };
    delete postData.callback;

    const response = yield client.findByMsbn(
      { msbn: patientNumber },
      {
        partnerid: partnerid === "" ? AppId : partnerid,
        appid: AppId,
        platform,
        cskhtoken: cskhToken,
        token: user?.info?.token || ""
      }
    );
    yield put({
      type: TYPES.GET_PATIENT_BY_MSBN_SUCCESS,
      patient: response.data
    });
    yield put({ type: TYPES.HIDE_LOADING });
    history.push("/xac-thuc-dien-thoai-benh-nhan");
  } catch (error) {
    const message = get(error, "response.data.message");
    openToast(message, "error");
    yield put({ type: TYPES.HIDE_LOADING });
  }
}

function* getPatientByUserIdWorker({ data }) {
  try {
    let baseUrl;
    if (!data.base_url) baseUrl = yield select(state => getBaseUrl());
    else baseUrl = data.base_url;
    console.log(data);
    const response = yield call(getByUserId, baseUrl, data);
    // const response = yield client.getPatientsByUserId({})
    if (typeof response.data.error_code === typeof undefined) {
      yield put({
        type: TYPES.GET_PATIENT_BY_USERID_SUCCESS,
        patientList: response.data
      });
    } else {
      if (response.data.error_code === -2) {
        yield put({
          type: TYPES.SHOW_ALERT_INFO_CHOOSE_PATIENT,
          errorMessage: response.data.error_message
        });
        yield put({
          type: TYPES.CLEAR_STATE
        });
      }
    }
  } catch (error) {
    yield put({ type: TYPES.GET_PATIENT_BY_USERID_FAILURE, error });
  }
}

function* insertPatientWorker({ data, callback }) {
  try {
    const baseUrl = yield select(state => getBaseUrl());
    const response = yield call(insertPatient, baseUrl, data);
    if (response.status !== 200) throw new Error("Server error");
    yield put({
      type: TYPES.INSERT_PATIENT_SUCCESS,
      supporterList: response.data
    });
    callback();
  } catch (error) {
    yield put({
      type: TYPES.INSERT_PATIENT_FAILURE,
      errorMessage: error.errorMessage
    });
  }
}

function* deletePatientWorker() {
  try {
    const { cskhToken } = yield select(state => state.customerService);

    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";
    const user = yield select(state => state.user);
    const { selectedPatient } = yield select(state => state.patient);
    yield client.unlinkPatient(
      { id: selectedPatient.id },
      { platform, token: user.info.token, cskhtoken: cskhToken }
    );

    openToast("Bạn đã xóa hồ sơ bệnh nhân thành công!");
  } catch (error) {
    const message = get(error, "response.data.message", "");
    openToast(message, "error");
    yield put({
      type: TYPES.DELETE_PATIENT_FAILURE,
      errorMessage: error.errorMessage
    });
  } finally {
    const customerService = yield select(state => state.customerService);
    yield put({
      type: typesCustomerService.GET_PATIENTS_BY_PHONE_CSKH,
      phone: customerService.searchedPhone
    });
    yield all([
      put({ type: TYPES.TOGGLE_MODAL_CONFIRM }),
      put({ type: TYPES.UMC_CHOOSE_PATIENT_LIST_REQUEST }),
      put({ type: TYPES.TOGGLE_MODAL_CHOOSE_PATIENT })
    ]);
    window.scrollTo(0, 0);
  }
}

function* searchPatientByInfoWorker() {
  try {
    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";
    const searchPatientInfo = yield select(
      state => state.patient.formSearchPatientByInfo
    );

    const keys = Object.keys(searchPatientInfo);
    const postData = {};
    keys.map(key => {
      postData[key] = searchPatientInfo[key].value;
      return "";
    });
    const partnerid = yield select(state => state.totalData.partnerId);
    yield put({ type: TYPES.SHOW_LOADING });
    const response = yield client.findByExtraInfo(postData, {
      partnerid: partnerid === "" ? AppId : partnerid,
      platform
    });
    const responseData = response.data;
    const isValidData = Array.isArray(responseData);
    yield put({
      type: TYPES.SEARCH_PATIENT_BY_INFO_REQUEST_SUCCESS,
      searchPatientByInfoList: isValidData ? responseData : []
    });
    yield put({ type: TYPES.HIDE_LOADING });
    history.push("/ket-qua-tim-benh-nhan");
  } catch (error) {
    const message = get(error, "response.data.message");
    openToast(message, "error");
    yield put({
      type: TYPES.SEARCH_PATIENT_BY_INFO_REQUEST_FAILURE,
      errorMessage: error.errorMessage
    });
    yield put({ type: TYPES.HIDE_LOADING });
  }
}

function* requestChoosePatientList() {
  try {
    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";
    const token = yield select(state => state.user.info.token);
    const CS = yield select(state => state.customerService);
    const t = yield select(state => state.totalData);

    const obj = {
      serviceId: t.service?.id,
      roomId: t.room.id,
      treeId: t.treeId,
      subjectId: t.subject?.id,
      doctorId: t.doctor?.id,
      bookingDate: moment(t.days.date).toISOString()
    };

    const jwtCS = window.localStorage.getItem("cskhToken");

    const response = yield client.getValidPatientsByUserIdV2(obj, {
      token,
      partnerid: t?.partnerId,
      platform,
      cskhtoken: CS?.selectedPatient?.secretKey || jwtCS
    });
    const { data } = response;

    yield put({
      type: TYPES.UMC_CHOOSE_PATIENT_LIST_REQUEST_SUCCESS,
      data
    });
  } catch (error) {
    console.log("error requestChoosePatientList :>> ", error);
    if (error.response.data.statusCode === 401) {
      yield put(logOutWithoutNoti());
      handleUnAuthorize();
    }
    yield put({
      type: TYPES.UMC_CHOOSE_PATIENT_LIST_REQUEST_FAIL,
      error
    });
  }
}

function* requestMedproPatientList() {
  try {
    const { info } = yield select(state => state.user);
    const response = yield call(apiRequestMedproPatientList, info);
    const { data } = response;
    if (typeof data.error_code === typeof undefined) {
      yield put({
        type: TYPES.GET_MEDPRO_PATIENT_LIST_SUCCESS,
        data
      });
    } else {
      if (data.error_code === -2) {
        yield put({
          type: TYPES.SHOW_ALERT_INFO_CHOOSE_PATIENT,
          alertMsg: data.error_message
        });
        yield put({
          type: USER_SESSSION_EXPIRED
        });
      }
    }
  } catch (error) {
    yield put({
      type: TYPES.GET_MEDPRO_PATIENT_LIST_FAIL,
      error
    });
  }
}

function* requestMedproPatientSuggestionList() {
  try {
    const { info } = yield select(state => state.user);
    const hospitalID = yield select(
      state => state.hospital.selectedHospital.id
    );
    const response = yield call(apiRequestMedproSuggestionPatientList, {
      ...info,
      hospital_id: hospitalID
    });
    const { data } = response;
    if (typeof data.error_code === typeof undefined) {
      yield put({
        type: TYPES.GET_MEDPRO_PATIENT_SUGGESTION_LIST_SUCCESS,
        data
      });
    } else {
      if (data.error_code === -2) {
        yield put({
          type: TYPES.SHOW_ALERT_INFO_CHOOSE_PATIENT,
          alertMsg: data.error_message
        });
        yield put({
          type: USER_SESSSION_EXPIRED
        });
      }
    }
  } catch (error) {
    yield put({
      type: TYPES.GET_MEDPRO_PATIENT_SUGGESTION_LIST_FAIL,
      error
    });
  }
}

function* requestCheckInsurance({ callback, text }) {
  try {
    yield put(onLoading());

    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";

    const { formCheckInsurance, selectedPatient } = yield select(
      state => state.patient
    );
    const patient = yield select(state => state.patient);

    const total = yield select(state => state.totalData);
    const partnerid = get(total, "partnerId");
    const bookingInfo = get(total, "schedulesSelected")[0];
    const bookingDate = moment(bookingInfo.date).format("YYYY-MM-DD");

    let insuranceCode = get(formCheckInsurance, "code.value");
    insuranceCode = insuranceCode?.toUpperCase();

    // const { fullName } = getInfoPatient(selectedPatient);
    const insuranceId =
      text === CHECK_WITH_INSURANCE_CODE_EXIST
        ? selectedPatient.insuranceCode
        : insuranceCode;
    const fullName = get(formCheckInsurance, "fullName.value");
    const birthday = get(formCheckInsurance, "birthday.value");
    const birthdayFormat = moment(birthday, "DD/MM/YYYY").format("YYYY-MM-DD");

    const postData = {
      fullName,
      insuranceId,
      birthday: birthdayFormat,
      bookingDate,
      patientId: selectedPatient.id
    };

    const response = yield client.getInsuranceDate(postData, {
      appid: AppId,
      partnerid,
      platform
    });

    const { data } = response;

    const objCheckFilter = {
      patientId: patient.selectedPatientId || "",
      treeId: total.treeId,
      stepId: "CheckBHYT",
      serviceId: total.service.id || "",
      template: "INSURANCE",
      hasInsurance: true,
      isValid: false,
      bookingDate: total.days.date ? moment(total.days.date).toISOString() : ""
    };

    const { data: checkFilter } = yield client.checkFilter(objCheckFilter, {
      partnerid,
      platform
    });

    // const message = get(data, "message", "");
    if (checkFilter?.requiredFilterCheck) {
      // trường hợp trái tuyến
      // openToast(message, "error");
      let route;
      if (["leloi"].includes(partnerid)) {
        route = routerByPartnerId(
          "/huong-dan-bao-hiem-y-te-trai-tuyen",
          partnerid
        );
        yield put(
          totalDataActions.setInsuranceChoice({
            insurance: "TAI_KHAM"
          })
        );
      } else if (["dalieuhcm", "bvmathcm"].includes(partnerid)) {
        route = routerByPartnerId(
          `/huong-dan-bao-hiem-y-te-trai-tuyen-${partnerid}`,
          partnerid
        );
        yield put(
          totalDataActions.setInsuranceChoice({
            insurance: "CHUYEN_TUYEN"
          })
        );
      } else {
        route = routerByPartnerId("/xac-nhan-thong-tin", partnerid);
      }
      yield put({
        type: TYPES.SELECT_PATIENT_FROM_DATA,
        payload: data
      });
      history.push(route);
    } else {
      // trường hợp đúng tuyến
      yield put(
        totalDataActions.setInsuranceChoice({
          insurance: "DUNG_TUYEN"
        })
      );
      const isExpiredInsurance = get(data, "expired", false);
      const message = get(data, "message", "");
      if (message) openToast(message, "error");
      if (!isExpiredInsurance) {
        if (data)
          yield put({
            type: TYPES.SELECT_PATIENT_FROM_DATA,
            payload: data
          });
        callback();
      } else {
        yield put({
          type: TYPES.CHECK_INSURANCE_OF_PATIENT_FAILED
        });
      }
    }
    yield put(offLoading());
  } catch (error) {
    yield put(offLoading());

    huyi({ name: "error-requestCheckInsurance", child: error, type: 0 });

    const message = get(error, "response.data.message");
    if (text !== CHECK_WITH_INSURANCE_CODE_EXIST) {
      openToast(message, "error");
    }
    yield put({
      type: TYPES.CHECK_INSURANCE_OF_PATIENT_FAILED
    });
  }
}

function* requestVerifyPhonePatient() {
  try {
    const currentRouter = get(history, "location.pathname");
    const partnerid = yield select(state => state.totalData.partnerId);
    const { selectedPatient, phoneNumberVerify } = yield select(
      state => state.patient
    );

    const postData = {
      phone: phoneNumberVerify,
      patientId: selectedPatient.id,
      msbn: selectedPatient.patientCode
    };

    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";

    const response = yield client.verifyPhone(postData, {
      partnerid,
      platform
    });
    if (currentRouter === "/cap-nhat-thong-tin") {
      yield put({
        type: patientFormTypes.UPDATE_PATIENT_FORM_REQUEST
      });
    } else {
      history.push("/xac-nhan-thong-tin-benh-nhan");
    }
    yield put(patientActions.verifyPhonePatientSuccess(response.data));
  } catch (error) {
    console.log(error);
    yield put(patientActions.verifyPhonePatientFailure(error.response.data));
    openToast(error.response.data.message, "error");
  }
}

function* requestVerifyInfoPatient() {
  try {
    const partnerid = yield select(state => state.totalData.partnerId);
    const { secretKey } = yield select(state => state.patient);
    const { cskhToken } = yield select(state => state.customerService);

    const postData = {
      secretKey: secretKey
    };
    const global = yield select(state => state.global);
    const platformType = get(global, "device.type");
    const platform = platformType === "desktop" ? "pc" : "web";

    const response = yield client.addPatientIntoUser(postData, {
      partnerid,
      platform,
      cskhtoken: cskhToken
    });

    openToast("Thêm bệnh nhân thành công.");
    yield put(selectPatientInfoCSKH(response?.data));
  } catch (error) {
    console.log("error requestVerifyInfoPatient :>> ", error);
    openToast(
      error.response?.data?.message || "Xin vui lòng thử lại thao tác này !",
      "error"
    );
  } finally {
    // const bookingTree = yield select(state => state.totalData.bookingTree);
    const { urlRedirectAfterCreatePatient } = yield select(
      state => state.patientForm
    );
    setTimeout(() => {
      // if (bookingTree) {
      //   history.push("/chon-ho-so");
      // } else {
      //   history.push("/user");
      // }
      history.push(urlRedirectAfterCreatePatient);
    }, 1000);
  }
}

export default function* watcher() {
  yield all([
    takeLatest(TYPES.GET_PATIENT_BY_MSBN_REQUEST, getPatientByMSBNWorker),
    takeLatest(TYPES.GET_PATIENT_BY_USERID_REQUEST, getPatientByUserIdWorker),
    takeLatest(TYPES.INSERT_PATIENT_REQUEST, insertPatientWorker),
    takeLatest(TYPES.DELETE_PATIENT_REQUEST, deletePatientWorker),
    takeLatest(TYPES.SEARCH_PATIENT_BY_INFO_REQUEST, searchPatientByInfoWorker),
    takeLatest(TYPES.UMC_CHOOSE_PATIENT_LIST_REQUEST, requestChoosePatientList),
    takeLatest(TYPES.GET_MEDPRO_PATIENT_LIST, requestMedproPatientList),
    takeLatest(
      TYPES.GET_MEDPRO_PATIENT_SUGGESTION_LIST,
      requestMedproPatientSuggestionList
    ),
    takeLatest(TYPES.VERIFY_PHONE_PATIENT, requestVerifyPhonePatient),
    takeEvery(TYPES.VERIFY_INFO_PATIENT, requestVerifyInfoPatient),
    takeLatest(TYPES.CHECK_INSURANCE_OF_PATIENT, requestCheckInsurance),
    takeLatest(TYPES.SELECT_PATIENT_REEXAM, requestSelectPatientReExam)
  ]);
}
