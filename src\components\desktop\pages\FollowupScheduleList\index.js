import React, { Component, Fragment } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  MDBRow,
  MDBCol,
  MDBCard,
  MDBCardHeader,
  MDBCardBody
} from "mdbreact";
import PatientInfomationLess from "~/components/desktop/molecules/PatientInfomationLess";
import cx from "classnames";
import TagName from "~/components/common/atoms/TagName";
import ListOfFollowUpSchedules from "~/components/mobile/molecules/ListOfFollowUpSchedules";
import styles from "./style.module.scss";
import { Facebook } from "react-content-loader";
import NoContentAlert from "~/components/desktop/atoms/NoContentAlert";

class FollowupScheduleList extends Component {
  render() {
    const {
      selectedPatient,
      rebooking,
      loading,
      errorMessage,
      selectedHospital
    } = this.props;
    if (!selectedPatient) return null;

    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow>
            <MDBCol md="4" lg="3">
              <PatientInfomationLess />
            </MDBCol>
            <MDBCol md="8" lg="9">
              <MDBCard className={styles.panels}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between"
                    ]}
                  >
                    <span>Danh sách lịch tái khám </span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody
                  className={cx(styles.card_body)}
                  style={{ minHeight: "250px" }}
                >
                  {loading ? (
                    <div>
                      <Facebook className={styles.svg_loader} />
                    </div>
                  ) : (
                      <Fragment>
                        {Array.isArray(rebooking) && rebooking.length === 0 && (
                          <NoContentAlert
                            message={`Bạn chưa có lịch tái khám tại ${selectedHospital.name}.`}
                          />
                        )}
                        {Array.isArray(rebooking) && rebooking.length > 0 && (
                          <ListOfFollowUpSchedules
                            rebooking={rebooking}
                            onBookExamination={this.handleBookExamination}
                          />
                        )}
                        {errorMessage ? (
                          <NoContentAlert
                            message={`Đã có lỗi xảy ra trong quá trình tải dữ liệu, vui lòng
                          thử lại sau.`}
                          />
                        ) : null}
                      </Fragment>
                    )}
                </MDBCardBody>
              </MDBCard>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default FollowupScheduleList;
