@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-common/timeline.scss";

.img_parallax {
  height: unset !important;
  padding-top: 60px !important;
  padding-bottom: 80px !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  display: flex;
  align-items: center;
  h1 {
    margin-bottom: 20px !important;
    color: #ffffff !important;
    span {
      font-size: 2.2rem;
    }
  }
  p {
    color: #fff !important;
  }
  @media #{$medium-and-down} {
    margin-top: 60px;
  }
}

.h1_about {
  text-align: center;
  text-transform: uppercase;
  color: #ffffff;
}
.procedure {
  position: relative;
  background: #fff;
  margin: 0px auto 120px;
  border-radius: 5px;
  min-height: 300px;
  padding: 30px 0 0 0;
  max-width: 1140px;
  @media (min-width: 992px) {
    padding: 30px;
  }
}
.introduce_h1 {
  text-align: center;
  text-transform: uppercase;
  color: #fff;
}
.procedure_item {
  min-height: 400px;
  position: relative;
}

// ----------------------------Minh anh
.procedure_minhanh {
  .procedure_item {
    .wapper_page_inner {
      ul {
        li {
          ::before {
            background: #db2233 !important;
          }
          p {
            color: #db2233 !important;
          }
          div {
            ::before {
              background: unset !important;
            }

            h3 {
              color: #db2233 !important;
            }
          }
        }
      }
    }
  }
}
