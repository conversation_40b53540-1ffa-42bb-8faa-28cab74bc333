pipeline {
    agent {
         node {
        label 'docker-build'
         }
}
options {
  retry(2)
}

environment {
        URL_NAME = "docker.medpro.com.vn"
        IMAGE_NAME = "medpro-web-v2-cskh-testing"

    }
  stages {
  // stage("Import secretg file to config"){
  //   steps{
  //       withCredentials([string(credentialsId: "${APP_PROJECT}" , variable: 'SECRET'  )])
  //     {
  //      sh '''
  //      touch config/production.env
  //      echo ["${SECRET}"] | xargs -n1 > config/production.env
  //      '''
  //     }
  //   }
  //   }
//     stage("Clean Last Build"){
//         steps{
//         script{
//        try{
//         sh """
//             docker image rm `docker image ls -q`
//         """
//                 }
//     catch(Exception e){
//         println("Removed All Resoure Last Build")
//         }
//     }
//     }
// }
    stage('Building image') {

      steps{
        script {
          sh """
          docker build -t ${URL_NAME}/${GIT_BRANCH}:date-${BUILD_TIMESTAMP}.ver-${BUILD_NUMBER} --no-cache . -f Dockerfile.dev
          docker push ${URL_NAME}/${GIT_BRANCH}:date-${BUILD_TIMESTAMP}.ver-${BUILD_NUMBER}
          """
        }
      }
    }
    stage('pull and deploy ') {
       agent {
         node {
        label 'docker-deploy'
         }
}
      options {
        retry(2)
      }
    steps{

        script {
        withCredentials([usernamePassword(credentialsId: 'e3e8c537-e3e4-4789-bf8f-e8487bda1ca2', passwordVariable: 'password', usernameVariable: 'username')]) {
          sh ''' argocd login argocd.medpro.com.vn --username $username --password $password --insecure  --grpc-web '''
}
          sh """
          argocd app set medpro-web-v2120-testing --kustomize-image ${URL_NAME}/${GIT_BRANCH}:date-${BUILD_TIMESTAMP}.ver-${BUILD_NUMBER}
          argocd app sync medpro-web-v2120-testing
          """
        }
      }
    }
//   stage("Notyfication  to slack ")
  }
    post {
        failure {
            emailext body: '$PROJECT_NAME - Build # $BUILD_NUMBER - $BUILD_STATUS:Check console output at $BUILD_URL to view the results.', subject: 'CI_Intergration',  recipientProviders: [[$class: 'DevelopersRecipientProvider'], [$class: 'RequesterRecipientProvider']]
        }
    }
}
