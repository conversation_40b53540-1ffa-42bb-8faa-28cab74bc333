import cx from "classnames";
import {
  MDBCard,
  MDBCardBody,
  MDBCardImage,
  MDBCardText,
  MDBCol,
  MDBContainer,
  MDBRow,
  MDBView
} from "mdbreact";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { apiNews } from "~/utils/constants";
import styles from "./style.module.scss";
const NewsSame = () => {
  const [data, setdata] = useState([]);

  useEffect(() => {
    const getdata = async () => {
      const url = `https://cms.medpro.com.vn/posts?_sort=updated_at:DESC&_limit=5`;
      const x = await fetch(url);
      const dta = await x.json();
      setdata(dta);
    };
    getdata();
  }, []);

  return (
    <MDBContainer className={cx(styles.box_right_article, "p-0")}>
      <MDBRow>
        <MDBCol className="mb-4">
          <h5 className="text-uppercase font-weight-bolder text-center">
            Tin cùng chuyên mục
          </h5>
        </MDBCol>
      </MDBRow>

      <MDBRow className={styles.article_item}>
        <MDBCol>{listMap(data)}</MDBCol>
      </MDBRow>
      <MDBRow className={styles.view_all} center>
        <MDBCol className="text-center">
          <Link to="/tin-tuc">Xem tất cả</Link>
        </MDBCol>
      </MDBRow>
    </MDBContainer>
  );
};
export default NewsSame;

const listMap = data =>
  // eslint-disable-next-line camelcase
  data?.map(({ title, image, id, updated_at, slug }) => {
    return (
      <MDBCard className={styles.card_news} key={id}>
        <MDBView className={styles.view}>
          <Link to={"/tin-tuc/" + slug}>
            <MDBCardImage className="img-fluid" src={apiNews + image[0]?.url} />
          </Link>
        </MDBView>
        <MDBCardBody className={styles.card_body}>
          <MDBCardText className={styles.content}>
            <Link to={"/tin-tuc/" + slug}>
              <p className={styles.title}>{title}</p>
            </Link>
            <p className={styles.tag}>
              {moment(updated_at).format("DD/MM/YYYY, hh:mm")}
            </p>
          </MDBCardText>
        </MDBCardBody>
      </MDBCard>
    );
  });
