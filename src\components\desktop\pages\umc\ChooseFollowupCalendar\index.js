import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  MDBContainer,
  MDBRow,
  MDBCol,
  MDBCardBody,
  MDBCard,
  MDBCardHeader,
  // MDBBreadcrumb,
  // MDBBreadcrumbItem,
} from 'mdbreact';
import PatientInfomationLess from '~/components/desktop/molecules/PatientInfomationLess';
import PatientInfomationBooking from '~/components/desktop/molecules/PatientInfomationBooking';
import { withRouter, Redirect } from 'react-router-dom';
import ListCalendar from '~/components/common/molecules/ListCalendar';
import TagName from '~/components/common/atoms/TagName';
// import cx from 'classnames';
import styles from './style.module.scss';
import { requestHoliday } from '~/store/resource/resourceAction';
import InfoBHYT from '~/components/common/molecules/InfoBHYT';
import { range } from 'lodash';
import moment from 'moment';
import {
  selectedBHYT,
  submitDoctorAndTime,
  showMessageError,
  getDoctorTimeSlot,
  resetRedirectToConfirmInfo,
  selectedTime,
  removeDoctorAndTime,
} from '~/store/room/roomAction';
import {
  selectedMonth,
  selectedDate,
  selectedShowOneRow,
  closeToChooseCalendarAgain,
} from '~/store/dateAndSpecialist/dateAndSpecialistActions';
import TimeBooking from '~/components/desktop/molecules/umc/TimeBooking';
import PKHBtn from '~/components/common/atoms/Button';
import BreadcrumbStream from '~/components/desktop/molecules/BreadCrumbStream';
import Modal from '~/components/common/molecules/Modal';
class DateAndSpecialist extends Component {
  constructor(props) {
    super(props);
    this.buttonRef = React.createRef();
    this.props.selectDate(undefined);
    this.state = {
      isShowModalMissInformation: false,
    };
  }

  toggleModalMissInformation = () => {
    this.setState({
      isShowModalMissInformation: !this.state.isShowModalMissInformation,
    });
  };

  requestDoctorTimeSlot = (
    date = this.props.rebookingSelected.rebooking_date,
  ) => {
    const { rebookingSelected, selectDate, getDoctorTimeSlot } = this.props;
    if (rebookingSelected) {
      const { detail, doctor } = rebookingSelected;
      const weekdayOfFollowup = moment(date).days();
      const roomSelected = detail.find(
        room => +room.ptime.weekday === weekdayOfFollowup,
      );
      const roomIdList = detail
        .filter(room => +room.ptime.weekday === weekdayOfFollowup)
        .map(room => room.id)
        .join(',');
      if (roomSelected) {
        selectDate(date);
        const room = { ...roomSelected, id: roomIdList, doctor };
        getDoctorTimeSlot(room);
      }
    }
  };

  afterSelectedDate = (date = '') => {
    this.props.onSelectedTime({ id: 0 });
    this.requestDoctorTimeSlot(date);
  };

  handleOnSelectedBHYT = info => {
    this.props.onSelectedBHYT(info);
  };

  onValidateBeforeSubmit = callback => {
    const { selectedBHYT, selectedTime } = this.props;
    if (
      Object.keys(selectedBHYT).length > 1 &&
      Object.keys(selectedTime).length > 1 &&
      Number(selectedBHYT.id) > -1
    ) {
      // this.props.showMessageError('');
      callback();
    } else {
      // this.props.showMessageError('Vui lòng chọn Giờ khám và BHYT');
      this.setState({
        isShowModalMissInformation: !this.state.isShowModalMissInformation,
      });
    }
  };

  onSubmitDoctorAndTime = () => {
    const { dateAndSpecialist } = this.props;
    this.props.onSubmitDoctorAndTime(dateAndSpecialist);
  };

  handleCloseToChooseAgain = () => {
    this.props.onSelectedTime({ id: 0 });
    this.props.selectDate(undefined);
    this.props.closeToChooseCalendarAgain();
  };

  handleGoBack = () => {
    const route = '/umc/danh-sach-lich-tai-kham';
    this.props.history.push(route);
  };

  componentDidMount() {
    const { sumaryInfo, selectedSpecialist } = this.props;
    if (sumaryInfo.length > 0) {
      const index = sumaryInfo.findIndex(
        item => item.selectedSpecialist.id === selectedSpecialist.id,
      );
      if (index > -1) {
        this.props.onRemoveDoctorAndTime(sumaryInfo.length - 1);
      }
    }
    this.props.requestHoliday();
    this.props.selectedShowOneRow();
    this.requestDoctorTimeSlot();
    this.props.selectMonth(this.props.rebookingSelected.rebooking_date);
  }

  componentWillUnmount() {
    this.props.onResetRedirectToConfirmInfo();
  }

  render() {
    const {
      rebookingSelected,
      redirectToConfirmInfo,
      dateAndSpecialist,
      selectedBHYT,
      selectedTime,
    } = this.props;

    if (redirectToConfirmInfo)
      return <Redirect to="/umc/xac-nhan-thong-tin" push />;
    let disabledWeekdays = [];
    if (rebookingSelected) {
      disabledWeekdays = range(0, 7);
      const { detail = [] } = rebookingSelected;
      detail.forEach(({ ptime }) => {
        disabledWeekdays = disabledWeekdays.filter(
          weekday => weekday !== +ptime.weekday,
        );
      });
    }

    const objCheckNextStep = Object.create(null);
    if (
      Object.keys(selectedBHYT).length > 1 &&
      Object.keys(selectedTime).length > 1 &&
      Number(selectedBHYT.id) > -1
    ) {
      objCheckNextStep.nextStep = true;
      window.scrollTo({
        left: 0,
        top: this.buttonRef.current ? this.buttonRef.current.offsetTop : 0,
        behavior: 'smooth',
      });
    }

    const flowSteps = {
      step1: { name: 'Danh sách lịch tái khám', class: 'prev_step' },
      step2: { name: 'Chọn lịch tái khám', class: 'active' },
      step3: { name: 'Xác nhận thông tin', class: '' },
      step4: { name: 'Thanh toán', class: '' },
    };

    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow>
            <MDBCol md="4" lg="3">
              <PatientInfomationLess />
              <PatientInfomationBooking />
            </MDBCol>
            <MDBCol md="8" lg="9">
              <BreadcrumbStream flowSteps={flowSteps} />
              <MDBCard className={styles.card_box}>
                <MDBCardHeader className={styles.card_header}>
                  <TagName
                    element="h2"
                    className={[
                      'title_component',
                      'title_choose_date',
                      'space_between',
                    ]}>
                    <span>Vui lòng chọn ngày khám </span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody className={styles.body_calendar}>
                  <ListCalendar
                    afterSelectedDate={this.afterSelectedDate}
                    disabledWeekdays={disabledWeekdays}
                    listDisabledDay={[0]}
                    disableCurrent
                    noBookingAfterTime="16:30:00"
                  />
                  <Modal
                    modal={this.state.isShowModalMissInformation}
                    iconTitle={<i className="fal fa-bell" />}
                    title="Thông báo"
                    children="Vui lòng chọn Ngày khám, giờ khám, hình thức Bảo hiểm y tế!"
                    toggle={this.toggleModalMissInformation}
                    centered
                    className="centered"
                  />
                </MDBCardBody>
                <div
                  className={styles.choose_time}
                  style={
                    dateAndSpecialist.selectedDate
                      ? { display: 'block' }
                      : { display: 'none' }
                  }>
                  <div className={styles.box_list_time}>
                    <TimeBooking />
                    <PKHBtn onClick={this.handleCloseToChooseAgain}>
                      <i className="fal fa-times" />
                      Đóng
                    </PKHBtn>
                  </div>
                  <InfoBHYT
                    onChangeBHYT={this.handleOnSelectedBHYT}
                    title="Chọn bảo hiểm y tế"
                  />
                  <div className={styles.next_prev} ref={this.buttonRef}>
                    <PKHBtn
                      backdesktop="backdesktop"
                      onClick={this.handleGoBack}>
                      Quay lại
                    </PKHBtn>
                    <PKHBtn
                      onClick={() =>
                        this.onValidateBeforeSubmit(this.onSubmitDoctorAndTime)
                      }
                      buttonArrow="buttonArrow"
                      {...objCheckNextStep}
                      create="create">
                      Tiếp tục{' '}
                    </PKHBtn>
                  </div>
                </div>
              </MDBCard>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    rebooking: { rebookingSelected },
    doctorAndTime: { redirectToConfirmInfo },
    dateAndSpecialist: { selectedDate, selectedSpecialist },
    doctorAndTime: { isModalPickTime, selectedTime, selectedBHYT, sumaryInfo },
  } = state;

  return {
    rebookingSelected,
    redirectToConfirmInfo,
    selectedDate,
    isModalPickTime,
    selectedTime,
    selectedBHYT,
    dateAndSpecialist: state.dateAndSpecialist,
    sumaryInfo,
    selectedSpecialist,
  };
};

const mapDispatchToProps = dispatch => ({
  requestHoliday: () => {
    dispatch(requestHoliday());
  },
  getDoctorTimeSlot: room => {
    dispatch(getDoctorTimeSlot(room));
  },
  onSelectedBHYT: info => {
    dispatch(selectedBHYT(info));
  },
  onSubmitDoctorAndTime: dateAndSpecialist => {
    dispatch(submitDoctorAndTime(dateAndSpecialist));
  },
  showMessageError: message => {
    dispatch(showMessageError(message));
  },
  onResetRedirectToConfirmInfo: () => {
    dispatch(resetRedirectToConfirmInfo());
  },
  selectMonth: month => {
    dispatch(selectedMonth(month));
  },
  selectDate: date => dispatch(selectedDate(date)),
  onSelectedTime: time => {
    dispatch(selectedTime(time));
  },
  closeToChooseCalendarAgain: () => {
    dispatch(closeToChooseCalendarAgain());
  },
  selectedShowOneRow: () => dispatch(selectedShowOneRow()),
  onRemoveDoctorAndTime: index => {
    dispatch(removeDoctorAndTime(index));
  },
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(DateAndSpecialist));
