import QLKH from "~/assets/img/desktop/HompageClinic/QLKH.svg";
import BCTK from "~/assets/img/desktop/HompageClinic/BCTK.svg";
import QLKD from "~/assets/img/desktop/HompageClinic/QLKD.svg";
import TVKB from "~/assets/img/desktop/HompageClinic/TVKB.svg";
export const data = [
  {
    img: QLKH,
    title: "DỊCH VỤ QUẢN LÝ KHÁCH HÀNG",
    value:
      "Với ứng dụng MedPro Clinic, bạn sẽ dễ dàng quản lý lịch khám hoặc tái khám của từng người bệnh đến được chia theo từng nguồn."
  },
  {
    img: TVKB,
    title: "TƯ VẤN KHÁM BỆNH TỪ XA",
    value:
      "Ngoài việc khám trực tiếp tại phòng khám, ứng dụng kết nối giữa bác sĩ và người bệnh thông qua hệ thống gọi video giúp bác sĩ có thể nhìn thấy người bệnh, lắng nghe trao đổi trực tiếp, xem xét hồ sơ bệnh lý liên quan, tư vấn các hướng xử lý vấn đề của người bệnh đang cần."
  },
  {
    img: BCTK,
    title: "BÁO CÁO THỐNG KÊ",
    value:
      "Tổng hợp tình hình hoạt động và tài chính của phòng khám/phòng mạch theo tổng hợp riêng với các biểu đồ đa dạng."
  },
  {
    img: QLKD,
    title: "KÊ TOA VÀ QUẢN LÝ KHO DƯỢC",
    value:
      "Hỗ trợ nhanh bác sĩ kê toa nhanh chóng. Theo dõi được số lượng hàng nhập, xuất, tồn đơn giản."
  }
];
