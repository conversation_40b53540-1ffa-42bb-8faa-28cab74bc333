import React from "react";
import FormForgotPatientCode from "~/components/common/molecules/FormForgotPatientCode";
import ListSupportUser from "~/components/mobile/molecules/ListSupportUser";
import InputMask from "react-input-mask";
import PKHBtn from "~/components/common/atoms/Button";
import { connect } from "react-redux";
import history from "~/history";
import AppId from "~/utils/partner";
import { checkDevice } from "~/utils/func";
import styles from "./style.module.scss";

const HaveEverExamined = ({
  toggleModalViewImage,
  isCLS,
  handleChangePatientNumber,
  checkPatientNumber,
  errorMessage,
  showLoading,
  patientNumber
}) => {
  const isMobile = checkDevice();
  return (
    <div className={styles.have_ever_examined}>
      <div className={styles['left']}>
      <form className={styles.form_have_ever_examined}>
        <p className={styles.tag}>NHẬP MÃ SỐ BỆNH NHÂN / MÃ SỐ BHYT</p>

        <div className={styles.form_group}>
          <InputMask
            id="label_hoten_2"
            maskChar=" "
            className="form-control"
            placeholder="Nhập mã số bệnh nhân / Mã số BHYT"
            onChange={e => {
              handleChangePatientNumber(e.target.value.toUpperCase());
            }}
            onKeyDown={event => {
              if (event.keyCode === 13) {
                event.preventDefault();
                checkPatientNumber();
              }
            }}
            value={patientNumber}
          />
          {errorMessage && (
            <span className={styles.alertSpan}>{errorMessage}</span>
          )}
        </div>

        {isMobile && AppId !== "115" && (
          <p
            className={styles.url_scan_qr_code}
            onClick={() => history.push("/find-bhyt")}
          >
            Quét mã BHYT
          </p>
        )}

        <PKHBtn
          className="m-0"
          create="create"
          block="block"
          onClick={checkPatientNumber}
        >
          {showLoading ? "Loading..." : "Tìm kiếm"}
        </PKHBtn>
      </form>

      {/*<ListSupportUser isCLS toggleModalViewImage={toggleModalViewImage} />*/}
      </div>
      <div className={styles['right']}>
        <div className={styles['title']}>
          Tìm lại mã bệnh nhân
        </div>
        <div className={styles['formWrapper']}>
          <FormForgotPatientCode className={styles['form']}/>
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = state => {
  const {
    hospital: { selectedHospital },
    patient: { showLoading }
  } = state;
  return { selectedHospital, showLoading };
};

const mergeProps = (stateProps, dispatchProps, ownProps) => {
  return { ...stateProps, ...dispatchProps, ...ownProps };
};

export default connect(mapStateToProps, {}, mergeProps)(HaveEverExamined);
