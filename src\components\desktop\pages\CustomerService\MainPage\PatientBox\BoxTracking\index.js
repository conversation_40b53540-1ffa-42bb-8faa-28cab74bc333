import { size } from "lodash";
import { MDBCol, MDBRow } from "mdbreact";
import moment from "moment";
import React from "react";
import styles from "./style.module.scss";
export const ListTracking = ({ data, patient, userTracking }) => {
  if (size(data) === 0) {
    return (
      <p style={{ fontWeight: 700 }}>
        Ch<PERSON><PERSON> ghi nhận bất kỳ thao tác nào với hồ sơ này...
      </p>
    );
  }
  return (
    <div className={styles.listTracking}>
      {data?.map((item, index) => {
        return (
          <div key={item.id} className={styles.tracking}>
            <p>
              Tà<PERSON> khoản:{" "}
              <span style={{ color: "red", fontWeight: 700 }}>
                {item.userAction.fullname}
              </span>{" "}
              <i>
                ( medproId:
                {item.userAction.username} )
              </i>{" "}
              thực hiện{" "}
              <span style={{ color: "red", fontWeight: 700 }}>
                {item.action}
              </span>
              .
            </p>
            <MDBRow className={styles.row}>
              <MDBCol className={styles.col}>
                <p className={styles.title}>T<PERSON><PERSON> hồ sơ (BN)</p>
                <p>{patient.surname + " " + patient.name}</p>
              </MDBCol>
              <MDBCol className={styles.col}>
                <p className={styles.title}>Thao tác</p>
                <p>{item.action}</p>
              </MDBCol>
            </MDBRow>
            <MDBRow className={styles.row}>
              <MDBCol className={styles.col}>
                <p className={styles.title}>Tài khoản (BN)</p>
                <p>{userTracking.username}</p>
              </MDBCol>
              <MDBCol className={styles.col}>
                <p className={styles.title}>Thời gian</p>
                <p>{moment(item.updatedAt).format("DD-MM-YYYY HH:mm")}</p>
              </MDBCol>
            </MDBRow>
          </div>
        );
      })}
    </div>
  );
};
