import cx from "classnames";
import { filter, size } from "lodash";
import {
  MDBBtn,
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBFormInline,
  MDBIcon,
  MDBModal,
  MDBModalBody,
  MDBModalHeader,
  MDBRow,
  MDBTooltip
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { withRouter } from "react-router";
import { openToast } from "~/components/common/molecules/ToastNotification";
import history from "~/history";
import {
  getPatientsByPhoneCSKH,
  resetBookingByPatientCS
} from "~/store/customerService/customerServiceActions";
import {
  selectedPatientDetail,
  toggleModalConfirm,
  deletePatient,
  getPatientList,
  selectedPatient
} from "~/store/patient/patientAction";
import {
  changeUrlRedirectAfterCreatePatient,
  getPatientDetail
} from "~/store/patientForm/patientFormAction";
import { setTypeAction } from "~/store/totalData/actions";
import { TypeAction, urlPage, REMOVE, C<PERSON><PERSON><PERSON> } from "~/utils/constants";
import PatientBoxBody from "./PatientBoxBody";
import styles from "./style.module.scss";
import { client } from "~/utils/medproSDK";
import { ListTracking } from "./BoxTracking";
import Modal from "~/components/common/molecules/Modal";

class PatientBox extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpenDeleteModal: false,
      list: props.patientList,
      visableSeach: false,
      visibleHistory: false,
      listTracking: [],
      userTracking: ""
    };
  }

  handleEdit = patient => {
    const { history } = this.props;
    this.props.resetPatientForm();
    this.props.handleSelectedPatient(patient);
    this.props.setTypeAction(TypeAction.SuaHoSo);
    this.props.changeUrlRedirectAfterCreatePatient(urlPage.CSKH);

    this.props.handleGetPatientDetail(patient.id, patient.secretKey, () => {
      history.push(urlPage.Chon_Benh_Vien);
    });
  };

  handleDelete = () => {
    const { isOpenDeleteModal } = this.state;
    const { handleDeletePatient } = this.props;

    if (isOpenDeleteModal) {
      this.closeDeleteModal();
      handleDeletePatient();
    }
  };

  closeDeleteModal = () => {
    this.setState({ isOpenDeleteModal: false });
  };

  handleToggleDeleteModal = selectedPatient => {
    const { isOpenDeleteModal } = this.state;
    this.props.selectPatient(selectedPatient.id);
    this.setState({ isOpenDeleteModal: !isOpenDeleteModal });
  };

  handleHaveEverExam = () => {
    const { cs } = this.props;
    if (!cs.searchedPhone) {
      openToast(
        "Vui lòng nhập số điện thoại trước khi thực hiện tính năng !",
        "error"
      );
      const phoneUser = window.document.getElementById("phoneUser");
      phoneUser.focus();
    } else {
      this.props.changeUrlRedirectAfterCreatePatient(urlPage.CSKH);
      this.props.setTypeAction(TypeAction.DaTungKham);
      history.push(urlPage.Chon_Benh_Vien);
    }
  };

  handleCreatePatient = () => {
    const { cs } = this.props;
    if (!cs.searchedPhone) {
      openToast(
        "Vui lòng nhập số điện thoại trước khi thực hiện tính năng !",
        "error"
      );
      const phoneUser = window.document.getElementById("phoneUser");
      phoneUser.focus();
    } else {
      this.props.resetBookingByPatientCS();
      this.props.changeUrlRedirectAfterCreatePatient(urlPage.CSKH);
      this.props.setTypeAction(TypeAction.ChuaTungKham);
      history.push(urlPage.Chon_Benh_Vien);
    }
  };

  handleSearchPatient = event => {
    event.preventDefault();
    const { patientList } = this.props;
    const { value } = event.target;
    const filtered = filter(patientList, v => {
      const fullname = v.surname + " " + v.name;
      return (
        fullname?.toLowerCase().includes(value?.toLowerCase()) ||
        v.mobile?.includes(value)
      );
    });

    this.setState({
      list: filtered
    });
  };

  handleVisableSearch = () => {
    this.setState(v => ({ ...v, visableSeach: !v.visableSeach }));
  };

  handleTracking = async () => {
    try {
      const { selectedPatient } = this.props;
      const { data } = await client.getPatientTracking({
        patientId: selectedPatient._id
      });
      const { data: dataTracking, userPatient } = data;

      this.setState({
        listTracking: dataTracking,
        userTracking: userPatient,
        visibleHistory: !this.state.visibleHistory
      });
    } catch (error) {
      console.log("error :>> ", error);
    }
  };

  render() {
    const { patientList, selectedPatient } = this.props;
    const {
      visableSeach,
      isOpenDeleteModal,
      visibleHistory,
      listTracking,
      userTracking
    } = this.state;
    return (
      <MDBCard className={cx(styles.CardPatientBox, "my-3")}>
        <MDBCardHeader
          color="light-blue darken-2"
          className={cx(styles.CardHeader, "py-1 px-3")}
        >
          <MDBRow className=" d-flex align-items-center">
            <MDBCol sm="12" md="12">
              Danh Sách Hồ Sơ Bệnh Nhân
            </MDBCol>
            <MDBCol sm="12" md="12" className=" text-right">
              <div className={styles.divAction}>
                {size(patientList) > 5 && (
                  <MDBFormInline
                    onSubmit={e => e.preventDefault()}
                    className={cx(styles.searchPatient, "md-form")}
                  >
                    <MDBIcon icon="search" onClick={this.handleVisableSearch} />
                    <input
                      className={
                        visableSeach
                          ? "form-control form-control-sm ml-3 w-75"
                          : styles.hidden
                      }
                      type="search"
                      placeholder="Tên, số điện thoại ..."
                      aria-label="Search"
                      onChange={this.handleSearchPatient}
                    />
                  </MDBFormInline>
                )}

                {size(patientList) > 5 && (
                  <MDBTooltip placement="top">
                    <MDBBtn
                      color="primary"
                      size="sm"
                      onClick={() => {
                        const elmnt = document.getElementById(
                          "activePatientCS"
                        );
                        elmnt && elmnt.scrollIntoView({ behavior: "smooth" });
                      }}
                    >
                      <MDBIcon icon="hand-pointer" className="px-1" />
                    </MDBBtn>
                    <div>Hồ sơ đang chọn</div>
                  </MDBTooltip>
                )}
                {/* <MDBBtn
                  color=" red lighten-1"
                  size="sm"
                  onClick={() => {
                    this.handleTracking();
                  }}
                >
                  Lịch sử thao tác
                </MDBBtn> */}
                <MDBBtn
                  color="primary"
                  size="sm"
                  onClick={this.handleHaveEverExam}
                >
                  Đã từng khám
                </MDBBtn>

                <MDBBtn
                  color=" green accent-4"
                  size="sm"
                  onClick={this.handleCreatePatient}
                >
                  <MDBIcon icon="plus" className="px-1" /> Tạo mới
                </MDBBtn>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBCardHeader>
        <MDBCardBody className={cx(styles.CardBody)}>
          <PatientBoxBody
            list={this.state.list}
            {...this.props}
            onEdit={this.handleEdit}
            onDelete={this.handleToggleDeleteModal}
          />
        </MDBCardBody>
        <MDBModal
          isOpen={visibleHistory}
          centered
          className={styles.modal}
          toggle={() => {
            this.setState({
              visibleHistory: !visibleHistory
            });
          }}
        >
          <MDBModalHeader
            className={styles.Header}
            titleClass="w-100 font-weight-bold"
            toggle={() => {
              this.setState({
                visibleHistory: !visibleHistory
              });
            }}
          >
            <i className="fal fa-bell" style={{ fontSize: "16px" }} />
            <span className={styles.TitleHeader}>
              Hệ thống ghi nhận thao tác
            </span>
          </MDBModalHeader>
          <MDBModalBody className={styles.Body}>
            <p
              style={{
                marginBottom: 8,
                fontStyle: "italic"
              }}
            >
              Hồ sơ bệnh nhân{" "}
              <span style={{ color: "#F44336", fontWeight: 700 }}>
                {selectedPatient.surname + " " + selectedPatient.name}
              </span>
            </p>
            <ListTracking
              data={listTracking}
              patient={selectedPatient}
              userTracking={userTracking}
            />
          </MDBModalBody>
        </MDBModal>
        <Modal
          modal={isOpenDeleteModal}
          title="Thông báo"
          children="Bạn có chắc muốn xóa bệnh nhân này?"
          centered
          className="centered"
          footer
          footerConfirm="true"
          cancelText={CLOSE}
          okText={REMOVE}
          onCancel={this.closeDeleteModal}
          onOk={this.handleDelete}
          toggle={this.closeDeleteModal}
        />
      </MDBCard>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { info },
    patientForm: { redirectToPatientUpdate },
    patient: { patientList, loading, data, selectedPatient },
    customerService: cs
  } = state;
  return {
    cs,
    user: info,
    patients: patientList,
    redirectToPatientUpdate,
    loading,
    data,
    selectedPatient
  };
};

const mapDispatchToProps = {
  resetBookingByPatientCS: resetBookingByPatientCS,
  setTypeAction: setTypeAction,
  handleSelectedPatient: selectedPatientDetail,
  handleGetPatientDetail: getPatientDetail,
  changeUrlRedirectAfterCreatePatient: changeUrlRedirectAfterCreatePatient,
  toggleModalConfirm: () => toggleModalConfirm,
  handleDeletePatient: () => deletePatient(),
  handleGetPatients: () => getPatientList,
  selectPatient: selectPatient => selectedPatient(selectPatient),
  requestPatientsInfoByPhone: getPatientsByPhoneCSKH
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(PatientBox));
