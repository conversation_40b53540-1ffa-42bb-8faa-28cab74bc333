@import "src/assets/scss/custom-variables.scss";

.btnAct {
  min-width: 120px;
  text-transform: initial;
}

.listInfo {
  li {
    width: 50%;
    margin: 5px 0;
    font-size: 0.875rem;

    .title {
      width: auto;
    }

    .value {
      // width: 60%;
      font-weight: 500;
      margin-left: 5px;
    }

    &:first-child,
    &:last-child {
      .title {
        width: auto;
      }

      .value {
        width: auto;
        padding-left: 5px;
      }
    }
  }

  @media #{$medium-and-down} {
    li {
      width: 100%;

      .title {
        width: auto;
      }

      .value {
        width: auto;
      }
    }
  }

  @media (max-width: 1926px) {
    li {
      width: 100%;

      .title {
        width: auto;
      }

      .value {
        width: auto;
      }
    }
  }
}

.cardBodyWrapper {
  position: relative;
  padding: 5px 10px !important;

  .cardInfoWrapper {
    li {
      margin: 0 0 4px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        margin-bottom: 0;
        margin-left: 0;
        display: flex;
        align-items: center;
        gap: 5px;
        flex-shrink: 0;
        max-width: 100%;

        &.title {
          i {
            font-size: 15px;
            max-width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        &.value {
          .tag {
            padding: 3px 6px;
            border-radius: 4px;
          }
        }
      }

      .partner {
        font-size: 17px;
        margin-bottom: -4px;
        color: #0066ff !important;
      }

      .fullname {
        font-size: 17px;
        margin-bottom: -4px;
      }

      .bookingCode {
        border-bottom: 1px dashed #0066ff;
        cursor: pointer;
        color: #0066ff !important;
      }

      .dateBooking {
        color: #0066ff !important;
      }

      &.groupLi {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 8px;

        @media #{$small-and-down} {
          flex-direction: column;
          align-items: flex-start;
          height: auto;
        }
      }

      .groupLabel {
        display: flex;
        align-items: center;
        gap: 8px;

        @media #{$small-and-down} {
          flex-flow: wrap;
        }
      }
    }
  }
}

.rowBtn {
  z-index: 3;
  text-align: center;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  align-content: center;
  @media #{$medium-and-down} {
    flex-direction: row;
    justify-content: center;
  }

  @media (max-width: 1200px) {
    flex-direction: row;
    justify-content: center;
  }
}
