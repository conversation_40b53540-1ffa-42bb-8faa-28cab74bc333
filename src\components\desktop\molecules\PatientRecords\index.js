/* eslint-disable react/jsx-curly-brace-presence */
/* eslint-disable react/jsx-indent */
import React, { Component, Fragment } from "react";
import { Facebook } from "react-content-loader";
import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
// import cx from "classnames";
// import Select from "~/components/common/atoms/Select";
import ListPatientRecords from "~/components/mobile/molecules/ListPatientRecords";
// import styles from "./style.module.scss";
import Modal from "~/components/common/molecules/Modal";
import { REMOVE, CLOSE } from "~/utils/constants";
import {
  setPatientInfo,
  getPatientDetail
} from "~/store/patientForm/patientFormAction";
import {
  selectedPatientDetail,
  deletePatient,
  selectedPatient,
  toggleModalConfirm,
  getPatientList
} from "~/store/patient/patientAction";
import {
  requestHospitalList,
  selectHospital,
  resetSelectedHospital
} from "~/store/hospital/hospitalActions";
import NoContentAlert from "~/components/desktop/atoms/NoContentAlert";
// import AppId from "~/utils/partner";

const titleDeleteUser = "Xóa bệnh nhân này ?";
const contentDeleteUser =
  "Bạn có chắc là muốn xóa bệnh nhân này khỏi danh sách ?";
class PatientRecords extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpenDeleteModal: false,
      selectedPatient: false,
      baseUrl: ""
    };
  }

  componentDidMount = () => {
    this.props.handleGetPatients(false);
  };

  closeDeleteModal = () => {
    this.props.toggleModalConfirm();
    this.setState({ isOpenDeleteModal: false });
  };

  handleToggleDeleteModal = selectedPatient => {
    const { isOpenDeleteModal } = this.state;
    this.props.selectPatient(selectedPatient.id);
    this.setState({ isOpenDeleteModal: !isOpenDeleteModal, selectedPatient });
  };

  handleDelete = () => {
    const { isOpenDeleteModal } = this.state;

    const { handleDeletePatient } = this.props;

    if (isOpenDeleteModal) {
      this.closeDeleteModal();
      const callBack = () => this.props.handleGetPatients();
      handleDeletePatient(callBack);
    }
  };

  handleEdit = patient => {
    const { history } = this.props;

    this.props.handleSelectedPatient(patient);
    this.props.handleGetPatientDetail(patient.id, "", () => {
      history.push(`/cap-nhat-thong-tin`);
    });
  };

  handleSelect = patient => {
    const { history, handleSelectedPatient } = this.props;
    handleSelectedPatient(patient);
    const { id } = patient;
    history.push(`/ho-so-benh-nhan/chi-tiet/${id}`);
  };

  renderListPatient = () => {
    const { loading, data } = this.props;

    if (loading) {
      return (
        <React.Fragment>
          <Facebook height={117} />
        </React.Fragment>
      );
    }

    if (data.length > 0) {
      return (
        <ListPatientRecords
          patients={data}
          onSelect={this.handleSelect}
          onDelete={this.handleToggleDeleteModal}
          onEdit={this.handleEdit}
        />
      );
    } else {
      return (
        <NoContentAlert
          message={
            "Bạn chưa có hồ sơ bệnh nhân. Vui lòng tạo mới hồ sơ để được đặt khám."
          }
        />
      );
    }
  };

  render() {
    const { isOpenDeleteModal } = this.state;
    return (
      <Fragment>
        {/* {AppId === "medpro" && this.renderFormSelectHospital()} */}

        {this.renderListPatient()}

        <Modal
          modal={isOpenDeleteModal}
          title={titleDeleteUser}
          children={contentDeleteUser}
          centered
          className="centered"
          footer
          footerConfirm="true"
          cancelText={CLOSE}
          okText={REMOVE}
          // eslint-disable-next-line react/jsx-handler-names
          onCancel={this.closeDeleteModal}
          onOk={this.handleDelete}
          toggle={this.closeDeleteModal}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { info },
    patientForm: { redirectToPatientUpdate },
    patient: { patientList, loading, data }
  } = state;
  return {
    user: info,
    patients: patientList,
    redirectToPatientUpdate,
    loading,
    data
  };
};

const mapDispatchToProps = dispatch => ({
  handleGetPatients: () => {
    dispatch(getPatientList());
  },
  handleDeletePatient: deleteFunctionCallba => {
    dispatch(deletePatient(deleteFunctionCallba));
  },
  handleSetPatientInfo: info => {
    dispatch(setPatientInfo(info));
  },
  handleSelectedPatient: patient => {
    dispatch(selectedPatientDetail(patient));
  },
  handleGetPatientDetail: id => {
    dispatch(getPatientDetail(id));
  },
  requestHospitalList: () => dispatch(requestHospitalList()),
  selectHospital: id => dispatch(selectHospital(id)),
  resetSelectedHospital: () => dispatch(resetSelectedHospital()),
  selectPatient: id => dispatch(selectedPatient(id)),
  toggleModalConfirm: () => dispatch(toggleModalConfirm())
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(PatientRecords));
