import cx from "classnames";
import { get } from "lodash";
import React from "react";
import styles from "./styles.module.scss";

const Status = ({ bookingInfo }) => {
  const status = get(bookingInfo, "status", 1);
  const bookingStatusStyle = get(bookingInfo, "bookingStatusStyle");
  const description = get(bookingInfo, "description");

  const classGreen = styles.greenNote;
  const classRed = styles.redNote;
  const classGrey = styles.greyNote;

  const classTimeNote = cx({
    [classGreen]: true,
    [classRed]: [0, 6].includes(status),
    [classGrey]: [-2].includes(status)
  });

  return (
    <div className={cx(styles.status)}>
      <span
        style={bookingStatusStyle || {}}
        className={cx(styles.description, classTimeNote)}
      >
        {description}
      </span>
    </div>
  );
};

export default Status;
