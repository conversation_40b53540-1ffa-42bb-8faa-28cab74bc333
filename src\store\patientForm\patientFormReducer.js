/* eslint-disable no-extra-boolean-cast */
import * as types from "~/store/patientForm/patientFormType";
import moment from "moment";
import { get } from "lodash";

const initialState = {
  loading: false,
  error: "",
  cmnd: "",
  info: {
    id: { value: 0 },
    msbn: { value: "" },
    // isMedicalBillCreated: { value: false },
    fullname: { value: "" },
    name: { value: "" },
    surname: { value: "" },
    birthdate: { value: "" },
    birthday: { value: 0 },
    birthmonth: { value: 0 },
    birthyear: { value: "" },
    mobile: { value: "" },
    cmnd: { value: "" },
    email: { value: "" },
    profession_id: { value: 0 },
    sex: { value: -1 },
    country_code: { value: "VIE" },
    dantoc_id: { value: "medpro_1" },
    city_id: { value: 0 },
    district_id: { value: 0 },
    ward_id: { value: 0 },
    address: { value: "" },
    relative_name: { value: "" },
    relative_mobile: { value: "" },
    relative_email: { value: "" },
    relative_type_id: { value: "" },
    bv_id: { value: "" },
    isUpdateFull: { value: true }
  },
  infoConfirm: {},
  isModal: false,
  redirectToChooseCalendar: false,
  redirectToChooseProfile: false,
  redirectToPatientUpdate: false,
  redirectToChooseSubject: false,
  urlRedirectAfterCreatePatient: "",
  isCreateWithSuggestInfo: false,
  checkPatientBeforeCreate: {
    newPatient: null,
    listOldPatient: []
  },
  disableCreate: false
};

const setInfo = info => {
  let birthdate;
  if (info.birthdate) {
    if (info.birthdate.length > 10) {
      birthdate = moment(info.birthdate);
    } else {
      birthdate = moment(info.birthdate, "DD/MM/YYYY");
    }
  } else {
    birthdate = moment("0/0/0");
  }

  const relation = () => {
    if (get(info, "relation")) {
      return {
        relative_name: { value: get(info, "relation.relative_name", "") },
        relative_mobile: { value: get(info, "relation.relative_mobile", "") },
        relative_email: { value: get(info, "relation.relative_email", "") },
        relative_type_id: { value: get(info, "relation.relative_type_id", "") }
      };
    } else {
      return {
        relative_name: { value: get(info, "relative_name", "") },
        relative_mobile: { value: get(info, "relative_mobile", "") },
        relative_email: { value: get(info, "relative_email", "") },
        relative_type_id: { value: get(info, "relative_type_id", "") }
      };
    }
  };

  return {
    id: { value: info.id },
    msbn: { value: info.patientCode ? info.patientCode : "" },
    // isMedicalBillCreated: { value: info.isMedicalBillCreated },
    name: { value: info.name?.toUpperCase() },
    surname: { value: info.surname?.toUpperCase() },
    fullname: {
      value: info.surname?.toUpperCase() + " " + info.name?.toUpperCase()
    },
    birthday: { value: birthdate.isValid() ? birthdate.format("D") : 0 },
    birthmonth: { value: birthdate.isValid() ? birthdate.format("M") : 0 },
    birthyear: {
      value: birthdate.isValid() ? birthdate.format("Y") : info.birthyear
    },
    mobile: { value: info.mobile },
    cmnd: { value: info.cmnd !== null ? info.cmnd : "" },
    email: { value: get(info, "email", "") },
    profession_id: {
      value: info.profession_id !== null ? info.profession_id : ""
    },
    sex: { value: info.sex > -1 ? info.sex : -1 },
    country_code: { value: get(info, "country.code", "") || "VIE" },
    dantoc_id: {
      value:
        info.dantoc_id === null || info.dantoc_id === "Kinh"
          ? "medpro_1"
          : info.dantoc_id
    },
    city_id: { value: !!info.city_id ? info.city_id : "" },
    district_id: { value: !!info.district_id ? info.district_id : "" },
    ward_id: { value: !!info.ward_id ? info.ward_id : "" },
    address: { value: !!info.address ? info.address : "" },
    ...relation(),
    isUpdateFull: { value: info.isUpdateFull }
  };
};

function extractBirthParts(birthdateStr) {
  const date = moment(birthdateStr, "DD/MM/YYYY", true);
  if (!date.isValid()) return [null, null, null];
  const day = date
    .date()
    .toString()
    .padStart(2, "0");
  const month = (date.month() + 1).toString().padStart(2, "0");
  const year = date.year().toString();
  return [day, month, year];
}

export default function patientForm(state = initialState, action = {}) {
  const mergeState = {
    ...state,
    info: {
      ...state.info,
      ...action.payload
    }
  };
  switch (action.type) {
    case types.PATIENT_FORM_SAVE_FIELD:
      return mergeState;

    case types.CREATE_PATIENT_FORM_REQUEST:
      return {
        ...state,
        disableCreate: true
        // loading: true
      };
    case types.CREATE_PATIENT_FORM_SUCCESS:
      return {
        ...state,
        loading: false,
        info: { ...initialState.info },
        disableCreate: false
      };

    case types.CREATE_PATIENT_FORM_FAILURE:
      return {
        ...state,
        disableCreate: false
      };

    case types.DETAIL_PATIENT_FORM_REQUEST:
      return {
        ...state
        // loading: true
      };
    case types.DETAIL_PATIENT_FORM_SUCCESS:
      return {
        ...state,
        loading: false,
        info: setInfo(action.info),
        redirectToPatientUpdate: true
      };
    case types.SET_PATIENT_INFO:
      return {
        ...state,
        info: setInfo(action.info),
        // redirectToPatientUpdate: true,
        loading: false,
        isCreateWithSuggestInfo: true
      };
    case types.CREATE_PATIENT_OVER_MAX:
      return {
        ...state,
        loading: false,
        isModal: true,
        redirectToChooseProfile: true
      };
    case types.CREATE_PATIENT_OVER_MAX_CLOSE_MODAL:
      return {
        ...state,
        isModal: false
      };

    case types.RESET_REDIRECT_PATIENT_FORM:
      return {
        ...state,
        redirectToChooseCalendar: false,
        redirectToChooseProfile: false,
        redirectToPatientUpdate: false,
        redirectToChooseSubject: false
      };
    case types.RESET_PATIENT_FORM:
      return {
        ...state,
        info: { ...initialState.info },
        infoConfirm: { modalConfirm: false },
        disableCreate: false
      };
    case types.RESET_DEFAULT_COUNTRY:
      return {
        ...state,
        info: {
          ...state.info,
          country_code: { ...state.info.country_code, value: action.id }
        }
      };
    case types.RESET_DEFAULT_NATION:
      return {
        ...state,
        info: {
          ...state.info,
          dantoc_id: { ...state.info.dantoc_id, value: action.id }
        }
      };
    case types.URL_REDIRECT_AFTER_CREATE_PATIENT:
      return {
        ...state,
        urlRedirectAfterCreatePatient: action.payload
      };

    case types.CHECK_PATIENT_BEFORE_CREATE: {
      return {
        ...state,
        checkPatientBeforeCreate: {
          ...state.checkPatientBeforeCreate,
          newPatient: action.newPatient,
          listOldPatient: action.listOldPatient
        }
      };
    }

    case types.GET_INFO_PATIENT_CMND:
      return {
        ...state,
        cmnd: action.payload,
        loading: true
      };
    case types.GET_INFO_PATIENT_CMND_CONFIRM_SUCCESS: {
      const [splDay, splMonth, splYear] = extractBirthParts(
        action.payload?.birthdate
      );
      const mapInfo = {
        fullname: { value: action.payload?.fullname },
        birthdate: { value: action.payload?.birthdate },
        cccd: { value: action.payload?.cccd },
        district_id: { value: action.payload?.district_id },
        district_name: { value: action.payload?.districtName },
        ward_id: { value: action.payload?.ward_id },
        ward_name: { value: action.payload?.wardName },
        city_id: { value: action.payload?.city_id },
        city_name: { value: action.payload?.cityName },
        address: { value: action.payload?.address },
        sex: { value: action.payload?.sex },
        birthday: { value: splDay },
        birthmonth: { value: splMonth },
        birthyear: { value: splYear },
        modalConfirm: true
      };
      return {
        ...state,
        infoConfirm: { ...state.infoConfirm, ...mapInfo },
        loading: false
      };
    }
    case types.GET_INFO_PATIENT_CMND_SUCCESS: {
      let infoConfirm;
      if (action?.payload?.infoConfirm) {
        const [splDay, splMonth, splYear] = extractBirthParts(
          action?.payload?.infoConfirm?.birthdate?.value
        );
        infoConfirm = {
          ...action?.payload?.infoConfirm,
          birthday: { value: splDay },
          birthmonth: { value: splMonth },
          birthyear: { value: splYear }
        };
      } else {
        infoConfirm = state.infoConfirm;
      }
      return {
        ...state,
        info: { ...state.info, ...infoConfirm },
        infoConfirm: { modalConfirm: false },
        loading: false
      };
    }
    case types.GET_INFO_PATIENT_CMND_CONFIRM_CANCEL: {
      return {
        ...state,
        infoConfirm: { modalConfirm: false },
        loading: false
      };
    }
    case types.GET_INFO_PATIENT_CMND_FAILURE:
      return {
        ...state,
        error: action.payload.message,
        loading: false
      };

    default:
      return state;
  }
}
