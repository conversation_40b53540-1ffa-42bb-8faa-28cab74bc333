import {
  MDBModalBody,
  MDBModalHeader,
  MDBModal,
  MDBModalFooter,
  MDBBtn,
  MDBSpinner,
  MDBIcon
} from "mdbreact";
import React, { useEffect, useRef } from "react";
import styles from "./style.module.scss";
import moment from "moment";
import { get } from "lodash";

const ModalCancelInfo = ({ isOpen, toggle, cancelInfo, loading }) => {
  // #region State
  const timerRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      // Initialize modal when opened
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isOpen]);

  const renderCancelInfo = () => {
    const username = get(cancelInfo, "userAction.username", "");
    const fullname = get(cancelInfo, "userAction.fullname", "");
    const cancelledBy = get(cancelInfo, "cancelledBy", "");
    return (
      <div className={styles.cancelInfoW<PERSON>per}>
        <div className={styles.cancelInfoItem}>
          <span className={styles.cancelLabel}>
            <MDBIcon icon="user" className="mr-2" />
            Người thao tác:
          </span>
          <span className={styles.cancelValue}>
            {`${username}` + ` - ${fullname}` + ` - ${cancelledBy}`}
          </span>
        </div>

        <div className={styles.cancelInfoItem}>
          <label className={styles.cancelLabel}>
            <MDBIcon icon="calendar-times" className="mr-2" />
            Thời gian:
          </label>
          <span className={styles.cancelValue}>
            {cancelInfo?.cancelledAt
              ? moment(cancelInfo?.cancelledAt).format("HH:mm, DD/MM/YYYY")
              : ""}
          </span>
        </div>

        <div className={styles.cancelInfoItem}>
          <label className={styles.cancelLabel}>
            <MDBIcon icon="comment-alt" className="mr-2" />
            Lý do:
          </label>
          <ul className={styles.cancelReason1}>
            {cancelInfo?.reasonsData?.map((item, index) => (
              <li key={item.id} className={styles.cancelReasonItem}>
                {item.name + (item.content && ` - ${item.content}`)}
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  };

  const renderBankingInfo = () => {
    if (!cancelInfo || !cancelInfo.accountName) {
      return null;
    }

    const { accountName, bankCode, bankName, bankNumber } = cancelInfo;

    return (
      <div className={styles.bankingInfoWrapper}>
        <h6 className={styles.sectionTitle}>
          <MDBIcon icon="university" className="mr-2" />
          Thông tin tài khoản hoàn tiền
        </h6>

        <div className={styles.bankingInfoGrid}>
          <div className={styles.bankingInfoItem}>
            <span className={styles.bankingLabel}>Tên tài khoản:</span>
            <span className={styles.bankingValue}>{accountName}</span>
          </div>

          <div className={styles.bankingInfoItem}>
            <span className={styles.bankingLabel}>Mã ngân hàng:</span>
            <span className={styles.bankingValue}>{bankCode}</span>
          </div>

          <div className={styles.bankingInfoItem}>
            <span className={styles.bankingLabel}>Tên ngân hàng:</span>
            <span className={styles.bankingValue}>{bankName}</span>
          </div>

          <div className={styles.bankingInfoItem}>
            <span className={styles.bankingLabel}>Số tài khoản:</span>
            <span className={styles.bankingValue}>{bankNumber}</span>
          </div>
        </div>
      </div>
    );
  };

  const renderBookingInfo = () => {
    const partnerName = get(cancelInfo, "booking.partner.name", "");
    const { name, surname } = get(cancelInfo, "booking.patient", {});
    const description = get(cancelInfo, "booking.description", "");
    return (
      <div className={styles.bookingInfoWrapper}>
        <h6 className={styles.sectionTitle}>
          <MDBIcon icon="file-alt" className="mr-2" />
          Thông tin
        </h6>

        <div className={styles.bookingInfoGrid}>
          <div className={styles.bookingInfoItem}>
            <span className={styles.bookingLabel}>Bệnh nhân:</span>
            <span className={styles.bookingValue}>{surname + " " + name}</span>
          </div>

          <div className={styles.bookingInfoItem}>
            <span className={styles.bookingLabel}>Bệnh viện:</span>
            <span className={styles.bookingValue}>{partnerName}</span>
          </div>

          <div className={styles.bookingInfoItem}>
            <span className={styles.bookingLabel}>Ngày khám:</span>
            <span className={styles.bookingValue}>
              {cancelInfo?.bookingDate
                ? moment(cancelInfo?.bookingDate).format("HH:mm, DD/MM/YYYY")
                : ""}
            </span>
          </div>

          <div className={styles.bookingInfoItem}>
            <span className={styles.bookingLabel}>Trạng thái:</span>
            <span className={styles.bookingValue}>{description}</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <MDBModal isOpen={isOpen} centered>
      <MDBModalHeader className={styles.headerModal}>
        <MDBIcon icon="info-circle" className="mr-2" />
        Thông tin phiếu - {cancelInfo?.bookingCode}
      </MDBModalHeader>

      <MDBModalBody className={styles.bodyModal}>
        {!cancelInfo ? (
          <div className={styles.noCancelInfo}>
            <MDBIcon icon="info-circle" className="mr-2" />
            Không có thông tin
          </div>
        ) : (
          <div className={loading ? styles.loadingBlur : ""}>
            {renderBookingInfo()}

            <div className={styles.divider} />

            <div className={styles.cancelSection}>
              <h6 className={styles.sectionTitle}>
                <MDBIcon icon="exclamation-triangle" className="mr-2" />
                Chi tiết
              </h6>
              {renderCancelInfo()}
            </div>

            {renderBankingInfo() && (
              <>
                <div className={styles.divider} />
                {renderBankingInfo()}
              </>
            )}
          </div>
        )}
      </MDBModalBody>

      <MDBModalFooter>
        <MDBBtn
          size="sm"
          color="primary"
          className={styles.btnAct}
          onClick={toggle}
        >
          <MDBIcon icon="times" className="mr-1" />
          Đóng
        </MDBBtn>
      </MDBModalFooter>
    </MDBModal>
  );
};

export default ModalCancelInfo;
