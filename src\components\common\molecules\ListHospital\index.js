import cx from "classnames";
import { get } from "lodash";
import {
  MDBAnimation,
  MDBListGroup,
  MDBListGroupItem,
  MDBSpinner
} from "mdbreact";
import React, { Component, Fragment } from "react";
import { connect } from "react-redux";
import NotFoundResult from "~/components/common/atoms/NotFoundResult";
import Modal from "~/components/common/molecules/Modal";
import { requestAllFeatures } from "~/store/features/featuresActions";
import * as hospitalActions from "~/store/hospital/hospitalActions";
import { selectHospital } from "~/store/totalData/actions";
import styles from "./style.module.scss";

class ListHospital extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
      message: "",
      isShowMessage: false,
      currentPage: 0, // Trang hiện tại (bắt đầu từ 0)
      itemsPerPage: 30 // Số bệnh viện hiển thị trên mỗi trang
    };
  }

  handleChooseHospital = item => {
    const message = get(item, "message", "");
    if (message) {
      this.setState({
        isShowMessage: true,
        message
      });
    } else {
      this.props.selectPartner(item.partnerId, item);
      this.props.selectHospital(item.partnerId);
      this.props.switchSelectFlow(true);
      this.props.toggleLoading(true);
      this.props.requestAllFeatures();
    }
  };

  goToPage = page => {
    this.setState({ currentPage: page });
  };

  // Hàm chuyển đến trang trước
  goToPreviousPage = e => {
    e.stopPropagation();
    this.setState(prevState => ({
      currentPage: Math.max(prevState.currentPage - 1, 0)
    }));
  };

  // Hàm chuyển đến trang sau
  goToNextPage = e => {
    e.stopPropagation();
    const { hospitalList, listHospitalFilter } = this.props;
    const { itemsPerPage } = this.state;
    const displayHospitals =
      listHospitalFilter?.length > 0 ? listHospitalFilter : hospitalList;
    const pageCount = Math.ceil(displayHospitals.length / itemsPerPage);

    this.setState(prevState => ({
      currentPage: Math.min(prevState.currentPage + 1, pageCount - 1)
    }));
  };

  renderHospitalList = hospitalList => {
    const { currentPage, itemsPerPage } = this.state;

    // Tính toán danh sách bệnh viện hiển thị trên trang hiện tại
    const startIndex = currentPage * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedHospitals = hospitalList.slice(startIndex, endIndex);

    if (hospitalList.length === 0) {
      return <NotFoundResult style_result="h5" />;
    }

    const chooseHospitalListNode = paginatedHospitals?.map(item => {
      const { partnerId, status, image, name, address, deliveryMessage } = item;

      if (partnerId === "medpro" || status === 0) {
        return null; // Bỏ qua các bệnh viện không hợp lệ
      }

      const imageErrorSrc = "/assets/img/logo.png";
      const urlImage = image || imageErrorSrc;
      const deliStatus = deliveryMessage || "";

      return (
        <MDBListGroupItem
          key={`hospital-${partnerId}`}
          onClick={e => {
            e.stopPropagation();
            this.handleChooseHospital(item);
          }}
        >
          <div className={styles.item_hospital}>
            <img src={urlImage} alt={name} loading="lazy" />
            <div className={styles.text_hospital}>
              <div className={styles.name_hospital}>
                {name}
                <em className={styles.deliStatus}> {deliStatus}</em>
              </div>
              <div className={styles.address_hospital}>{address}</div>
            </div>
          </div>
        </MDBListGroupItem>
      );
    });

    return <Fragment>{chooseHospitalListNode}</Fragment>;
  };

  // Hàm render các nút phân trang
  renderPagination = totalItems => {
    const { itemsPerPage, currentPage } = this.state;
    const pageCount = Math.ceil(totalItems / itemsPerPage);

    if (pageCount <= 1) return null;

    const pageButtons = [];
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
      // Mobile: Hiển thị tối đa 3 nút số trang
      const maxPagesToShow = 3;
      let startPage = Math.max(0, currentPage - 1);
      const endPage = Math.min(pageCount, startPage + maxPagesToShow);

      if (endPage - startPage < maxPagesToShow) {
        startPage = Math.max(0, endPage - maxPagesToShow);
      }

      for (let i = startPage; i < endPage; i++) {
        pageButtons.push(
          <button
            key={i}
            className={cx(styles.pageButton, {
              [styles.active]: i === currentPage
            })}
            onClick={e => {
              e.stopPropagation();
              e.preventDefault();
              this.goToPage(i);
            }}
          >
            {i + 1}
          </button>
        );
      }

      if (startPage > 0) {
        pageButtons.unshift(
          <span key="start-ellipsis" className={styles.ellipsis}>
            ...
          </span>
        );
      }
      if (endPage < pageCount) {
        pageButtons.push(
          <span key="end-ellipsis" className={styles.ellipsis}>
            ...
          </span>
        );
      }
    } else {
      // Desktop: Hiển thị 5 nút xung quanh trang hiện tại + trang cuối
      const maxPagesToShow = 5; // Tổng cộng 5 nút (2 trước + trang hiện tại + 2 sau)
      const halfPages = Math.floor(maxPagesToShow / 2); // 2 trang trước/sau
      let startPage = Math.max(0, currentPage - halfPages);
      const endPage = Math.min(pageCount, startPage + maxPagesToShow);

      // Điều chỉnh nếu ở gần cuối danh sách
      if (endPage - startPage < maxPagesToShow) {
        startPage = Math.max(0, endPage - maxPagesToShow);
      }

      // Thêm nút trang đầu nếu không nằm trong khoảng hiển thị
      if (startPage > 0) {
        pageButtons.push(
          <button
            key={0}
            className={cx(styles.pageButton, {
              [styles.active]: currentPage === 0
            })}
            onClick={e => {
              e.stopPropagation();
              this.goToPage(0);
            }}
          >
            1
          </button>
        );
        if (startPage > 1) {
          pageButtons.push(
            <span key="start-ellipsis" className={styles.ellipsis}>
              ...
            </span>
          );
        }
      }

      // Thêm các nút trong khoảng startPage đến endPage
      for (let i = startPage; i < endPage; i++) {
        pageButtons.push(
          <button
            key={i}
            className={cx(styles.pageButton, {
              [styles.active]: i === currentPage
            })}
            onClick={e => {
              e.stopPropagation();
              this.goToPage(i);
            }}
          >
            {i + 1}
          </button>
        );
      }

      // Thêm dấu ba chấm và trang cuối nếu cần
      if (endPage < pageCount) {
        if (endPage < pageCount - 1) {
          pageButtons.push(
            <span key="end-ellipsis" className={styles.ellipsis}>
              ...
            </span>
          );
        }
        pageButtons.push(
          <button
            key={pageCount - 1}
            className={cx(styles.pageButton, {
              [styles.active]: pageCount - 1 === currentPage
            })}
            onClick={e => {
              e.stopPropagation();
              this.goToPage(pageCount - 1);
            }}
          >
            {pageCount}
          </button>
        );
      }
    }

    return (
      <div className={styles.pagination}>
        <button
          className={styles.pageButton}
          onClick={this.goToPreviousPage}
          disabled={currentPage === 0}
        >
          Trước
        </button>
        {pageButtons}
        <button
          className={styles.pageButton}
          onClick={this.goToNextPage}
          disabled={currentPage === pageCount - 1}
        >
          Sau
        </button>
      </div>
    );
  };

  renderModalChooseHospital = () => {
    return (
      <Modal
        iconTitle={<i className="fal fa-bell" />}
        modal={this.state.isShowMessage}
        toggle={this.toggleModalChoosePatient}
        title="Thông báo"
        children={this.state.message}
        centered
        className="centered"
      />
    );
  };

  toggleModalChoosePatient = () => {
    this.setState({
      isShowMessage: !this.state.isShowMessage
    });
  };

  componentWillUnmount() {
    this.setState({
      loading: false
    });
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.listHospitalFilter !== this.props.listHospitalFilter ||
      prevProps.hospitalList !== this.props.hospitalList
    ) {
      this.resetCurrentPage();
    }
  }

  resetCurrentPage = () => {
    if (this.state.currentPage !== 0) {
      this.setState({ currentPage: 0 });
    }
  };

  render() {
    const { type, listHospitalFilter, hospitalList } = this.props;
    const displayHospitals =
      listHospitalFilter?.length > 0 ? listHospitalFilter : hospitalList;
    const contentLoader =
      type === "desktop" ? (
        <MDBAnimation type="fadeIn" className={styles.wapper_loading}>
          <div className="loading absolute">
            <MDBSpinner big />
          </div>
        </MDBAnimation>
      ) : null;
    return (
      <Fragment>
        {!listHospitalFilter || listHospitalFilter.loading ? (
          contentLoader
        ) : (
          <MDBAnimation type="fadeIn" style={{ minHeight: "250px" }}>
            <>
              <MDBListGroup
                className={cx(
                  styles.list_group,
                  styles.hospital,
                  listHospitalFilter && listHospitalFilter.length === 1
                    ? "justify-content-center"
                    : ""
                )}
              >
                {this.renderHospitalList(displayHospitals)}
                {this.renderPagination(displayHospitals.length)}
              </MDBListGroup>
            </>
          </MDBAnimation>
        )}
        {this.renderModalChooseHospital()}
      </Fragment>
    );
  }
}
const mapStateToProps = state => {
  const {
    features: { selectedFeature },
    global: {
      device: { type }
    },
    hospital: {
      hospitalList: { data: hospitalList }
    }
  } = state;
  return {
    type,
    hospitalList,
    selectedFeature
  };
};

const mapDispatchToProps = dispatch => ({
  selectPartner: (id, hospital) => {
    dispatch(selectHospital(id, hospital));
  },
  selectHospital: id => {
    dispatch(hospitalActions.selectHospital(id));
  },
  switchSelectFlow: status => {
    dispatch(hospitalActions.switchSelectFlow(status));
  },
  requestAllFeatures: () => {
    dispatch(requestAllFeatures());
  }
});
export default connect(mapStateToProps, mapDispatchToProps)(ListHospital);
