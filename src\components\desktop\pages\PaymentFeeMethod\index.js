/* eslint-disable react/jsx-handler-names */
import cx from "classnames";
import { get } from "lodash";
// import { Link } from 'react-router-dom';
import {
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBContainer,
  MDBListGroup,
  MDBListGroupItem,
  MDBRow,
  MDBSpinner
} from "mdbreact";
import React, { Component } from "react";
import PKHBtn from "~/components/common/atoms/Button";
import TagName from "~/components/common/atoms/TagName";
import ListPaymentMethod from "~/components/desktop/molecules/ListPaymentMethod";
import PatientFeeInformation from "~/components/desktop/molecules/PatientFeeInformation";
import { getFormatMoney } from "~/utils/getFormatMoney";
import styles from "./style.module.scss";

class PaymentMethod extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedPaymentMethodGroupId: 0
    };
    this.scrollToDiv = React.createRef();
  }

  selectPaymentMethodGroup = group => {
    this.props.resetPaymentMethod();
    if (
      group.methodId !== this.state.selectedPaymentMethodGroupId &&
      group.paymentTypes.length === 1
    ) {
      this.props.handleSelectedMethod(group.paymentTypes[0], group.methodId);
    }
    this.setState({
      selectedPaymentMethodGroupId:
        group.methodId !== this.state.selectedPaymentMethodGroupId
          ? group.methodId
          : 0
    });
  };

  toggleShowPaymentConfirm = () => {
    const { selectedMethod } = this.props;
    if (selectedMethod.code) {
      this.props.toggleShowPaymentConfirm();
    } else {
      this.props.toggleAlert();
    }
  };

  renderInfoFeePayment = data => {
    const { selectedFee } = this.props;
    const content = get(selectedFee, "content");
    const feeCode = get(selectedFee, "fee_code");
    const amount = get(selectedFee, "amount");

    return (
      <MDBListGroup className={styles.list_group}>
        <MDBListGroupItem>
          <div className={styles.column1}>Nội dung:</div>
          <div className={styles.column2}>{content}</div>
        </MDBListGroupItem>
        <MDBListGroupItem>
          <div className={styles.column1}>Số hóa đơn:</div>
          <div className={styles.column2}>{feeCode}</div>
        </MDBListGroupItem>
        <MDBListGroupItem>
          <div className={styles.column1}>Số tiền thanh toán:</div>
          <div className={styles.column2}>
            <strong>{getFormatMoney(amount)} VNĐ</strong>
          </div>
        </MDBListGroupItem>
      </MDBListGroup>
    );
  };

  componentDidUpdate() {
    const { selectedMethod } = this.props;
    if (Object.keys(selectedMethod).length > 1) {
      if (this.scrollToDiv.current) {
        const {
          top,
          bottom
        } = this.scrollToDiv.current.getBoundingClientRect();
        if (top < 0 || bottom > window.innerHeight) {
          this.scrollToDiv.current.scrollIntoView({
            behavior: "smooth",
            block: "end"
          });
        }
      }
    }
  }

  render() {
    const {
      data,
      selectedMethod,
      handleSelectedMethod,
      loadingReserveBooking,
      loadingPaymentFee,
      price,
      handleGoBack,
      patient
    } = this.props;

    const subTotal = get(price, "subTotal", 0);
    const totalFee = get(price, "totalFee", 0);
    const grandTotal = get(price, "grandTotal", 0);

    const objCheckNextStep = Object.create(null);
    if (Object.keys(selectedMethod).length > 1) {
      objCheckNextStep.nextStep = true;
    }

    if (loadingReserveBooking || loadingPaymentFee) {
      return (
        <div className="loading">
          <MDBSpinner big crazy tag="div" />
        </div>
      );
    }

    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="3">
              <PatientFeeInformation patient={patient} />
            </MDBCol>
            <MDBCol md="12" lg="9">
              <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between",
                      "title_header_mobile"
                    ]}
                  >
                    <span>Chọn phương thức thanh toán</span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody className={styles.card_body}>
                  <MDBRow>
                    <MDBCol md="6">
                      <div className={styles.group_payment}>
                        <ListPaymentMethod
                          allPaymentMethodGroups={data}
                          handleSelectedMethod={handleSelectedMethod}
                          selectedMethod={selectedMethod}
                          selectPaymentMethodGroup={
                            this.selectPaymentMethodGroup
                          }
                          selectedPaymentMethodGroupId={
                            this.state.selectedPaymentMethodGroupId
                          }
                        />
                      </div>
                    </MDBCol>
                    <MDBCol md="6">
                      <div className={styles.list_group_payment}>
                        <div className={styles.sub_title}>
                          <i className="fal fa-info-square" />
                          Thông tin thanh toán
                        </div>
                        {this.renderInfoFeePayment()}
                        <div className={styles.total_payment}>
                          <MDBListGroup className={styles.list_group}>
                            <MDBListGroupItem>
                              Tiền thanh toán:
                              <strong>{getFormatMoney(subTotal)} VNĐ</strong>
                            </MDBListGroupItem>
                            <MDBListGroupItem>
                              Phí tiện ích:
                              <strong>
                                {totalFee === 0
                                  ? "0 VNĐ"
                                  : getFormatMoney(totalFee) + " VNĐ"}{" "}
                              </strong>
                            </MDBListGroupItem>
                            <MDBListGroupItem>
                              TỔNG CỘNG:
                              <strong>{getFormatMoney(grandTotal)} VNĐ</strong>
                            </MDBListGroupItem>
                          </MDBListGroup>
                        </div>
                      </div>
                    </MDBCol>
                  </MDBRow>
                  <div className={styles.next_prev}>
                    <PKHBtn backdesktop="backdesktop" onClick={handleGoBack}>
                      Quay lại
                    </PKHBtn>
                    <PKHBtn
                      create="create"
                      buttonArrow="buttonArrow"
                      {...objCheckNextStep}
                      onClick={this.toggleShowPaymentConfirm}
                    >
                      Thanh toán
                    </PKHBtn>
                  </div>
                </MDBCardBody>
              </MDBCard>
              <div ref={this.scrollToDiv} />
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </div>
    );
  }
}

export default PaymentMethod;
