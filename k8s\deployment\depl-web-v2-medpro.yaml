apiVersion: apps/v1
kind: Deployment
metadata:
  name:  medpro-web-v2
  namespace: medpro-web
  labels:
    app: medpro

spec:
  replicas: 4
  selector:
    matchLabels:
      app: medpro
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: medpro
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: role
                  operator: In
                  values:
                    - "worker"
      serviceAccountName: sva-medpro
      containers:
      - image:  repo:8083/medpro-web-v2120-testing
        name:  medpro-web-v2
        resources:
          requests:
            cpu: "150m"
            memory: "200M"
          limits:
            cpu: "1000m"
            memory: "2000M"
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 10  
        ports:
        - containerPort:  3000
          name:  port
      restartPolicy: Always
