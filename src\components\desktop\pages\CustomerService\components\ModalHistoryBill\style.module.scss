.ModalHistoryBill {
  min-width: 940px;
  max-width: 1024px !important;
  & > div {
    border-radius: 10px !important;
  }
  table {
    border-radius: 10px;
    border-collapse: collapse;
    border: none;
    background-color: #f5f5f5;
    width: 100%;

    th,
    td {
      p {
        margin-bottom: 3px;
      }
      border: none;
      padding: 5px;
      &:first-child,
      &:last-child {
        text-align: center;
      }
      border: 1px solid white;

      padding: 5px;
      .label {
        font-weight: bold;
      }
      .value {
        margin-left: 5px;
      }
      .noRow {
        padding: 15px;
      }
    }

    th {
      &:first-child {
        border-radius: 5px 0 0 0;
      }

      &:last-child {
        border-radius: 0 5px 0 0;
      }

      font-size: 16px;
      font-weight: bold;
    }

    tr {
      &:last-child td {
        &:first-child {
          border-radius: 0 0 0 5px;
        }

        &:last-child {
          border-radius: 0 0 5px 0;
        }
      }

      &:hover td {
        background-color: gold;
      }
    }
  }
}
