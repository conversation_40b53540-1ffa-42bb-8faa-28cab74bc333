import cx from "classnames";
import _ from "lodash";
import {
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBCollapse,
  MDBContainer,
  MDBFormInline,
  MDBInput,
  MDBRow,
  MDBTable,
  MDBTableBody,
  MDBTableHead
} from "mdbreact";
import React, { Component, Fragment } from "react";
import PKHBtn from "~/components/common/atoms/Button";
import TagName from "~/components/common/atoms/TagName";
import Modal from "~/components/common/molecules/Modal";
import PatientInfomationBooking from "~/components/desktop/molecules/PatientInfomationBooking";
import {
  SERVICE_TYPE_BOTH,
  SERVICE_TYPE_INSURANCE,
  SERVICE_TYPE_NO_INSURANCE
} from "~/utils/constants";
import { getFormatMoney } from "~/utils/getFormatMoney";
import partnerIdDomain from "~/utils/partner";
import styles from "./style.module.scss";

const FalseS = "false_";
const TrueS = "true_";

class ChooseService extends Component {
  constructor(props) {
    super(props);
    this.scrollToDiv = React.createRef();
    this.state = {
      missingInfo: false,
      camKet: false,
      service: "",
      radio: [],
      error: "",
      collapseID: "",
      btnNext: false
    };
  }

  toggeModalMissInformation = () => {
    this.setState({ missingInfo: !this.state.missingInfo });
  };

  componentDidUpdate() {
    const { serviceId } = this.props;
    if (serviceId && this.scrollToDiv.current) {
      const { top, bottom } = this.scrollToDiv.current.getBoundingClientRect();
      if (top < 0 || bottom > window.innerHeight) {
        this.scrollToDiv.current.scrollIntoView({
          behavior: "smooth",
          block: "end"
        });
      }
    }
  }

  renderServiceList = () => {
    const {
      serviceList,
      serviceId,
      changeServiceType,
      selectedServiceType
    } = this.props;
    const { radio, collapseID } = this.state;
    return serviceList.map((service, i) => {
      const addonServices = _.get(service, "detail.addonServices", []);
      const id = _.get(service, "detail.id", "");

      const serviceName = _.get(service, "detail.name", "");
      const price = _.get(service, "detail.price", "");
      const serviceType = _.get(service, "detail.serviceType");

      const bookingNote = _.get(service, "detail.bookingNote", "");
      const days = _.get(service, "detail.days", "");

      const fillterSVAddOn = _.filter(
        radio,
        v => v.id.includes(serviceName) && v.id.includes("true")
      );

      const priceOfAddOn = fillterSVAddOn.reduce((init, { price }) => {
        return init + price;
      }, 0);

      return (
        <Fragment key={id}>
          <tr onClick={() => this.handleChooseService(service, id)}>
            <td>{i + 1}</td>
            <td>
              <b>{serviceName}</b>
              {days && (
                <div className={styles.node_description}>
                  Lịch khám: {days.indexOf("CN") !== 0 ? "Thứ" : ""} {days}
                </div>
              )}
              {bookingNote && (
                <div className={styles.node_description}>({bookingNote})</div>
              )}
            </td>
            <td>{getFormatMoney(price + priceOfAddOn)}đ</td>
            <td>
              <PKHBtn
                active={collapseID === `collapse${id}` ? "active" : ""}
                chosen={
                  serviceId === service.id || collapseID === `collapse${id}`
                    ? "chosen"
                    : ""
                }
                choose={collapseID !== `collapse${id}` ? "choose" : ""}
              >
                Chọn
              </PKHBtn>
            </td>
          </tr>

          <tr
            className={
              collapseID === `collapse${id}` ? styles.active : styles.hidden
            }
          >
            <td colSpan={4}>
              <MDBCollapse
                id={`collapse${id}`}
                isOpen={collapseID}
                className={styles.collapsev}
              >
                {addonServices &&
                  addonServices.map(v => {
                    const idAddOn = serviceName + "_" + v.name + "//" + v.id;

                    const checked = el => {
                      const is = _.findIndex(radio, {
                        id: el + idAddOn
                      });
                      return is > -1;
                    };

                    return (
                      <MDBRow
                        className={cx(styles.row_bhyt)}
                        key={_.uniqueId()}
                      >
                        <MDBCol md="6">
                          <span>+ {v.name}</span>
                        </MDBCol>
                        <MDBCol md="6">
                          <MDBFormInline>
                            <MDBInput
                              gap
                              onClick={this.onClick(
                                "false_" + idAddOn,
                                idAddOn,
                                v
                              )}
                              checked={checked(FalseS)}
                              label="Không"
                              type="radio"
                              id={"false_" + idAddOn}
                            />
                            <MDBInput
                              gap
                              onClick={this.onClick(
                                "true_" + idAddOn,
                                idAddOn,
                                v
                              )}
                              checked={checked(TrueS)}
                              label="Có"
                              type="radio"
                              id={"true_" + idAddOn}
                            />
                          </MDBFormInline>
                        </MDBCol>
                      </MDBRow>
                    );
                  })}
                <p
                  className={
                    this.state.error.id === id ? styles.error : styles.hiddenErr
                  }
                >
                  <em>{this.state.error.message}</em>
                </p>
              </MDBCollapse>
            </td>
          </tr>

          {service.id === serviceId && serviceType === SERVICE_TYPE_BOTH && (
            <tr>
              <td colSpan="4">
                <MDBRow className={styles.row_bhyt}>
                  <MDBCol md="6">
                    <span>Bạn có đăng ký khám BHYT?</span>
                  </MDBCol>
                  <MDBCol md="6">
                    <MDBFormInline>
                      <MDBInput
                        gap
                        onClick={() => {
                          changeServiceType(SERVICE_TYPE_NO_INSURANCE);
                          this.handleChooseService(service);
                        }}
                        checked={
                          selectedServiceType === SERVICE_TYPE_NO_INSURANCE
                        }
                        label="Không"
                        type="radio"
                        id="radio2"
                      />
                      <MDBInput
                        gap
                        onClick={() => {
                          changeServiceType(SERVICE_TYPE_INSURANCE);
                          this.handleChooseService(service);
                        }}
                        checked={selectedServiceType === SERVICE_TYPE_INSURANCE}
                        label="Có"
                        type="radio"
                        id="radio1"
                      />
                    </MDBFormInline>
                  </MDBCol>
                </MDBRow>
              </td>
            </tr>
          )}
        </Fragment>
      );
    });
  };

  toggleCamKet = () => {
    this.setState({
      camKet: !this.state.camKet
    });
  };

  dongYCamKet = () => {
    this.setState({
      camKet: false
    });

    const { service } = this.state;

    this.props.selectService(service);
  };

  renderModalCamKet = () => {
    const { camKet, service } = this.state;

    const popupContent = service.detail?.popupContent;

    const txtContent = <p className="text-justify">{popupContent}</p>;

    return (
      <Modal
        iconTitle={<i className="fal fa-bell" />}
        modal={camKet}
        title="Cam kết"
        children={txtContent}
        centered
        footer
        footerConfirm
        className="centered"
        toggle={this.toggleCamKet}
        cancelText="Hủy"
        okText="Đồng ý"
        onCancel={() => this.toggleCamKet()}
        onOk={() => this.dongYCamKet()}
      />
    );
  };

  toggleCollapse = collapseID => {
    this.setState(prevState => ({
      collapseID: prevState.collapseID !== collapseID ? collapseID : "",
      btnNext: prevState.collapseID !== collapseID ? true : !prevState.btnNext
    }));
  };

  handleChooseService = (service, id) => {
    this.setState({
      service: service
    });

    const addonServices = _.get(service, "detail.addonServices", []);

    const quantityAddon = addonServices.length;

    if (addonServices && quantityAddon > 0) {
      this.toggleCollapse(`collapse${id}`);
    } else {
      this.punyNext(service);
    }
  };

  punyNext = service => {
    if (service.detail?.serviceType === "INSURANCE_ONLY") {
      if (service.detail?.popupType === 1) {
        this.toggleCamKet();
      } else {
        this.props.selectService(service);
      }
    } else {
      this.props.selectService(service);
    }
  };

  handleNext = () => {
    const { radio, service } = this.state;
    const {
      setAddonServices,
      selectRadioServiceAddOn,
      setPriceOfSvAddOn
    } = this.props;

    const id = _.get(service, "detail.id", "");

    const addonServices = _.get(service, "detail.addonServices", []);

    const quantityAddon = addonServices.length;

    const x = _.filter(radio, v => v.id.includes(service.detail.name));

    if (quantityAddon) {
      if (x.length < quantityAddon) {
        // không hợp lệ
        this.setState({
          error: { id, message: "Vui lòng chọn đầy đủ thông tin !" }
        });
      } else {
        // hợp lệ
        this.setState({
          error: { id, message: "" }
        });

        const filter = _.filter(
          radio,
          v => v.id.includes(service.detail.name) && v.id.includes("true")
        );

        const getId = filter.map(v => v.id.split("//")[1]);

        const priceOfAddOn = filter.reduce((init, { price }) => {
          return init + price;
        }, 0);

        setPriceOfSvAddOn(priceOfAddOn);
        setAddonServices(getId);
        selectRadioServiceAddOn(filter);
        this.punyNext(service);
      }
    } else {
      this.punyNext(service);
    }
  };

  onClick = (nr, id, item) => () => {
    const { radio } = this.state;

    const isRemove = _.findIndex(radio, { id: nr });

    if (isRemove > -1) {
      radio.splice(isRemove, 1);
      this.setState({
        radio: radio
      });
    } else {
      const obj = {
        id: nr,
        idSV: item.id,
        price: item.price,
        priceText: item.priceText,
        name: item.name
      };

      if (nr.includes(TrueS + id)) {
        const isKhong = _.findIndex(radio, { id: FalseS + id });
        if (isKhong > -1) {
          radio.splice(isKhong, 1);
        }
      }
      if (nr.includes(FalseS + id)) {
        const isCo = _.findIndex(radio, { id: TrueS + id });
        if (isCo > -1) {
          radio.splice(isCo, 1);
        }
      }

      this.setState({
        radio: [...radio, obj]
      });
    }
  };

  render() {
    const { missingInfo, btnNext } = this.state;
    const { handleGoBack } = this.props;
    return (
      <div
        className={cx(
          styles.wapper_page_desktop,
          styles["wapper_page_desktop_" + partnerIdDomain]
        )}
      >
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="3">
              <PatientInfomationBooking />
            </MDBCol>
            <MDBCol md="12" lg="9">
              <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between",
                      "title_header_mobile"
                    ]}
                  >
                    <span>Vui lòng chọn dịch vụ </span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody className={styles.card_body}>
                  <MDBTable responsive className={styles.tableResponsive}>
                    <MDBTableHead>
                      <tr>
                        <th>#</th>
                        <th>Tên dịch vụ </th>
                        <th>Giá tiền</th>
                        <th />
                      </tr>
                    </MDBTableHead>
                    <MDBTableBody>{this.renderServiceList()}</MDBTableBody>
                  </MDBTable>
                </MDBCardBody>
                <div className={styles.next_prev}>
                  <PKHBtn onClick={handleGoBack}>Quay lại</PKHBtn>

                  {btnNext ? (
                    <PKHBtn
                      create="create"
                      buttonArrow="buttonArrow"
                      nextStep
                      onClick={this.handleNext}
                    >
                      Tiếp tục
                    </PKHBtn>
                  ) : null}
                </div>
              </MDBCard>
              <div ref={this.scrollToDiv} />
            </MDBCol>
          </MDBRow>
          <Modal
            modal={missingInfo}
            iconTitle={<i className="fal fa-bell" />}
            title="Thông báo"
            children="Vui lòng chọn Dịch vụ!"
            toggle={this.toggeModalMissInformation}
            centered
            className="centered"
          />
        </MDBContainer>
        {this.renderModalCamKet()}
      </div>
    );
  }
}

export default ChooseService;
