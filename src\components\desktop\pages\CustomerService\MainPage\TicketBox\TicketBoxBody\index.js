import React, { Component, Fragment } from "react";
import styles from "./style.module.scss";
import { MDBAlert } from "mdbreact";

// components
import TicketCard from "../../../components/TicketCard";

class TicketBoxBody extends Component {
  renderTicketList = (ticketList, loading) => {
    if (!loading) {
      if (!ticketList || ticketList.length === 0) {
        return <MDBAlert>Không tìm thấy phiếu hỗ trợ trên tài khoản.</MDBAlert>;
      } else {
        return ticketList.map(ticket => (
          <div key={ticket._id}>
            <TicketCard ticketInfo={ticket} />
          </div>
        ));
      }
    } else {
      return (
        <Fragment>
          <div className={styles.loader}>loading...</div>
          <div className={styles.loadingText}>
            <span>đang tìm kiếm dữ liệu...</span>
          </div>
        </Fragment>
      );
    }
  };

  render() {
    return (
      <Fragment>
        {/* <div className={styles.search} style={style}>
          ABC
        </div> */}
        <div className={styles.Box_Wrapper}>
          {this.renderTicketList(this.props.ticketList, this.props.loading)}
        </div>
      </Fragment>
    );
  }
}
export default TicketBoxBody;
