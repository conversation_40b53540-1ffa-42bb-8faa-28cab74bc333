/* eslint-disable no-useless-escape */
export const getReplaceUTF8 = (str = "") => {
  str = str.replace(/[ăâáàảạãắẳẵằặấầẩẫậ]/gi, "a");
  str = str.replace(/[éèẻẽẹêếềểễệ]/gi, "e");
  str = str.replace(/đ/gi, "d");
  str = str.replace(/[íìỉĩị]/gi, "i");
  str = str.replace(/[óòỏõọôốồổỗộơớờởỡợ]/gi, "o");
  str = str.replace(/[úùủũụưứừữửự]/gi, "u");
  str = str.replace(/ýỳỷỹỵ/gi, "y");
  return str;
};

const kyTuDacBiet = /[`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi;

export const validateName = value => {
  if (!value) return "";
  value = value.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  value = value.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  value = value.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  value = value.replace(/o|ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  value = value.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  value = value.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  value = value.replace(/đ|d/g, "d");
  value = value.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // Huyền sắc hỏi ngã nặng
  value = value.replace(/\u02C6|\u0306|\u031B/g, ""); // Â, Ê, Ă, Ơ, Ư

  return value;
};

export const validateValue = value => {
  value = value.replace(kyTuDacBiet, "");
  value = value.replace(/\s+|\s+$/g, " ");
  value = value.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  value = value.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  value = value.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  value = value.replace(/o|ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  value = value.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  value = value.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  value = value.replace(/đ|d/g, "d");
  value = value.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // Huyền sắc hỏi ngã nặng
  value = value.replace(/\u02C6|\u0306|\u031B/g, ""); // Â, Ê, Ă, Ơ, Ư

  return value;
};

export const normalizeBankAccountName = value => {
  if (!value.trim()) return "";
  value = value.replace(/[`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/0-9]/gi, "");
  value = value.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
  value = value.replace(/đ/gi, "d");
  value = value.toUpperCase();

  return value;
};
