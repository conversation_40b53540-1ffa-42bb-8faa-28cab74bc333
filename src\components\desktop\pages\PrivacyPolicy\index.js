import { find, get } from "lodash";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  MDBCol,
  MDBContainer,
  MDBRow,
  MDBSpinner,
  MDBView
} from "mdbreact";
import React, { Component } from "react";
import bgContact from "~/assets/img/desktop/common/blur-04.jpg";
import TagName from "~/components/common/atoms/TagName";
import { partnerInfo } from "~/configs/partnerDetails";
import styles from "./style.module.scss";
class Introduce extends Component {
  render() {
    const { loading, error } = this.props;
    const info = get(partnerInfo, "menuFooter");
    const privacyPolicy = find(info, { key: "chinh-sach-bao-mat" });
    return (
      <MDBAnimation type="fadeIn">
        <div className="d-none d-lg-block">
          <MDBView className={styles.img_parallax} src={bgContact} fixed>
            <MDBContainer>
              <MDBRow>
                <MDBCol>
                  <TagName
                    element="h1"
                    className={[
                      "title_component",
                      "title_line",
                      "title_contact"
                    ]}
                  >
                    <span>Ch<PERSON>h sách bảo mật</span>
                  </TagName>
                </MDBCol>
              </MDBRow>
            </MDBContainer>
          </MDBView>
        </div>
        <div className={styles.introduce}>
          <MDBContainer>
            <MDBRow>
              <MDBCol md="12" className="mb-4">
                <div className={styles.introduce_item}>
                  {loading ? (
                    <div className="loading absolute">
                      <MDBSpinner big />
                    </div>
                  ) : !error ? (
                    <div
                      className={styles.wapper_page_inner}
                      dangerouslySetInnerHTML={{
                        __html: privacyPolicy.content
                      }}
                    />
                  ) : (
                    ""
                  )}
                </div>
              </MDBCol>
            </MDBRow>
          </MDBContainer>
        </div>
      </MDBAnimation>
    );
  }
}

export default Introduce;
