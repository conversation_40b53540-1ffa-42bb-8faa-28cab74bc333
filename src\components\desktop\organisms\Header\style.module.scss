@import "src/assets/scss/custom-variables.scss";
@import "src/assets/scss/pro-custom/button.scss";

.MenuTop {
  padding: 0;
  position: fixed;
  z-index: 999;
  top: 0;
}
.header {
  position: relative;
  background-color: rgba(255, 255, 255, 0.97);
  z-index: 99;
  @media #{$medium-and-down} {
    position: fixed;
    top: 0;
    right: 0;
    background: #1da1f2;
    z-index: 20;
    left: 0;
    height: 60px;
  }
}
.wrap {
  margin: 0 auto;
  position: relative;
  max-width: 1140px;
  @media (max-width: 1200px) {
    padding: 0 15px;
    max-width: 100%;
    .navigator {
      padding: 0 3% !important;
    }
    .nav_info {
      justify-content: flex-end !important;
      padding: 12px 0 !important;
      width: calc(100% - 193px);
    }
    .widget {
      display: none;
    }
    .menu_extra {
      position: absolute !important;
      top: 15px;
      right: 15px;
    }
  }
  @media #{$medium-and-down} {
    padding: 0;
    .menu_extra {
      display: none !important;
    }
    .nav_info {
      margin-right: 70px;
      width: auto;
      padding: 8px 0 !important;
    }
    .navigator {
      padding: 0% !important;
    }
    .header_brand {
      position: absolute;
      top: 1px;
      left: 15px;
      z-index: 20;
    }
    .logoDefault {
      width: 130px !important;
    }
    .mdbNavbarNav {
      li {
        text-align: left !important;
      }
    }
  }
}

.header_classic {
  background-color: #ffffff;
  .icons_info {
    display: flex;
    align-items: center;
    flex-direction: row;
    a {
      border-bottom: 1px dotted rgba(0, 0, 0, 0.5);
      letter-spacing: 3px;
      font-weight: 500;
    }
  }
  .header_content {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .header_brand {
    min-width: 250px;

    a {
      transition: all 0.2s ease-in-out 0s;
    }
    .logoDefault {
      min-width: 154px;
      max-height: 124px;
      max-width: 300px;
    }
    .logoDefaulttrungvuong {
      width: 300px;
      height: 50px;
    }
  }
  .navigator {
    flex-grow: 1;
    padding: 0 4.5%;
    position: relative;
    .nav_info {
      position: relative;
      padding: 15px 0;
      text-align: center;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      &:before {
        height: 0;
        content: "";
        bottom: 0;
        left: 0;
        right: 0;
        position: absolute;
        opacity: 0.15;
        filter: alpha(opacity=15);
      }
    }
  }
  .menu_extra {
    display: table;
    position: relative;
    white-space: nowrap;
    .header_info_text {
      display: table-cell;
      vertical-align: middle;
      .icons {
        margin-right: 15px;
        flex-basis: 40px;
        width: 40px;
      }
    }
    .header_info_text_minhanh {
      .info_right {
        color: #db2233 !important;
        a {
          color: #db2233 !important;
        }
      }
    }
  }
}
.widget {
  font-size: 15px;
  padding: 0;
  margin-right: 15px;
  .widget_content {
    color: $main_black;
    font-weight: 500;
  }
  .dot {
    color: #627792;
    &:hover {
      color: #0352cc;
    }
  }
}
.logo {
  width: 160px;
  img {
    width: 100%;
  }
  @media #{$medium-and-down} {
    width: 135px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: 3;
  }
}
.header_item {
  position: relative;
  min-height: 55px;
}
.mdbNavbar {
  box-shadow: none;
  padding: 0;
  @media #{$medium-and-down} {
    .mdbCollapse {
      background: #fff;
      border-top: 1px solid #0352cc;
    }
    .mdbNavbarToggler {
      position: absolute;
      right: 15px;
      top: -45px;
      i {
        color: #0352cc;
      }
    }
  }
}
.mdbNavbarNav {
  position: relative;
  border-top: 1px solid #dfe3eb;
  li {
    text-align: center;
    flex-grow: 1;
    position: relative;
    &:before {
      border-color: #0352cc !important;
      content: "";
      height: 0;
      position: absolute;
      width: 0;
      right: 0;
      top: -2px;
      border-bottom: 3px solid;
      transition: width 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
    }
    &:hover:before {
      width: 100%;
      left: 0;
    }
    &:hover a {
      color: #627792;
    }
    a {
      padding: 0.8rem;
      color: #12263f;
      transition: all 0.3s ease-in-out;
      outline: none;
      font-weight: 400;
      white-space: nowrap;
      transition: width 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
    }
    &.active {
      a {
        color: #0352cc;
        font-weight: 500;
      }
      &:before {
        width: 100%;
        left: 0;
      }
    }
  }
  .itemMenu_minhanh {
    &:before {
      border-color: red !important;
    }
    a {
      &:hover {
        color: #db2233 !important;
      }
    }
  }

  .active_minhanh {
    &::before {
      border-color: #db2233 !important;
    }
    a {
      color: #db2233 !important;
    }
  }

  @media (min-width: 1200px) {
    a {
      padding: 0.8rem 1.2rem !important;
    }
  }
  @media #{$medium-and-down} {
    a {
      &.active {
        border-color: transparent !important;
      }
    }
  }
}
//login
.list_group_dangnhap {
  flex-direction: unset;
  li {
    border: 0;
    padding: 0;
  }
}

.downloadApp_minhanh {
  color: #db2233 !important;
  border: 1px solid #db2233 !important;

  &:hover:after {
    background: #db2032 !important;
  }
}

.login_minhanh {
  color: white !important;
  border: 1px solid #db2032 !important;
  background: #db2032 !important;
}
.clinic {
  position: relative;
  border: 1px solid #0352cc;
  border-radius: 3px;
  font-size: 14px;
  height: 37px;
  line-height: 37px;
  position: relative;
  padding: 8px 14px 8px 40px !important;
  color: #0352cc;
  white-space: nowrap;
  background: transparent !important;
  &::before {
    content: "\f108";
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    font-weight: 900;
    text-indent: 0;
    position: absolute;
    top: 52%;
    left: 15px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  &:hover:after {
    transform: scaleY(1);
    transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
    position: absolute;
    background: #0352cc;
    z-index: -1;
  }
  &:hover {
    color: #fff !important;
    z-index: 2;
  }
  @media #{$medium-and-down} {
    display: none;
  }
}
.downloadApp {
  margin-left: 15px;
  position: relative;
  border: 1px solid #0352cc;
  border-radius: 3px;
  font-size: 14px;
  height: 37px;
  line-height: 37px;
  position: relative;
  padding: 8px 14px 8px 34px !important;
  color: #0352cc;
  white-space: nowrap;
  background: transparent !important;
  &::before {
    content: "\f10b";
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    font-weight: 900;
    text-indent: 0;
    position: absolute;
    top: 52%;
    left: 14px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  &:hover:after {
    transform: scaleY(1);
    transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
    position: absolute;
    background: #0352cc;
    z-index: -1;
  }
  &:hover {
    color: #fff !important;
    z-index: 2;
  }
  @media #{$medium-and-down} {
    display: none;
  }
}
.after_login {
  margin-left: 15px;
  border: 1px solid #0352cc !important;
  background: transparent !important;
  color: #0352cc;
  box-shadow: unset;
  padding: 7px 14px 7px 40px;
  &:hover,
  &:focus,
  &:active,
  &.show {
    box-shadow: unset;
  }
  &:before {
    content: "\10f2bd";
    font-family: "Font Awesome 5 Duotone";
    font-size: 16px;
    font-style: normal;
    font-weight: 900;
    text-indent: 0;
    position: absolute;
    top: 52%;
    left: 15px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  &:hover:after {
    transform: scaleY(1);
    transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
    position: absolute;
    background: #0352cc;
    z-index: -1;
  }
  &:hover {
    color: #fff !important;
    z-index: 2;
  }
}
.mdbdropdownMenu {
  padding: 0 !important;
  a {
    border-radius: 0 !important;
    outline: 0;
    padding: 0.5rem 15px !important;
    cursor: pointer;
    &:hover,
    &:focus {
      box-shadow: unset !important;
    }
  }
}

.bell {
  box-shadow: unset;
  position: relative;
  background-color: transparent !important;
  width: 45px;
  height: 45px;
  transition: color 0.25s ease-in-out;
  border-radius: 50%;
  padding: 0px 15px;
  overflow: visible;
  margin-right: 15px;
  overflow: visible;
  &:hover,
  &:focus,
  &:active {
    box-shadow: unset !important;
    background: #e9f4fe !important;
  }
  i {
    margin: 0 !important;
    font-size: 1.1rem;
    line-height: 2.875rem;
    color: #627792;
  }
  &:hover i,
  &.active i {
    animation: ring 1.5s ease;
    transition: all 0.3s ease;
    color: #0352cc;
  }
  .count {
    position: absolute;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background-color: #fe696a;
    color: #fff;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.25rem;
    top: 3px;
    right: 2px;
  }
}

.mdbdropdownBell {
  width: 380px;
  top: 0px !important;
  border: 1px solid #dfe3eb;
  border-radius: 0 !important;
  transform: translate3d(-230px, 45px, 0px) !important;
  > div {
    outline: none;
  }
  ul {
    li {
      border-bottom: 1px solid #dfe3eb;
      padding: 10px 15px;
      margin: 0;
      font-size: 0.875rem;

      cursor: pointer;
      transition: all 0.3s ease-in-out;
      &:last-child {
        border-bottom: none !important;
        padding: 0;
      }

      &.new {
        color: #307be7;
        background: #fdfdfd;
      }
      &.read {
        background: #fdfdfd;
        color: #627792;
      }
      p {
        margin-bottom: 5px;
      }
      &:hover {
        background: #f3f6f9;
      }
    }
  }
}

.mdbdropdownItem {
  &:hover {
    background: transparent !important;
    box-shadow: unset !important;
  }
  white-space: normal;
}
.list_news_head {
  font-weight: 500;
  margin-bottom: 10px;
}
.head_alert {
  background: #f3f3f3;
  padding-bottom: 10px;
  font-weight: 500;
  font-size: 12px;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    &:last-child {
      cursor: pointer;
      font-weight: 500;
      color: #0352cc;
      i {
        font-size: 11px;
        margin-right: 3px;
      }
    }
  }
}
.view_all {
  display: block;
  background: transparent;
  border: 0 !important;
  padding: 0 !important;
  background: transparent !important;
  cursor: pointer;
  text-align: center;
  span {
    display: inline-block;
    vertical-align: middle;
  }
}

.mdbdropdownProfile {
  width: 350px;
  padding: 20px !important;
  border: 1px solid #dfe3eb !important;
  top: 8px !important;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 0 40px rgba(34, 34, 34, 0.2);
  ul {
    li {
      font-size: 0.875rem;
      margin: 0;
      border: 0;
      a {
        display: flex;
        color: $main_black;
        background: transparent !important;
        align-items: center;
        padding: 10px 0px !important;
        cursor: pointer;
        transition: all 0.3s ease-in-out !important;
        i {
          margin-right: 7px;
          font-size: 1rem;
          width: 16px;
        }
        &:hover,
        &:focus {
          color: #0352cc;
        }
        &.btn_edit {
          background-image: linear-gradient(45deg, #6a78d1, #00a4bd) !important;
          text-transform: none;
          padding: 10px 20px !important;
          margin: 10px 0 0 0;
          box-shadow: none;
          color: #ffffff;
          border-radius: 3px !important;
          &:hover,
          &:focus {
            box-shadow: none;
          }
        }
      }
      img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
      &:first-child {
        border-bottom: 1px solid #c7d4e7;
        padding-bottom: 20px !important;
        margin-bottom: 10px;
        padding-top: 0;
      }
      &:last-child {
        border-top: 1px solid #c7d4e7;
        margin-top: 10px;
        a {
          padding: 20px 0 0 0 !important;
        }
      }
    }
  }
}

.item_user {
  display: flex;

  padding: 10px 0 !important;
  border: unset;
  i {
    display: block;
    font-size: 40px;
    margin-right: 20px;
    color: #b0bcda;
  }
  p:last-child {
    margin-bottom: 0;
  }
}

.info_name {
  b {
    text-transform: capitalize;
  }
}

.mdbdropdownSubmenu {
  border: 0;
  ul {
    li {
      a {
        &:hover {
          color: #0352cc !important;
        }
      }
      &:hover {
        &:before {
          border-color: transparent !important;
          width: 0;
        }
      }
    }
  }
}
@keyframes ring {
  0% {
    -webkit-transform: rotate(35deg);
    transform: rotate(35deg);
  }
  12.5% {
    -webkit-transform: rotate(-30deg);
    transform: rotate(-30deg);
  }
  25% {
    -webkit-transform: rotate(25deg);
    transform: rotate(25deg);
  }
  37.5% {
    -webkit-transform: rotate(-20deg);
    transform: rotate(-20deg);
  }
  50% {
    -webkit-transform: rotate(15deg);
    transform: rotate(15deg);
  }
  62.5% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg);
  }
  75% {
    -webkit-transform: rotate(5deg);
    transform: rotate(5deg);
  }
  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
}

.version {
  padding-top: 10px;
  color: #ddd;
}

//  --------------------------- Minh anh

.header_minhanh {
  .navigator {
    .nav_info {
      .list_group_dangnhap {
        .after_login {
          &:hover {
            color: #fff !important;
            z-index: 2;
          }
          color: #db2233 !important;
          border: 1px solid #db2233 !important;

          &:hover:after {
            color: white !important;
            background: #db2032 !important;
          }
        }
      }
      .bell {
        &:hover i,
        &.active i {
          color: #db2032;
        }
      }
      .head_alert {
        span {
          &:last-child {
            color: #db2032 !important;
          }
        }
      }
      .mdbdropdownItem {
        a {
          &:hover,
          :focus {
            color: #db2233 !important;
          }
        }
      }
    }
  }
}
