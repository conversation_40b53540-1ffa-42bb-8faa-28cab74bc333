import {
  MDBBtn,
  MDBCol,
  MDBContainer,
  MDBIcon,
  MDBInputGroup,
  MDBRow
} from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import history from "~/history";
import { requestHospitalList } from "~/store/hospital/hospitalActions";
import { redirectToMedproId } from "~/store/login/actions";
import { logOut } from "~/store/user/userAction";
import { urlLogoHeaderMedpro } from "~/utils/manageResource";
import styles from "./style.module.scss";
class CustomerServiceHeader extends Component {
  state = {
    showTicketDropDown: true
  };

  logOutClickHandler = () => {
    this.props.logOut();
    history.push("/cham-soc-khach-hang");
  };

  handleNumber = evt => {
    var ch = String.fromCharCode(evt.which);

    if (!/[0-9]/.test(ch)) {
      evt.preventDefault();
    }
  };

  render() {
    const { infoUser } = this.props;
    return (
      <MDBContainer fluid className={styles.wapper_Header}>
        <MDBContainer fluid>
          <MDBRow className=" d-flex align-items-center ">
            <MDBCol md="2" className="d-none d-sm-none d-md-flex ">
              <div className={styles.logoBrand}>
                <Link to="/">
                  <img width="240" alt="Fragment" src={urlLogoHeaderMedpro} />
                </Link>
              </div>
            </MDBCol>
            <MDBCol md="3" className="text-center">
              <Link to="/">
                <h4 className="text-white text-uppercase m-0 text-center h4-responsive py-0 py-md-2 py-lg-0">
                  Chăm Sóc Khách Hàng
                </h4>
              </Link>
            </MDBCol>
            <MDBCol md="3" className="text-center">
              <MDBInputGroup
                type="search"
                id="phoneUser"
                value={this.props.inputPhone}
                onChange={event => this.props.inputPhoneChangeHandler(event)}
                onKeyDown={event => {
                  if (event.keyCode === 13) {
                    event.preventDefault();
                    this.props.requestInfoByPhone();
                  }
                }}
                onKeyPress={this.handleNumber}
                hint="Số điện thoại"
                containerClassName=""
                append={
                  <MDBBtn
                    onClick={() => this.props.requestInfoByPhone()}
                    color="ins"
                    className="m-0 px-3 py-2 z-depth-0"
                  >
                    <MDBIcon icon="phone" />
                  </MDBBtn>
                }
              />
            </MDBCol>
            <MDBCol
              md="4"
              className="text-right d-flex justify-content-center "
            >
              <div className={styles.account_info}>
                <span className=" text-danger font-weight-bolder mr-2">
                  <MDBIcon icon="user-cog" className="px-1" />
                  {infoUser?.fullName}
                </span>
                <MDBBtn
                  rounded
                  size="sm"
                  color="blue"
                  className={styles.round_button}
                  onClick={() => this.logOutClickHandler()}
                >
                  <MDBIcon icon="sign-out-alt" /> Đăng xuất
                </MDBBtn>
              </div>
            </MDBCol>
          </MDBRow>
          <MDBRow className={styles.rowMenu}>
            <MDBCol className={styles.colMenu}>
              <ul className={styles.listMenu}>
                <li className="d-none">
                  <Link to="/tim-kiem-thong-tin-tai-kham">
                    Tìm kiếm thông tin tái khám
                  </Link>
                </li>
                {/* <li>
                  <Link to="/">vấn đề khác</Link>
                </li> */}
              </ul>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </MDBContainer>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { info: infoUser },
    customerService: { totalTicket }
  } = state;
  return { infoUser, totalTicket };
};

const mapDispatchToProps = {
  logOut,
  redirectToMedproId,
  requestHospitalList: requestHospitalList
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(CustomerServiceHeader);
