@import "src/assets/scss/custom-variables.scss";

.loading {
  min-height: 70vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  align-content: center;
  opacity: 0.5;
}

.printercskh {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  min-width: 400px;
  max-width: 400px;

  .banner {
    width: 100%;
    background-color: white;
    color: black;
    font-family: Helvetica, Arial, Sans-Serif;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    margin-top: 20px;
    border: 1px solid black;

    .title {
      font-weight: bold;
      font-size: 16px;
      text-align: center;
      color: black;
      margin-bottom: 5px;
    }

    .subTitle {
      font-size: 13px;
      text-align: center;
      color: black;
      margin-bottom: 5px;
    }

    .group {
      display: flex;
      justify-content: space-around;

      .QR {
        padding: 5px 5px 0;
        border-radius: 5px;
        background-color: white;
      }

      .linkDown {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        align-content: center;

        p {
          font-style: italic;
          color: black;
          font-size: 13px;
          font-weight: bolder;
          margin: 0 0 0 10px;
        }
      }
    }
  }

  .print {
    overflow: hidden;
    font-family: Helvetica, Arial, Sans-Serif;
    background-color: white;
    padding: 15px;
    min-width: 400px;
    max-width: 400px;
    color: black !important;
    font-size: 16px;

    @media screen and (max-width: 576px) {
      margin: 0px;
      max-width: unset;
      width: 100%;
    }

    ul {
      list-style: none;
      padding-left: 0;
      margin-bottom: 0;

      li {
        b,
        strong {
          font-weight: 500;
          text-align: right;
        }

        .nbold {
          font-weight: normal;
        }

        p {
          margin-bottom: 0;
        }

        .column_left {
          font-weight: 500;
        }
        .textSpecial {
          font-size: 32px !important;
          font-weight: 600;
        }
        .column_right {
          text-align: right;
        }
      }
    }

    .totalPaymentMessage {
      color: orangered;
      margin-top: 20px;
      text-align: center;
    }

    .green {
      color: black;
    }

    .title_hospital {
      margin-bottom: 10px;
      text-align: center;

      img {
        width: 70%;
        height: auto;
        margin: 15px 0;
      }

      .sub_title {
        font-size: 18px;
        text-align: center;
        font-weight: bold;
      }

      .nameParner {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .addressParner {
        font-size: 13px;
      }
    }
    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
    .info1 {
      margin-bottom: 10px;
      justify-content: space-around;
    }
    .numbericalOrderNext {
      width: 50%;
      border-radius: 8px;
      padding: 12px;
      border: black 1px solid;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .binhthanhStyles {
      width: 100%;
      flex-direction: row !important;
      justify-content: space-around;
      border: none;
      .number,
      .gray {
        font-size: 16px !important;
      }
      .txtNum,
      .txtNum1 {
        font-size: 60px !important;
        font-weight: 700 !important;
        line-height: 75px !important;
      }
      padding-bottom: 0;
    }
    .bar_code {
      margin-bottom: 20px;
      text-align: center;

      display: flex;
      flex-direction: column;
      align-content: center;
      align-items: center;
      p {
        font-weight: 500;
        margin-bottom: 5px;
      }

      svg {
        max-width: 100%;
        margin: 0 0 !important;
      }
    }

    .form_code {
      text-align: center;
      margin-bottom: 15px;
    }

    .info_examination {
      text-align: center;
      margin-bottom: 20px;

      ul {
        li {
          font-weight: 500;
        }
      }
    }

    .txtNum {
      font-size: 24px;
      font-weight: 700 !important;
      line-height: 19px;
      color: #000;
    }
    .txtNum1 {
      margin-bottom: 0;
      font-size: 24px !important;
      font-weight: 700 !important;
      line-height: 19px;
      color: #000;
    }
    .number,
    .gray {
      font-size: 14px;
      color: #000;
      font-weight: bold;
      line-height: 14px;
      text-align: center;
      margin-bottom: 12px;
    }

    .gray {
      color: #000;
    }

    .top_note {
      text-align: center;
      font-weight: 500;
      margin-bottom: 5px;
      font-size: 0.9rem;
    }

    .attention {
      color: black;
      font-style: italic;
      font-size: 14px;
      font-weight: bold;
    }

    .time_note {
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      align-content: center;

      span {
        padding: 7px 25px;
        border-radius: 15px;
        color: white;
        font-size: 14px;
      }

      .timeNoteCancel {
        color: #c6cace;
      }

      .greenNote {
        background-color: black;
      }

      .redNote {
        background-color: black;
      }

      .greyNote {
        background-color: #c6cace;
      }

      .btnShare {
        margin-left: 0.5rem;
        display: inline-block;
        padding: 7px 25px;
        border-radius: 15px;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border: 1px solid snow;
        box-shadow: 0 2.8px 2.2px rgba(0, 0, 0, 0.034),
          0 12.5px 10px rgba(0, 0, 0, 0.06), 0 100px 80px rgba(0, 0, 0, 0.12);

        a {
          color: blue;
        }
      }
    }

    .status_number {
      text-align: center;

      span {
        display: inline-block;
        padding: 0.25em 0.4em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
        background-color: #6e84a3;
        color: #ffffff;
      }
    }
    .info_room_subjectName {
      border-radius: 8px;
      padding: 8px;
      border: black 1px solid;
      ul {
        li {
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          align-content: center;
          justify-content: space-between;

          &:last-child {
            margin-bottom: 0;
          }

          span {
            display: block;
            min-width: 160px;
          }
        }
      }
    }
    .list_detail {
      margin-top: 10px;
      margin-bottom: 10px;

      ul {
        li {
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          align-content: center;
          justify-content: space-between;

          &:last-child {
            margin-bottom: 0;
          }

          span {
            display: block;
            min-width: 160px;
          }
        }
      }
    }

    .note {
      margin-bottom: 10px;
      font-style: italic;
    }

    .profile_number,
    .note_time {
      margin-bottom: 10px;
    }

    .organization {
      text-align: center;

      p {
        font-weight: bold;

        span {
          text-align: center;
          width: 100%;
          font-size: 24px;
        }
      }
    }

    .thanhToanLai {
      display: flex;
      flex-direction: column;
      align-items: center;
      align-content: center;
      justify-content: center;

      .noti_unpayment {
        text-align: center;
        color: red;
        margin: 15px 0;
      }
    }

    .awaitMessage {
      color: #ffb340;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      align-content: center;
      flex-direction: column;
      margin-bottom: 15px;

      p {
        padding: 0 35px;
      }

      .spin {
        display: block;
      }
    }

    //  đường line chuột gặm
    .line {
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: space-between;
      margin: 0 -35px;
      position: relative;
      // padding: 15px 0;
      $size: 35px;

      .circleLeft,
      .circleRight {
        height: $size;
        width: $size;
        // background-color: whitesmoke;
        // border: 1px solid whitesmoke;
        border-radius: 50%;
      }

      .circleLeft {
        &::before {
          z-index: 1;
          content: "";
        }

        // border-bottom-right-radius: $size * 2;
        // border-top-right-radius: $size * 2;
      }

      .circleRight {
        // border-bottom-left-radius: $size * 2;
        // border-top-left-radius: $size * 2;
      }

      .dashed {
        width: 80%;
        border-top: 1px dashed black;
      }
    }

    .top {
      padding: 0;
      margin-top: -28px;

      .dashed {
        border: none;
      }
    }

    .bottom {
      padding: 0;
      margin-bottom: -28px;

      .dashed {
        border: none;
      }
    }

    //  đường line chuột gặm
  }
}

:root {
  @media print {
    @page {
      margin: 0;
    }

    .printercskh {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;

      .banner {
        margin-right: -8px;
      }
    }
  }
}
