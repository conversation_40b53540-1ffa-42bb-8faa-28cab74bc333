/* eslint-disable react/jsx-handler-names */
import { <PERSON><PERSON><PERSON>, MDBContainer, MDBRow } from "mdbreact";
import queryString from "query-string";
import React, { Component } from "react";
import { connect } from "react-redux";
import history from "~/history";
import { cancelMedicalBill, hiddenBill } from "~/store/booking/bookingAction";
import {
  changeHospitalCSKH,
  clearSelectedPatientCSKH,
  deleteSelectedPatientByCSKH,
  getBookingbyPatientCSKH,
  insertPatientIntoUserCskh,
  requestPaymentInfoCSKH,
  resetBookingInfoCS,
  selectPatientInfoCSKH,
  setCskhSecretKey
} from "~/store/customerService/customerServiceActions";
import { requestHospitalList } from "~/store/hospital/hospitalActions";
import {
  clearState,
  selectPatientFromData
} from "~/store/patient/patientAction";
import {
  changeUrlRedirectAfterCreatePatient,
  resetPatientForm
} from "~/store/patientForm/patientFormAction";
import { requestAllCity } from "~/store/resource/resourceAction";
import {
  resetBookingStepData,
  setPartnerId,
  setTypeAction
} from "~/store/totalData/actions";
import { TypeAction } from "~/utils/constants";
import ModalBillDetail from "../components/ModalBillDetail";
import ChooseHospitalModal from "../components/ModalChooseHospital";
import ModalRemoveConfirm from "../components/ModalRemoveConfirm";
import ModalSendBooking from "../components/ModalSendBooking";
import BillBox from "./BillBox";
import PatientBox from "./PatientBox";
import styles from "./style.module.scss";

class MainPage extends Component {
  constructor(props) {
    super(props);
    const query = queryString.parse(props.location.search);

    this.state = {
      showHospitalModal: false,
      inputPhone: query.phone ? query.phone : "",
      showBillDetailModal: false,
      showPatientDetailModal: false,
      showRemoveConfirmModal: false,
      showRedirectModal: false,
      showSendBookingModal: false,
      selectingBill: null,
      removeType: "",
      removingObject: {},
      patientCodeFilter: ""
    };
  }

  toggleModalChooseHospital = () => {
    this.setState({ showHospitalModal: !this.state.showHospitalModal });
  };

  toggleModalBillDetail = () => {
    this.setState({ showBillDetailModal: !this.state.showBillDetailModal });
  };

  toggleModalRemoveConfirm = (type = "", data = {}) => {
    this.setState({
      showRemoveConfirmModal: !this.state.showRemoveConfirmModal,
      removeType: type,
      removingObject: data
    });
  };

  toggleModalSendBooking = billInfo => {
    this.setState({
      showSendBookingModal: !this.state.showSendBookingModal,
      selectingBill: billInfo
    });
  };

  getBillDetailHandler = bill => {
    const { id, partnerId } = bill;
    this.props.requestPaymentInfoCSKH({ id, partnerId });
    this.toggleModalBillDetail();
    this.props.setPartnerId(partnerId);
  };

  getBillDetailForCare247 = bill => {
    const { id, partnerId } = bill;
    this.props.requestPaymentInfoCSKH({ id, partnerId });
    this.props.setPartnerId(partnerId);
  };

  handleEditBill = bill => {
    const { transactionId, partnerId } = bill;
    this.props.setPartnerId(partnerId);

    this.props.requestHospitalList();
    this.props.resetBookingStepData();
    this.props.requestPaymentInfoCSKH({
      transactionId,
      partnerId,
      typeAction: TypeAction.SuaPhieuKham
    });
  };

  btnBookingClickHandler = patientInfo => {
    this.props.setTypeAction(TypeAction.DatKham);
    this.props.clearStatePatient();
    this.props.selectPatientInfoCSKH(patientInfo);
    this.props.resetBookingInfoCS();
    history.push("/chon-benh-vien");
  };

  cancelMedicalBill = () => {
    const {
      handleCancelMedicalBill,
      user,
      match: {
        params: { code }
      }
    } = this.props;
    const data = {
      user_id: user.user_id,
      access_token: user.access_token,
      booking_id: code
    };
    handleCancelMedicalBill(data);
    this.method.handleToggleCancelModal();
  };

  handleRemoveObject = () => {
    const { handleCancelMedicalBill } = this.props;
    const { removeType, removingObject } = this.state;
    const { id, partnerId } = removingObject;
    switch (removeType) {
      case "patient":
        this.props.deleteSelectedPatientByCSKH();
        this.toggleModalRemoveConfirm("", {});
        break;
      case "bill":
        handleCancelMedicalBill(id, partnerId);
        break;
      default:
        break;
    }
  };

  selectPatientHandler = patientInfo => {
    this.props.setTypeAction(null);
    this.props.setCskhSecretKey(patientInfo.secretKey);
    this.props.selectPatientInfoCSKH(patientInfo);
    this.props.selectPatientFromData(patientInfo);
    // console.log(3);
    this.props.getBookingbyPatientCSKH(patientInfo);
  };

  openRecentBillHandler = () => {
    console.log(this.props.mpTransaction);
    if (this.props.mpTransaction) {
      this.getBillDetailHandler();
    }
  };

  handleAddAgainUser = patientInfo => {
    this.props.setTypeAction(TypeAction.ThemLaiHoSo);
    this.props.setCskhSecretKey(patientInfo.secretKey);
    this.props.selectPatientInfoCSKH(patientInfo);
    this.props.insertPatientIntoUserCskh(patientInfo);
  };

  handlerHiddenBill = bill => {
    this.props.hiddenBill(bill.id);
  };

  render() {
    const { selectedBill } = this.props;

    return (
      <React.Fragment>
        <MDBContainer fluid>
          <MDBRow className={styles.page_body}>
            <MDBCol lg="6" className="pr-2">
              <PatientBox
                {...this.props}
                handleAddAgainUser={this.handleAddAgainUser}
                patientList={this.props.phoneSearchResult?.patients}
                toggleDelete={this.toggleModalRemoveConfirm}
                btnBookingClickHandler={this.btnBookingClickHandler}
                loading={this.props.loadingPatient}
                selectPatientHandler={this.selectPatientHandler}
                selectedPatient={this.props.selectedPatient}
              />
            </MDBCol>
            <MDBCol lg="6" className="pl-2">
              <BillBox
                {...this.props}
                handleEditBill={this.handleEditBill}
                billList={this.props.phoneSearchResult?.bookings}
                getBillDetailHandler={this.getBillDetailHandler}
                toggleDelete={this.toggleModalRemoveConfirm}
                toggleSendBooking={this.toggleModalSendBooking}
                loading={this.props.loadingPatient}
                selectedPatient={this.props.selectedPatient}
                hiddenBill={this.handlerHiddenBill}
              />
            </MDBCol>
          </MDBRow>
        </MDBContainer>

        <ChooseHospitalModal
          openModal={this.state.showHospitalModal}
          toggle={this.toggleModalChooseHospital}
          allCities={this.props.allCities}
          hospitalList={this.props.hospitalList.data}
          changeHospitalOnStore={this.props.changeHospital}
        />
        {this.state.showBillDetailModal && (
          <ModalBillDetail
            toggleShowModal={this.toggleModalBillDetail}
            modalShow={this.state.showBillDetailModal}
            paymentInformation={selectedBill}
            clearSelectedPatientCSKH
            sendMedicalBill={this.toggleModalSendBooking}
            setPartnerId={this.props.setPartnerId}
          />
        )}
        <ModalRemoveConfirm
          toggle={this.toggleModalRemoveConfirm}
          show={this.state.showRemoveConfirmModal}
          type={this.state.removeType}
          data={this.state.removingObject}
          onContinue={this.handleRemoveObject}
        />
        <ModalSendBooking
          toggle={this.toggleModalSendBooking}
          show={this.state.showSendBookingModal}
          itemCoupon={this.state.selectingBill}
        />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: {
      medicalBillList,
      selectedBill,
      selectedHospital,
      phoneSearchResult,
      searchedPhone,
      selectedPatient,
      ticketList,
      loadingTicket,
      loadingPatient
    },
    user: { IsAuthenticated, info: infoUser },
    hospital: { hospitalList },
    resource: { dataCity: allCities }
  } = state;
  return {
    medicalBillList,
    hospitalList,
    selectedBill,
    allCities,
    selectedHospital,
    phoneSearchResult,
    selectedPatient,
    ticketList,
    loadingTicket,
    loadingPatient,
    IsAuthenticated,
    infoUser,
    searchedPhone
  };
};

const mapDispatchToProps = {
  setTypeAction: setTypeAction,
  resetBookingStepData,
  requestHospitalList: requestHospitalList,
  changeUrlRedirectAfterCreatePatient: changeUrlRedirectAfterCreatePatient,
  resetPatientForm: resetPatientForm,
  clearStatePatient: clearState,
  hiddenBill: hiddenBill,
  handleCancelMedicalBill: cancelMedicalBill,
  requestAllCity: requestAllCity,
  changeHospital: changeHospitalCSKH,
  requestPaymentInfoCSKH: requestPaymentInfoCSKH,
  selectPatientInfoCSKH: selectPatientInfoCSKH,
  clearSelectedPatientCSKH: clearSelectedPatientCSKH,
  setCskhSecretKey: setCskhSecretKey,
  deleteSelectedPatientByCSKH: deleteSelectedPatientByCSKH,
  getBookingbyPatientCSKH: getBookingbyPatientCSKH,
  insertPatientIntoUserCskh: insertPatientIntoUserCskh,
  selectPatientFromData: selectPatientFromData,
  resetBookingInfoCS: resetBookingInfoCS,
  setPartnerId
};
export default connect(mapStateToProps, mapDispatchToProps)(MainPage);
