/* eslint-disable react/jsx-handler-names */
import { size } from "lodash";
import {
  MDBBtn,
  MDBModal,
  MDBModalBody,
  MDBModalFooter,
  MDBModalHeader
} from "mdbreact";
import React, { Component, Fragment } from "react";
import { dataTransform, getMedicalBillStatus } from "~/utils/func";

class ModalRemoveConfirm extends Component {
  renderMessage = (type, data) => {
    switch (type) {
      case "patient":
        return (
          <Fragment>
            <strong>Bệnh nhân:</strong> {data.surname} {data.name}
            <br />
            <strong><PERSON><PERSON> hồ sơ:</strong> {data.code}
          </Fragment>
        );
      case "bill":
        if (size(data?.bookingsRelation) > 1) {
          return (
            <>
              <h4>Khi bạn hủy thì các phiếu sẽ bị hủy?</h4>
              {data?.bookingsRelation?.map((item, i) => {
                return (
                  <div key={i} style={{ marginBottom: "15px" }}>
                    {item?.info?.bookingCode && (
                      <>
                        <strong><PERSON><PERSON> phiếu:</strong> {item?.info?.bookingCode}
                        <br />
                      </>
                    )}
                    {item?.info?.subject?.name && (
                      <>
                        <strong>Chuyên khoa:</strong>{" "}
                        {item?.info?.subject?.name}
                        <br />
                      </>
                    )}
                    {item?.info?.service?.name && (
                      <>
                        <strong>Dịch vụ:</strong> {item?.info?.service?.name}
                      </>
                    )}
                    <br />
                    {item?.info?.doctor?.name && (
                      <>
                        <strong>Bác sĩ:</strong> {item?.info?.doctor?.name}
                        <br />
                      </>
                    )}
                  </div>
                );
              })}
              <hr />
              <strong>Ngày đặt:</strong>{" "}
              {dataTransform(data?.createdAt, "date", [])}
              <br />
              <strong>Ngày khám:</strong>{" "}
              {dataTransform(data?.date, "date", [])}
              <br />
              <br />
            </>
          );
        } else {
          return (
            // transactionId
            <p>
              <strong>Mã phiếu:</strong> {data.bookingCode}
              <br />
              <strong>Mã thanh toán:</strong> {data.transactionId}
              <br />
              <strong>Ngày đặt:</strong>{" "}
              {dataTransform(data.createdAt, "date", [])}
              <br />
              <strong>Ngày khám:</strong> {dataTransform(data.date, "date", [])}
              <br />
              <strong>Trạng thái:</strong>{" "}
              {getMedicalBillStatus(data.status, data.date)}
              <br />
            </p>
          );
        }
      default:
        return <p>Lỗi: không xác định được đối tượng</p>;
    }
  };

  renderHeaderMessage = type => {
    switch (type) {
      case "bill":
        return "Bạn có chắc muốn hủy phiếu:";
      default:
        return "Bạn có chắc muốn xóa:";
    }
  };

  render() {
    return (
      <Fragment>
        <MDBModal
          isOpen={this.props.show}
          toggle={this.props.toggle}
          position="center"
        >
          <MDBModalHeader>
            <strong>{this.renderHeaderMessage(this.props.type)}</strong>
          </MDBModalHeader>
          <MDBModalBody>
            {this.renderMessage(this.props.type, this.props.data)}
          </MDBModalBody>
          <MDBModalFooter>
            <MDBBtn color="warning" onClick={this.props.toggle}>
              Không
            </MDBBtn>
            <MDBBtn color="danger" onClick={this.props.onContinue}>
              {this.props.type !== "bill" ? "Xóa" : "Hủy"}
            </MDBBtn>
          </MDBModalFooter>
        </MDBModal>
      </Fragment>
    );
  }
}

export default ModalRemoveConfirm;
