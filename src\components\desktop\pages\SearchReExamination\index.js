/* eslint-disable prettier/prettier */
import { MDBAnimation, MDBCol, MDBContainer, MDBRow, MDBView } from "mdbreact";
import { createForm } from "rc-form";
import React, { Component } from "react";
import { connect } from "react-redux";
import bgContact from "~/assets/img/desktop/common/blur-04.jpg";
import TagName from "~/components/common/atoms/TagName";
import { openToast } from "~/components/common/molecules/ToastNotification";
import {

  requestSearchReExam
} from "~/store/followUpExam/actions";
import {
  requestHospitalList
} from "~/store/hospital/hospitalActions";
import styles from "./style.module.scss";

class SearchReExamination extends Component {


  componentDidMount() {
    this.props.requestHospitalList()
  }

  handleSearch = event => {
    event.preventDefault();
    this.props.form.validateFields((error, value) => {
      console.log("value :>> ", value, error);
      if (!error) {
        this.props.requestSearchReExam({ data: value })
      }
    });
  };

  copyTexthandler = url => {
    openToast("Đường dẫn đã được sao chép.");
    console.log("navigator.clipboard");
    var textArea = document.createElement("textarea");
    textArea.value = url;
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand("copy");
    } catch (err) { }

    document.body.removeChild(textArea);

  };

  redirectDatTaiKham = url => {
    window.location.href = url
  };

  render() {
    const {
      form: { getFieldProps, getFieldError },
      hospitalList,
      listReExamByPhone
    } = this.props;

    const errorMessage = ele => {
      const field = getFieldError(ele);

      const mess = field ? field.join(", ") : null;
      return <span className={styles.alertSpan}>{mess}</span>;
    };

    return (
      <MDBAnimation type="fadeIn">
        <div className="d-none d-lg-block">
          <MDBView className={styles.img_parallax} src={bgContact} fixed>
            <MDBContainer>
              <MDBRow>
                <MDBCol>
                  <TagName
                    element="h1"
                    className={[
                      "title_component",
                      "title_line",
                      "title_contact"
                    ]}
                  >
                    <span>Tìm kiếm Thông tin Tái khám</span>
                  </TagName>
                </MDBCol>
              </MDBRow>
            </MDBContainer>
          </MDBView>
        </div>
        <div className={styles.introduce}>
          <MDBContainer>
            <MDBRow>
              <MDBCol md="12" className="mb-4">
                <form className={styles.form}>
                  <div className={styles.formWarpper}>
                    <div className={styles.formControl}>
                      <div className={styles.formInput}>
                        <fieldset>
                          <legend>Số điện thoại:</legend>
                          <input
                            placeholder="Nhập số điện thoại..."
                            {...getFieldProps("phone", {
                              rules: [
                                {
                                  required: true,
                                  message: "Vui lòng nhập số điện thoại !"
                                }
                              ]
                            })}
                          />
                        </fieldset>
                      </div>
                      {errorMessage("phone")}
                    </div>
                    <div className={styles.formControl}>
                      <div className={styles.formInput}>
                        <fieldset>
                          <legend>Mã bệnh nhân:</legend>
                          <input
                            placeholder="Nhập mã bệnh nhân..."
                            {...getFieldProps("patientCode", {
                              rules: [
                                {
                                  required: false,
                                  message: "Vui lòng nhập mã bệnh nhân !"
                                }
                              ]
                            })}
                          />
                        </fieldset>
                      </div>
                      {errorMessage("patientCode")}
                    </div>
                    <div className={styles.formControl}>
                      <div className={styles.formInput}>
                        <fieldset>
                          <legend>Chọn bệnh viện:</legend>


                          <input
                            list="browsers"
                            autoComplete="off"
                            placeholder="Chọn bệnh viện..."
                            {...getFieldProps("partnerId", {
                              rules: [
                                {
                                  required: true,
                                  message: "Vui lòng nhập bệnh viện !"
                                }
                              ]
                            })}
                          />
                          <datalist id="browsers">
                            {hospitalList.data.map((item) => {
                              return <option key={item.partnerId} value={item.partnerId}>{item?.name}</option>
                            })}
                          </datalist>
                        </fieldset>
                      </div>
                      {errorMessage("partnerId")}
                    </div>
                    <button className={styles.btnSearch} onClick={this.handleSearch}>Tìm kiếm</button>
                  </div>
                </form>
              </MDBCol>

              <MDBCol md="12" className="mb-4">
                <table width="100%" className={styles.table}>
                  <thead>
                    <tr>
                      <th>Stt</th>
                      <th>Họ tên</th>
                      <th>Ngày sinh</th>
                      <th>Giới tính</th>
                      <th />
                    </tr>
                  </thead>
                  <tbody>
                    {
                      listReExamByPhone.map((item, i) => {
                        return (
                          <tr key={i}>
                            <td>{i + 1}</td>
                            <td>{item.patientDetail.fullname}</td>
                            <td>{item.patientDetail.birthdate}</td>
                            <td>{item.patientDetail.sex}</td>
                            <td><button className={styles.btnTaiKham} onClick={() => this.copyTexthandler(item.quickBookingUrl)}>Coppy link</button>

                              <button className={styles.btnTaiKham} onClick={() => this.redirectDatTaiKham(item.quickBookingUrl)}>Hổ trợ tái khám</button>
                            </td>
                          </tr>)
                      })
                    }
                  </tbody>
                </table>
              </MDBCol>
            </MDBRow>
          </MDBContainer>
        </div>
      </MDBAnimation>
    );
  }
}

const mapStateToProps = state => {
  const {
    totalData: { partnerId },
    hospital: {
      hospitalList
    },
    followUpExam: { listReExamByPhone }
  } = state;

  return { partnerId, hospitalList, listReExamByPhone };
};

const mapDispatchToProps = {
  requestSearchReExam: requestSearchReExam,
  requestHospitalList: requestHospitalList
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  createForm({
    mapPropsToFields: props => { },
    onFieldsChange: (props, fields) => { }
  })(SearchReExamination)
);
