/* eslint-disable react/jsx-handler-names */
import cx from "classnames";
import { get, size } from "lodash";
import {
  MDBCard,
  MDBCardBody,
  MDBCardHeader,
  MDBCol,
  MDBContainer,
  MDBListGroup,
  MDBListGroupItem,
  MDBRow,
  MDBTable,
  MDBTableBody,
  MDBTableHead,
  MDBInput,
  MDBPopover,
  MDBPopoverBody
} from "mdbreact";
import moment from "moment";
import React, { Component } from "react";
import { Redirect } from "react-router-dom";
import PKHBtn from "~/components/common/atoms/Button";
import TagName from "~/components/common/atoms/TagName";
import Modal from "~/components/common/molecules/Modal";
import PatientInformation from "~/components/common/molecules/PatientInformation";
import PatientInfomationLess from "~/components/desktop/molecules/PatientInfomationLess";
import { getFormatMoney } from "~/utils/getFormatMoney";
import styles from "./style.module.scss";
import iconSupport from "~/assets/img/svg/support.svg";
import voucher from "~/assets/img/svg/voucher.svg";

class TrustInformation extends Component {
  state = {
    showDeletingBookingModal: false,
    redirectToPaymentMethod: false,
    redirectToHomePage: false,
    scheduleToDelete: {},
    disableButton: false,
    checkedMedproCare: this.props.addonMedproCare?.id || ""
  };

  toggleDeleteingBookingModal = () => {
    this.setState({
      showDeletingBookingModal: !this.state.showDeletingBookingModal
    });
  };

  handleConfirmBeforeDelete = (schedule, index) => {
    this.setState({
      showDeletingBookingModal: !this.state.showDeletingBookingModal,
      scheduleToDelete: schedule,
      indexSchedule: index
    });
  };

  handleDeleteBooking = () => {
    const { removeSchedule, schedulesSelected } = this.props;

    const { indexSchedule } = this.state;
    removeSchedule({ indexSchedule });

    if (size(schedulesSelected) > 0) {
      this.setState({
        showDeletingBookingModal: !this.state.showDeletingBookingModal
      });
    } else {
      this.setState({
        redirectToHomePage: !this.state.redirectToHomePage
      });
    }
  };

  handleOnSubmit = () => {
    this.setState({
      disableButton: true
    });
    if (this.props.medproCare) {
      const addonIsChoose = this.props.medproCare.addonServices.find(
        item => item.id === this.state.checkedMedproCare
      );
      this.props.selectMedproCare(addonIsChoose);
    }

    this.props.handleNextStep();

    setTimeout(() => {
      this.setState({
        disableButton: false
      });
    }, 3000);
  };

  renderBookingInformation = () => {
    const {
      schedulesSelected,
      room,
      isHasSubject,
      isHasService,
      isHasDoctor,
      isHasRoom,
      partnerId
    } = this.props;

    const roomType = get(room, "type", "");

    // header
    const header = (
      <tr>
        <th>#</th>
        {isHasSubject && <th>Chuyên khoa </th>}
        {isHasService && <th>Dịch vụ</th>}
        {isHasDoctor && <th>Bác sĩ</th>}
        {isHasRoom && <th>Phòng khám</th>}
        <th>{roomType === "CHECK_IN" ? "Giờ tiếp nhận" : "Giờ khám"}</th>
        <th>Tiền khám</th>
        <th />
      </tr>
    );

    // body
    const body = schedulesSelected.map((schedule, index) => {
      const displayDetail = get(schedule, "service.displayDetail", 0);

      const subjectName = get(schedule, "subject.name", "");
      const serviceName = get(schedule, "service.name", "");
      const doctorName = get(schedule, "doctor.name", "");
      const roomName = get(schedule, "room.name", "");
      const price = get(schedule, "service.price", 0);
      const date = schedule?.date
        ? moment(get(schedule, "date", "")).format("DD/MM")
        : "Chờ cập nhật";
      // const startTime = get(schedule, "time.startTime", "");
      // const endTime = get(schedule, "time.endTime", "");
      const startTime = schedule?.time?.startTime || "";
      const endTime = schedule?.time?.endTime || "";

      const infoLine2 = get(schedule, "service.infoLine2", "");
      return (
        <React.Fragment key={index}>
          <tr key={index}>
            <td>{index + 1}</td>
            {isHasSubject && <td>{subjectName}</td>}
            {isHasService && <td>{serviceName}</td>}
            {isHasDoctor && <td>{doctorName}</td>}
            {isHasRoom && <td>{roomName}</td>}
            <td>
              {date} {startTime}
            </td>
            <td>
              {displayDetail && displayDetail !== ""
                ? displayDetail
                : getFormatMoney(price) + " đ"}
            </td>{" "}
            <td>
              <PKHBtn
                onClick={() => {
                  this.handleConfirmBeforeDelete(
                    {
                      subjectName,
                      serviceName,
                      roomName,
                      doctorName,
                      date,
                      startTime,
                      endTime
                    },
                    index
                  );
                }}
              >
                <i className="far fa-trash-alt" />
                Xoá
              </PKHBtn>
            </td>
          </tr>
          {partnerId === "bvsingapore" && infoLine2 && (
            <tr>
              <td>{index + 2}</td>
              <td>{infoLine2?.serviceName}</td>
              <td>{infoLine2?.roomName}</td>
              <td>
                {date} {startTime}
              </td>
              <td>{infoLine2?.price}</td>
              <td />
            </tr>
          )}
        </React.Fragment>
      );
    });

    return { header, body };
  };

  toggleRadio = id => {
    const { checkedMedproCare } = this.state;
    if (id === checkedMedproCare) {
      this.setState({
        checkedMedproCare: ""
      });
    } else {
      this.setState({
        checkedMedproCare: id
      });
    }
  };

  render() {
    const {
      redirectToPaymentMethod,
      redirectToHomePage,
      scheduleToDelete,
      showDeletingBookingModal,
      checkedMedproCare
    } = this.state;

    const {
      handleGoBack,
      extraConfig,
      flow,
      schedulesSelected,
      medproCare
    } = this.props;
    if (redirectToPaymentMethod)
      return <Redirect push to="/hinh-thuc-thanh-toan" />;

    if (redirectToHomePage) {
      return <Redirect push to="/" />;
    }

    const showBtnAddSubject = () => {
      const limitBooking = extraConfig?.treeId?.[flow]?.bookingLimit || 0;
      if (limitBooking < 2) {
        return false;
      } else {
        if (size(schedulesSelected) === limitBooking) {
          return false;
        } else {
          return true;
        }
      }
    };

    const renderLabelMedproCare = item => {
      const comparePrice =
        getFormatMoney(item?.originalPrice) > getFormatMoney(item?.price);
      return (
        <div className={styles.ItemCare247}>
          <p className={styles.careItemName}>
            <span>
              {item.name} {item?.subname && `(${item.subname})`}
            </span>
            <div className={styles.support} onClick={e => e.preventDefault()}>
              <img src={iconSupport} />
              <div
                className={styles.description}
                dangerouslySetInnerHTML={{ __html: item?.description }}
              />
            </div>
            {item?.preferentialPriceTimeDescription && (
              <div div className={styles.badge}>
                <img
                  src={voucher}
                  alt="voucher"
                  width={10}
                  height={10}
                  layout="fixed"
                />
                <span>
                  {item?.preferentialPriceTimeDescription || "Ưu đãi khung giờ"}
                </span>
              </div>
            )}
          </p>

          <p className={styles.careItemPrice}>
            <span className={styles.price}>
              Giá: {getFormatMoney(item?.price)}
              {item.currency} / {item.duration}
            </span>
            {comparePrice && (
              <span className={styles.originalPrice}>
                {getFormatMoney(item?.originalPrice)}
                {item.currency}
              </span>
            )}
          </p>
        </div>
      );
    };

    return (
      <div className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow className="row_mobile">
            <MDBCol md="12" lg="3">
              <PatientInfomationLess />
            </MDBCol>
            <MDBCol md="12" lg="9">
              <MDBCard className={cx(styles.panels, styles.panel_mobile)}>
                <MDBCardHeader className={styles.panels_header}>
                  <TagName
                    element="h2"
                    className={[
                      "title_component",
                      "title_choose_date",
                      "space_between",
                      "title_header_mobile"
                    ]}
                  >
                    <span> Xác nhận thông tin khám</span>
                  </TagName>
                </MDBCardHeader>
                <MDBCardBody className={styles.card_body}>
                  <MDBTable responsive className={styles.tableResponsive}>
                    <MDBTableHead>
                      {this.renderBookingInformation().header}
                    </MDBTableHead>
                    <MDBTableBody>
                      {this.renderBookingInformation().body}
                    </MDBTableBody>
                  </MDBTable>
                </MDBCardBody>
                {size(medproCare?.addonServices) > 0 && (
                  <>
                    <MDBCardHeader
                      className={cx(
                        styles.panels_header,
                        "mt-3 d-none d-lg-block"
                      )}
                    >
                      <TagName
                        element="h2"
                        className={[
                          "title_component",
                          "title_choose_date",
                          "space_between",
                          "title_header_mobile"
                        ]}
                      >
                        <span>
                          Dịch vụ đặt thêm (cung cấp bởi{" "}
                          {`${medproCare.provider.name}`})
                        </span>
                      </TagName>
                    </MDBCardHeader>
                    <MDBCardBody className={styles.card_body}>
                      {medproCare?.addonServices.map(item => {
                        return (
                          <div key={item?.id} className={styles.medproCareItem}>
                            <MDBInput
                              key={item.id}
                              id={item.id}
                              type="radio"
                              checked={checkedMedproCare === item.id}
                              onClick={e => {
                                this.toggleRadio(item.id);
                              }}
                              label={renderLabelMedproCare(item)}
                            />
                          </div>
                        );
                      })}
                    </MDBCardBody>
                  </>
                )}
                <MDBCardHeader
                  className={cx(styles.panels_header, "mt-3 d-none d-lg-block")}
                >
                  <div className={styles.sub_card_header}>
                    Thông tin bệnh nhân
                  </div>
                </MDBCardHeader>
                <MDBCardBody
                  className={cx(styles.card_body, "d-none d-lg-block")}
                >
                  <PatientInformation />
                </MDBCardBody>
                <div className={styles.next_prev}>
                  <PKHBtn backdesktop="backdesktop" onClick={handleGoBack}>
                    Quay lại
                  </PKHBtn>
                  <div className={styles.groupBtnRight}>
                    {showBtnAddSubject() && (
                      <PKHBtn
                        create="create"
                        onClick={this.props.handleAddMultiSchedule}
                      >
                        Thêm chuyên khoa
                      </PKHBtn>
                    )}
                    <PKHBtn
                      onClick={this.handleOnSubmit}
                      buttonArrow="buttonArrow"
                      nextStep
                      create="create"
                      disabled={this.props.isUpdatingBooking}
                    >
                      {this.props.isUpdatingBooking
                        ? "Đang thực hiên"
                        : "Xác nhận"}
                    </PKHBtn>
                  </div>
                </div>
              </MDBCard>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
        <Modal
          iconTitle={<i className="fal fa-bell" />}
          title="Bạn có muốn xoá thông tin đặt khám ?"
          modal={showDeletingBookingModal}
          footer
          footerConfirm
          className="centered"
          centered
          okText="Đồng ý"
          onOk={() => this.handleDeleteBooking()}
          cancelText="Hủy"
          // eslint-disable-next-line react/jsx-handler-names
          onCancel={this.toggleDeleteingBookingModal}
          toggle={this.toggleDeleteingBookingModal}
        >
          <MDBListGroup className={styles.list_group}>
            {scheduleToDelete.subjectName && (
              <MDBListGroupItem>
                <div className={styles.column1}>
                  <i className="fal fa-stethoscope" />
                  Chuyên khoa
                </div>
                <div className={cx(styles.column2, styles.fullname)}>
                  {scheduleToDelete.subjectName}
                </div>
              </MDBListGroupItem>
            )}
            {scheduleToDelete.serviceName && (
              <MDBListGroupItem>
                <div className={styles.column1}>
                  <i className="fal fa-file-medical" />
                  Dịch vụ
                </div>
                <div className={cx(styles.column2, styles.fullname)}>
                  {scheduleToDelete.serviceName}
                </div>
              </MDBListGroupItem>
            )}
            {scheduleToDelete.roomName && (
              <MDBListGroupItem>
                <div className={styles.column1}>
                  <i className="fal fa-address-card" />
                  Phòng khám
                </div>
                <div className={cx(styles.column2, styles.fullname)}>
                  {scheduleToDelete.roomName}
                </div>
              </MDBListGroupItem>
            )}
            {scheduleToDelete.startTime && (
              <MDBListGroupItem>
                <div className={styles.column1}>
                  <i className="fal fa-alarm-clock" />
                  Ngày khám
                </div>
                <div className={cx(styles.column2, styles.fullname)}>
                  {scheduleToDelete.date} ({scheduleToDelete.startTime}-
                  {scheduleToDelete.endTime})
                </div>
              </MDBListGroupItem>
            )}
          </MDBListGroup>
        </Modal>
      </div>
    );
  }
}

export default TrustInformation;
