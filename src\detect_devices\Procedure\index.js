import { find, get } from "lodash";
import React, { Component } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import { SEO } from "~/components/common/molecules/SEO";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const ProcedurePageMobile = Loadable({
  loader: () => import("~/components/mobile/pages/Procedure"),
  loading: LoadableLoading
});
const ProcedurePageDesktop = Loadable({
  loader: () => import("~/components/desktop/pages/Procedure"),
  loading: LoadableLoading
});

class Procedure extends Component {
  render() {
    const info = get(partnerInfo, "menu");
    const procedureInfo = find(info, { key: "quy-trinh" });
    const { device } = this.props;
    return (
      <React.Fragment>
        <SEO
          title={`Quy Trình | ${hospitalName.value}`}
          meta={[
            {
              name: `description`,
              content: `Medpro, đặt khám trực tuyến, thanh toán viện phí, nhắc uống thuốc, khám chuyên khoa, bác sĩ, tư vấn sức khoẻ từ xa`
            },
            {
              property: `og:title`,
              content: "Quy Trình"
            },
            {
              property: `og:description`,
              content: `Trang Quy Trình`
            },
            {
              property: `og:url`,
              content: "https://medpro.vn/quy-trinh"
            },
            {
              property: `og:image`,
              content: "https://cms.medpro.com.vn/uploads/d_e1b14f67d2.jpg"
            },
            {
              property: `og:type`,
              content: `website`
            }
          ]}
        />
        {device === "mobile" ? (
          <ProcedurePageMobile procedureInfo={procedureInfo} />
        ) : (
          <ProcedurePageDesktop procedureInfo={procedureInfo} />
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    }
  } = state;
  return {
    device: type
  };
};

export default connect(mapStateToProps)(Procedure);
