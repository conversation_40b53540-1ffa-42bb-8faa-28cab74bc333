import cx from "classnames";
import {
  MDBCard,
  MDBCardBody,
  MDBCardImage,
  MDBCardText,
  MDBCol,
  MDBContainer,
  MDBRow,
  MDBView
} from "mdbreact";
import React from "react";
import Slider from "react-slick";
import TagName from "~/components/common/atoms/TagName";
import styles from "./style.module.scss";

export const DeployedHospital = () => {
  const settings = {
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 3,
    autoplay: true,
    autoplaySpeed: 2000,
    responsive: [
      {
        breakpoint: 1198,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
          initialSlide: 3,
          dots: true,
          infinite: false,
          arrows: false
        }
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          initialSlide: 1,
          dots: true,
          infinite: false,
          arrows: false
        }
      }
    ]
  };
  return (
    <MDBContainer fluid className={cx(styles.DeployedHospital, "py-5")}>
      <MDBContainer className={cx(styles.ContainerDeployedHospital)}>
        <MDBRow center>
          <MDBCol className="text-center">
            <TagName
              element="h3"
              className={["sub_title_section", "title_featureMedpro"]}
            >
              H<PERSON> thống bệnh viện triển khai
            </TagName>
          </MDBCol>
        </MDBRow>
        <style>{cssstyle}</style>
        <Slider {...settings} className={styles.Slider}>
          {data.map(({ title, image, link }, i) => {
            return (
              <div key={i}>
                <MDBCard className={cx(styles.card_news, "mx-2")}>
                  <MDBView className={styles.view}>
                    <a href={link} target="_blank" rel="noreferrer">
                      <MDBCardImage className="img-fluid" src={image} waves />
                    </a>
                  </MDBView>
                  <MDBCardBody className={styles.card_body}>
                    <MDBCardText className="text-uppercase text-center font-small text-dark">
                      <a href={link} target="_blank" rel="noreferrer">
                        {title}
                      </a>
                    </MDBCardText>
                  </MDBCardBody>
                </MDBCard>
              </div>
            );
          })}
        </Slider>
      </MDBContainer>
    </MDBContainer>
  );
};

export default DeployedHospital;

const cssstyle = `
.slick-next:before, .slick-prev:before {
    color: #000;
}
`;

export const data = [
  {
    title: "bệnh viện đa khoa an phước",
    image: `https://cms.medpro.com.vn/uploads/small_anphuoc_3f1882b9e8.jpg?1431191.8649999998`,
    link: "https://anphuochospital.medpro.com.vn"
  },
  {
    title: "bệnh viện đại học y dược",
    image: `https://cms.medpro.com.vn/uploads/small_umc_65b2859915.jpg?1431193.2100000004`,
    link: "https://umc.medpro.com.vn/"
  },
  {
    title: "bệnh viện đa khoa đồng nai",
    image: `https://cms.medpro.com.vn/uploads/small_dongnai_0fe06d735d.jpg?1431219.7100000002`,
    link: "https://bvdongnai.medpro.com.vn/"
  },
  {
    title: "bệnh viện trưng vương",
    image: `https://cms.medpro.com.vn/uploads/small_trungvuong_2c48b8893a.jpg?1431194.42`,
    link: "https://trungvuonghcm.medpro.com.vn/"
  },
  {
    title: "bệnh viện bình thạnh",
    image: `https://cms.medpro.com.vn/uploads/small_binhthanh_e7308d3174.jpg?1431224.91`,
    link: "https://bvbinhthanh.medpro.com.vn/"
  },

  {
    title: "bệnh viện nhi đồng 1",
    image: `https://cms.medpro.com.vn/uploads/small_nhidong1_0f7279b8fb.jpg?1431198.0000000002`,
    link: "https://nhidong1.medpro.com.vn/"
  },
  {
    title: "bệnh viên lê lợi",
    image: `https://cms.medpro.com.vn/uploads/small_leloi_b6a4c841b0.jpg?1431220.7399999998`,
    link: "https://leloi.medpro.com.vn"
  },
  {
    title: "bệnh viên thủ đức",
    image: `https://cms.medpro.com.vn/uploads/small_thuduc_234b425728.jpg?1431221.8800000001`,
    link: "https://thuduc.medpro.com.vn/"
  }
];
