import cx from "classnames";
import { MDB<PERSON><PERSON>I<PERSON>, MDBPageNav, MDBPagination } from "mdbreact";
import React from "react";
import styles from "./style.module.scss";
const Pagination = () => {
  return (
    <MDBPagination className={cx(styles["custom-pagi"], " mb-4")}>
      <MDBPageItem>
        <MDBPageNav aria-label="Previous">
          <span aria-hidden="true">&laquo;</span>
        </MDBPageNav>
      </MDBPageItem>
      <MDBPageItem>
        <MDBPageNav>1</MDBPageNav>
      </MDBPageItem>
      <MDBPageItem className={styles.active}>
        <MDBPageNav>2</MDBPageNav>
      </MDBPageItem>
      <MDBPageItem>
        <MDBPageNav>3</MDBPageNav>
      </MDBPageItem>
      <MDBPageItem>
        <MDBPageNav aria-label="Previous">
          <span aria-hidden="true">&raquo;</span>
        </MDBPageNav>
      </MDBPageItem>
    </MDBPagination>
  );
};

export default Pagination;
