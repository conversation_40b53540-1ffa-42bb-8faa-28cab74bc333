import React, { Component } from "react";
import { connect } from "react-redux";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import IntroMobileApp from "~/components/mobile/pages/IntroApp";

class IntroApp extends Component {
  render() {
    return (
      <React.Fragment>
        <IntroMobileApp />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {};

const UsageRulesHelmet = withTitle({
  component: IntroApp,
  title: "Medpro | Giới thiệu"
});

export default connect(mapStateToProps)(UsageRulesHelmet);
