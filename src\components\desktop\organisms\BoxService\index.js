import cx from "classnames";
import { MDBModal, MDBModalBody } from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import PKHBtn from "~/components/common/atoms/Button";
import TitlePage from "~/components/common/atoms/TitlePage";
import ListServiceDesktop from "~/components/desktop/molecules/ListService";
import history from "~/history";
import AppId from "~/utils/partner";
import {
  selectFeature,
  selectFeatureById
} from "~/store/features/featuresActions";
import styles from "./style.module.scss";

class BoxService extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showModalDangKyTiemCovid: false
    };
  }

  handleChooseFeature = type => {
    console.log(type);
    this.props.selectFeature(type);
  };

  toggleShowModalDangKyTiemCovid = params => {
    this.setState({
      showModalDangKyTiemCovid: !this.state.showModalDangKyTiemCovid
    });
  };

  handlerRouter = params => {
    switch (params) {
      case "personal":
        return history.push("/khai-bao-ho-so-ca-nhan");

      case "business":
        return history.push("/khai-bao-ho-so-doanh-nghiep");

      default:
        break;
    }
  };

  renderModalDangKyVacxin() {
    return (
      <MDBModal
        isOpen={this.state.showModalDangKyTiemCovid}
        toggle={() => this.toggleShowModalDangKyTiemCovid()}
        centered
        className={styles.modal}
      >
        <MDBModalBody className={styles.modalBody}>
          <ul>
            <li className={styles.item}>
              <span className={styles.title}>Đăng ký vacxin covid-19</span>
            </li>
            <li className={styles.item}>
              <p className={styles.subTitle}>
                Chỉ dành riêng cho Khu Công Nghiệp Phần Mền Quang Trung
              </p>
            </li>
            <li className={styles.item}>
              <PKHBtn
                create="create"
                onClick={() => this.handlerRouter("personal")}
              >
                Cá nhân
              </PKHBtn>
            </li>
            <li className={styles.item}>
              <PKHBtn
                create="create"
                onClick={() => this.handlerRouter("business")}
              >
                Doanh nghiệp
              </PKHBtn>
            </li>
            <li className={styles.item}>
              <PKHBtn
                flat
                onClick={() => this.toggleShowModalDangKyTiemCovid()}
              >
                Hủy bỏ
              </PKHBtn>
            </li>
          </ul>
        </MDBModalBody>
      </MDBModal>
    );
  }

  checkHidden = () => {
    // partner có trong list thì hiển thị nút btn
    const listPartnerShowBtnVacxin = [""];
    if (listPartnerShowBtnVacxin.includes(AppId)) {
      return null;
    } else {
      return "d-none";
    }
  };

  render() {
    return (
      <div
        className={cx(
          styles.box_service,
          styles["box_advance_medpro_" + AppId]
        )}
      >
        <div className={cx(styles.box_service_item, styles.active)}>
          <TitlePage title="Chọn dịch vụ" className="text_center" />
          <ListServiceDesktop />
          <PKHBtn
            color="red"
            className={cx(styles.btnVacxin, this.checkHidden())}
            onClick={() => this.toggleShowModalDangKyTiemCovid()}
          >
            Đăng ký tiêm vắc-xin covid-19
          </PKHBtn>
        </div>
        {this.renderModalDangKyVacxin()}
      </div>
    );
  }
}

const mapStateToProps = state => {
  const {
    user: { IsAuthenticated },
    features: {
      allFeatures: { loading, list }
    }
  } = state;
  return {
    list,
    loading,
    IsAuthenticated
  };
};
export default connect(mapStateToProps, { selectFeature, selectFeatureById })(
  BoxService
);
