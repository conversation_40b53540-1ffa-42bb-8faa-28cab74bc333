import cx from "classnames";
import { get } from "lodash";
import { MDBAlert, MDBInput, MDBListGroup, MDBListGroupItem } from "mdbreact";
import React, { useState } from "react";
import styles from "./style.module.scss";
import partnerId from "~/utils/partner";
const ListPaymentMethod = ({
  allPaymentMethodGroups,
  handleSelectedMethod,
  selectedMethod,
  selectPaymentMethodGroup,
  selectedPaymentMethodGroupId
}) => {
  const [ck, setCK] = useState(null);

  const code = get(ck, "code", "");
  const bankName = get(ck, "name", "");
  const bankAccount = get(ck, "bankAccount", "");
  const accountHolder = get(ck, "bankAccount.accountHolder", "");
  const accountNumber = get(ck, "bankAccount.accountNumber", 0);
  const bankBranch = get(ck, "bankAccount.bankBranch", "");

  return allPaymentMethodGroups.map((group, i) => {
    const paymentTypes = get(group, "paymentTypes", []);
    return (
      <div
        key={i}
        className={cx(
          styles.group_payment_item,
          styles["group_payment_item_" + partnerId],
          group.methodId === selectedPaymentMethodGroupId
            ? [styles.active, styles["active_" + partnerId]]
            : ""
        )}
      >
        <MDBInput
          checked={group.methodId === selectedPaymentMethodGroupId}
          label={group.name}
          type="radio"
          id={group.methodId}
          onClick={() => selectPaymentMethodGroup(group)}
          disabled={group.status === 0}
        />
        <div className={styles.list_card}>
          <MDBListGroup className={styles.list_group}>
            {paymentTypes.map((item, i) => {
              const imageErrorSrc = "/assets/img/payment.gif";
              const urlImage = item.paymentIcon.path || imageErrorSrc;

              return (
                <MDBListGroupItem
                  key={i}
                  className={
                    selectedMethod.code === item.code ? styles.active : ""
                  }
                  onClick={() => {
                    handleSelectedMethod(item, group.methodId);
                    setCK(item);
                  }}
                >
                  <img
                    onError={e => {
                      e.target.src = imageErrorSrc;
                    }}
                    src={urlImage}
                    alt={item.code}
                  />
                </MDBListGroupItem>
              );
            })}

            {bankAccount && selectedMethod.code === code && (
              <MDBAlert color="primary" className={styles.method_ck}>
                {bankName} <br />
                Tên tài khoản: <strong>{accountHolder}</strong> <br />
                Số tài khoản: <strong>{accountNumber}</strong>
                <br />
                Chi nhánh: <strong>{bankBranch}</strong>
              </MDBAlert>
            )}
          </MDBListGroup>
        </div>
      </div>
    );
  });
};

export default ListPaymentMethod;
