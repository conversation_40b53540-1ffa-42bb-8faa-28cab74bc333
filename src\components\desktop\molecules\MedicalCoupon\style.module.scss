@import "src/assets/scss/custom-variables.scss";

.loading {
  min-height: 70vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  align-content: center;
  opacity: 0.5;
}

.listCoupon {
  display: flex;
  gap: 1rem;
}

.sliderCoupon {
  width: 100vw;
  min-width: 360px;
  display: flex;
  justify-content: center;

  position: relative;
  > button {
    position: absolute;
    top: 0;
    &:first-child {
      left: 0;
    }
    &:last-child {
      right: 0;
    }
  }

  > div {
    // margin-top: 35px;
    > div {
      display: flex;

      @media (min-width: 560px) {
        justify-content: center;
      }
      > div {
        display: flex !important;

        height: auto;
        // align-items: center; //optional
        // justify-content: center; //optional
        padding: 15px;
        > div {
          cursor: pointer;
          width: 100%;
        }
      }
    }
  }
}
