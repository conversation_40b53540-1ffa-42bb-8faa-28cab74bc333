import { MDBModal, MDBModalBody, MDBModalHeader } from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import { getFormatMoney } from "~/utils/getFormatMoney";
import styles from "./style.module.scss";
import moment from "moment";

class ModalHistoryBill extends Component {
  state = {
    z: ""
  };

  renderChildTd(item, key) {
    return (
      <>
        {item?.sequenceNumber && (
          <p className={styles.itemTd}>
            <span className={styles.label}>STT : </span>
            <span className={styles.value}>{item?.sequenceNumber}</span>
          </p>
        )}{" "}
        {item?.room && (
          <p className={styles.itemTd}>
            <span className={styles.label}>Phòng : </span>
            <span className={styles.value}>{item?.room}</span>
          </p>
        )}{" "}
        {item?.subject && (
          <p className={styles.itemTd}>
            <span className={styles.label}>Chuyên khoa : </span>
            <span className={styles.value}>{item?.subject}</span>
          </p>
        )}{" "}
        {item?.doctor && (
          <p className={styles.itemTd}>
            <span className={styles.label}>Bác sĩ : </span>
            <span className={styles.value}>{item?.doctor}</span>
          </p>
        )}{" "}
        {item?.service && (
          <p className={styles.itemTd}>
            <span className={styles.label}>Dịch vụ : </span>
            <span className={styles.value}>{item?.service}</span>
          </p>
        )}{" "}
        {(item?.totalText || item?.originTotalText) && (
          <p className={styles.itemTd}>
            <span className={styles.label}>Tổng tiền : </span>
            <span className={styles.value}>
              {item?.totalText || item?.originTotalText}
            </span>
          </p>
        )}{" "}
        {item?.date && (
          <p className={styles.itemTd}>
            <span className={styles.label}>Thời gian : </span>
            <span className={styles.value}>{item?.date}</span>
          </p>
        )}{" "}
      </>
    );
  }

  renderDifferentPrice = item => {
    const prePrice = Number(item.prevBooking.originTotal);
    const changePrice = Number(item.bookingChanged.total);

    const colorClass = params => {
      if (prePrice === changePrice) {
        return "";
      } else if (prePrice < changePrice) {
        return "text-danger";
      } else {
        return "text-success";
      }
    };

    return (
      <p className={colorClass()}>
        {prePrice === changePrice
          ? "-"
          : getFormatMoney(prePrice - changePrice) + "VND"}
      </p>
    );
  };

  render() {
    const { historyBooking } = this.props;
    return (
      <React.Fragment>
        <MDBModal
          isOpen={this.props.isOpen}
          toggle={() => this.props.toggle()}
          backdrop={false}
          className={styles.ModalHistoryBill}
        >
          <MDBModalHeader toggle={() => this.props.onClose()}>
            Lịch sử thay đổi phiếu
          </MDBModalHeader>
          <MDBModalBody>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th>STT</th>
                  <th>Thông tin cũ</th>
                  <th>Thông tin mới</th>
                  <th>Chênh lệch với phí gốc</th>
                </tr>
              </thead>
              <tbody>
                {historyBooking?.count < 1 ? (
                  <tr>
                    <td colSpan={1000}>
                      <p className={styles.noRow}>
                        Chưa có lịch sử thay đổi phiếu !
                      </p>
                    </td>
                  </tr>
                ) : (
                  historyBooking?.data?.map((item, i) => {
                    return (
                      <tr key={item.bookingId}>
                        <td>{i + 1}</td>
                        <td>
                          {this.renderChildTd(item.prevBooking, "pre")}
                          <p> - </p>
                        </td>
                        <td>
                          {this.renderChildTd(item.bookingChanged, "change")}
                          {item?.userPortal ? (
                            <p>
                              <span className={styles.label}>
                                Thay đổi bởi :{" "}
                              </span>
                              <span className={styles.value}>
                                {item?.userPortal} (Portal)
                              </span>
                            </p>
                          ) : (
                            <p>
                              <span className={styles.label}>
                                Thay đổi bởi:{" "}
                              </span>

                              <span>
                                {item.user?.username}
                                {item.user?.fullname
                                  ? ` - ${item.user?.fullname}`
                                  : ""}
                              </span>
                            </p>
                          )}
                          <p className={styles.itemTd}>
                            <span className={styles.label}>
                              Thời gian thay đổi :{" "}
                            </span>
                            <span className={styles.value}>
                              {moment(item?.createdAt).format(
                                "HH:mm DD/MM/YYYY"
                              )}
                            </span>
                          </p>
                        </td>
                        <td>{this.renderDifferentPrice(item)}</td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </MDBModalBody>
        </MDBModal>
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { historyBooking }
  } = state;
  return {
    historyBooking
  };
};

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ModalHistoryBill);
