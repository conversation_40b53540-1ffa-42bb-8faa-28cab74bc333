import React from "react";
import { MDBInput, MDBModal, MDBModalBody } from "mdbreact";
import styles from "./style.module.scss";
import { connect } from "react-redux";
import Select, { components } from "react-select";
import { client } from "~/utils/medproSDK";
import { openToast } from "~/components/common/molecules/ToastNotification";
import { get, isEmpty } from "lodash";
import moment from "moment";
import cx from "classnames";
import { getExtraConfig } from "~/store/totalData/actions";
import { normalizeBankAccountName } from "~/utils/getReplaceUTF8";

class ModalCancelBillPay extends React.Component {
  _isMounted = false;
  state = {
    currentStep: 1,
    phone: "",
    showNotification: false,
    cancelReasons: [],
    selectedReasons: [],
    otherReason: "",
    bankList: [],
    refundInfo: {
      bankName: "",
      accountNumber: "",
      accountHolder: "",
      refundAmount: 0
    }
  };

  handleNextStep = () => {
    if (this.state.currentStep === 1) {
      this.setState({ currentStep: this.state.currentStep + 1 });
      if (this.state.bankList.length === 0) {
        this.fetchBankList();
      }
    }
  };

  handleReasonChange = key => {
    const { cancelReasons } = this.state;

    // Cập nhật trạng thái của reason được click
    const updatedReasons = cancelReasons.map(reason =>
      reason.key === key ? { ...reason, selected: !reason.selected } : reason
    );

    // Lấy array các key đã được chọn
    const selectedKeys = updatedReasons
      .filter(reason => reason.selected)
      .map(reason => reason.key);

    this.setState({
      cancelReasons: updatedReasons,
      selectedReasons: selectedKeys // Lưu array key để submit
    });

    return selectedKeys;
  };

  handleOtherReasonChange = e => {
    this.setState({ otherReason: e.target.value });
  };

  fetchBankList = async () => {
    try {
      const response = await client.getBankListWeb();
      const { data } = response;
      if (data && this._isMounted) {
        this.setState({
          bankList: data || []
        });
      }
    } catch (error) {
      console.error("Error fetching refund reasons:", error);
    }
  };

  fetchReasons = async () => {
    try {
      const { data } = await client.getCancellationReasonsListWeb();
      if (data && this._isMounted) {
        this.setState({
          cancelReasons: data || []
        });
      }
    } catch (error) {
      console.error("Error fetching refund reasons:", error);
    }
  };

  handleCloseModal = () => {
    this.setState({
      showCancelModal: false,
      currentStep: 1, // Reset về bước 1
      // Reset các trường của Step 1
      cancelReasons: this.state.cancelReasons.map(reason => ({
        ...reason,
        selected: false
      })),
      otherReason: "",
      // Reset các trường của Step 2
      refundInfo: {
        bankName: "",
        accountNumber: "",
        accountHolder: "",
        refundAmount: 0
      }
    });
    this.props.toggle();
  };

  renderStepContent = () => {
    const { currentStep } = this.state;

    if (currentStep === 1) {
      return this.renderStep1();
    } else if (currentStep === 2) {
      return this.renderStep2();
    }
  };

  renderStep1 = () => {
    const { cancelReasons, otherReason } = this.state;
    const { billInfo } = this.props;
    const showOtherTextarea = cancelReasons.some(
      reason => reason.key === "other" && reason.selected
    );

    // Kiểm tra điều kiện để nút "Tiếp tục" được kích hoạt
    const hasSelectedReasons = cancelReasons.some(reason => reason.selected);
    const hasSelectedOther = cancelReasons.some(
      reason => reason.key === "other" && reason.selected
    );
    const isOtherTextValid = hasSelectedOther
      ? otherReason.trim() !== ""
      : true;

    const isNextEnabled = hasSelectedReasons && isOtherTextValid;
    return (
      <div>
        <p className={styles.modalDescription}>
          Medpro mong nhận được sự góp ý của bạn để có thể phục vụ tốt hơn
        </p>

        <div className={styles.reasonList}>
          {cancelReasons.map(reason => (
            <div key={reason.key} className={styles.card_body_cance}>
              <MDBInput
                type="checkbox"
                id={reason.key}
                onChange={() => this.handleReasonChange(reason.key)}
                label={reason.label}
                className={styles.reasonCheckbox}
                checked={!!reason.selected}
              />
            </div>
          ))}
        </div>

        {showOtherTextarea && (
          <textarea
            className={styles.reasonTextarea}
            placeholder="Điều khiến bạn muốn hủy phiếu khám"
            value={otherReason}
            onChange={this.handleOtherReasonChange}
          />
        )}

        <div className={styles.modalActions}>
          <button
            onClick={this.handleCloseModal}
            className={styles.cancelButton}
          >
            Không hủy
          </button>
          <button
            disabled={!isNextEnabled}
            onClick={
              billInfo.cancelStep
                ? this.handleNextStep
                : this.handleSubmitCancel
            }
            className={styles.confirmButton}
          >
            {billInfo.cancelStep ? "Tiếp tục" : "Gửi"}
          </button>
        </div>
      </div>
    );
  };

  CustomSingleValue = ({ data, ...props }) => {
    return (
      <components.SingleValue {...props} className={styles.singleValueBox}>
        <div className={styles.singleValue}>
          <span className={styles.label}>{data.label}</span>
        </div>
      </components.SingleValue>
    );
  };

  renderStep2 = () => {
    const { refundInfo, bankList } = this.state;
    const { billInfo, extraConfig } = this.props;

    return (
      <div>
        <div className={styles.appointmentInfo}>
          <h5 className={styles.sectionTitle}>Thông tin phiếu khám</h5>
          <div className={styles.infoGrid}>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>Mã phiếu:</span>
              <span className={styles.infoValue}>{billInfo.smsCode}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>Tên bệnh nhân:</span>
              <span className={styles.infoValue}>
                {billInfo.patientVersion.surname} {billInfo.patientVersion.name}
              </span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>Ngày khám:</span>
              <span className={styles.infoValue}>
                {moment(get(billInfo, "date")).format("DD/MM/YYYY")}
              </span>
            </div>
          </div>
        </div>

        <div className={styles.refundForm}>
          <h5 className={styles.sectionTitle}>
            Nhập thông tin tài khoản ngân hàng để nhận tiền hoàn
          </h5>
          <div className={styles.formGroup}>
            <label className={styles.formLabel}>
              Ngân hàng <span>*</span>
            </label>
            <Select
              className={styles.formSelect}
              placeholder="Chọn ngân hàng"
              isSearchable={true}
              filterOption={(option, inputValue) => {
                if (!inputValue) return true;
                const searchText = inputValue.toLowerCase();
                return (
                  option.data.label.props.children[1].props.children.toLowerCase().includes(searchText) ||
                  option.data.value.toLowerCase().includes(searchText)
                );
              }}
              onChange={e => {
                this.setState({
                  refundInfo: {
                    ...refundInfo,
                    bankName: e.value,
                    bankCode: e.key
                  }
                });
              }}
              options={bankList.map(bank => ({
                key: bank.code,
                value: bank.shortName,
                label: (
                  <div className={styles.singleValue}>
                    <img
                      src={bank.logo}
                      width={20}
                      height={20}
                      alt={bank.shortName}
                      className={styles.icon}
                    />
                    <span className={styles.label}>{bank.name}</span>
                  </div>
                ),
                icon: bank.logo
              }))}
              components={{
                SingleValue: this.CustomSingleValue
              }}
              noOptionsMessage={({ inputValue }) =>
                !inputValue.trim()
                  ? 'Nhập tên ngân hàng để tìm kiếm'
                  : 'Không tìm thấy ngân hàng phù hợp'
              }
            />
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label className={styles.formLabel}>
                Số tài khoản <span>*</span>
              </label>
              <input
                type="text"
                className={styles.formInput}
                placeholder="Nhập số tài khoản"
                onChange={e => {
                  this.setState({
                    refundInfo: {
                      ...refundInfo,
                      bankNumber: e.target.value
                    }
                  });
                }}
              />
            </div>
            <div className={styles.formGroup}>
              <label className={styles.formLabel}>
                Tên chủ tài khoản <span>*</span>
              </label>
              <input
                type="text"
                className={styles.formInput}
                placeholder="Nhập tên chủ tài khoản"
                value={refundInfo.accountName}
                onChange={e => {
                  const inputValue = e.target.value;
                  const formatted = normalizeBankAccountName(inputValue);
                  console.log("formatted", formatted);
                  this.setState({
                    refundInfo: {
                      ...refundInfo,
                      accountName: formatted
                    }
                  });
                }}
              />
            </div>
          </div>
        </div>

        <div
          className={styles.refundNote}
          dangerouslySetInnerHTML={{ __html: extraConfig.cancelNoteBooking }}
        />

        <div className={styles.modalActions}>
          <button
            onClick={this.handleCloseModal}
            className={styles.cancelButton}
          >
            Không hủy
          </button>
          <button
            onClick={this.handleSubmitCancel}
            className={styles.confirmButton}
          >
            Gửi
          </button>
        </div>
      </div>
    );
  };

  handleSubmitCancel = async () => {
    const { selectedReasons, otherReason, refundInfo } = this.state;
    const { billInfo: bill, actionType = "cancel" } = this.props;

    // Kiểm tra phải có ít nhất một lý do được chọn
    if (!selectedReasons || selectedReasons.length === 0) {
      openToast("Vui lòng chọn ít nhất một lý do hủy phiếu", "error");
      return;
    }

    // Lấy bookingId từ bill
    const bookingId = bill?._id;
    if (!bookingId) {
      openToast("Không tìm thấy thông tin booking", "error");
      return;
    }

    // Chuẩn bị reasonIds array
    const reasonIds = [...selectedReasons];

    // Nếu có otherReason thì thêm "other" vào reasonIds
    if (otherReason && otherReason.trim()) {
      if (!reasonIds.includes("other")) {
        reasonIds.push("other");
      }
    }

    const cancelData = {
      reasonIds: reasonIds,
      otherContent: otherReason.trim() || ""
    };

    if (bill?.cancelStep) {
      // Kiểm tra các thông tin bắt buộc
      if (!refundInfo.bankName) {
        openToast("Vui lòng chọn ngân hàng", "error");
        return;
      }

      if (!refundInfo.bankNumber) {
        openToast("Vui lòng nhập số tài khoản", "error");
        return;
      }

      if (!refundInfo.accountName) {
        openToast("Vui lòng nhập tên chủ tài khoản", "error");
        return;
      }

      cancelData.bankCode = refundInfo.bankCode;
      cancelData.bankName = refundInfo.bankName;
      cancelData.bankNumber = refundInfo.bankNumber;
      cancelData.accountName = refundInfo.accountName;
    }

    // Tạo dữ liệu gửi API theo format yêu cầu

    try {
      // Gọi API hủy phiếu
      const response = await client.cancelBookingByIdWeb(bookingId, cancelData);

      if (response) {
        openToast("Yêu cầu hủy phiếu đã được gửi thành công", "success");
        this.handleCloseModal();

        // Gọi callback sau khi xử lý thành công nếu có
        if (this.props.onFallbackSuccess) {
          this.props.onFallbackSuccess();
        }
      }
    } catch (error) {
      openToast(
        error.response?.data?.message ||
        "Có lỗi xảy ra khi gửi yêu cầu hủy phiếu. Vui lòng thử lại sau",
        "error"
      );
    }
  };

  componentDidMount() {
    const { extraConfig, getExtraConfig, billInfo } = this.props;
    this._isMounted = true;
    this.fetchReasons();
    if (isEmpty(extraConfig)) {
      getExtraConfig(billInfo?.partnerId);
    }
  }

  componentWillUnmount() {
    this._isMounted = false;
  }

  render() {
    const { isOpen, toggle, billInfo } = this.props;
    return (
      <MDBModal isOpen={isOpen} toggle={toggle} className={styles.cancelModal}>
        <MDBModalBody className={styles.modalBody}>
          <h3>HỦY PHIẾU KHÁM</h3>

          {/* Step Indicator với logic active */}
          <div
            className={billInfo.cancelStep ? styles.stepIndicator : "d-none"}
          >
            <div
              className={cx(
                styles.stepCircle,
                this.state.currentStep >= 1
                  ? styles.stepActive
                  : styles.stepInactive
              )}
            >
              {this.state.currentStep > 1 ? "✓" : "1"}
            </div>
            <div
              className={cx(
                styles.stepLine,
                this.state.currentStep > 1
                  ? styles.lineActive
                  : styles.lineInactive
              )}
            />
            <div
              className={cx(
                styles.stepCircle,
                this.state.currentStep >= 2
                  ? styles.stepActive
                  : styles.stepInactive
              )}
            >
              2
            </div>
          </div>

          {/* Render nội dung theo step */}
          {this.renderStepContent()}
        </MDBModalBody>
      </MDBModal>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { historyBooking },
    totalData: { extraConfig }
  } = state;
  return {
    historyBooking,
    extraConfig
  };
};

const mapDispatchToProps = {
  getExtraConfig: getExtraConfig
};

export default connect(mapStateToProps, mapDispatchToProps)(ModalCancelBillPay);
