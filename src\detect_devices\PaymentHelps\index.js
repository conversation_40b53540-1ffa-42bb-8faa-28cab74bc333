/* eslint-disable react/jsx-handler-names */
import { find, get, reduce } from "lodash";
import { MD<PERSON>lert, MDBSpinner } from "mdbreact";
import React, { Component, Fragment } from "react";
import Loadable from "react-loadable";
import { connect } from "react-redux";
import {
  resetAlertSubmitFail,
  submitPayment
} from "~/actions/umc/submitPayment";
import Alert from "~/components/common/atoms/Alert";
import Modal from "~/components/common/molecules/Modal";
import { withTitle } from "~/components/common/molecules/TitleHelmet";
import LoadableLoading from "~/components/mobile/molecules/LoadingPage";
import { partnerInfo } from "~/configs/partnerDetails";
import {
  getAllPaymentMethod,
  hideModalPaymentMethod,
  resetPaymentMethod,
  selectedPaymentMethod,
  togglePaymentMethod
} from "~/store/payment/paymentAction";
import {
  bookingShareToPay,
  getBookingTree,
  repaymentBooking,
  reserveBooking
} from "~/store/totalData/actions";
import { getRouteFollowPartnerId } from "~/utils/func";
import styles from "./style.module.scss";

const info = get(partnerInfo, "info");
const hospitalName = find(info, { key: "name" });

const Desktop = Loadable({
  loader: () =>
    import(/* desktop */ "~/components/desktop/pages/PaymentHelpsMethod"),
  loading: LoadableLoading
});

class DetectPaymentMethod extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowAlert: false,
      isShowPaymentConfirm: false,
      loadingSubmitBooking: false
    };
  }

  methods = {
    calculate: () => {
      const { schedulesSelected, selectedMethod } = this.props;
      // summary để dùng tính tổng tiền cho nhiều chuyên khoa (umc)
      const total = reduce(
        schedulesSelected,
        function(sum, item) {
          const price = Number(item.service.price);
          return sum + price;
        },
        0
      );
      const medpro = selectedMethod.medproFee
        ? Number(selectedMethod.medproFee)
        : 0;
      const fee = selectedMethod.rate ? Number(selectedMethod.rate) : 0;
      const feeMore = selectedMethod.constRate
        ? Number(selectedMethod.constRate)
        : 0;
      const phi = parseInt((total + medpro) * fee + feeMore);
      const phiThanhToan = phi;
      const phiThanhToanGiaoDich = phiThanhToan || 0;
      const totalPhi = medpro + phiThanhToanGiaoDich;
      return {
        total,
        totalPhi
      };
    },
    toggleAlertFail: () => {
      this.props.OnResetErrorMessgeSubmitPayment();
      this.setState(state => ({
        loadingSubmitBooking: false
      }));
    },
    handleDoPayment: () => {
      const { isRepayment } = this.props;
      if (isRepayment) {
        this.props.repaymentBooking();
      } else {
        this.props.reserveBooking();
      }
      this.setState(state => ({
        isShowPaymentConfirm: false
      }));
    },

    toggleShowPaymentConfirm: () => {
      this.setState(state => ({
        isShowPaymentConfirm: !state.isShowPaymentConfirm
      }));
    },

    handleSelectedMethod: (method, methodId) => {
      this.props.selectedPaymentMethod(method, methodId);
    },
    toggleAlert: () => {
      this.setState({
        isShowAlert: !this.state.isShowAlert
      });
    },
    handleGoBack: () => {
      const { partnerId } = this.props;
      const route = getRouteFollowPartnerId("/xac-nhan-thong-tin", partnerId);
      this.props.history.push(route);
    }
  };

  styles = {
    loading: {
      position: "fixed",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)"
    },

    loading_submit_booking: {
      position: "fixed",
      top: 0,

      left: 0,
      bottom: 0,
      right: 0,
      zIndex: 1000,
      backgroundColor: "black",
      opacity: 0.5
    },

    body_modal: {
      fontSize: "1rem"
    },
    body_modal_image: {
      margin: "auto",
      display: "block",
      objectFit: "cover",
      marginBottom: "10px",
      maxHeight: "100px"
    }
  };

  renderBodyModalConfirmPayment = () => {
    const { selectedMethod } = this.props;
    const selectedMethodName =
      Object.keys(selectedMethod).length > 0
        ? selectedMethod.name
        : "Chọn phương thức thanh toán";

    const price = get(selectedMethod, "grandTotal", 0);
    const selectedMethodImage = get(selectedMethod, "paymentIcon.path", "");

    if (selectedMethod.bankAccount) {
      const accountHolder = get(
        selectedMethod,
        "bankAccount.accountHolder",
        ""
      );
      const accountNumber = get(
        selectedMethod,
        "bankAccount.accountNumber",
        ""
      );
      const bankBranch = get(selectedMethod, "bankAccount.bankBranch", "");
      return (
        <Fragment>
          <div style={this.styles.body_modal}>
            {selectedMethodImage && (
              <img
                style={this.styles.body_modal_image}
                src={selectedMethodImage}
                alt={selectedMethodName}
              />
            )}
            <MDBAlert color="primary">
              <p>Thông tin chuyển khoản:</p>
              Tài khoản: <strong>{accountHolder}</strong> <br />
              Số tài khoản: <strong>{accountNumber}</strong> <br />
              Chi nhánh: <strong>{bankBranch}</strong> <br />
              Số tiền: <strong>{Number(price).toLocaleString("vi-VN")}đ</strong>
            </MDBAlert>
          </div>
        </Fragment>
      );
    }

    return (
      <Fragment>
        <div style={this.styles.body_modal}>
          {selectedMethodImage && (
            <img
              style={this.styles.body_modal_image}
              src={selectedMethodImage}
              alt={selectedMethodName}
            />
          )}
          <p>
            Thanh toán số tiền{" "}
            <strong>{Number(price).toLocaleString("vi-VN")}đ</strong> bằng{" "}
            {selectedMethodName}
          </p>
          <MDBAlert color="primary">
            Bạn sẽ nhận được phiếu khám bệnh ngay khi{" "}
            <strong>thanh toán thành công</strong>. Trường hợp không nhận được
            phiếu khám bệnh, vui lòng liên hệ <strong>********</strong>.
          </MDBAlert>
        </div>
      </Fragment>
    );
  };

  componentDidMount() {
    const { resetPaymentMethod, hideModalPaymentMethod } = this.props;

    resetPaymentMethod();
    hideModalPaymentMethod();
    const {
      match: {
        params: { code }
      }
    } = this.props;
    this.props.bookingShareToPay(code);
  }

  componentWillUnmount() {
    this.props.OnResetErrorMessgeSubmitPayment();
  }

  render() {
    const { errorMsg, errorBooking, loading } = this.props;

    const {
      // selectedMethod,
      isShowAlert,
      isShowPaymentConfirm,
      loadingSubmitBooking
    } = this.state;

    // const objCheckNextStep = Object.create(null);
    // if (Object.keys(selectedMethod).length > 1) {
    //   objCheckNextStep.nextStep = true;
    // }

    const errorMessage =
      errorBooking.error_code === 1011
        ? `Chuyên khoa ${errorBooking.booking_info.subject.name} 
        đã hết số khung giờ này. Vui lòng chọn lại khung giờ khác!`
        : errorBooking.error_code;

    if (loading) {
      return (
        <div className={styles.loading_spinner}>
          <div className={styles.loading}>
            <MDBSpinner big crazy tag="div" />
          </div>
        </div>
      );
    }

    return (
      <Fragment>
        <Desktop {...this.props} {...this.methods} />

        {loadingSubmitBooking && !errorMsg && (
          <div style={this.styles.loading_submit_booking}>
            <div style={this.styles.loading}>
              <MDBSpinner big crazy tag="div" />
            </div>
          </div>
        )}

        <Modal
          iconTitle={<i className="fal fa-bell" />}
          modal={isShowPaymentConfirm}
          title="Xác nhận thanh toán"
          children={this.renderBodyModalConfirmPayment()}
          centered
          footer
          footerConfirm
          className="centered"
          toggle={this.methods.toggleShowPaymentConfirm}
          cancelText="Quay lại"
          okText="Đồng ý"
          onCancel={this.methods.toggleShowPaymentConfirm}
          onOk={this.methods.handleDoPayment}
        />

        <Alert
          isModal={!!errorBooking}
          message={errorMessage || ""}
          toggleAlert={this.methods.toggleAlertFail}
        />
        <Alert
          isModal={isShowAlert}
          message="Vui lòng chọn phương thức thanh toán"
          toggleAlert={this.methods.toggleAlert}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    global: {
      device: { type }
    },
    user: { IsAuthenticated },
    payment: { selectedMethod, data, showModal, price },
    doctorAndTime: { sumaryInfo },
    umcSubmitPayment: {
      isRedirectToPaymentSupportPage,
      errorMsg,
      errorBooking
    },
    features: { selectedFeature, selectedFeatureBooking },
    totalData: {
      schedulesSelected,
      bookingTree,
      loadingReserveBooking,
      loading,
      isRepayment,
      partnerId,
      subject: { isHasSubject },
      service: { isHasService },
      room: { isHasRoom },
      doctor: { isHasDoctor },
      paymentInformation
    },
    hospital: { selectedHospital }
  } = state;
  return {
    IsAuthenticated,
    device: type,
    selectedMethod,
    sumaryInfo,
    isRedirectToPaymentSupportPage,
    errorMsg,
    errorBooking,
    showModal,
    data,
    selectedFeature,
    selectedFeatureBooking,
    schedulesSelected,
    bookingTree,
    loadingReserveBooking,
    price,
    loading,
    isRepayment,
    selectedHospital,
    partnerId,
    isHasSubject,
    isHasService,
    isHasRoom,
    isHasDoctor,
    paymentInformation
  };
};

const mapDispatchToProps = dispatch => ({
  bookingShareToPay: code => {
    dispatch(bookingShareToPay(code));
  },
  OnRequestAllPaymentMethod: () => {
    dispatch(getAllPaymentMethod());
  },
  OnTogglePaymentMethod: () => {
    dispatch(togglePaymentMethod());
  },
  OnSubmitPayment: () => {
    dispatch(submitPayment());
  },
  OnResetErrorMessgeSubmitPayment: () => {
    dispatch(resetAlertSubmitFail());
  },
  selectedPaymentMethod: (method, methodId) => {
    dispatch(selectedPaymentMethod(method, methodId));
  },
  hideModalPaymentMethod: () => {
    dispatch(hideModalPaymentMethod());
  },
  resetPaymentMethod: () => dispatch(resetPaymentMethod()),
  reserveBooking: () => dispatch(reserveBooking()),
  repaymentBooking: () => dispatch(repaymentBooking()),
  getBookingTree: (text, id) => dispatch(getBookingTree(text, id))
});

const PaymentHelpsMethodHelmet = withTitle({
  component: DetectPaymentMethod,
  title: `${hospitalName.value} | Chọn hình thức thanh toán`
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PaymentHelpsMethodHelmet);
