@import "src/assets/scss/custom-variables.scss";
.popup_modal {
  i {
    margin-right: 7px;
  }
}

// modal full màn hình
.full_height {
  height: calc(100% - 15px);
  > div {
    background-color: #ffffff;
    height: 100%;
  }
  .title_popup {
    border: 0;
    padding: 15px;
    h4 {
      color: #0352cc;
      font-size: 1.4rem !important;
    }
    button {
      color: #5274a0;
      opacity: 0.6;
    }
  }
  .body {
    padding-top: 0;
    max-height: 75vh;
    overflow: auto;
  }
}

// modal nằm giửa
.centered {
  z-index: 9999;
  .title_popup {
    border: 0;
    background: linear-gradient(90deg, #0352cc, #0c71d5);
    font-size: 0.875rem;
    padding-top: 12px;
    padding-bottom: 12px;
    align-items: center;
    h4 {
      color: #ffffff;
      font-size: 0.875rem !important;
    }
    button {
      color: #fff;
      opacity: 1;
    }
  }
}

// modal nằm dưới
.bottom {
  min-height: 0 !important;
  top: auto !important;
  display: block;
  max-width: 100%;
  height: auto;
  bottom: 0;
  margin: 0 !important;
  position: absolute !important;
  left: 10px;
  right: 10px;
  bottom: 10px;

  > div {
    padding: 15px;
  }
  .title_popup {
    border: 0;
    font-size: 0.875rem;
    padding: 0 0 1.6rem;
    h4 {
      font-size: 1.125rem;
      color: #0352cc;
      font-weight: 500;
    }
    button {
      color: #5274a0;
      opacity: 1;
    }
  }
  .body {
    padding: 0 0 15px 0;
  }
  .modal_footer {
    border-top: 3px solid #dfe3eb;
    padding: 15px 0 0 0;
  }
}

.body {
  color: #212529;
  i {
    margin-right: 10px;
    color: #0352cc;
    font-size: 0.875rem;
  }
}

.backdrop_centered {
  z-index: 1050 !important;
}

.popup_modal_minhanh {
  .title_popup {
    background: #db2233 !important;
  }
}
