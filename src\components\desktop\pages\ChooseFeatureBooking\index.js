import { <PERSON><PERSON><PERSON><PERSON>, MD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "mdbreact";
import React, { Component } from "react";
import { connect } from "react-redux";
import PKHBtn from "~/components/common/atoms/Button";
import TagName from "~/components/common/atoms/TagName";
import ListFeatureBooking from "~/components/common/molecules/ListFeatureBooking";
import styles from "./style.module.scss";
// import cx from "classnames";

class ChooseFeatureBooking extends Component {
  render() {
    // const { loading } = this.props;
    // if (loading) {
    //   return (
    //     <div className="loading">
    //       <MDBSpinner big />
    //     </div>
    //   );
    // }
    return (
      <MDBAnimation className={styles.wapper_page_desktop}>
        <MDBContainer>
          <MDBRow>
            <MDBCol size={12}>
              <div className={styles.wapper_page_inner}>
                <TagName
                  element="h1"
                  className={[
                    "title_component",
                    "title_line",
                    "title_header_mobile"
                  ]}
                >
                  <span><PERSON><PERSON><PERSON> h<PERSON>nh thức đặt khám</span>
                </TagName>

                <ListFeatureBooking {...this.props} />
                <div className={styles.next_prev}>
                  <PKHBtn
                    backdesktop="backdesktop"
                    onClick={this.props.handleGoBack}
                  >
                    Quay lại
                  </PKHBtn>
                </div>
              </div>
            </MDBCol>
          </MDBRow>
        </MDBContainer>
      </MDBAnimation>
    );
  }
}

export default connect(null, {})(ChooseFeatureBooking);
