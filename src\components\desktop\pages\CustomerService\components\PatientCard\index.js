import {
  MDBBtn,
  MDBCard,
  MDBCardBody,
  MDBCol,
  MDBContainer,
  MDBIcon,
  MDBRow
} from "mdbreact";
import React, { Component } from "react";
import { dataTransform } from "~/utils/func";
import styles from "./style.module.scss";
import cx from "classnames";

class PatientCard extends Component {
  render() {
    const { patientInfo, focus, onEdit, onDelete } = this.props;
    const { district, city } = patientInfo;
    let customStyle = {};
    let address = (
      <span style={{ color: "red" }}> Địa chỉ chưa được cập nhật đầy đủ</span>
    );
    if (district && city) {
      address = dataTransform(patientInfo.address, "full-address", [
        patientInfo?.ward?.name,
        patientInfo?.district?.name,
        patientInfo?.city?.name
      ]);
    }
    if (focus) {
      customStyle = { border: "1px solid blue" };
    }

    return (
      <MDBCard
        id={focus ? "activePatientCS" : ""}
        className="my-2 hover-translate-y-n10 hover-shadow-lg"
        style={customStyle}
      >
        <MDBCardBody className={cx(styles.cardBodyWrapper, "py-1 px-2")}>
          <MDBContainer fluid className="">
            <MDBRow>
              <MDBCol
                xl="8"
                lg="12"
                md="12"
                className="pr-1 pl-0"
                onClick={() => {
                  this.props.selectPatientHandler(patientInfo);
                }}
              >
                <ul
                  className={cx(
                    styles.listInfo,
                    styles.cardInfoWrapper,
                    "list-group list-unstyled d-flex flex-row flex-wrap"
                  )}
                >
                  <li>
                    <label
                      id="fullname"
                      className={cx(
                        styles.value,
                        styles.partner,
                        "font-weight-bolder red-text"
                      )}
                    >
                      <span>
                        {patientInfo.surname + " " + patientInfo.name}
                      </span>
                    </label>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="file-alt" className="px-1" />
                        <span>Mã hồ sơ: </span>
                      </label>
                      <label className={cx(styles.value)}>
                        {patientInfo.code}
                      </label>
                    </div>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="transgender" className="px-1" />
                        <span>Giới tính: </span>
                      </label>
                      <label className={cx(styles.value)}>
                        {patientInfo.sex ? "Nam" : "Nữ"}
                      </label>
                    </div>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="birthday-cake" className="px-1" />
                        <span>Ngày sinh: </span>
                      </label>
                      <label className={cx(styles.value)}>
                        {dataTransform(patientInfo.birthdate, "reformat-date", [
                          patientInfo.birthyear
                        ])}
                      </label>
                    </div>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="mobile-alt" className="px-1" />
                        <span>Số điện thoại: </span>
                      </label>
                      <label className={cx(styles.value)}>
                        {patientInfo.mobile}
                      </label>
                    </div>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)} style={{flexDirection: "column", alignItems: "flex-start", gap: 0}}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="map-marker-alt" className="px-1" />
                        <span>Địa chỉ: </span>
                      </label>
                      <label className={styles.value}> {address}</label>
                    </div>
                  </li>
                </ul>
              </MDBCol>
              <MDBCol xl="4" lg="12" md="12" className={styles.rowBtn}>
                {patientInfo?.isValid && (
                  <>
                    <MDBBtn
                      color="blue"
                      size="sm"
                      className={styles.btnAct}
                      onClick={() => {
                        this.props.btnBookingClickHandler(patientInfo);
                      }}
                    >
                      <MDBIcon far icon="calendar-alt" /> Đặt khám
                    </MDBBtn>
                    <MDBBtn
                      onClick={onEdit}
                      color="yellow"
                      size="sm"
                      className={styles.btnAct}
                    >
                      <MDBIcon icon="wrench" className="px-1" /> Sửa
                    </MDBBtn>
                    <MDBBtn
                      onClick={onDelete}
                      color="red"
                      size="sm"
                      className={styles.btnAct}
                    >
                      <MDBIcon icon="trash" className="px-1" /> Xóa
                    </MDBBtn>
                  </>
                )}
                {patientInfo?.isValid === false && (
                  <MDBBtn
                    color="green"
                    circle
                    className={styles.btnAct}
                    size="sm"
                    onClick={() => {
                      this.props.handleAddAgainUser(patientInfo);
                    }}
                  >
                    <MDBIcon icon="user-times" /> Thêm lại
                  </MDBBtn>
                )}
              </MDBCol>
            </MDBRow>
          </MDBContainer>
        </MDBCardBody>
      </MDBCard>
    );
  }
}

export default PatientCard;
