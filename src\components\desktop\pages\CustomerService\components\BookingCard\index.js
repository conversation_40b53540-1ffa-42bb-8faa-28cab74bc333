/* eslint-disable react/jsx-indent */
/* eslint-disable prettier/prettier */
/* eslint-disable react/jsx-handler-names */
/* eslint-disable react/jsx-curly-newline */
import cx from "classnames";
import {
  MDBBtn,
  MDBCard,
  MDBCardBody,
  MDBCol,
  MDBContainer,
  MDBIcon,
  MDBRow
} from "mdbreact";
import moment from "moment";
import React, { Component } from "react";
import { connect } from "react-redux";
import {
  getHistoryBooking,
  setDataHistoryBooking,
  getBookingbyPatientCSKH,
  getHistoryBookingCSKH,
  getCare247IndependentCSKH
} from "~/store/customerService/customerServiceActions";
import { appointmentDate, dataTransform } from "~/utils/func";
import ModalHistoryBill from "../ModalHistoryBill";
import styles from "./style.module.scss";
import Modal from "../../../../../common/molecules/Modal";
import { client } from "~/utils/medproSDK";
import { openToast } from "~/components/common/molecules/ToastNotification";
import { CLOSE, OK } from "~/utils/constants";
import ModalCancelCare247 from "../ModalCancelCare247";
import ModalRoom from "../ModalRoom";
import {
  fetchMedproCareRoom,
  fetchHistoryTelemdRoom
} from "~/store/booking/bookingApi";
import ModalCare247Pay from "~/components/desktop/pages/CustomerService/components/ModalCare247Pay";
import ModalCancelBillPay from "~/components/desktop/pages/CustomerService/components/ModalCancelBillPay";
import ModalBillRefundRequest from "~/components/desktop/pages/CustomerService/components/ModalBillRefundRequest";
import ModalCancelInfo from "../ModalCancelInfo";

class BookingCard extends Component {
  state = {
    modalCare247Pay: false,
    modalCancelBillPay: false,
    modalBillRefundRequest: false,
    modalHistoryBill: false,
    modalCancelInfo: false,
    isOpenUnlockModal: false,
    idUnlock: "",
    modalAllUserCs: false,
    allUserCs: [],
    medproCareInstructorId: "",
    isCancelCare247: false,
    isOpenRoom: false,
    roomData: null,
    historyTelemdRoom: null,
    loadingRoom: false,
    setCare247Independent: null,
    loadingCare247Pay: false,
    cancelActionType: "",
    isCancelInfo: false,
    cancelInfo: undefined,
    loadingCancelInfo: false
  };

  toggleModalHistoryBill = () => {
    this.setState({
      modalHistoryBill: !this.state.modalHistoryBill
    });
  };

  toggleModalCare247Pay = () => {
    this.setState({
      modalCare247Pay: !this.state.modalCare247Pay
    });
  };

  toggleModalCancelBillPay = () => {
    this.setState({
      modalCancelBillPay: !this.state.modalCancelBillPay
    });
  };

  toggleModalBillRefundRequest = () => {
    this.setState({
      modalBillRefundRequest: !this.state.modalBillRefundRequest
    });
  };

  toggleModalCancelInfo = () => {
    this.setState({
      modalCancelInfo: !this.state.modalCancelInfo
    });
    if (!this.state.modalCancelInfo) {
      this.fetchCancelInfo();
    }
  };

  fetchCare247Pay = async billInfo => {
    try {
      this.setState({
        loadingCare247Pay: true
      });
      this.props.setCare247Independent(billInfo._id);
    } catch (error) {
      console.log("error :>> ", error);
    } finally {
      this.setState({
        loadingCare247Pay: false
      });
    }
  };

  toggleModalRoom = () => {
    this.setState({
      isOpenRoom: !this.state.isOpenRoom
    });
  };

  fetchHistoryTelemdRoom = async () => {
    try {
      const response = await this.props.historyTelemdRoom({
        bookingCode: this.props.billInfo.bookingCode
      });
      this.setState({
        historyTelemdRoom: response.data
      });
    } catch (error) {
      console.log("error :>> ", error);
    }
  };

  fetchTelemedRoom = async () => {
    try {
      this.setState({
        loadingRoom: true
      });
      const response = await this.props.medproCareRoom({
        bookingCode: this.props.billInfo.bookingCode
      });
      this.setState({
        roomData: response.data
      });
    } catch (error) {
      this.setState({
        roomDataError: error.response.data.message,
        roomData: null
      });
    } finally {
      this.setState({
        loadingRoom: false
      });
    }
  };

  fetchCancelInfo = async () => {
    try {
      this.setState({
        loadingCancelInfo: true
      });
      const response = await client.getBookingCancellation(this.props.billInfo._id);
      console.log('response?.data', response?.data)

      this.setState({
        cancelInfo: response?.data
      });

    } catch (error) {
      console.log("error :>> ", error);
    } finally {
      this.setState({
        loadingCancelInfo: false
      });
    }
  };

  closeModalHistoryBill = () => {
    this.setState({
      modalHistoryBill: false
    });
    this.props.setDataHistoryBooking(null);
  };

  initiateStatus = (status, date, description) => {
    switch (status) {
      case 2: // đã khám
        return (
          <span
            className={styles.tag}
            style={{ backgroundColor: "lightgreen" }}
          >
            {description}
          </span>
        );
      case 1: // đã thanh toán
        return (
          <span
            className={styles.tag}
            style={{ backgroundColor: "lightgreen" }}
          >
            {description}
          </span>
        );
      case 0: // chưa thanh toán
        return (
          <span
            className={styles.tag}
            style={{ backgroundColor: "lightcoral" }}
          >
            {description}
          </span>
        );
      case -2: // đã hủy
        return (
          <span className={styles.tag} style={{ backgroundColor: "gray" }}>
            {description}
          </span>
        );

      default:
        return (
          <span
            className={styles.tag}
            style={{ backgroundColor: "lightcoral" }}
          >
            {description}
          </span>
        );
    }
  };

  renderAction = status => {
    switch (status) {
      case 1:
        return (
          <React.Fragment>
            <button
              style={{
                backgroundImage:
                  "linear-gradient(90deg, #15e627 0%, #07be3e 100%)"
              }}
              onClick={() => this.props.toggleSendBooking(this.props.billInfo)}
            >
              <MDBIcon icon="chevron-circle-right" /> Gửi phiếu
            </button>
          </React.Fragment>
        );

      default:
        return null;
    }
  };

  onGetHisoryBill = billInfo => {
    this.props.getHistoryBooking({ bookingId: billInfo.id });

    this.toggleModalHistoryBill();
  };

  handleToggleUnlockModal = selectedBooking => {
    const { isOpenUnlockModal } = this.state;
    this.setState({
      isOpenUnlockModal: !isOpenUnlockModal,
      idUnlock: selectedBooking?.lockedInfo?._id
    });
  };

  handleUnlock = async () => {
    const { idUnlock } = this.state;
    console.log(idUnlock);
    try {
      if (idUnlock) {
        await client.unlockBooking({ id: idUnlock });
        openToast("Mở khóa thành công.");
        this.setState({ isOpenUnlockModal: false });
        this.props.getBookingbyPatientCSKH({
          secretKey: window.localStorage.getItem("cskhToken")
        });
      }
    } catch (error) {
      openToast(error.response.data.message, "error");
    }
  };

  onGetAllUserCs = async billInfo => {
    try {
      const { data } = await client.getAllUserCs();
      this.setState({
        allUserCs: data,
        modalAllUserCs: !this.state.modalAllUserCs,
        medproCareInstructorId: billInfo?.medproCare?.instructor?._id
      });
    } catch (error) {
      console.log("error :>> ", error);
    }
  };

  toggleCancelCare247 = () => {
    this.setState(preState => ({
      isCancelCare247: !preState.isCancelCare247
    }));
  };

  onCancelCare247 = async ({ id, message }) => {
    try {
      await client.cancelBookingMedproCare({ id, cancelReason: message });
      this.props.getBookingbyPatientCSKH({
        secretKey: window.localStorage.getItem("cskhToken")
      });
      openToast("Hủy dịch vụ care247 thành công");
    } catch {
      openToast("Hủy dịch vụ care247 thất bại", "error");
    } finally {
      this.toggleCancelCare247();
    }
  };

  toggleConversion = async () => {
    const { billInfo } = this.props;
    try {
      // Directly call the API to update checkConvertUser with the toggled value
      await client.updateBookingCheckConvertUser({
        bookingId: billInfo._id,
        checkConvertUser: !billInfo.checkConvertUser
      });

      // Refresh the booking data after API call
      this.props.getBookingbyPatientCSKH({
        secretKey: window.localStorage.getItem("cskhToken")
      });
    } catch (error) {
      console.error("Failed to update conversion status:", error);
      openToast("Cập nhật trạng thái conversion thất bại", "error");
    }
  };

  fetchInfoBookingtoCopy = async (bookingId) => {
    try {
      const response = await client.getCopyToClipboard(bookingId);

      const {
        bookingCode = "",
        bookingCodeV1 = "",
        subject = { name: "" },
        doctor = { name: "" },
        section = { name: "" },
        patient = {},
        date = null,
        service = { name: "" },
        room = { name: "" },
        partner = { name: "", address: "" },
        sequenceNumber = "",
        status = "",
        gatewayId = "",
        subTotal = "",
        description = ""
      } = response.data || {};

      let dateSection = "";
      let timeSection = "";
      if (date) {
        const dateObj = new Date(date);
        if (!isNaN(dateObj.getTime())) {
          dateSection = dateObj.toLocaleDateString("vi-VN");
          timeSection = dateObj.toLocaleTimeString("vi-VN", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false
          });
        }
      }

      const sections = [
        `Mã phiếu: ${bookingCode}`,
        `Họ tên: ${[patient.surname, patient.name].filter(Boolean).join(" ")}`,
        dateSection && `Ngày khám: ${dateSection}`,
        room?.name && `Phòng khám: ${room.name}`,
        timeSection && `Giờ khám: ${timeSection}`,
        sequenceNumber && `Số thứ tự: ${sequenceNumber}`,
        partner?.name && `Tên cơ sở: ${partner.name}`,
        service?.name && `Dịch vụ: ${service.name}`,
        status && `Trạng thái phiếu: ${status}`,
        description && `Trạng thái phiếu: ${description}`,
        partner?.address && `Địa chỉ: ${partner.address}`,
        subTotal
          ? `Phí khám bệnh: ${subTotal.toLocaleString()}`
          : "Phí khám bệnh: Thanh toán tại cơ sở",
        gatewayId && `Cổng thanh toán: ${gatewayId}`,
        bookingCodeV1 && `Mã phiếu V1: ${bookingCodeV1}`,
        subject?.name && `Chuyên khoa: ${subject.name}`,
        doctor?.name && `Bác sĩ: ${doctor.name}`,
        section?.name && `Khu khám: ${section.name}`
      ].filter(Boolean);

      const formattedText = sections.join("\n");

      this.setState({ formattedText });
      this.toggleConfirmCopyText();

    } catch (error) {
      openToast("Sao chép thất bại", "error");
    }
  }

  onCopy = () => {
    const { formattedText } = this.state;

    openToast("Đã sao chép thông tin phiếu", "success");

    const tempInput = document.createElement("textarea");
    tempInput.id = "copytempInput";
    tempInput.value = formattedText;
    tempInput.style.position = "fixed";
    document.body.appendChild(tempInput);
    tempInput.focus();
    tempInput.select();


    try {
      document.execCommand("copy");

    } catch (err) {
      console.error("Failed to copy:", err);
    }
    document.body.removeChild(tempInput);

    this.toggleConfirmCopyText();
  }

  toggleConfirmCopyText = () => {
    this.setState({ isOpenConfirmCopyText: !this.state.isOpenConfirmCopyText });
  }

  render() {
    const { billInfo, dataIndependent } = this.props;
    const { isOpenUnlockModal, isCancelCare247, loadingRoom } = this.state;
    const checkButtonCancel = billInfo => {
      const { date, status, partnerId } = billInfo;
      const ngayKham = moment(date);
      const ngayHienTai = moment();
      var isSameOrAfter = moment(ngayHienTai).isSameOrAfter(ngayKham);
      const isHungVuong = partnerId === "bvhungvuong";

      if (isHungVuong) return "d-none";
      if (isSameOrAfter) return "d-none"; // Trước ngày thì cho hủy phiếu
      if (status !== 1) return "d-none";
    };
    const checkButtonRequestRefund = billInfo => {
      const { date, status, partnerId } = billInfo;
      const ngayKham = moment(date);
      const ngayHienTai = moment();
      var isBefore = moment(ngayHienTai).isBefore(ngayKham);
      const isHungVuong = partnerId === "bvhungvuong";

      if (isHungVuong) return "d-none";
      if (isBefore) return "d-none"; // Trong ngày OR Qua ngày thì chỉ yêu cầu hoàn tiền, không cho hủy phiếu
      if (status !== 1) return "d-none";
    };
    const hiddenBtnEditBill = billInfo => {
      const { status, partnerId, isChanged } = billInfo;
      if (partnerId === "nhidong1") {
        if (status < 0) return "d-none";
      } else {
        const LIST_PARTNER_ALLOW_UPDATE_BOOKING = ["binhthanhhcm"];

        return !LIST_PARTNER_ALLOW_UPDATE_BOOKING.includes(partnerId) ||
          [2, -2].includes(status) ||
          isChanged
          ? "d-none"
          : "";
      }
    };

    const checkBtnHistory = billInfo => {
      const { isChanged } = billInfo;

      if (!isChanged) {
        return "d-none";
      }
    };

    return (
      <MDBCard cascade className="my-2 hover-translate-y-n10 hover-shadow-lg">
        <MDBCardBody className={cx(styles.cardBodyWrapper, "py-1 px-2")}>
          <MDBContainer fluid>
            <MDBRow>
              <MDBCol xl="24" lg="24" md="24" className="pr-1 pl-0">
                <ul
                  className={cx(
                    styles.listInfo,
                    styles.cardInfoWrapper,
                    "list-group list-unstyled d-flex flex-row flex-wrap"
                  )}
                >
                  <li>
                    <label
                      className={cx(styles.value, styles.partner, "w-100")}
                    >
                      <span>{billInfo?.partner?.name}</span>
                    </label>
                  </li>
                  {/* <li>
                    {billInfo?.bookingOrderId && (
                      <span style={{ color: "blue" }}>
                        <b>
                          <i>
                            Giao dịch thanh toán cho nhiều phiếu, chưa hỗ trợ
                            thanh toán lại.
                          </i>
                        </b>
                      </span>
                    )}
                  </li> */}
                  <li>
                    <label className={cx(styles.value, styles.fullname)}>
                      {billInfo?.patient?.fullname}
                    </label>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="file-alt" className="px-1" />
                        <span>Mã phiếu: </span>
                      </label>
                      <label
                        className={cx(styles.value, styles.bookingCode)}
                        style={{ display: "flex" }}
                        onClick={() =>
                          this.props.getBillDetailHandler(billInfo)
                        }
                      >
                        {billInfo.bookingCode}{" "}
                        {billInfo?.syncBookingType === 1 ? (
                          <span className={styles.syncV1}>Version 1</span>
                        ) : null}
                        {billInfo?.lockedInfo && (
                          <span className={styles.locked}>
                            <MDBIcon far icon="lock" className="px-1" /> Đã khóa
                          </span>
                        )}
                      </label>
                    </div>
                    <div className={cx(styles.actionExtend)}>
                      {typeof billInfo?.checkConvertUser === "boolean" && (
                        <div
                          style={{
                            display: "inline-block",
                            marginLeft: "10px"
                          }}
                        >
                          <div
                            className={styles.switchToggle}
                            style={{
                              backgroundColor: billInfo.checkConvertUser
                                ? "#2196F3"
                                : "#ccc"
                            }}
                            onClick={this.toggleConversion}
                          >
                            <span
                              className={styles.switchToggleText}
                              style={{
                                left: billInfo.checkConvertUser ? "5px" : "30px"
                              }}
                            >
                              {billInfo.checkConvertUser
                                ? "Convert"
                                : "Un-Convert"}
                            </span>
                            <span
                              className={styles.switchToggleCircle}
                              style={{
                                left: billInfo.checkConvertUser ? "76px" : "4px"
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="calendar-alt" className="px-1" />
                        <span className="bg-orange">Ngày đặt: </span>
                      </label>
                      <label className={cx(styles.value)}>
                        {dataTransform(billInfo.createdAt, "date", [])}
                      </label>
                    </div>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="dot-circle" className="px-1" />
                        <span>Trạng thái: </span>
                      </label>
                      <label className={cx(styles.value)}>
                        {this.initiateStatus(
                          billInfo.status,
                          billInfo.date,
                          billInfo.description
                        )}
                      </label>
                    </div>
                    <div className={cx(styles.actionExtend)}>
                      {/* Bổ sung button thông tin hủy phiếu */}
                      <MDBBtn
                        className={styles.cancelBtn}
                        onClick={this.toggleModalCancelInfo}
                      >
                        Hoàn/Hủy
                      </MDBBtn>
                    </div>
                  </li>
                  {/* billInfo.cancelation && (
                    <label style={{ marginLeft: 5 }}>
                      (Người huỷ:{" "}
                      {get(billInfo, "cancelation.userAction.username")}
                      {get(billInfo, "cancelation.userAction.fullname")
                        ? ` - ${get(
                          billInfo,
                          "cancelation.userAction.fullname"
                        )}`
                        : ""}
                      {", "}
                      {canceledDate
                        ? moment(canceledDate).format("HH:mm DD/MM/YYYY")
                        : ""}
                      )
                    </label>
                  ) */}
                  <li className={cx(styles.rowSpecial, styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="calendar-check" className="px-1" />
                        <span>Ngày khám: </span>
                      </label>
                      <label className={cx(styles.value, styles.dateBooking)}>
                        {appointmentDate(billInfo)}
                      </label>
                    </div>
                    <div className={cx(styles.actionExtend)}>
                      {/* Bổ sung text link sao chép mã phiếu */}
                      <MDBBtn
                        className={styles.copyCode}
                        onClick={() => {
                          this.fetchInfoBookingtoCopy(billInfo._id);

                        }}
                      >
                        Sao chép
                      </MDBBtn>
                    </div>
                  </li>
                  <li className={cx(styles.groupLi)}>
                    <div className={cx(styles.groupLabel)}>
                      <label className={cx(styles.title)}>
                        <MDBIcon icon="barcode" className="px-1" />
                        <span>Mã thanh toán: </span>
                      </label>
                      <label className={cx(styles.value)}>
                        {billInfo.transactionId}
                      </label>
                    </div>
                  </li>
                  {billInfo.care247?.status === 1 ||
                    (billInfo.status === 0 && billInfo?.care247) ? (
                    <li className={styles.groupLi}>
                      <div className={cx(styles.groupLabel)}>
                        <label className={cx(styles.title)}>
                          <MDBIcon icon="shopping-cart" className="px-1" />
                          <span>Dịch vụ đặt thêm: </span>
                        </label>
                        <label className={cx(styles.value, styles.medproCare)}>
                          {billInfo?.care247?.addonServices?.[0].name}{" "}
                          {billInfo?.care247.status === -2 && "( Đã hủy )"}
                        </label>
                      </div>
                    </li>
                  ) : billInfo.care247?.status === -2 ? (
                    <li className={cx(styles.groupLi)}>
                      <div className={cx(styles.groupLabel)}>
                        <label className={cx(styles.title)}>
                          <MDBIcon icon="shopping-cart" className="px-1" />
                          <span>Dịch vụ đặt thêm: </span>
                        </label>
                        <label
                          className={cx(styles.value, styles.cancelCare247)}
                        >
                          Đã hủy dịch vụ Care247
                        </label>
                      </div>
                    </li>
                  ) : billInfo.care247?.status === 0 ? (
                    <li className={cx(styles.groupLi)}>
                      <div className={cx(styles.groupLabel)}>
                        <label className={cx(styles.title)}>
                          <MDBIcon icon="shopping-cart" className="px-1" />
                          <span>Dịch vụ đặt thêm: </span>
                        </label>
                        <label
                          className={cx(styles.value, styles.noPaymentCare247)}
                        >
                          Trợ lý cá nhân - (Chưa thanh toán)
                        </label>
                      </div>
                    </li>
                  ) : null}
                  {billInfo.isChanged && (
                    <li className={cx(styles.groupLi)}>
                      <div className={cx(styles.groupLabel)}>
                        <label className={cx(styles.title)}>
                          <MDBIcon icon="dot-circle" className="px-1" />
                          <span>Lưu ý: </span>
                        </label>
                        <label className={cx(styles.value, "text-danger")}>
                          <em> Phiếu khám đã được thay đổi thông tin</em>
                        </label>
                      </div>
                    </li>
                  )}
                  {billInfo?.refundData?.content && (
                    <li>
                      <label className={cx(styles.refund, styles.value)}>
                        Hoàn tiền:{" "}
                        {moment(billInfo?.refundData?.date).format(
                          "HH:mm, DD/MM/YYYY"
                        )}{" "}
                        - {billInfo?.refundData?.content}
                      </label>
                    </li>
                  )}
                  {billInfo?.missingInfo && (
                    <li>
                      <label className={cx(styles.missingInfo, styles.value)}>
                        {billInfo?.missingInfo}
                      </label>
                    </li>
                  )}
                </ul>
              </MDBCol>
            </MDBRow>
            <MDBRow>
              <MDBCol xl="24" lg="24" md="24" className={styles.rowBtn}>
                {/* <MDBBtn
                 size="sm"
                 color="green"
                 className={styles.btnAct}
                 onClick={() => this.props.getBillDetailHandler(billInfo)}
                >
                 <MDBIcon far icon="eye" className="px-1" />
                 Chi tiết
                </MDBBtn> */}
                {billInfo?.canPayCare247 && (
                  <MDBBtn
                    color="primary"
                    size="sm"
                    className={cx(styles.btnAct, styles.btnCare247Pay)}
                    onClick={() => {
                      this.toggleModalCare247Pay();
                      this.fetchCare247Pay(billInfo);
                    }}
                  >
                    Thanh toán Care247
                  </MDBBtn>
                )}
                {billInfo?.partnerId === "digimed" && (
                  <MDBBtn
                    size="sm"
                    color="yellow"
                    className={styles.btnAct}
                    onClick={e => {
                      e.stopPropagation();
                      this.toggleModalRoom();
                      this.fetchTelemedRoom();
                      this.fetchHistoryTelemdRoom();
                    }}
                  >
                    Trực tuyến
                  </MDBBtn>
                )}
                <MDBBtn
                  size="sm"
                  color="green"
                  className={cx(styles.btnAct, hiddenBtnEditBill(billInfo))}
                  onClick={() => this.props.handleEditBill(billInfo)}
                >
                  <MDBIcon far icon="pencil" className="px-1" />
                  Sửa phiếu
                </MDBBtn>
                <MDBBtn
                  size="sm"
                  color="blue"
                  className={cx(styles.btnAct, "d-none")}
                  onClick={() => this.props.hiddenBill(billInfo)}
                >
                  Ẩn phiếu
                </MDBBtn>
                <MDBBtn
                  color="red"
                  size="sm"
                  className={cx(styles.btnAct, checkButtonCancel(billInfo))}
                  onClick={() => {
                    this.setState({
                      modalCancelBillPay: true,
                      cancelActionType: "cancel" // Set action type to 'cancel' for Hủy phiếu
                    });
                  }}
                >
                  Hủy phiếu
                </MDBBtn>

                <MDBBtn
                  color="orange"
                  size="sm"
                  className={cx(styles.btnAct, checkButtonRequestRefund(billInfo))}
                  onClick={() => {
                    this.setState({
                      modalBillRefundRequest: true,
                      cancelActionType: "refund"
                    });
                  }}
                >
                  Yêu cầu hoàn tiền
                </MDBBtn>

                <MDBBtn
                  color="blue-grey"
                  size="sm"
                  className={cx(styles.btnAct, checkBtnHistory(billInfo))}
                  onClick={() => this.onGetHisoryBill(billInfo)}
                >
                  <MDBIcon icon="history" className="px-1" /> Lịch sử
                </MDBBtn>
                {billInfo?.lockedInfo && (
                  <MDBBtn
                    size="sm"
                    color="orange"
                    className={styles.btnAct}
                    onClick={() => this.handleToggleUnlockModal(billInfo)}
                  >
                    <MDBIcon far icon="unlock" className="px-1" />
                    Mở khóa
                  </MDBBtn>
                )}
                {/* {billInfo.status === 1 && billInfo.care247?.status === 1 && (
                  <MDBBtn
                    size="sm"
                    color="secondary"
                    className={styles.btnCancelCare247}
                    onClick={this.toggleCancelCare247}
                  >
                    Hủy dv Care 247
                  </MDBBtn>
                )} */}
              </MDBCol>
            </MDBRow>
          </MDBContainer>

          {isCancelCare247 && (
            <ModalCancelCare247
              isOpen={this.state.isCancelCare247}
              toggle={this.toggleCancelCare247}
              infoExam={billInfo}
              onCancelCare247={this.onCancelCare247}
            />
          )}
          {/* Modal room */}
          <ModalRoom
            isOpen={this.state.isOpenRoom}
            toggle={this.toggleModalRoom}
            data={this.state.roomData}
            historyTelemdRoom={this.state.historyTelemdRoom}
            databooking={billInfo}
            roomDataError={this.state.roomDataError}
            loadingRoom={loadingRoom}
            fetchTelemedRoom={this.fetchTelemedRoom}
            fetchHistoryTelemdRoom={this.fetchHistoryTelemdRoom}
          />
          {/* Modal Care 247 Pay */}
          {this.state.modalCare247Pay && <ModalCare247Pay
            billInfo={billInfo}
            loadingCare247Pay={this.state.loadingCare247Pay}
            dataIndependent={dataIndependent}
            isOpen={this.state.modalCare247Pay}
            toggle={this.toggleModalCare247Pay}
            onClose={this.toggleModalCare247Pay}
          />}
          {/* Modal Hủy phiếu */}
          {this.state.modalCancelBillPay && <ModalCancelBillPay
            isOpen={this.state.modalCancelBillPay}
            toggle={this.toggleModalCancelBillPay}
            billInfo={this.props.billInfo}
            actionType={this.state.cancelActionType || "cancel"} // Default to 'cancel' if not set
            onFallbackSuccess={() => {
              // Refresh the booking data after successful action
              this.props.getBookingbyPatientCSKH({
                secretKey: window.localStorage.getItem("cskhToken")
              });
            }}
          />}
          {/* Modal Yêu cầu hoàn tiền */}
          {this.state.modalBillRefundRequest && <ModalBillRefundRequest
            isOpen={this.state.modalBillRefundRequest}
            toggle={this.toggleModalBillRefundRequest}
            billInfo={this.props.billInfo}
            actionType={this.state.cancelActionType || "cancel"} // Default to 'cancel' if not set
            onFallbackSuccess={() => {
              // Refresh the booking data after successful action
              this.props.getBookingbyPatientCSKH({
                secretKey: window.localStorage.getItem("cskhToken")
              });
            }}
          />}
          {/* Modal history bill */}
          {this.state.modalHistoryBill && <ModalHistoryBill
            isOpen={this.state.modalHistoryBill}
            toggle={this.toggleModalHistoryBill}
            onClose={this.closeModalHistoryBill}
          />}
          {/* Modal Cancel Info */}
          {this.state.modalCancelInfo && <ModalCancelInfo
            isOpen={this.state.modalCancelInfo}
            toggle={this.toggleModalCancelInfo}
            cancelInfo={this.state.cancelInfo}
            loading={this.state.loadingCancelInfo}
          />}
        </MDBCardBody>
        <Modal
          modal={isOpenUnlockModal}
          title="Thông báo"
          children="Bạn có chắc muốn mở khóa phiếu này?"
          centered
          className="centered"
          footer
          footerConfirm="true"
          cancelText={CLOSE}
          okText={"Mở khóa"}
          onCancel={this.handleToggleUnlockModal}
          onOk={this.handleUnlock}
          toggle={this.handleToggleUnlockModal}
        />
        <Modal
          modal={this.state.isOpenConfirmCopyText}
          title="Xác nhận thông tin !!"
          centered
          className="copy-text-modal"
          footer
          footerConfirm="true"
          cancelText={CLOSE}
          okText={OK}
          onCancel={this.togglenCofirmCopyText}
          onOk={this.onCopy}
          toggle={this.toggleConfirmCopyText}
        >
          <div className={styles.copyTextContainer}>
            {this.state.formattedText ? (
              <pre className={styles.formattedTextArea}>
                {this.state.formattedText}
              </pre>
            ) : (
              <div className={styles.loadingText}>Đang tải thông tin...</div>
            )}
          </div>
        </Modal>
      </MDBCard>
    );
  }
}

const mapStateToProps = state => {
  const {
    customerService: { transactionBill, dataIndependent }
  } = state;
  return {
    dataIndependent,
    transactionBill
  };
};

const mapDispatchToProps = dispatch => ({
  getHistoryBooking: payload => {
    dispatch(getHistoryBooking(payload));
  },
  setDataHistoryBooking: value => {
    dispatch(setDataHistoryBooking(value));
  },
  getBookingbyPatientCSKH: patientInfo => {
    dispatch(getBookingbyPatientCSKH(patientInfo));
  },
  setCare247Independent: payload => {
    dispatch(getCare247IndependentCSKH(payload));
  },
  getHistoryBookingCSKH: patientInfo => {
    dispatch(getHistoryBookingCSKH(patientInfo));
  },
  medproCareRoom: fetchMedproCareRoom,
  historyTelemdRoom: fetchHistoryTelemdRoom
});

export default connect(mapStateToProps, mapDispatchToProps)(BookingCard);
