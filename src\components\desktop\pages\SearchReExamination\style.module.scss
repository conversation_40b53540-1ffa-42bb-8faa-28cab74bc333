@import "src/assets/scss/custom-variables.scss";

.img_parallax {
  height: unset !important;
  padding-top: 60px !important;
  padding-bottom: 80px !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  display: flex;
  align-items: center;

  h1 {
    margin-bottom: 20px !important;
    color: #ffffff !important;

    span {
      font-size: 2.2rem;
    }
  }

  p {
    color: #fff !important;
  }

  @media #{$medium-and-down} {
    margin-top: 60px;
  }
}

// .img_parallax {
//   // height: 300px !important;
// }
.h1_about {
  text-align: center;
  text-transform: uppercase;
  color: #ffffff;
}

.introduce {
  position: relative;
  background: #fff;
  margin: -50px 20px;
  border-radius: 5px;
  min-height: 300px;
  padding: 30px;
  border: 1px solid #ddd;

  .form {

    .formWarpper {
      display: flex;
      align-items: flex-start;
      align-content: flex-start;

      .btnSearch {
        transition: all 0.3s;
        border-radius: 0.5rem;
        padding: 0.3rem 1rem;
        margin: 0.5rem 1rem;
        background-color: #00b5f1;
        color: white;
        border: none;
        min-height: 39px;
        font-size: 12px;

        &:hover {
          transform: translateY(-0.1rem);
        }
      }

      .formControl {

        margin: 0 1rem;

        .formInput {

          fieldset {
            legend {
              margin-left: 10px;
              padding: 0 5px;
              font-size: 11px;
              font-style: italic;
              margin-bottom: 0;
              width: fit-content;
            }

            border: 1px solid;
            border-radius: .5rem;
          }

          input {
            border: none;
            // border-bottom: 1px solid;
            padding: 0.3rem 1rem;
            font-size: 13px;
            outline: none;
            border-radius: .5rem;
            max-height: 30px;
          }

          input::-webkit-input-placeholder {
            font-size: 13px;
          }
        }

        .alertSpan {
          font-size: 11px;
          color: orangered;
          font-style: italic;
        }
      }
    }
  }


  table {
    border-radius: 10px;
    border-collapse: collapse;
    border: none;
    background-color: #f5f5f5;
    width: 100%;

    tbody {

      .btnTaiKham {
        transition: all 0.3s;
        background-color: #00b5f1;
        border-radius: 8px;
        border: none;
        color: white;
        padding: 0.3rem 0.5rem;
        margin: 0.5rem;

        &:hover {
          transform: translateY(-0.1rem);
        }
      }
    }

    th,
    td {
      border: none;
      padding: 5px;

      &:first-child,
      &:last-child {
        text-align: center;
      }

      border: 1px solid white;

      padding: 5px;

      .label {
        font-weight: 500;
      }

      .value {
        margin-left: 5px;
      }

      .noRow {
        padding: 15px;
      }
    }

    thead {
      background-color: #00b5f1;
      border-radius: 5px;
      font-weight: 500;
      color: white;
    }

    th {
      &:first-child {
        border-radius: 5px 0 0 0;
      }

      &:last-child {
        border-radius: 0 5px 0 0;
      }

      font-size: 16px;
      font-weight: 400;
    }

    tr {
      &:last-child td {
        &:first-child {
          border-radius: 0 0 0 5px;
        }

        &:last-child {
          border-radius: 0 0 5px 0;
        }
      }

      &:hover td {
        background-color: gold;
      }
    }


  }
}