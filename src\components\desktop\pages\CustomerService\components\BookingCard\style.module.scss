@import "src/assets/scss/custom-variables.scss";

.fullname {
  font-size: 0.875rem;
}

.btnAct {
  min-width: 120px;
  text-transform: initial;
  height: 32px;
}

.btnCare247Pay {
  padding: 4px 10px !important;
  font-size: 13px !important;
}

.btnCancelCare247 {
  min-width: 120px;
  padding: 8px 0px !important;
}

.modalAllUserCs {
  margin-top: 10%;
}

.medproCareBtn {
  padding: 0.5rem 1rem !important;
}

.selectInstructor {
  color: #333333 !important;
}

.instructorBtnContainer {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.listInfo {
  li {
    width: 50%;
    margin: 5px 0;
    font-size: 0.875rem;
    display: flex;
    align-items: flex-start;

    &.rowSpecial {
      align-items: center;

      label {
        margin-bottom: 0;
      }
    }

    .title {
      width: auto;
    }

    .value {
      width: auto;
      font-weight: 500;
      margin-left: 5px;
    }

    .syncV1 {
      border: 1px solid transparent;
      padding: 3px 6px;
      color: white;
      background-color: #3043c6fc;
      border-radius: 4px;
    }

    .switchToggle {
      position: relative;
      display: inline-block;
      width: 100px;
      height: 24px;
      border-radius: 24px;
      transition: all 0.3s;
      cursor: pointer;
      text-align: center;
      line-height: 24px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      overflow: hidden;
    }

    .actionExtend {
      button {
        max-width: 95px;
        width: 95px;
        margin: 0;
        padding: 3px 6px;
        text-transform: capitalize;
      }
    }

    .copyCode {
      background-color: #0fb900 !important;
      padding: 5px 8px;
      color: #ffffff;
      cursor: pointer;
      margin-left: 8px;
      font-size: 13px;
      border-radius: 4px;

      &:hover {
        color: #ffffff;
      }
    }

    .cancelBtn {
      background-color: #1da1f2 !important;
      padding: 5px 8px;
      color: #ffffff;
      cursor: pointer;
      margin-left: 8px;
      font-size: 13px;
      border-radius: 4px;

      &:hover {
        color: #ffffff;
      }
    }

    .switchToggleText {
      position: absolute;
      color: white;
      line-height: 24px;
      font-size: 12px;
      z-index: 1;
    }

    .switchToggleCircle {
      position: absolute;
      height: 20px;
      width: 20px;
      bottom: 2px;
      background-color: white;
      border-radius: 50%;
      transition: all 0.3s;
    }

    .locked {
      background-color: #cd6666;
      padding: 6px 10px;
      border-radius: 10px;
      margin-left: 1rem;
      color: #fff;
      align-items: center;
      gap: 6px;
    }

    .refund {
      color: #fb4907;
    }

    &:last-child {
      width: 100%;

      .title {
        width: auto;
      }

      .value {
        width: auto;
      }

      .medproCare {
        background-color: #33b5e5;
        color: white;
        border-radius: 4px;
      }
    }

    .cancelCare247,
    .noPaymentCare247 {
      border-radius: 4px;
    }

    .cancelCare247 {
      background-color: #808080 !important;
      color: white;
    }

    .noPaymentCare247 {
      padding: 0 6px;
      background-color: #e59c33 !important;
      color: white;
    }
  }

  @media #{$medium-and-down} {
    li {
      width: 100%;

      .title {
        width: auto;
      }

      .value {
        width: auto;
      }
    }
  }

  @media (max-width: 1926px) {
    li {
      width: 100%;

      .title {
        width: auto;
      }

      .value {
        width: auto;
      }
    }
  }
}

.cardBodyWrapper {
  position: relative;
  padding: 5px 10px !important;

  .cardInfoWrapper {
    li {
      margin: 0 0 4px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        margin-bottom: 0;
        margin-left: 0;
        display: flex;
        align-items: center;
        gap: 5px;
        flex-shrink: 0;
        max-width: 100%;

        &.title {
          i {
            font-size: 15px;
            max-width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        &.value {
          .tag {
            padding: 3px 6px;
            border-radius: 4px;
          }
        }
      }

      .partner {
        font-size: 17px;
        margin-bottom: -4px;
        color: #0066ff !important;
      }

      .fullname {
        font-size: 17px;
        margin-bottom: -4px;
      }

      .bookingCode {
        border-bottom: 1px dashed #0066ff;
        cursor: pointer;
        color: #0066ff !important;
      }

      .dateBooking {
        color: #0066ff !important;
      }

      .missingInfo {
        color: #e59c33;
        border-left: 3px solid #bbbfc4;
        padding-left: 5px;
      }

      &.groupLi {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 8px;
        height: 25px;

        @media #{$small-and-down} {
          flex-direction: column;
          align-items: flex-start;
          height: auto;
        }
      }

      .groupLabel {
        display: flex;
        align-items: center;
        gap: 8px;

        @media #{$small-and-down} {
          flex-flow: wrap;
        }
      }
    }
  }
}

.rowBtn {
  text-align: center;
  display: flex;
  align-items: flex-end;
  align-content: center;
  flex-flow: wrap;
  margin-top: 5px;

  @media #{$medium-and-down} {
    flex-direction: row;
    justify-content: center;
  }

  @media (max-width: 1200px) {
    flex-direction: row;
    justify-content: center;
  }
}
