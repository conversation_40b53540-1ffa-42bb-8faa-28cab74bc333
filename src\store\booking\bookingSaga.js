import { put, call, fork, all, takeLatest, select } from "redux-saga/effects";
import * as types from "~/store/booking/bookingType";
import * as api from "~/store/booking/bookingApi";
import * as actions from "./bookingAction";
import { getBaseUrl } from "~/utils/getBaseUrl";
import * as healthInsuranceCardTypes from "~/store/payment/paymentType"; // chỗ này chưa move vào store
// import * as notiActions from "~/store/notifications/notificationsActions";
import * as totalDataTypes from "~/store/totalData/types";
import { openToast } from "~/components/common/molecules/ToastNotification";
import * as notiAction from "~/store/notifications/notificationsActions";
import { client } from "~/utils/medproSDK";
import { get } from "lodash";
import history from "~/history";
// medical bills
function* getMedicalBills(data) {
  try {
    const baseUrl = yield select(state => getBaseUrl(state.hospital));
    const response = yield call(api.getMedicalBills, baseUrl, data);
    if (response.status !== 200 || response.data.error_code)
      throw new Error("Lỗi server!");
    if (response) {
      const { data } = response;
      yield put({ type: types.GET_MEDICAL_BILLS_SUCCESS, data });
    }
  } catch (error) {
    yield put({ type: types.GET_MEDICAL_BILLS_FAILURE, error });
  }
}
function* watchGetMedicalBills() {
  yield takeLatest(types.GET_MEDICAL_BILLS, getMedicalBills);
}

// medical bill detail
function* getMedicalBillDetail(data) {
  try {
    const baseUrl = yield select(state => getBaseUrl(state.hospital));
    const response = yield call(api.getMedicalBillDetail, baseUrl, data);
    if (response.status !== 200 || response.data.error_code)
      throw new Error("Lỗi server!");
    if (response) {
      const { data } = response;
      yield put({ type: types.GET_MEDICAL_BILL_DETAIL_SUCCESS, data });
    }
  } catch (error) {
    yield put({ type: types.GET_MEDICAL_BILL_DETAIL_FAILURE, error });
  }
}
function* watchGetMedicalBillDetail() {
  yield takeLatest(types.GET_MEDICAL_BILL_DETAIL, getMedicalBillDetail);
}

// cancel medical bill
function* cancelMedicalBill({ data }) {
  try {
    const partnerid = yield select(
      state => state.totalData.paymentInformation.bookingInfo.partnerId
    );
    const { bookingInfo } = yield select(
      state => state.totalData.paymentInformation
    );
    const token = yield select(state => state.user.info.token);
    const response = yield client.cancelBooking(
      { id: bookingInfo.id },
      {
        partnerid,
        token
      }
    );
    if (response.status !== 200 || response.data.error_code)
      throw new Error("Lỗi server!");
    if (response) {
      const { data } = response;
      const transId = get(data, "transactionId");
      yield put({
        type: types.CANCEL_MEDICAL_BILL_SUCCESS,
        data
      });
      yield put({
        type: totalDataTypes.GET_BOOKING_INFO,
        payload: transId
      });

      history.push(`/phieu-kham-benh/chi-tiet/${transId}`);

      yield put({
        type: totalDataTypes.GET_ALL_MEDICAL_BILLS
      });
    }
  } catch (error) {
    const message = get(error, "response.data.message", "");
    openToast(message, "error");
    yield put({ type: types.CANCEL_MEDICAL_BILL_FAILURE, error });
  } finally {
    yield put(notiAction.getNotificationOnPage({ category: 1 }));
  }
}
function* watchCancelMedicalBill() {
  yield takeLatest(types.CANCEL_MEDICAL_BILL, cancelMedicalBill);
}

// resend sms
function* resendSMS(submitData) {
  try {
    const baseUrl = yield select(state => getBaseUrl(state.hospital));
    const response = yield call(api.resendSMS, baseUrl, submitData);
    if (response.status !== 200 || response.data.error_code)
      throw new Error("Lỗi server!");
    const { data } = response;
    if (typeof data.error_code === typeof undefined) {
      const message = "Gửi SMS thành công";
      yield put({ type: types.RESEND_SMS_SUCCESS, data });
      yield put({
        type:
          healthInsuranceCardTypes.TOGGLE_RESEND_SMS_MODAL_HEALTH_INSURANCE_CARD
      });
      yield put({
        type:
          healthInsuranceCardTypes.SHOW_RESEND_SMS_OK_MODAL_HEALTH_INSURANCE_CARD,
        message
      });
    } else {
      const message = "Bạn chỉ gởi được 2 tin nhắn cho mỗi phiếu khám bệnh.";
      yield put({
        type:
          healthInsuranceCardTypes.TOGGLE_RESEND_SMS_MODAL_HEALTH_INSURANCE_CARD
      });
      yield put({
        type:
          healthInsuranceCardTypes.SHOW_RESEND_SMS_OK_MODAL_HEALTH_INSURANCE_CARD,
        message
      });
    }
  } catch (error) {
    const message = "Có lỗi xảy ra.";
    yield put({ type: types.RESEND_SMS_FAILURE, error });
    yield put({
      type:
        healthInsuranceCardTypes.TOGGLE_RESEND_SMS_MODAL_HEALTH_INSURANCE_CARD
    });
    yield put({
      type:
        healthInsuranceCardTypes.SHOW_RESEND_SMS_OK_MODAL_HEALTH_INSURANCE_CARD,
      message
    });
  }
}
function* watchResendSMS() {
  yield takeLatest(types.RESEND_SMS, resendSMS);
}

function* submitBooking({ postData }) {
  try {
    yield put(actions.loadingSubmitBooking());
    const baseUrl = yield select(state => getBaseUrl(state.hospital));
    const response = yield call(api.submitBooking, baseUrl, postData);
    const { data } = response;
    if (data.error_code) throw Error(data.error_message);
    yield put(actions.submitBookingSuccess(data));
    if (
      data.payment_url &&
      postData.method_id !== 6 &&
      postData.method_id !== 5
    ) {
      window.location.href = data.payment_url;
    }
  } catch (error) {
    yield put(actions.submitBookingFail(error));
  }
}

function* watchSubmitBooing() {
  yield takeLatest(types.SUBMIT_BOOKING, submitBooking);
}

export default function* root() {
  yield all([
    fork(watchGetMedicalBills),
    fork(watchGetMedicalBillDetail),
    fork(watchCancelMedicalBill),
    fork(watchResendSMS),
    fork(watchSubmitBooing)
  ]);
}
