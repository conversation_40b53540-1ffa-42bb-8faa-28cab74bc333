import React, { Component } from "react";
import { MDBContainer, MDBRow, MDBCol, MDBBtn, MDBIcon } from "mdbreact";
import styles from "./style.module.scss";
import tick from "~/assets/img/desktop/HompageClinic/icon_tick.svg";
import laptop from "~/assets/img/desktop/HompageClinic/Laptop.svg";
import { data } from "./utils/data";
import { uniqueId } from "lodash-es";

class AdvanceMedproClinic extends Component {
  render() {
    return (
      <MDBContainer className={styles.Container}>
        <div className={styles.Label}>
          <p>Ưu điểm phần mềm Medpro Clinic</p>
        </div>
        <MDBRow className={styles.rowAdvance}>
          <MDBCol xl={5} md={5} xs={12} s className={styles.colAds}>
            <figure className={styles.img}>
              <img src={laptop} alt="" />
            </figure>
          </MDBCol>
          <MDBCol xl={7} md={7} xs={12} className={styles.colAdvance}>
            <ul className={styles.listAdvance}>
              {data.map(item => {
                return (
                  <li key={uniqueId()}>
                    <div className={styles.Advance}>
                      <figure className={styles.img}>
                        <img src={tick} alt="" />
                      </figure>
                      <p className={styles.content}>{item.content}</p>
                    </div>
                  </li>
                );
              })}
            </ul>
          </MDBCol>
          <div className={styles.Btn}>
            <MDBBtn className={styles.btnRegister} gradient="peach">
              Đăng kí dùng thử <MDBIcon icon="arrow-right" className="ml-3" />
            </MDBBtn>
          </div>
        </MDBRow>
      </MDBContainer>
    );
  }
}
export default AdvanceMedproClinic;
