.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.coupon {
  background-color: white;
  padding: 15px;
  width: 360px;
  max-width: 360px;
  color: #12263f;
  font-size: 14px;

  height: fit-content;
}
.btnAddonTime {
  width: 360px;
  border-radius: 10px;
  margin-top: 32px;
}
.radioCare247 {
  display: flex;
  flex-direction: column;
  gap: 4px;
  p {
    margin-bottom: 0;
  }
  .careItemName {
    font-size: 16px;
    font-weight: 500;
    line-height: 19.36px;
    text-align: left;
  }
  .careItemPrice {
    font-size: 16px;
    font-weight: 400;
    line-height: 19.36px;
    text-align: left;
    .originalPrice {
      margin-left: 20px;
      color: #d7dbe0;
      text-decoration: line-through;
    }
  }
}
.modal {
  .headerModal {
    display: flex;
    justify-content: center;
  }
  :global {
    .modal-content {
      border-radius: 14px;
    }
    .modal-header {
      border-bottom: 0;
    }
    .close {
      padding-top: 10px;
      font-size: 32px;
    }
    .modal-header .modal-title {
      width: 100%;
      text-align: center;
      font-size: 22px;
      font-weight: 600;
      line-height: 21.6px;
    }
    .modal-body {
      display: flex;
      flex-direction: column;
      gap: 8px;
      > div {
        height: 70px !important;
        border-radius: 12px;
        padding: 12px;
        border: 1px solid #d7dbe0;
      }
    }
    .modal-footer {
      border-top: 0;
    }
  }
}
