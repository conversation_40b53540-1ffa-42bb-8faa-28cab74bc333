import React, { Fragment, Component } from "react";
import { connect } from "react-redux";
import moment from "moment";
import {
  MDBListGroup,
  MDBListGroupItem,
  MDBCard,
  MDBCardBody,
  MDBCardHeader
} from "mdbreact";
import { genSlotTime } from "~/utils/genSlotTime";
// import { first } from 'lodash';
import styles from "./style.module.scss";
class PatientInfomationBooking extends Component {
  render() {
    const {
      hospital: { selectedHospital },
      dateAndSpecialist: { selectedDate, selectedSpecialist },
      selectedDoctor,
      doctorAndTime: { selectedTime }
    } = this.props;
    return (
      <Fragment>
        <MDBCard className={styles.panels}>
          <MDBCardHeader className={styles.panels_header}>
            Thông tin khám
          </MDBCardHeader>
          <MDBCardBody className={styles.card_body}>
            <h5 className={styles.hospital_name}>
              <i className="far fa-hospital" />
              {selectedHospital.name}
            </h5>

            <MDBListGroup className={styles.list_group}>
              {selectedDoctor.doctor && (
                <MDBListGroupItem>
                  <i className="fal fa-user-md" />
                  {selectedDoctor.doctor.role} {selectedDoctor.doctor.name}
                </MDBListGroupItem>
              )}
              {selectedSpecialist.name && (
                <MDBListGroupItem>
                  <i className="fal fa-stethoscope" />
                  Chuyên khoa {selectedSpecialist.name}
                </MDBListGroupItem>
              )}
              {selectedDate && (
                <MDBListGroupItem>
                  <i className="fal fa-calendar-alt"></i>
                  Ngày khám: {moment(selectedDate).format("DD/MM/YYYY")}
                </MDBListGroupItem>
              )}
              {selectedTime && selectedTime.id > 0 ? (
                <MDBListGroupItem>
                  <i className="far fa-clock" />
                  Giờ khám:{" "}
                  {selectedTime.from
                    ? genSlotTime(selectedTime.from)
                    : ""} -{" "}
                  {selectedTime.to ? genSlotTime(selectedTime.to) : ""}
                </MDBListGroupItem>
              ) : null}
            </MDBListGroup>
          </MDBCardBody>
        </MDBCard>
      </Fragment>
    );
  }
}

const mapStateToProps = state => {
  const {
    hospital,
    dateAndSpecialist,
    doctorAndTime,
    doctor: { selectedDoctor }
  } = state;
  return {
    hospital,
    dateAndSpecialist,
    doctorAndTime,
    selectedDoctor
  };
};

export default connect(mapStateToProps)(PatientInfomationBooking);
