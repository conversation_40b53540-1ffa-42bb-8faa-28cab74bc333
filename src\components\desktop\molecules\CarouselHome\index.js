/* eslint-disable no-unused-vars */
import React, { Component } from "react";
import Swiper from "swiper";
import "swiper/css/swiper.min.css";
import styles from "./style.module.scss";
import cx from "classnames";
import img1 from "~/assets/img/desktop/common/carousel1.jpg";

export default class Carousel extends Component {
  constructor(props) {
    super(props);
    this.state = {
      listSlide: [
        {
          id: 1,
          title: "ĐĂNG KÝ KHÁM BỆNH",
          subtitle:
            "Đăng ký khám bệnh theo ngày, theo bác sĩ, tái khám theo lịch hẹn.",
          img: img1
        },
        {
          id: 2,
          title: "THANH TOÁN VIỆN PHÍ",
          subtitle:
            "Đăng ký khám bệnh theo ngày, theo bác sĩ, tái khám theo lịch hẹn.",
          img: img1
        },
        {
          id: 3,
          title: "TƯ VẤN SỨC KHOẺ TRỰC TUYẾN",
          subtitle:
            "Tư vấn sức khỏe trực tuyến với các bác sĩ đầu ngành & chuyên gia.",
          img: img1
        },
        {
          id: 4,
          title: "TIN TỨC TỪ CÁC BỆNH VIỆN",
          subtitle:
            "Tin tức về sức khỏe, cập nhật kiến thức chăm sóc sức khỏe từ các chuyên gia.",
          img: img1
        },
        {
          id: 5,
          title: "KẾT QUẢ CẬN LÂM SÀNG",
          subtitle:
            "Kết quả Cận Lâm Sàng sẽ được cập nhật trực tiếp trên phần mềm.",
          img: img1
        }
      ]
    };
  }

  renderListSlide = () => {
    return this.state.listSlide.map(slide => {
      return (
        <div
          className={cx("swiper-slide", styles.carousel_slide)}
          key={slide.id}
        >
          <img src={slide.img} alt="" className="swiper-lazy" />
          <div
            className={cx("title", styles.carousel_text)}
            data-swiper-parallax-opacity="0"
          >
            <h4 className={styles.carouser_title}>{slide.title}</h4>
            <p className={styles.carousel_subtitle}>{slide.subtitle}</p>
          </div>
        </div>
      );
    });
  };

  componentDidMount() {
    // Docs của swiper: https://swiperjs.com/get-started/
    var mySwiper = new Swiper(".swiper-container", {
      effect: "coverflow",
      updateOnWindowResize: true,
      setWrapperSize: true,
      loop: true,
      loopedSlides: 6,
      centeredSlides: true,
      slidesPerView: 3,
      initialSlide: 3,
      spaceBetween: 0,
      keyboardControl: true,
      mousewheelControl: true,
      lazyLoading: true,
      preventClicks: false,
      preventClicksPropagation: false,
      lazyLoadingInPrevNext: true,
      parallax: true,
      coverflowEffect: {
        rotate: 0,
        stretch: 0,
        depth: 180,
        modifier: 2,
        slideShadows: true
      },
      autoplay: false,
      pagination: {
        el: ".swiper-pagination", // show dấu chấm
        clickable: true // click vào dấu chấm
      },
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev"
      }
    });
  }

  render() {
    return (
      <div className={cx("row", "swiper-container", styles.carousel_container)}>
        <div className={cx("swiper-wrapper", styles.carousel_wrapper)}>
          {this.renderListSlide()}
        </div>
        <div className="swiper-button-next" />
        <div className="swiper-button-prev" />
        <div className={styles.mobile_border} />
      </div>
    );
  }
}
