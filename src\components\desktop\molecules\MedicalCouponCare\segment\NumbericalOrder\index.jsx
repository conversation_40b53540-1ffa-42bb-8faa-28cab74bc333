import cx from "classnames";
import { MDBSpinner } from "mdbreact";
import moment from "moment";
import React from "react";
import styles from "./styles.module.scss";
import { get } from "lodash";
import { BOOKING_STATUS } from "~/utils/constants";

const NumbericalOrder = ({ bookingInfo, dataBookingTime }) => {
  const partnerId = get(bookingInfo, "partnerId");
  const status = get(bookingInfo, "status", 1);
  const care247 = get(bookingInfo, "medproCare", "");
  const partner = get(bookingInfo, "partner", "");
  const timeCare247 = get(care247, "time", "");

  if ([BOOKING_STATUS.CHUA_THANH_TOAN].includes(status)) {
    return "";
  }

  return (
    <div className={styles.numbericalOrder}>
      <p className={styles.timeSlot}>
        Giờ đồng hành: <span>{timeCare247}</span>
      </p>
      <p><PERSON><PERSON><PERSON> chỉ: {partner?.name}</p>
      <p>{partner?.address}</p>
    </div>
  );
};

export default NumbericalOrder;
