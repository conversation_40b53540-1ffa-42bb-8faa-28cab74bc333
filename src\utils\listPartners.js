const getDomainByEnv = () => {
  if (
    window.location.hostname.includes("testing") ||
    window.location.hostname.includes("internal-medpro.ddns.net") ||
    window.location.hostname.includes("localhost")
  ) {
    return "https://bo-api-testing.medpro.com.vn";
  } else {
    return "https://bo-api.medpro.com.vn:5000";
  }
};

const getListParners = () => {
  var xhr = new XMLHttpRequest();
  xhr.open("GET", getDomainByEnv() + "/partner-domain/list", false);
  xhr.send();
  const list = xhr.response ? JSON.parse(xhr.response) : "";
  window.localStorage.setItem("listPartners", JSON.stringify(list));
  return list;
};

export default getListParners;
