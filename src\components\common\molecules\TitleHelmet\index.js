/* eslint-disable max-len */
import React, { Fragment } from "react";
import Helmet from "react-helmet";

const withTitle = ({
  component: Component,
  title,
  meta = [],
  post = false
}) => {
  return class Title extends React.Component {
    render() {
      const defaultTitle = "Medpro | Đặt khám trực tuyến huyi";
      return (
        <Fragment>
          <Helmet
            title={title || defaultTitle}
            htmlAttributes={{ lang: "vi" }}
            meta={meta}
          />

          <Component {...this.props} />
        </Fragment>
      );
    }
  };
};

export { withTitle };
