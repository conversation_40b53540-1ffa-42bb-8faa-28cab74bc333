import { MDBModal, MDBModalBody, MDBModalHeader } from "mdbreact";
import moment from "moment";
import { createForm } from "rc-form";
import React, { useEffect } from "react";
import ReactInputMask from "react-input-mask";
import { connect } from "react-redux";
import Select from "~/components/common/atoms/Select";
import history from "~/history";
import { getRouteFollowPartnerId } from "~/utils/func";
import styles from "./styles.module.scss";

const AddInfoXNC = ({
  open,
  toggle,
  selectedPatientId,
  quocGiaXuatCanh,
  postInfoXNC,
  form,
  partnerId
}) => {
  useEffect(() => {
    form.setFieldsInitialValue({
      countryId: quocGiaXuatCanh.length <= 1 ? quocGiaXuatCanh[0]?.id : ""
    });
  }, [quocGiaXuatCanh, selectedPatientId, form]);

  const handleOke = event => {
    event.preventDefault();

    form.validateFields((error, value) => {
      // console.log("error :>> ", error);
      // console.log("value :>> ", value);
      if (!error) {
        postInfoXNC({
          ...value,
          patientId: selectedPatientId,
          interviewDate: moment(
            value?.interviewDate,
            "DD/MM/YYYY"
          ).toISOString()
        });

        history.push(getRouteFollowPartnerId("/xac-nhan-thong-tin", partnerId));
      } else {
      }
    });
  };

  const handleChangeType = id => {
    form.setFieldsValue({
      countryId: ""
    });
  };

  const onChange = e => {
    const { value } = e.target;
    form.setFieldsValue({
      profileCode: value
    });
  };

  const validatorDay = async (rule, value, cb) => {
    const date = moment(value, "DD/MM/YYYY");
    if (!date.isValid()) {
      cb("Vui lòng nhập đúng định dạng ngày tháng! Ví dụ: 19/07/2021");
    } else {
      cb();
    }
  };

  const { getFieldProps, getFieldError } = form;

  const errorMessage = ele => {
    const field = getFieldError(ele);

    const mess = field ? field.join(", ") : null;
    return <span className={styles.alertSpan}>{mess}</span>;
  };

  return (
    <div className={styles.container}>
      <MDBModal isOpen={open} centered>
        <MDBModalHeader
          className={styles.Header}
          titleClass="w-100 font-weight-bold"
          toggle={toggle}
        >
          <i className="fal fa-bell" style={{ fontSize: "18px" }} />
          <span className={styles.TitleHeader}>Bổ sung thông tin</span>
        </MDBModalHeader>
        <MDBModalBody className={styles.Body}>
          <p className={styles.description}>
            Vui lòng bổ sung thông tin cho người khám xuất nhập cảnh:
          </p>

          <form>
            <div className={styles.AddInfo}>
              <label>
                Số phỏng vấn: <sup style={{ color: "red" }}>*</sup>{" "}
                <span style={{ color: "gray" }}>(Case number)</span>
              </label>
              <input
                {...getFieldProps("profileCode", {
                  rules: [
                    {
                      required: true,
                      message: "Vui lòng nhập số hồ sơ !"
                    }
                  ]
                })}
                className={styles.Input}
                type="text"
                placeholder="Vui lòng nhập số hồ sơ (ví dụ: HCMXXXX XXXXXX)"
                onChange={onChange}
              />
              {errorMessage("profileCode")}
            </div>

            <div className={styles.AddInfo}>
              <label>
                Quốc gia xuất cảnh: <sup style={{ color: "red" }}>*</sup>
              </label>
              {/* <input className={styles.Input} type="text" placeholder="Ex:Mỹ" /> */}
              <Select
                data={quocGiaXuatCanh}
                onChange={handleChangeType}
                {...getFieldProps("countryId", {
                  rules: [
                    {
                      required: true,
                      message: "Vui lòng chọn Quốc gia xuất cảnh !"
                    }
                  ]
                })}
              />
              {errorMessage("countryId")}
            </div>

            <div className={styles.AddInfo}>
              <label>
                Ngày phỏng vấn: <sup style={{ color: "red" }}>*</sup>
              </label>

              <ReactInputMask
                className="form-control"
                {...getFieldProps("interviewDate", {
                  rules: [{ validator: validatorDay }]
                })}
                placeholder="Nhập Ngày/Tháng/Năm"
                mask="99/99/2029"
                type="text"
              />

              {errorMessage("interviewDate")}
            </div>
            <button className={styles.nextObj} onClick={handleOke}>
              Tiếp tục
            </button>
          </form>
        </MDBModalBody>
      </MDBModal>
    </div>
  );
};

const mapStateToProps = state => {
  const {
    totalData: { partnerId }
  } = state;

  return { partnerId };
};

const mapDispatchToProps = {};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  createForm({
    mapPropsToFields: props => {},
    onFieldsChange: (props, fields) => {}
  })(AddInfoXNC)
);
