import cx from "classnames";
import { get } from "lodash";
import React from "react";
import Barcode from "react-barcode";
import styles from "./styles.module.scss";
const QRCode = require("qrcode.react");

const TypeCode = ({ code, treeId }) => {
  switch (code?.type) {
    case "barcode":
      return (
        <React.Fragment>
          {code?.value && <p>{code?.title}</p>}
          {code?.isNew && (
            <p className={styles.isNewUserTxt}>{code?.message}</p>
          )}
          {code?.value && (
            <Barcode
              value={code?.value}
              format="CODE128"
              height={50}
              width={1}
              fontSize={16}
            />
          )}
        </React.Fragment>
      );
    case "qrcode":
      return (
        <React.Fragment>
          {code?.value && <p>{code?.title}</p>}
          {code?.isNew && (
            <p className={styles.isNewUserTxt}>{code?.message}</p>
          )}
          {code?.value && (
            <QRCode fgColor="#000000" size={90} value={code?.value} />
          )}
          {treeId === "CLS" && <p className="mt-2">{code?.value}</p>}
        </React.Fragment>
      );
    default:
      return null;
  }
};

const DisplayTypeCode = ({ bookingInfo }) => {
  const status = get(bookingInfo, "status", 1);
  const treeId = get(bookingInfo, "treeId", "");
  const displayCodeBooking = get(bookingInfo, "displayCodeBooking", "");

  if ([-2, 0, 6].includes(status)) return null;

  return (
    <div className={cx(styles.displayCodeBooking)}>
      <TypeCode code={displayCodeBooking} treeId={treeId} />
    </div>
  );
};

export default DisplayTypeCode;
