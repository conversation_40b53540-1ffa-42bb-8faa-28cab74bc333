import {
  MDBModalBody,
  MDBModalHeader,
  MDBModal,
  MDBModalFooter,
  MDBBtn,
  MDBSpinner,
  MDBIcon
} from "mdbreact";
import React, { useEffect, useRef } from "react";
import styles from "./style.module.scss";
import moment from "moment";

const ModalRoom = ({
  isOpen,
  toggle,
  data,
  databooking,
  roomDataError,
  loadingRoom,
  fetchTelemedRoom,
  historyTelemdRoom,
  fetchHistoryTelemdRoom
}) => {
  // #region State
  const timerRef = useRef(null);
  const doctor = data?.users?.find(user => user.role === "Doctor");
  const patient = data?.users?.find(user => user.role === "Patient");
  useEffect(() => {
    if (isOpen) {
      startAutoRefresh();
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isOpen]);
  /**
   * Bắt đầu quá trình tự động làm mớ<PERSON> dữ liệ<PERSON> phòng Telemed.
   * - Nếu `timerRef.current` đã tồn tại, nó sẽ bị xóa để tránh nhiều interval chạy cùng lúc.
   * - Gọi `fetchTelemedRoom()` ngay lập tức để cập nhật dữ liệu ban đầu.
   * - Gọi `fetchHistoryTelemdRoom()` ngay lập tức để cập nhật lịch sử tham gia.
   *
   * @function startAutoRefresh
   */
  const startAutoRefresh = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    fetchTelemedRoom();
    fetchHistoryTelemdRoom();
  };

  const getIconByEvent = (event, role) => {
    if (event === "participantJoined") {
      return role === "Doctor" ? "user-md" : "user";
    } else if (event === "participantLeft") {
      return "sign-out-alt";
    }
    return "info-circle";
  };

  const getColorByEvent = event => {
    if (event === "participantJoined") {
      return "success";
    } else if (event === "participantLeft") {
      return "warning";
    }
    return "info";
  };

  const formatTime = timeString => {
    if (!timeString) return "";

    // Handle both formats (timestamps and date strings)
    if (
      timeString.includes("checkInTime") ||
      timeString.includes("checkOutTime")
    ) {
      return timeString;
    }

    return moment(timeString).format("HH:mm, DD/MM/YYYY");
  };

  return (
    <MDBModal isOpen={isOpen} centered>
      <MDBModalHeader className={styles.headerModal}>
        Booking ID: {databooking?.bookingCode}
      </MDBModalHeader>
      <MDBModalBody className={styles.bodyModal}>
        <div className={loadingRoom ? styles.loadingBlur : ""}>
          {data?.users ? (
            <div
              className="d-flex justify-content-between"
              style={{ width: "100%" }}
            >
              <div className="doctor-side" style={{ wdth: "50%" }}>
                <h5>Thông tin bác sĩ</h5>
                {doctor ? (
                  <>
                    <div>Họ và tên: {doctor.doctorName}</div>
                    <div>Học hàm / học vị: {doctor.doctorRole}</div>
                    <div>Thời gian vào: {doctor.createdAtTimestamp}</div>
                  </>
                ) : (
                  <div className={styles.noUserMessage}>
                    Chưa có bác sĩ trong phòng
                  </div>
                )}
              </div>

              <div className="patient-side" style={{ wdth: "50%" }}>
                <h5>Thông tin bệnh nhân</h5>
                {patient ? (
                  <>
                    <div>Tên: {patient.patientName}</div>
                    <div>Giới tính: {patient.patientSex}</div>
                    <div>SĐT: {patient.phone}</div>
                    <div>Mã đặt khám: {patient.bookingCode}</div>
                    <div>Thời gian vào: {patient.createdAtTimestamp}</div>
                  </>
                ) : (
                  <div className={styles.noUserMessage}>
                    Chưa có bệnh nhân trong phòng
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className={styles.messageContent}>{roomDataError}</div>
          )}
        </div>
        {loadingRoom && (
          <div className={styles.loadingOverlay}>
            <MDBSpinner big />
          </div>
        )}
        <div className={styles.timelineSection}>
          <h5 className="mt-4 mb-3">Lịch sử tham gia phiếu khám</h5>
          {historyTelemdRoom && historyTelemdRoom.history && (
            <div className={styles.verticalTimelineWrapper}>
              {[...historyTelemdRoom.history].reverse().map((item, index) => (
                <div
                  key={index}
                  className={
                    styles.timelineItem +
                    " " +
                    (item.role === "Doctor" ? styles.doctor : styles.patient) +
                    " " +
                    (item.event === "participantJoined"
                      ? styles.joined
                      : styles.left)
                  }
                >
                  <div className={styles.timelineDot}></div>
                  <div className={styles.timelineContent}>
                    <div className={styles.timelineHeader}>
                      <span className={styles.timelineTitle}>
                        {item.role === "Doctor" ? "Bác sĩ" : "Bệnh nhân"}:{" "}
                        {item.doctorName || item.user || "Không xác định"}
                        {item.platform && (
                          <span className={styles.timelineDevice}>
                            {item.platform}
                          </span>
                        )}
                      </span>
                      <span className={styles.timelineStatus}>
                        {item.event === "participantJoined"
                          ? "Tham gia"
                          : "Rời đi"}
                      </span>
                    </div>
                    <div className={styles.timelineTime}>
                      {item.checkInTime
                        ? formatTime(item.checkInTime)
                        : formatTime(item.checkOutTime)}
                      {item.location && (
                        <div className={styles.timelineLocation}>
                          {item.location}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </MDBModalBody>
      <MDBModalFooter>
        <MDBBtn
          size="sm"
          color="orange"
          className={styles.btnAct}
          onClick={startAutoRefresh}
        >
          Tải lại
        </MDBBtn>
        <MDBBtn
          size="sm"
          color="primary"
          className={styles.btnAct}
          onClick={toggle}
        >
          Đóng
        </MDBBtn>
      </MDBModalFooter>
    </MDBModal>
  );
};

export default ModalRoom;
