apiVersion: v1
kind: ServiceAccount
metadata:
  name: sva-medpro
  namespace: medpro-web
  labels:
    app: medpro-web
imagePullSecrets:
  - name: docker-registry
    
---
apiVersion: v1
data:
  .dockerconfigjson: ************************************************************************************************************************
kind: Secret
metadata:
  name: docker-registry
  namespace: medpro-web
type: kubernetes.io/dockerconfigjson