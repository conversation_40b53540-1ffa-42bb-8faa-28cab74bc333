import cx from "classnames";
import { find, get } from "lodash";
import {
  MDBAnimation,
  MDBCol,
  MDBContainer,
  MDBListGroup,
  MDBListGroupItem,
  MDBRow
} from "mdbreact";
import React from "react";
import LazyLoad from "react-lazyload";
import calendar from "~/assets/img/common/calendar.svg";
import care from "~/assets/img/common/care.svg";
import ios from "~/assets/img/common/logo/appstore.svg";
import android from "~/assets/img/common/logo/googleplay.svg";
import medicine from "~/assets/img/common/medicine.svg";
import organic from "~/assets/img/common/organic.svg";
import pay from "~/assets/img/common/pay.svg";
import TagName from "~/components/common/atoms/TagName";
import { partnerInfo } from "~/configs/partnerDetails";
import {
  LINK_ANDROID_APP,
  LINK_IOS_APP,
  urlSlide
} from "~/utils/manageResource";
import partnerId from "~/utils/partner";
import styles from "./style.module.scss";

const FeatureHospital = () => {
  const info = get(partnerInfo, "info");
  const nameInfo = find(info, { key: "name" });

  return (
    <div
      className={cx(
        styles.feature_medpro,
        styles["feature_medpro_" + partnerId]
      )}
      id="download_application"
    >
      <MDBContainer>
        <MDBRow>
          <MDBCol size={12}>
            <MDBAnimation
              reveal="true"
              type="fadeIn"
              className={styles.feature_medpro_inner}
            >
              <TagName element="span" className={["title_section"]}>
                download
              </TagName>
              <TagName
                element="h3"
                className={["sub_title_section", "title_featureMedpro"]}
              >
                Tải ứng dụng
                <div className={styles.nameHospital}>
                  {`${nameInfo.value}`.toUpperCase()}
                </div>
              </TagName>
              <MDBListGroup className={styles.list_group}>
                <MDBListGroupItem>
                  <a
                    href={LINK_IOS_APP}
                    alt="apple-app-icon"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <LazyLoad height={45}>
                      <img src={ios} alt="" />
                    </LazyLoad>
                  </a>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <a
                    href={LINK_ANDROID_APP}
                    alt="google-app-icon"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <LazyLoad height={45}>
                      <img src={android} alt="" />
                    </LazyLoad>
                  </a>
                </MDBListGroupItem>
              </MDBListGroup>
            </MDBAnimation>
          </MDBCol>
        </MDBRow>
        <MDBRow>
          <MDBCol size={12}>
            <MDBAnimation
              reveal="true"
              type="fadeIn"
              className={styles.feature_medpro_list}
            >
              <MDBListGroup className={cx(styles.list_group, styles.list_left)}>
                <MDBListGroupItem>
                  <span>Chủ động đặt lịch khám trong vòng 1 phút </span>
                  <img src={calendar} alt="" />
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <span> Thay đổi & cập nhật lịch khám bệnh </span>
                  <LazyLoad height={55}>
                    <img src={calendar} alt="" />
                  </LazyLoad>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <span> Đặt lịch nhắc nhở uống thuốc </span>
                  <LazyLoad height={55}>
                    <img src={medicine} alt="" />
                  </LazyLoad>
                </MDBListGroupItem>
              </MDBListGroup>
              <div className={styles.dt}>
                <LazyLoad height={520}>
                  <img src={urlSlide} alt="" className={styles.img_fluid} />
                </LazyLoad>
              </div>
              <MDBListGroup
                className={cx(styles.list_group, styles.list_right)}
              >
                <MDBListGroupItem>
                  <LazyLoad height={55}>
                    <img src={organic} alt="" />
                  </LazyLoad>
                  <span>Giao diện thân thiện dễ sử dụng</span>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <LazyLoad height={55}>
                    <img src={pay} alt="" />
                  </LazyLoad>
                  <span>Thanh toán nhanh chóng và tiện lợi</span>
                </MDBListGroupItem>
                <MDBListGroupItem>
                  <LazyLoad height={55}>
                    <img src={care} alt="" />
                  </LazyLoad>
                  <span>Lưu trữ và theo dõi hồ sơ sức khỏe của chính bạn</span>
                </MDBListGroupItem>
              </MDBListGroup>
            </MDBAnimation>
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    </div>
  );
};

export default FeatureHospital;
