# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
package-lock.json

npm-debug.log*
yarn.lock
yarn-debug.log*
yarn-error.log*



# format
.vscode

#

# Dockerfile
# _update.sh
# .gitlab-ci.yml