.numbericalOrder {
  .number,
  .gray {
    font-family: <PERSON><PERSON>;
    font-size: 12px;
    color: #24313d;
    font-weight: bold;
    line-height: 14px;
    text-align: center;
    margin-bottom: 4px;

    .txtNum {
      font-size: 16px !important;
      font-weight: 700 !important;
      line-height: 19px;
      color: #24313d;
    }
    .txtNum1 {
      margin-bottom: 0;
      font-size: 16px !important;
      font-weight: 700 !important;
      line-height: 19px;
      color: #24313d;
    }
  }
  .title {
    margin-bottom: 4px;
    font-size: 12px;
    font-weight: 400;
    line-height: 14.52px;
  }
  .classWaiting {
    font-size: 40px !important;
  }
  .awaitMessage {
    color: #ffb340;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    flex-direction: column;
    margin-bottom: 15px;

    p {
      padding: 0 35px;
    }

    .spin {
      display: block;
    }
  }
  .numbericalOrderNext {
    display: flex;
    flex-direction: column;
    gap: 4px;
    border-radius: 8px;
    padding: 12px;
    border: #11a2f3 1px solid;
  }
}
