import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MD<PERSON>pinner } from "mdbreact";
import React, { useEffect } from "react";
import { connect } from "react-redux";
import TagName from "~/components/common/atoms/TagName";
import FormStepper from "~/components/desktop/molecules/FormStepper";
import { clearState } from "~/store/patient/patientAction";
import {
  onChangeCity,
  onChangeCountry,
  onChangeDistrict,
  requestAllCareers,
  requestAllCountries,
  requestAllNation,
  requestAllRelationShip
} from "~/store/resource/resourceAction";
import styles from "./style.module.scss";

const PatientUpdate = ({
  requestAllCountries,
  requestAllCareers,
  requestAllRelationShip,
  onChangeCity,
  onChangeDistrict,
  requestAllNation,
  history,
  selectedPatient,
  dataDistrict,
  loading,
  patientForm,
  requiredFields
}) => {
  useEffect(() => {
    requestAllCountries();
    requestAllCareers();
    requestAllRelationShip();
    requestAllNation();
    onChangeCity({
      id: selectedPatient.city_id
    });
  }, [
    requestAllCareers,
    requestAllCountries,
    requestAllNation,
    requestAllRelationShip,
    selectedPatient,
    onChangeCity
  ]);

  useEffect(() => {
    if (
      patientForm?.info?.city_id?.value === selectedPatient.city_id &&
      patientForm?.info?.district_id?.value
    ) {
      onChangeDistrict({
        id: selectedPatient.district_id
      });
    }
  }, [dataDistrict, onChangeDistrict, selectedPatient]);

  useEffect(() => {
    if (patientForm?.info?.city_id?.value !== selectedPatient.city_id) {
      onChangeDistrict({
        id: patientForm?.info?.district_id?.value
      });
    }
  }, [onChangeDistrict, selectedPatient, patientForm]);

  if (loading) {
    return (
      <div className={styles.loading_spinner}>
        <div className={styles.loading}>
          <MDBSpinner big crazy tag="div" />
        </div>
      </div>
    );
  }
  return (
    <div className={styles.wapper_page_desktop}>
      <MDBContainer>
        <MDBRow>
          <MDBCol md="12">
            <div className={styles.wapper_page_inner}>
              <TagName
                element="h1"
                className={[
                  "title_component",
                  "title_line",
                  "title_header_mobile"
                ]}
              >
                <span> Cập nhật thông tin</span>
              </TagName>
              <FormStepper
                goBack={history.goBack}
                type="update"
                requiredFields={requiredFields}
              />
            </div>
          </MDBCol>
        </MDBRow>
      </MDBContainer>
    </div>
  );
};

const mapStateToProps = state => {
  const {
    patientForm,
    patient: { selectedPatient },
    resource: { dataDistrict },
    totalData: { loading }
  } = state;
  return { selectedPatient, dataDistrict, loading, patientForm };
};

const mapDispatchToProps = {
  requestAllCountries: requestAllCountries,
  requestAllCareers: requestAllCareers,
  requestAllRelationShip: requestAllRelationShip,
  requestAllNation: requestAllNation,
  onChangeCountry: onChangeCountry,
  onChangeCity: onChangeCity,
  onChangeDistrict: onChangeDistrict,
  clearState: clearState
};

export default connect(mapStateToProps, mapDispatchToProps)(PatientUpdate);
