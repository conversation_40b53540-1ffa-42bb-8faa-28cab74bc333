import { MDBBtn, MDBModal, MDBModalBody, MDBModalHeader } from "mdbreact";
import React from "react";
import { connect } from "react-redux";
import { client } from "~/utils/medproSDK";
import styles from "./style.module.scss";
import { openToast } from "~/components/common/molecules/ToastNotification";
import { validatePhoneNumber } from "~/utils/func";

class ModalCare247Pay extends React.Component {
  state = {
    phone: ""
  };

  handlePhoneChange = e => {
    this.setState({ phone: e.target.value });
  };

  handleCopy = () => {
    const { dataIndependent } = this.props;
    if (dataIndependent?.link) {
      console.log("navigator.clipboard");
      var textArea = document.createElement("input");
      textArea.value = dataIndependent.link;
      textArea.style.position = "fixed";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand("copy");
      } catch (err) {
        console.error("Failed to copy:", err);
      }

      document.body.removeChild(textArea);

      openToast("Đường dẫn đã được sao chép!");
    } else {
      openToast("Không có đường dẫn để sao chép!", "error");
    }
  };

  handleSendSMS = async () => {
    const { dataIndependent } = this.props;
    const { phone } = this.state;

    // Kiểm tra số điện thoại hợp lệ
    if (!validatePhoneNumber(phone)) {
      openToast("Số điện thoại không hợp lệ!", "error");
      return;
    }

    try {
      // Gọi API chỉ với số điện thoại
      await client.sendSmsCare247PaymentIndependent({
        phone,
        token: dataIndependent.token
      });
      openToast("Đã gửi tin nhắn thành công!", "success");
    } catch (error) {
      openToast("Gửi tin nhắn thất bại!", "error");
    }
  };

  render() {
    const { isOpen, toggle, dataIndependent } = this.props;
    return (
      <MDBModal
        isOpen={isOpen}
        toggle={toggle}
        backdrop={false}
        className={styles.ModalSendBooking}
      >
        <MDBModalHeader toggle={toggle} className={styles.header}>
          Dịch vụ giúp việc cá nhân - Care247
        </MDBModalHeader>
        <MDBModalBody>
          <div className={styles.section}>
            <label className={styles.label}>
              <i className="fas fa-link" style={{ marginRight: 4 }} />
              Đường dẫn thanh toán DV đặt thêm Care247:
            </label>
            <div className={styles.copyRow}>
              <input
                className={styles.input}
                type="text"
                value={dataIndependent?.link}
                readOnly
              />
              <MDBBtn
                color="primary"
                className={styles.copyBtn}
                onClick={this.handleCopy}
                disabled={!dataIndependent?.link}
              >
                SAO CHÉP
              </MDBBtn>
            </div>
          </div>

          <div className={styles.section}>
            <label className={styles.label}>
              <i className="fas fa-mobile-alt" style={{ marginRight: 4 }} />
              Gửi SMS:
            </label>
            <div className={styles.smsRow}>
              <input
                className={styles.input}
                type="text"
                placeholder="Nhập số điện thoại"
                value={this.state.phone}
                onChange={this.handlePhoneChange}
              />
              <MDBBtn
                color="primary"
                className={styles.sendBtn}
                onClick={this.handleSendSMS}
              >
                GỬI
              </MDBBtn>
            </div>
          </div>
        </MDBModalBody>
      </MDBModal>
    );
  }
}
const mapStateToProps = state => {
  const {
    customerService: { historyBooking }
  } = state;
  return {
    historyBooking
  };
};

const mapDispatchToProps = dispatch => ({});

export default connect(mapStateToProps, mapDispatchToProps)(ModalCare247Pay);
