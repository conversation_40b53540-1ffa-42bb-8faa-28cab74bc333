select {
  font-size: 0.875rem !important;
}

.customSelectWrapper {
  position: relative;
  width: 100%;
  margin: 0 !important;

  &.disabled {
    .customSelectBox {
      cursor: no-drop;
      background: #f0f2f4 !important;
    }
  }
}

.isDefault {
  color: #6c757d !important;
}

.customSelectBox {
  border: 1px solid rgba(3, 42, 95, 0.1);
  padding: 8px;
  cursor: pointer;
  background: white;
  border-radius: 5px;
  min-height: 45px;
  color: #032a5f;
  font-size: 16px;
  font-style: normal;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 !important;

  .arrowIcon {
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #333;
    margin-right: 4px;

    &::after {
      content: "";
      display: block;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 4px solid #333;
      margin-top: 6px;
      margin-left: -4px;
    }
  }
}

.customSelectDropdown {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  z-index: 100;
  border-radius: 5px;
}

.inputWrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  margin: 0 !important;

  .arrowIcon {
    position: absolute;
    top: 50%;
    right: 13px;
    transform: translateY(-50%);
    pointer-events: none;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #333;

    &::after {
      content: "";
      display: block;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 4px solid #333;
      margin-top: 6px;
      margin-left: -4px;
    }
  }
}

.customSelectSearch {
  width: 100%;
  padding: 8px;
  border: 1px solid #0352cc !important;
  margin-bottom: 0 !important;
}

.customSelectOptions {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 150px;
  overflow: auto;
  //margin-top: 1px;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 0px 2px rgba(0, 0, 0, 0.16),
    0 0px 0px rgba(0, 0, 0, 0.19);

  &::-webkit-scrollbar {
    display: block !important;
    width: 8px;
    height: 8px;
    opacity: 1;
    transition: opacity 0.3s ease;
    cursor: grab;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background: #4687ca !important;
    transition: background-color 0.3s ease;
    cursor: grab;
    display: block !important;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f5f5 !important;
    border-radius: 8px;
  }

  &::-webkit-scrollbar-corner {
    background-color: #f5f5f5;
  }

  &.notSeach {
    .customSelectOption {
      &:hover,
      &.active {
        &:first-child {
          border-top-left-radius: 8px;
        }
      }
    }
  }
}

.customSelectOption {
  padding: 4px 8px;
  cursor: pointer;

  &:hover,
  &.active {
    background: #0352cc;
    color: white;

    &:last-child {
      border-bottom-left-radius: 8px;
    }
  }
}

.customSelectNoOption {
  padding: 8px;
  color: #999;
}
